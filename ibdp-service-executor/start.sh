#!/bin/bash

# IBDP Service Executor 启动脚本

# 设置Java环境变量
export JAVA_HOME=${JAVA_HOME:-/usr/lib/jvm/java-8-openjdk}
export PATH=$JAVA_HOME/bin:$PATH

# 设置应用目录
APP_HOME=$(cd "$(dirname "$0")" && pwd)
APP_NAME="ibdp-service-executor"
JAR_FILE="$APP_HOME/target/$APP_NAME-3.0.0.jar"
PID_FILE="$APP_HOME/$APP_NAME.pid"
LOG_FILE="$APP_HOME/logs/$APP_NAME.log"

# 创建日志目录
mkdir -p "$APP_HOME/logs"

# JVM参数
JVM_OPTS="-Xms512m -Xmx1024m -XX:+UseG1GC -XX:+PrintGCDetails -XX:+PrintGCTimeStamps -Xloggc:$APP_HOME/logs/gc.log"

# DataX配置
DATAX_HOME=${DATAX_HOME:-/opt/datax}
export DATAX_HOME

# 启动函数
start() {
    if [ -f "$PID_FILE" ]; then
        PID=$(cat "$PID_FILE")
        if ps -p $PID > /dev/null 2>&1; then
            echo "$APP_NAME is already running (PID: $PID)"
            return 1
        else
            rm -f "$PID_FILE"
        fi
    fi

    echo "Starting $APP_NAME..."
    
    # 检查JAR文件是否存在
    if [ ! -f "$JAR_FILE" ]; then
        echo "Error: JAR file not found: $JAR_FILE"
        echo "Please run 'mvn clean package' first"
        return 1
    fi

    # 启动应用
    nohup java $JVM_OPTS -Ddatax.home=$DATAX_HOME -jar "$JAR_FILE" > "$LOG_FILE" 2>&1 &
    
    PID=$!
    echo $PID > "$PID_FILE"
    
    echo "$APP_NAME started (PID: $PID)"
    echo "Log file: $LOG_FILE"
}

# 停止函数
stop() {
    if [ ! -f "$PID_FILE" ]; then
        echo "$APP_NAME is not running"
        return 1
    fi

    PID=$(cat "$PID_FILE")
    if ! ps -p $PID > /dev/null 2>&1; then
        echo "$APP_NAME is not running"
        rm -f "$PID_FILE"
        return 1
    fi

    echo "Stopping $APP_NAME (PID: $PID)..."
    kill $PID

    # 等待进程结束
    for i in {1..30}; do
        if ! ps -p $PID > /dev/null 2>&1; then
            rm -f "$PID_FILE"
            echo "$APP_NAME stopped"
            return 0
        fi
        sleep 1
    done

    # 强制杀死进程
    echo "Force killing $APP_NAME..."
    kill -9 $PID
    rm -f "$PID_FILE"
    echo "$APP_NAME force stopped"
}

# 重启函数
restart() {
    stop
    sleep 2
    start
}

# 状态检查函数
status() {
    if [ ! -f "$PID_FILE" ]; then
        echo "$APP_NAME is not running"
        return 1
    fi

    PID=$(cat "$PID_FILE")
    if ps -p $PID > /dev/null 2>&1; then
        echo "$APP_NAME is running (PID: $PID)"
        return 0
    else
        echo "$APP_NAME is not running"
        rm -f "$PID_FILE"
        return 1
    fi
}

# 查看日志函数
logs() {
    if [ -f "$LOG_FILE" ]; then
        tail -f "$LOG_FILE"
    else
        echo "Log file not found: $LOG_FILE"
    fi
}

# 主函数
case "$1" in
    start)
        start
        ;;
    stop)
        stop
        ;;
    restart)
        restart
        ;;
    status)
        status
        ;;
    logs)
        logs
        ;;
    *)
        echo "Usage: $0 {start|stop|restart|status|logs}"
        echo ""
        echo "Commands:"
        echo "  start   - Start the executor service"
        echo "  stop    - Stop the executor service"
        echo "  restart - Restart the executor service"
        echo "  status  - Check service status"
        echo "  logs    - View service logs"
        echo ""
        echo "Environment Variables:"
        echo "  JAVA_HOME  - Java installation directory (default: /usr/lib/jvm/java-8-openjdk)"
        echo "  DATAX_HOME - DataX installation directory (default: /opt/datax)"
        exit 1
        ;;
esac

exit $?
