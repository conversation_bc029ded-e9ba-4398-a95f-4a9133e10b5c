@echo off
rem IBDP Service Executor 启动脚本 (Windows)

setlocal enabledelayedexpansion

rem 设置应用信息
set APP_NAME=ibdp-service-executor
set APP_VERSION=3.0.0
set JAR_FILE=%~dp0target\%APP_NAME%-%APP_VERSION%.jar

rem 设置Java环境
if "%JAVA_HOME%"=="" (
    set JAVA_CMD=java
) else (
    set JAVA_CMD=%JAVA_HOME%\bin\java
)

rem JVM参数
set JVM_OPTS=-Xms512m -Xmx1024m -XX:+UseG1GC

rem DataX配置
if "%DATAX_HOME%"=="" (
    set DATAX_HOME=C:\datax
)

rem 创建日志目录
if not exist "%~dp0logs" mkdir "%~dp0logs"

rem 检查JAR文件是否存在
if not exist "%JAR_FILE%" (
    echo Error: JAR file not found: %JAR_FILE%
    echo Please run 'mvn clean package' first
    pause
    exit /b 1
)

rem 启动应用
echo Starting %APP_NAME%...
echo JAR File: %JAR_FILE%
echo DataX Home: %DATAX_HOME%
echo.

"%JAVA_CMD%" %JVM_OPTS% -Ddatax.home=%DATAX_HOME% -jar "%JAR_FILE%"

pause
