package com.sevb.executor.service;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.Response;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.util.concurrent.TimeUnit;

/**
 * 数据源服务客户端
 * 用于从admin模块获取数据源配置信息
 * 
 * <AUTHOR>
 */
@Service
@Slf4j
public class DatasourceClient {

    @Value("${xxl.job.admin.addresses}")
    private String adminAddresses;

    @Value("${xxl.job.admin.accessToken}")
    private String accessToken;

    private final ObjectMapper objectMapper = new ObjectMapper();

    private final OkHttpClient httpClient = new OkHttpClient.Builder()
            .connectTimeout(10, TimeUnit.SECONDS)
            .readTimeout(30, TimeUnit.SECONDS)
            .writeTimeout(30, TimeUnit.SECONDS)
            .build();



    /**
     * 获取数据源信息
     */
    public DatasourceInfo getDatasource(Long datasourceId) {
        try {
            // 构建请求URL（使用executor专用接口，返回明文密码）
            String baseUrl = adminAddresses.endsWith("/") ? adminAddresses : adminAddresses + "/";
            String requestUrl = baseUrl + "datasource/loadForExecutor/" + datasourceId;
            
            log.info("请求数据源信息: {}", requestUrl);

            // 使用OkHttp发送请求，带上accessToken
            Request request = new Request.Builder()
                    .url(requestUrl)
                    .get()
                    .addHeader("Content-Type", "application/json")
                    .addHeader("XXL-JOB-ACCESS-TOKEN", accessToken)
                    .build();

            try (Response response = httpClient.newCall(request).execute()) {
                int responseCode = response.code();
                log.info("数据源接口响应码: {}", responseCode);

                if (response.body() == null) {
                    throw new RuntimeException("响应体为空");
                }

                String responseBody = response.body().string();
                log.info("数据源接口响应内容: {}", responseBody);

                if (response.isSuccessful()) {
                    // 检查响应是否为JSON格式
                    if (!responseBody.trim().startsWith("{") && !responseBody.trim().startsWith("[")) {
                        log.error("数据源接口返回非JSON格式内容: {}", responseBody.substring(0, Math.min(200, responseBody.length())));
                        throw new RuntimeException("数据源接口返回非JSON格式内容，可能是404页面或错误页面");
                    }

                    // 解析响应
                    JsonNode responseNode = objectMapper.readTree(responseBody);
                    if (responseNode.has("code") && responseNode.get("code").asInt() == 200) {
                        JsonNode contentNode = responseNode.get("content");
                        log.info("获取数据源信息成功: datasourceId={}", datasourceId);
                        return parseDatasourceInfo(contentNode);
                    } else {
                        String errorMsg = responseNode.has("msg") ? responseNode.get("msg").asText() : "未知错误";
                        throw new RuntimeException("获取数据源失败: " + errorMsg);
                    }
                } else {
                    log.error("数据源接口请求失败: responseCode={}, responseBody={}", responseCode, responseBody);
                    if (responseCode == 302) {
                        throw new RuntimeException("accessToken认证失败，请检查配置");
                    } else {
                        throw new RuntimeException("HTTP请求失败，响应码: " + responseCode + ", 错误信息: " + responseBody);
                    }
                }
            } catch (IOException e) {
                log.error("网络请求异常: datasourceId={}", datasourceId, e);
                throw new RuntimeException("网络请求异常: " + e.getMessage(), e);
            }

        } catch (Exception e) {
            log.error("获取数据源信息失败: datasourceId={}", datasourceId, e);
            throw new RuntimeException("获取数据源信息失败: " + e.getMessage(), e);
        }
    }

    /**
     * 解析数据源信息
     */
    private DatasourceInfo parseDatasourceInfo(JsonNode contentNode) {
        DatasourceInfo datasourceInfo = new DatasourceInfo();
        
        datasourceInfo.setId(contentNode.get("id").asLong());
        datasourceInfo.setName(contentNode.get("name").asText());
        datasourceInfo.setType(contentNode.get("type").asText());
        datasourceInfo.setHost(contentNode.get("host").asText());
        datasourceInfo.setPort(contentNode.get("port").asInt());
        datasourceInfo.setUsername(contentNode.get("username").asText());
        datasourceInfo.setPassword(contentNode.get("password").asText());
        
        if (contentNode.has("connectionParams")) {
            datasourceInfo.setConnectionParams(contentNode.get("connectionParams").asText());
        }

        log.info("解析数据源信息成功: id={}, name={}, type={}, host={}, port={}", 
                datasourceInfo.getId(), datasourceInfo.getName(), datasourceInfo.getType(), 
                datasourceInfo.getHost(), datasourceInfo.getPort());

        return datasourceInfo;
    }

    /**
     * 数据源信息类
     */
    @Setter
    @Getter
    public static class DatasourceInfo {
        // Getters and Setters
        private Long id;
        private String name;
        private String type;
        private String host;
        private Integer port;
        private String username;
        private String password;
        private String connectionParams;

        /**
         * 构建JDBC URL
         */
        public String buildJdbcUrl(String database) {
            String template = switch (type.toUpperCase()) {
                case "MYSQL", "DORIS" ->
                        "*************************************************************************************************************************";
                case "POSTGRESQL" -> "jdbc:postgresql://%s:%d/%s";
                case "ORACLE" -> "**************************";
                case "HIVE" -> "jdbc:hive2://%s:%d/%s";
                default -> throw new RuntimeException("不支持的数据源类型: " + type);
            };

            String jdbcUrl = String.format(template, host, port, database);
            
            // 添加额外的连接参数
            if (connectionParams != null && !connectionParams.trim().isEmpty()) {
                String separator = jdbcUrl.contains("?") ? "&" : "?";
                jdbcUrl += separator + connectionParams;
            }
            
            return jdbcUrl;
        }

        /**
         * 获取驱动类名
         */
        public String getDriverClass() {
            return switch (type.toUpperCase()) {
                case "MYSQL", "DORIS" -> "com.mysql.cj.jdbc.Driver";
                case "POSTGRESQL" -> "org.postgresql.Driver";
                case "ORACLE" -> "oracle.jdbc.driver.OracleDriver";
                case "HIVE" -> "org.apache.hive.jdbc.HiveDriver";
                default -> throw new RuntimeException("不支持的数据源类型: " + type);
            };
        }
    }
}
