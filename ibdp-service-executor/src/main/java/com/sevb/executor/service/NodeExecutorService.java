package com.sevb.executor.service;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.sevb.executor.handler.CentralNodeExecutorHandler.NodeExecutionParam;
import com.sevb.executor.handler.DataxNodeExecutor;
import com.sevb.executor.handler.PythonNodeExecutor;
import com.sevb.executor.handler.ShellNodeExecutor;
import com.sevb.executor.handler.SqlNodeExecutor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;

/**
 * 节点执行服务
 * 负责根据节点类型调用相应的执行器
 * 
 * <AUTHOR>
 */
@Service
@Slf4j
public class NodeExecutorService {

    @Autowired
    private DataxNodeExecutor dataxNodeExecutor;

    @Autowired
    private SqlNodeExecutor sqlNodeExecutor;

    @Autowired
    private ShellNodeExecutor shellNodeExecutor;

    @Autowired
    private PythonNodeExecutor pythonNodeExecutor;

    private final ObjectMapper objectMapper = new ObjectMapper();

    // ========== 调度中心编排模式的节点执行方法 ==========

    /**
     * 执行SQL节点（调度中心编排模式）
     */
    public boolean executeSqlNode(NodeExecutionParam nodeParam) {
        try {
            log.info("执行SQL节点(调度中心编排): nodeCode={}", nodeParam.getNodeCode());

            // 构建SQL节点参数
            String sqlParam = buildSqlNodeParam(nodeParam);

            // 调用现有的SQL节点执行器
            return sqlNodeExecutor.executeWithParam(sqlParam);

        } catch (Exception e) {
            log.error("SQL节点执行失败: nodeCode={}", nodeParam.getNodeCode(), e);
            return false;
        }
    }

    /**
     * 执行Shell节点（调度中心编排模式）
     */
    public boolean executeShellNode(NodeExecutionParam nodeParam) {
        try {
            log.info("执行Shell节点(调度中心编排): nodeCode={}", nodeParam.getNodeCode());

            // 构建Shell节点参数
            String shellParam = buildShellNodeParam(nodeParam);

            // 调用Shell节点执行器
            return shellNodeExecutor.executeWithParam(shellParam);

        } catch (Exception e) {
            log.error("Shell节点执行失败: nodeCode={}", nodeParam.getNodeCode(), e);
            return false;
        }
    }

    /**
     * 执行Python节点（调度中心编排模式）
     */
    public boolean executePythonNode(NodeExecutionParam nodeParam) {
        try {
            log.info("执行Python节点(调度中心编排): nodeCode={}", nodeParam.getNodeCode());

            // 构建Python节点参数
            String pythonParam = buildPythonNodeParam(nodeParam);

            // 调用Python节点执行器
            return pythonNodeExecutor.executeWithParam(pythonParam);

        } catch (Exception e) {
            log.error("Python节点执行失败: nodeCode={}", nodeParam.getNodeCode(), e);
            return false;
        }
    }

    /**
     * 执行DataX节点（调度中心编排模式）
     */
    public boolean executeDataxNode(NodeExecutionParam nodeParam) {
        try {
            log.info("执行DataX节点(调度中心编排): nodeCode={}", nodeParam.getNodeCode());

            // 构建DataX节点参数
            String dataxParam = buildDataxNodeParam(nodeParam);

            // 调用现有的DataX节点执行器
            return dataxNodeExecutor.executeWithParam(dataxParam);

        } catch (Exception e) {
            log.error("DataX节点执行失败: nodeCode={}", nodeParam.getNodeCode(), e);
            return false;
        }
    }

    /**
     * 构建SQL节点参数
     */
    private String buildSqlNodeParam(NodeExecutionParam nodeParam) throws Exception {
        ObjectMapper mapper = new ObjectMapper();

        // 解析节点配置
        Object configParams = mapper.readValue(nodeParam.getNodeConfig(), Object.class);

        Map<String, Object> sqlConfigMap = new HashMap<>();
        sqlConfigMap.put("type", "SQL");
        sqlConfigMap.put("config", configParams);

        Map<String, Object> sqlMap = new HashMap<>();
        sqlMap.put("jobJson", mapper.writeValueAsString(sqlConfigMap));
        return mapper.writeValueAsString(sqlMap);
    }

    /**
     * 构建DataX节点参数
     */
    private String buildDataxNodeParam(NodeExecutionParam nodeParam) throws Exception {
        ObjectMapper mapper = new ObjectMapper();

        Map<String, Object> dataxMap = new HashMap<>();
        dataxMap.put("jobJson", nodeParam.getNodeConfig());
        return mapper.writeValueAsString(dataxMap);
    }

    /**
     * 构建Shell节点参数
     */
    private String buildShellNodeParam(NodeExecutionParam nodeParam) throws Exception {
        ObjectMapper mapper = new ObjectMapper();

        // 解析节点配置
        Object configParams = mapper.readValue(nodeParam.getNodeConfig(), Object.class);

        Map<String, Object> shellConfigMap = new HashMap<>();
        shellConfigMap.put("type", "SHELL");
        shellConfigMap.put("config", configParams);

        Map<String, Object> shellMap = new HashMap<>();
        shellMap.put("jobJson", mapper.writeValueAsString(shellConfigMap));
        return mapper.writeValueAsString(shellMap);
    }

    /**
     * 构建Python节点参数
     */
    private String buildPythonNodeParam(NodeExecutionParam nodeParam) throws Exception {
        ObjectMapper mapper = new ObjectMapper();

        // 解析节点配置
        Object configParams = mapper.readValue(nodeParam.getNodeConfig(), Object.class);

        Map<String, Object> pythonConfigMap = new HashMap<>();
        pythonConfigMap.put("type", "PYTHON");
        pythonConfigMap.put("config", configParams);

        Map<String, Object> pythonMap = new HashMap<>();
        pythonMap.put("jobJson", mapper.writeValueAsString(pythonConfigMap));
        return mapper.writeValueAsString(pythonMap);
    }
}
