package com.sevb.executor.handler;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.sevb.core.handler.annotation.XxlJob;
import com.sevb.core.context.XxlJobHelper;
import com.sevb.executor.util.NodeCallbackUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.io.*;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.concurrent.TimeUnit;

/**
 * Shell节点执行器
 * 负责执行Shell脚本任务
 *
 * <AUTHOR>
 */
@Component
@Slf4j
public class ShellNodeExecutor {

    private final ObjectMapper objectMapper = new ObjectMapper();

    @Autowired
    private NodeCallbackUtil nodeCallbackUtil;

    // 脚本执行超时时间（秒）
    private static final int DEFAULT_TIMEOUT = 300;

    @XxlJob("shellNodeExecutor")
    public void execute() throws Exception {
        String jobParam = XxlJobHelper.getJobParam();
        executeWithParam(jobParam);
    }

    /**
     * 使用指定参数执行Shell节点
     */
    public boolean executeWithParam(String jobParam) throws Exception {
        try {
            log.info("开始执行Shell节点任务");
            XxlJobHelper.log("开始执行Shell节点任务");

            // 验证任务参数
            if (jobParam == null || jobParam.trim().isEmpty()) {
                throw new RuntimeException("任务参数为空");
            }

            log.info("任务参数: {}", jobParam);
            XxlJobHelper.log("任务参数: " + jobParam);

            // 解析任务参数，获取Shell脚本配置
            JsonNode paramNode = objectMapper.readTree(jobParam);
            JsonNode configNode = null;

            if (paramNode.has("jobJson")) {
                String jobJson = paramNode.get("jobJson").asText();
                JsonNode jobNode = objectMapper.readTree(jobJson);
                if (jobNode.has("config")) {
                    configNode = jobNode.get("config");
                }
            } else if (paramNode.has("config")) {
                configNode = paramNode.get("config");
            } else if (paramNode.has("script")) {
                // 直接包含脚本内容
                configNode = paramNode;
            }

            if (configNode == null) {
                throw new RuntimeException("Shell脚本配置为空");
            }

            log.info("解析到的Shell配置: {}", configNode.toString());
            XxlJobHelper.log("解析到的Shell配置: " + configNode.toString());

            // 执行Shell任务
            executeShellTask(configNode);

            // 回调调度中心通知执行成功
            nodeCallbackUtil.safeCallback(jobParam, "SHELL", "ShellNodeExecutor", true, null);

            XxlJobHelper.log("Shell任务执行成功");
            log.info("Shell任务执行成功");
            return true;

        } catch (Exception e) {
            log.error("Shell任务执行失败", e);
            XxlJobHelper.log("Shell任务执行失败: " + e.getMessage());

            // 回调调度中心通知执行失败
            nodeCallbackUtil.safeCallback(jobParam, "SHELL", "ShellNodeExecutor", false, e.getMessage());

            XxlJobHelper.handleFail("Shell任务执行失败: " + e.getMessage());
            return false;
        }
    }

    /**
     * 执行Shell任务
     */
    private void executeShellTask(JsonNode config) throws Exception {
        // 解析配置参数
        String script = null;
        String scriptFile = null;
        String workingDir = config.has("workingDir") ? config.get("workingDir").asText() : System.getProperty("user.dir");
        int timeout = config.has("timeout") ? config.get("timeout").asInt() : DEFAULT_TIMEOUT;
        String shell = config.has("shell") ? config.get("shell").asText() : "/bin/bash";
        boolean logOutput = !config.has("logOutput") || config.get("logOutput").asBoolean();

        // 获取脚本内容或脚本文件路径
        if (config.has("script")) {
            script = config.get("script").asText();
        } else if (config.has("scriptFile")) {
            scriptFile = config.get("scriptFile").asText();
        } else {
            throw new RuntimeException("必须指定script（脚本内容）或scriptFile（脚本文件路径）");
        }

        log.info("Shell配置 - 工作目录: {}, 超时: {}秒, Shell: {}", workingDir, timeout, shell);
        XxlJobHelper.log(String.format("Shell配置 - 工作目录: %s, 超时: %d秒, Shell: %s", workingDir, timeout, shell));

        Path tempScriptFile = null;
        try {
            // 如果是脚本内容，创建临时脚本文件
            if (StringUtils.hasText(script)) {
                tempScriptFile = createTempScriptFile(script);
                scriptFile = tempScriptFile.toString();
            }

            // 验证脚本文件存在
            if (!StringUtils.hasText(scriptFile) || !Files.exists(Paths.get(scriptFile))) {
                throw new RuntimeException("脚本文件不存在: " + scriptFile);
            }

            // 执行Shell脚本
            executeShellScript(shell, scriptFile, workingDir, timeout, logOutput);

        } finally {
            // 清理临时脚本文件
            if (tempScriptFile != null) {
                cleanupTempFile(tempScriptFile);
            }
        }
    }

    /**
     * 创建临时脚本文件
     */
    private Path createTempScriptFile(String script) throws IOException {
        Path tempFile = Files.createTempFile("shell_script_", ".sh");
        Files.write(tempFile, script.getBytes(StandardCharsets.UTF_8));

        // 设置执行权限
        tempFile.toFile().setExecutable(true);

        log.info("创建临时脚本文件: {}", tempFile.toString());
        XxlJobHelper.log("创建临时脚本文件: " + tempFile.toString());

        return tempFile;
    }

    /**
     * 执行Shell脚本
     */
    private void executeShellScript(String shell, String scriptFile, String workingDir,
                                    int timeout, boolean logOutput) throws Exception {

        // 构建Shell执行命令
        ProcessBuilder processBuilder = new ProcessBuilder();
        processBuilder.command(shell, scriptFile);
        processBuilder.directory(new File(workingDir));
        processBuilder.redirectErrorStream(true);

        log.info("执行Shell命令: {} {}", shell, scriptFile);
        XxlJobHelper.log("执行Shell命令: " + shell + " " + scriptFile);

        // 启动进程
        Process process = processBuilder.start();

        // 读取输出
        StringBuilder output = new StringBuilder();
        try (BufferedReader reader = new BufferedReader(new InputStreamReader(process.getInputStream(), StandardCharsets.UTF_8))) {
            String line;
            while ((line = reader.readLine()) != null) {
                if (logOutput) {
                    log.info("Shell输出: {}", line);
                    XxlJobHelper.log("Shell输出: " + line);
                }
                output.append(line).append("\n");
            }
        }

        // 等待进程完成
        boolean finished = process.waitFor(timeout, TimeUnit.SECONDS);

        if (!finished) {
            // 超时，强制终止进程
            process.destroyForcibly();
            throw new RuntimeException("Shell脚本执行超时（" + timeout + "秒）");
        }

        int exitCode = process.exitValue();
        log.info("Shell脚本执行完成，退出码: {}", exitCode);
        XxlJobHelper.log("Shell脚本执行完成，退出码: " + exitCode);

        if (exitCode != 0) {
            String errorMsg = "Shell脚本执行失败，退出码: " + exitCode;
            if (!output.isEmpty()) {
                errorMsg += "\n输出内容:\n" + output.toString();
            }
            throw new RuntimeException(errorMsg);
        }

        XxlJobHelper.log("Shell脚本执行成功");
    }

    /**
     * 清理临时文件
     */
    private void cleanupTempFile(Path tempFile) {
        try {
            if (tempFile != null && Files.exists(tempFile)) {
                Files.delete(tempFile);
                log.info("清理临时脚本文件: {}", tempFile.toString());
            }
        } catch (IOException e) {
            log.warn("清理临时脚本文件失败: {}", tempFile.toString(), e);
        }
    }
}
