package com.sevb.executor.handler;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.sevb.core.context.XxlJobHelper;
import com.sevb.core.handler.annotation.XxlJob;
import com.sevb.executor.service.DatasourceClient;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.sql.*;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * SQL节点执行器
 * 负责执行SQL查询和更新任务
 *
 * <AUTHOR>
 */
@Component
@Slf4j
public class SqlNodeExecutor {

    private final ObjectMapper objectMapper = new ObjectMapper();

    @Value("${xxl.job.admin.accessToken}")
    private String accessToken;

    @Autowired
    private DatasourceClient datasourceClient;

    @XxlJob("sqlNodeExecutor")
    public void execute() throws Exception {
        String jobParam = XxlJobHelper.getJobParam();
        executeWithParam(jobParam);
    }

    /**
     * 使用指定参数执行SQL节点
     */
    public boolean executeWithParam(String jobParam) throws Exception {

        JsonNode configNode = null;
        try {
            log.info("开始执行SQL节点任务");
            XxlJobHelper.log("开始执行SQL节点任务");

            // 验证任务参数
            if (jobParam == null || jobParam.trim().isEmpty()) {
                throw new RuntimeException("任务参数为空");
            }

            log.info("任务参数: {}", jobParam);
            XxlJobHelper.log("任务参数: " + jobParam);

            // 解析任务参数
            log.info("SQL节点接收到的原始参数: {}", jobParam);
            XxlJobHelper.log("SQL节点接收到的原始参数: " + jobParam);

            JsonNode paramNode = objectMapper.readTree(jobParam);


            if (paramNode.has("jobJson")) {
                String jobJson = paramNode.get("jobJson").asText();
                log.info("解析jobJson: {}", jobJson);
                XxlJobHelper.log("解析jobJson: " + jobJson);

                JsonNode jobJsonNode = objectMapper.readTree(jobJson);
                if (jobJsonNode.has("config")) {
                    configNode = jobJsonNode.get("config");
                } else {
                    throw new RuntimeException("jobJson中缺少config字段");
                }
            } else if (paramNode.has("config")) {
                configNode = paramNode.get("config");
            } else {
                // 如果参数本身就是config
                configNode = paramNode;
            }

            if (configNode == null) {
                throw new RuntimeException("SQL配置为空");
            }

            log.info("解析到的SQL配置: {}", configNode.toString());
            XxlJobHelper.log("解析到的SQL配置: " + configNode.toString());

            // 执行SQL任务
            executeSqlTask(configNode);

            // 显式调用回调接口通知调度中心执行成功
            try {
                callbackNodeComplete(configNode, true, null);
            } catch (Exception callbackException) {
                log.error("回调调度中心失败", callbackException);
            }

            XxlJobHelper.log("SQL任务执行成功");
            log.info("SQL任务执行成功");
            return true;

        } catch (Exception e) {
            log.error("SQL任务执行失败", e);
            XxlJobHelper.log("SQL任务执行失败: " + e.getMessage());

            // 显式调用回调接口通知调度中心执行失败
            try {
                callbackNodeComplete(configNode, false, e.getMessage());
            } catch (Exception callbackException) {
                log.error("回调调度中心失败", callbackException);
            }

            XxlJobHelper.handleFail("SQL任务执行失败: " + e.getMessage());
            return false;
        }
    }

    /**
     * 执行SQL任务
     */
    private void executeSqlTask(JsonNode config) throws Exception {
        // 打印配置信息用于调试
        log.info("SQL任务配置: {}", config.toString());
        XxlJobHelper.log("SQL任务配置: " + config.toString());

        // 解析nodeConfig中的SQL配置
        JsonNode nodeConfigNode = config.get("nodeConfig");
        if (nodeConfigNode == null || nodeConfigNode.isNull()) {
            throw new RuntimeException("缺少nodeConfig配置");
        }

        // 如果nodeConfig是字符串，需要解析为JSON
        JsonNode sqlConfig;
        if (nodeConfigNode.isTextual()) {
            try {
                sqlConfig = objectMapper.readTree(nodeConfigNode.asText());
                log.info("解析后的SQL配置: {}", sqlConfig.toString());
            } catch (Exception e) {
                throw new RuntimeException("解析nodeConfig JSON失败: " + e.getMessage());
            }
        } else {
            sqlConfig = nodeConfigNode;
        }

        // 解析配置，添加空值检查
        JsonNode datasourceIdNode = sqlConfig.get("datasourceId");
        if (datasourceIdNode == null || datasourceIdNode.isNull()) {
            throw new RuntimeException("SQL配置中缺少datasourceId字段");
        }
        Long datasourceId = datasourceIdNode.asLong();

        JsonNode databaseNode = sqlConfig.get("database");
        if (databaseNode == null || databaseNode.isNull()) {
            throw new RuntimeException("SQL配置中缺少database字段");
        }
        String database = databaseNode.asText();

        JsonNode sqlNode = sqlConfig.get("sql");
        if (sqlNode == null || sqlNode.isNull()) {
            throw new RuntimeException("SQL配置中缺少sql字段");
        }
        String sql = sqlNode.asText();
        String sqlType = sqlConfig.has("sqlType") ? sqlConfig.get("sqlType").asText() : "SELECT";
        String resultHandling = sqlConfig.has("resultHandling") ? sqlConfig.get("resultHandling").asText() : "LOG";
        int maxRows = sqlConfig.has("maxRows") ? sqlConfig.get("maxRows").asInt() : 1000;
        int queryTimeout = sqlConfig.has("queryTimeout") ? sqlConfig.get("queryTimeout").asInt() : 30;
        boolean useTransaction = sqlConfig.has("useTransaction") && sqlConfig.get("useTransaction").asBoolean();

        log.info("SQL配置 - 数据源ID: {}, 数据库: {}, SQL类型: {}, 结果处理: {}",
                datasourceId, database, sqlType, resultHandling);
        XxlJobHelper.log(String.format("SQL配置 - 数据源ID: %d, 数据库: %s, SQL类型: %s, 结果处理: %s",
                datasourceId, database, sqlType, resultHandling));

        // 获取数据库连接信息
        DatasourceClient.DatasourceInfo datasourceInfo = datasourceClient.getDatasource(datasourceId);
        String jdbcUrl = datasourceInfo.buildJdbcUrl(database);
        String driverClass = datasourceInfo.getDriverClass();

        // 执行SQL
        Connection connection = null;
        try {
            connection = createConnection(jdbcUrl, datasourceInfo.getUsername(),
                    datasourceInfo.getPassword(), driverClass);

            if (useTransaction) {
                connection.setAutoCommit(false);
            }

            if ("SELECT".equalsIgnoreCase(sqlType)) {
                executeQuery(connection, sql, maxRows, queryTimeout, resultHandling);
            } else {
                executeUpdate(connection, sql, queryTimeout, useTransaction);
            }

            if (useTransaction) {
                connection.commit();
                XxlJobHelper.log("事务提交成功");
            }

        } catch (Exception e) {
            if (useTransaction && connection != null) {
                try {
                    connection.rollback();
                    XxlJobHelper.log("事务回滚成功");
                } catch (SQLException rollbackEx) {
                    log.error("事务回滚失败", rollbackEx);
                }
            }
            throw e;
        } finally {
            if (connection != null) {
                try {
                    connection.close();
                } catch (SQLException e) {
                    log.warn("关闭数据库连接失败", e);
                }
            }
        }
    }

    /**
     * 执行查询SQL
     */
    private void executeQuery(Connection connection, String sql, int maxRows, int queryTimeout, String resultHandling) throws SQLException {
        log.info("执行查询SQL: {}", sql);
        XxlJobHelper.log("执行查询SQL: " + sql);

        try (PreparedStatement stmt = connection.prepareStatement(sql)) {
            stmt.setQueryTimeout(queryTimeout);
            stmt.setMaxRows(maxRows);

            try (ResultSet rs = stmt.executeQuery()) {
                ResultSetMetaData metaData = rs.getMetaData();
                int columnCount = metaData.getColumnCount();

                // 输出列名
                List<String> columnNames = new ArrayList<>();
                for (int i = 1; i <= columnCount; i++) {
                    columnNames.add(metaData.getColumnName(i));
                }

                String columnHeader = String.join("\t", columnNames);
                log.info("查询结果列名: {}", columnHeader);
                XxlJobHelper.log("查询结果列名: " + columnHeader);

                // 处理查询结果
                int rowCount = 0;
                List<Map<String, Object>> results = new ArrayList<>();

                while (rs.next() && rowCount < maxRows) {
                    Map<String, Object> row = new HashMap<>();
                    List<String> rowValues = new ArrayList<>();

                    for (int i = 1; i <= columnCount; i++) {
                        Object value = rs.getObject(i);
                        String columnName = columnNames.get(i - 1);
                        row.put(columnName, value);
                        rowValues.add(value != null ? value.toString() : "NULL");
                    }

                    results.add(row);

                    if ("LOG".equals(resultHandling)) {
                        String rowData = String.join("\t", rowValues);
                        XxlJobHelper.log("查询结果[" + (rowCount + 1) + "]: " + rowData);
                    }

                    rowCount++;
                }

                log.info("查询完成，共返回 {} 行数据", rowCount);
                XxlJobHelper.log("查询完成，共返回 " + rowCount + " 行数据");

                // 根据结果处理方式处理数据
                if ("FILE".equals(resultHandling)) {
                    // TODO: 将结果写入文件
                    XxlJobHelper.log("结果文件输出功能待实现");
                }
            }
        }
    }

    /**
     * 执行更新SQL
     */
    private void executeUpdate(Connection connection, String sql, int queryTimeout, boolean useTransaction) throws SQLException {
        log.info("执行更新SQL: {}", sql);
        XxlJobHelper.log("执行更新SQL: " + sql);

        try (PreparedStatement stmt = connection.prepareStatement(sql)) {
            stmt.setQueryTimeout(queryTimeout);

            int affectedRows = stmt.executeUpdate();

            log.info("SQL执行完成，影响行数: {}", affectedRows);
            XxlJobHelper.log("SQL执行完成，影响行数: " + affectedRows);
        }
    }

    /**
     * 创建数据库连接
     */
    private Connection createConnection(String jdbcUrl, String username, String password, String driverClass) throws SQLException {
        try {
            Class.forName(driverClass);
        } catch (ClassNotFoundException e) {
            throw new SQLException("数据库驱动未找到: " + driverClass, e);
        }

        log.info("连接数据库: {}", jdbcUrl);
        XxlJobHelper.log("连接数据库: " + jdbcUrl);

        return DriverManager.getConnection(jdbcUrl, username, password);
    }

    /**
     * 回调调度中心通知节点执行完成
     */
    private void callbackNodeComplete(JsonNode config, boolean success, String errorMessage) {
        try {
            String callbackUrl = config.has("callbackUrl") ? config.get("callbackUrl").asText() : null;
            if (callbackUrl == null || callbackUrl.trim().isEmpty()) {
                log.warn("回调URL为空，跳过回调");
                return;
            }

            // 构建NodeExecutionResult对象
            Map<String, Object> result = new HashMap<>();
            result.put("executionId", config.has("executionId") ? config.get("executionId").asText() : "");
            result.put("nodeCode", config.has("nodeCode") ? config.get("nodeCode").asText() : "");
            result.put("success", success);
            result.put("errorMessage", errorMessage);
            result.put("startTime", System.currentTimeMillis() - 5000); // 假设执行了5秒，实际可以记录真实开始时间
            result.put("endTime", System.currentTimeMillis());
            result.put("duration", 5000L); // 执行时长（毫秒）
            result.put("executorAddress", "localhost:9999"); // 执行器地址
            result.put("outputParams", new HashMap<>()); // 输出参数
            result.put("logContent", success ? "SQL执行成功" : "SQL执行失败: " + errorMessage);
            result.put("jobId", null); // XXL-JOB任务ID
            result.put("jobLogId", null); // XXL-JOB日志ID
            result.put("retryCount", 0); // 重试次数

            // 发送HTTP回调请求
            String jsonParams = objectMapper.writeValueAsString(result);

            log.info("发送回调请求到: {}, 参数: {}", callbackUrl, jsonParams);

            // 使用OkHttp发送HTTP POST请求
            sendHttpCallback(callbackUrl, jsonParams);

            XxlJobHelper.log("节点执行" + (success ? "成功" : "失败") + "，已通知调度中心");

        } catch (Exception e) {
            log.error("回调调度中心失败", e);
            XxlJobHelper.log("回调调度中心失败: " + e.getMessage());
        }
    }

    /**
     * 发送HTTP回调请求
     */
    private void sendHttpCallback(String callbackUrl, String jsonParams) {
        try {
            okhttp3.OkHttpClient client = new okhttp3.OkHttpClient.Builder()
                    .connectTimeout(10, java.util.concurrent.TimeUnit.SECONDS)
                    .writeTimeout(10, java.util.concurrent.TimeUnit.SECONDS)
                    .readTimeout(30, java.util.concurrent.TimeUnit.SECONDS)
                    .build();

            okhttp3.RequestBody body = okhttp3.RequestBody.create(
                    jsonParams,
                    okhttp3.MediaType.parse("application/json; charset=utf-8")
            );

            okhttp3.Request request = new okhttp3.Request.Builder()
                    .url(callbackUrl)
                    .post(body)
                    .addHeader("Content-Type", "application/json")
                    .addHeader("XXL-JOB-ACCESS-TOKEN", accessToken)
                    .build();

            try (okhttp3.Response response = client.newCall(request).execute()) {
                if (response.isSuccessful()) {
                    String responseBody = response.body() != null ? response.body().string() : "";
                    log.info("回调成功，响应: {}", responseBody);
                    XxlJobHelper.log("回调调度中心成功: " + responseBody);
                } else {
                    log.error("回调失败，HTTP状态码: {}, 响应: {}",
                            response.code(),
                            response.body() != null ? response.body().string() : "");
                    XxlJobHelper.log("回调调度中心失败，HTTP状态码: " + response.code());
                }
            }
        } catch (Exception e) {
            log.error("发送HTTP回调请求失败", e);
            XxlJobHelper.log("发送HTTP回调请求失败: " + e.getMessage());
        }
    }
}
