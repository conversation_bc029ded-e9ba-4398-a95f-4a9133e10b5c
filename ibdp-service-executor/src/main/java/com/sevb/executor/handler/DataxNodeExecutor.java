package com.sevb.executor.handler;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.sevb.core.handler.annotation.XxlJob;
import com.sevb.core.context.XxlJobHelper;
import com.sevb.executor.util.NodeCallbackUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.*;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.concurrent.TimeUnit;

/**
 * DataX节点执行器
 * 负责执行DataX数据同步任务
 * 
 * <AUTHOR>
 */
@Component
@Slf4j
public class DataxNodeExecutor {

    private final ObjectMapper objectMapper = new ObjectMapper();

    @Autowired
    private NodeCallbackUtil nodeCallbackUtil;

    // DataX安装路径，支持Windows和Linux
    private static final String DATAX_HOME = getDataxHome();
    private static final String DATAX_PYTHON = getDataxPythonScript();
    private static final String PYTHON_CMD = System.getProperty("datax.python", "python");
    private static final int DATAX_TIMEOUT = Integer.parseInt(System.getProperty("datax.timeout", "3600"));

    /**
     * 获取DataX安装路径
     */
    private static String getDataxHome() {
        String defaultPath = isWindows() ? "D:/datax" : "/opt/datax";
        return System.getProperty("datax.home", defaultPath);
    }

    /**
     * 获取DataX Python脚本路径
     */
    private static String getDataxPythonScript() {
        return Paths.get(DATAX_HOME, "bin", "datax.py").toString();
    }

    /**
     * 判断是否为Windows系统
     */
    private static boolean isWindows() {
        return System.getProperty("os.name").toLowerCase().contains("windows");
    }

    /**
     * 从参数中获取DataX配置JSON
     */
    private String getDataxConfigFromParams(JsonNode paramNode) throws Exception {
        // 检查是否已经是DataX配置格式
        if (paramNode.has("job")) {
            return paramNode.toString();
        }

        // 从nodeConfig中获取DataX配置
        if (paramNode.has("nodeConfig")) {
            String nodeConfigStr = paramNode.get("nodeConfig").asText();
            if (nodeConfigStr != null && !nodeConfigStr.trim().isEmpty()) {
                JsonNode nodeConfig = objectMapper.readTree(nodeConfigStr);

                // 检查是否直接就是DataX配置格式
                if (nodeConfig.has("job")) {
                    log.info("从nodeConfig中获取DataX配置成功（直接格式）");
                    return nodeConfigStr;
                }

                // 新的配置结构：检查dataxConfig字段
                if (nodeConfig.has("dataxConfig")) {
                    JsonNode dataxConfigNode = nodeConfig.get("dataxConfig");
                    if (dataxConfigNode != null && !dataxConfigNode.isNull()) {
                        String dataxJson = objectMapper.writeValueAsString(dataxConfigNode);
                        log.info("从nodeConfig.dataxConfig中获取预生成的DataX配置成功");
                        return dataxJson;
                    }
                }
            }
        }

        // 如果没有预生成的配置，直接报错
        throw new RuntimeException("未找到预生成的DataX配置，请检查节点配置是否正确生成");
    }



    @XxlJob("dataxNodeExecutor")
    public void execute() throws Exception {
        String jobParam = XxlJobHelper.getJobParam();
        executeWithParam(jobParam);
    }

    /**
     * 使用指定参数执行DataX节点
     */
    public boolean executeWithParam(String jobParam) throws Exception {
        try {
            log.info("开始执行DataX节点任务");
            XxlJobHelper.log("开始执行DataX节点任务");

            // 验证任务参数
            if (jobParam == null || jobParam.trim().isEmpty()) {
                throw new RuntimeException("任务参数为空");
            }

            log.info("任务参数: {}", jobParam);
            XxlJobHelper.log("任务参数: " + jobParam);

            // 解析任务参数，获取DataX配置JSON
            JsonNode paramNode = objectMapper.readTree(jobParam);
            String dataxJson = getDataxConfigFromParams(paramNode);

            log.info("使用DataX配置JSON: {}", dataxJson);
            XxlJobHelper.log("使用DataX配置JSON: " + dataxJson);

            // 创建临时配置文件
            Path tempConfigFile = createTempConfigFile(dataxJson);
            
            try {
                // 执行DataX任务
                executeDataxJob(tempConfigFile);

                // 回调调度中心通知执行成功
                nodeCallbackUtil.safeCallback(jobParam, "DATAX", "DataxNodeExecutor", true, null);

                XxlJobHelper.log("DataX任务执行成功");
                log.info("DataX任务执行成功");
                return true;

            } finally {
                // 清理临时文件
                cleanupTempFile(tempConfigFile);
            }

        } catch (Exception e) {
            log.error("DataX任务执行失败", e);
            XxlJobHelper.log("DataX任务执行失败: " + e.getMessage());

            // 回调调度中心通知执行失败
            nodeCallbackUtil.safeCallback(jobParam, "DATAX", "DataxNodeExecutor", false, e.getMessage());

            XxlJobHelper.handleFail("DataX任务执行失败: " + e.getMessage());
            return false;
        }
    }

    /**
     * 创建临时配置文件
     */
    private Path createTempConfigFile(String dataxJson) throws IOException {
        // 创建临时目录
        Path tempDir = Paths.get(System.getProperty("java.io.tmpdir"), "datax-jobs");
        if (!Files.exists(tempDir)) {
            Files.createDirectories(tempDir);
        }

        // 创建临时配置文件
        String fileName = "datax-job-" + System.currentTimeMillis() + ".json";
        Path configFile = tempDir.resolve(fileName);
        
        // 写入配置内容
        Files.write(configFile, dataxJson.getBytes("UTF-8"));
        
        log.info("创建临时配置文件: {}", configFile.toString());
        XxlJobHelper.log("创建临时配置文件: " + configFile.toString());
        
        return configFile;
    }

    /**
     * 执行DataX任务
     */
    private void executeDataxJob(Path configFile) throws Exception {
        // 检查DataX是否安装
        if (!Files.exists(Paths.get(DATAX_PYTHON))) {
            throw new RuntimeException("DataX未安装或路径配置错误: " + DATAX_PYTHON);
        }

        // 构建DataX执行命令
        ProcessBuilder processBuilder = new ProcessBuilder();

        // 根据操作系统构建不同的命令
        if (isWindows()) {
            // Windows环境：使用cmd /c执行
            processBuilder.command("cmd", "/c", PYTHON_CMD, DATAX_PYTHON, configFile.toString());
        } else {
            // Linux/Unix环境
            processBuilder.command(PYTHON_CMD, DATAX_PYTHON, configFile.toString());
        }

        processBuilder.redirectErrorStream(true);

        String command = String.join(" ", processBuilder.command());
        log.info("执行DataX命令: {}", command);
        XxlJobHelper.log("执行DataX命令: " + command);

        // 启动进程
        Process process = processBuilder.start();

        // 读取输出
        try (BufferedReader reader = new BufferedReader(new InputStreamReader(process.getInputStream()))) {
            String line;
            while ((line = reader.readLine()) != null) {
                log.info("DataX输出: {}", line);
                XxlJobHelper.log("DataX输出: " + line);
            }
        }

        // 等待进程完成
        boolean finished = process.waitFor(DATAX_TIMEOUT, TimeUnit.SECONDS);

        if (!finished) {
            process.destroyForcibly();
            throw new RuntimeException("DataX任务执行超时（" + DATAX_TIMEOUT + "秒）");
        }

        int exitCode = process.exitValue();
        if (exitCode != 0) {
            throw new RuntimeException("DataX任务执行失败，退出码: " + exitCode);
        }

        log.info("DataX任务执行完成，退出码: {}", exitCode);
        XxlJobHelper.log("DataX任务执行完成，退出码: " + exitCode);
    }

    /**
     * 清理临时文件
     */
    private void cleanupTempFile(Path tempFile) {
        try {
            if (Files.exists(tempFile)) {
                Files.delete(tempFile);
                log.info("清理临时文件: {}", tempFile.toString());
                XxlJobHelper.log("清理临时文件: " + tempFile.toString());
            }
        } catch (IOException e) {
            log.warn("清理临时文件失败: {}", tempFile.toString(), e);
            XxlJobHelper.log("清理临时文件失败: " + tempFile.toString() + ", " + e.getMessage());
        }
    }
}
