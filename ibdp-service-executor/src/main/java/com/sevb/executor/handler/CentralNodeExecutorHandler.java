package com.sevb.executor.handler;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.sevb.core.handler.annotation.XxlJob;
import com.sevb.core.context.XxlJobHelper;
import com.sevb.executor.service.NodeExecutorService;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;
import org.springframework.web.client.RestTemplate;

import java.net.InetAddress;
import java.util.Map;

/**
 * 调度中心节点执行器Handler
 * 用于执行调度中心编排模式下的单个节点任务
 * 
 * <AUTHOR>
 */
@Component
@Slf4j
public class CentralNodeExecutorHandler {

    @Autowired
    private NodeExecutorService nodeExecutorService;

    private final RestTemplate restTemplate = new RestTemplate();
    private final ObjectMapper objectMapper = new ObjectMapper();
    
    /**
     * 调度中心节点执行器
     */
    @XxlJob("centralNodeExecutor")
    public void executeNode() throws Exception {
        String param = XxlJobHelper.getJobParam();
        log.info("调度中心节点执行器收到任务参数: {}", param);
        
        NodeExecutionResult result = null;
        try {
            // 解析节点执行参数
            NodeExecutionParam nodeParam = objectMapper.readValue(param, NodeExecutionParam.class);

            // 记录节点开始执行
            XxlJobHelper.log("开始执行节点: " + nodeParam.getNodeCode());
            log.info("开始执行节点: nodeCode={}, executionId={}", nodeParam.getNodeCode(), nodeParam.getExecutionId());
            
            // 构建执行结果对象
            result = new NodeExecutionResult();
            result.setExecutionId(nodeParam.getExecutionId());
            result.setNodeCode(nodeParam.getNodeCode());
            result.setCallbackUrl(nodeParam.getCallbackUrl());
            result.setStartTime(System.currentTimeMillis());
            result.setExecutorAddress(getLocalAddress());
            result.setJobLogId(XxlJobHelper.getJobId()); // 使用getJobId()而不是getJobLogId()
            
            // 根据节点类型执行具体任务
            boolean success = executeNodeByType(nodeParam);
            
            // 设置执行结果
            result.setSuccess(success);
            result.setEndTime(System.currentTimeMillis());
            result.setDuration(result.getEndTime() - result.getStartTime());
            
            // 记录执行结果
            if (success) {
                XxlJobHelper.log("节点执行成功: " + nodeParam.getNodeCode());
                XxlJobHelper.handleSuccess("节点执行成功");
                log.info("节点执行成功: nodeCode={}, executionId={}, duration={}ms", 
                        nodeParam.getNodeCode(), nodeParam.getExecutionId(), result.getDuration());
            } else {
                String errorMsg = "节点执行失败: " + nodeParam.getNodeCode();
                XxlJobHelper.log(errorMsg);
                XxlJobHelper.handleFail(errorMsg);
                result.setErrorMessage(errorMsg);
                log.error("节点执行失败: nodeCode={}, executionId={}", 
                        nodeParam.getNodeCode(), nodeParam.getExecutionId());
            }
            
        } catch (Exception e) {
            log.error("节点执行异常", e);
            XxlJobHelper.log("节点执行异常: " + e.getMessage());
            
            // 构建失败结果
            if (result == null) {
                result = buildFailResult(param, e.getMessage());
            } else {
                result.setSuccess(false);
                result.setErrorMessage(e.getMessage());
                result.setEndTime(System.currentTimeMillis());
                if (result.getStartTime() != null) {
                    result.setDuration(result.getEndTime() - result.getStartTime());
                }
            }
            
            XxlJobHelper.handleFail("节点执行异常: " + e.getMessage());
        } finally {
            // 无论成功失败都要回调调度中心
            if (result != null) {
                callbackScheduleCenter(result);
            }
        }
    }
    
    /**
     * 根据节点类型执行具体任务
     */
    private boolean executeNodeByType(NodeExecutionParam nodeParam) {
        try {
            String nodeType = nodeParam.getNodeType();
            if (!StringUtils.hasText(nodeType)) {
                throw new IllegalArgumentException("节点类型不能为空");
            }
            
            log.info("执行{}类型节点: nodeCode={}", nodeType, nodeParam.getNodeCode());
            
            // 根据节点类型调用相应的执行器
            return switch (nodeType.toUpperCase()) {
                case "SQL" -> nodeExecutorService.executeSqlNode(nodeParam);
                case "SHELL" -> nodeExecutorService.executeShellNode(nodeParam);
                case "PYTHON" -> nodeExecutorService.executePythonNode(nodeParam);
                case "DATAX" -> nodeExecutorService.executeDataxNode(nodeParam);
                default -> throw new IllegalArgumentException("不支持的节点类型: " + nodeType);
            };
            
        } catch (Exception e) {
            log.error("执行节点失败: nodeCode={}, nodeType={}", 
                    nodeParam.getNodeCode(), nodeParam.getNodeType(), e);
            return false;
        }
    }
    
    /**
     * 回调调度中心
     */
    private void callbackScheduleCenter(NodeExecutionResult result) {
        try {
            String callbackUrl = result.getCallbackUrl();
            if (!StringUtils.hasText(callbackUrl)) {
                log.warn("回调URL为空，跳过回调: nodeCode={}", result.getNodeCode());
                return;
            }
            
            log.info("开始回调调度中心: nodeCode={}, url={}", result.getNodeCode(), callbackUrl);
            
            ResponseEntity<String> response = restTemplate.postForEntity(callbackUrl, result, String.class);
            
            if (response.getStatusCode().is2xxSuccessful()) {
                log.info("节点执行结果回调成功: nodeCode={}, executionId={}", 
                        result.getNodeCode(), result.getExecutionId());
            } else {
                log.warn("节点执行结果回调失败: nodeCode={}, status={}, response={}", 
                        result.getNodeCode(), response.getStatusCode(), response.getBody());
            }
            
        } catch (Exception e) {
            log.error("回调调度中心失败: nodeCode={}, executionId={}", 
                    result.getNodeCode(), result.getExecutionId(), e);
        }
    }
    
    /**
     * 构建失败结果
     */
    private NodeExecutionResult buildFailResult(String param, String errorMessage) {
        NodeExecutionResult result = new NodeExecutionResult();
        
        try {
            // 尝试解析参数获取基本信息
            NodeExecutionParam nodeParam = objectMapper.readValue(param, NodeExecutionParam.class);
            result.setExecutionId(nodeParam.getExecutionId());
            result.setNodeCode(nodeParam.getNodeCode());
            result.setCallbackUrl(nodeParam.getCallbackUrl());
        } catch (Exception e) {
            log.warn("解析节点参数失败，使用默认值", e);
            result.setExecutionId("unknown");
            result.setNodeCode("unknown");
        }
        
        result.setSuccess(false);
        result.setErrorMessage(errorMessage);
        result.setEndTime(System.currentTimeMillis());
        result.setExecutorAddress(getLocalAddress());
        result.setJobLogId(XxlJobHelper.getJobId()); // 使用getJobId()而不是getJobLogId()
        
        return result;
    }
    
    /**
     * 获取本地地址
     */
    private String getLocalAddress() {
        try {
            return InetAddress.getLocalHost().getHostAddress();
        } catch (Exception e) {
            log.warn("获取本地地址失败", e);
            return "unknown";
        }
    }
    
    /**
     * 节点执行参数内部类
     */
    @Setter
    @Getter
    public static class NodeExecutionParam {
        // Getters and Setters
        private String executionId;
        private Long workflowId;
        private String nodeCode;
        private String nodeName;
        private String nodeType;
        private String nodeConfig;
        private Map<String, Object> globalParams;
        private Map<String, Object> inputParams;
        private String callbackUrl;
        private Long timeout;
        private Integer retryTimes;
        private String executeUser;
        private String orchestrationMode;

    }
    
    /**
     * 节点执行结果内部类
     */
    @Setter
    @Getter
    public static class NodeExecutionResult {
        // Getters and Setters
        private String executionId;
        private String nodeCode;
        private boolean success;
        private String errorMessage;
        private Map<String, Object> outputParams;
        private Long startTime;
        private Long endTime;
        private Long duration;
        private String executorAddress;
        private String callbackUrl;
        private String logContent;
        private Long jobLogId;
        private Integer retryCount;
    }
}
