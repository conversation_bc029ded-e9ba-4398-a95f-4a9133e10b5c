package com.sevb.executor.util;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.sevb.core.context.XxlJobHelper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.net.http.HttpClient;
import java.net.http.HttpRequest;
import java.net.http.HttpResponse;
import java.time.Duration;
import java.util.HashMap;
import java.util.Map;

/**
 * 节点执行回调工具类
 * 统一处理所有节点类型的执行完成回调
 */
@Slf4j
@Component
public class NodeCallbackUtil {

    private static final ObjectMapper objectMapper = new ObjectMapper();
    private static final HttpClient httpClient = HttpClient.newHttpClient();

    @Value("${xxl.job.admin.accessToken}")
    private String accessToken;

    /**
     * 回调调度中心，通知节点执行完成
     *
     * @param jobParam     任务参数JSON字符串
     * @param nodeType     节点类型 (DATAX, SQL, PYTHON, SHELL等)
     * @param executorType 执行器类型
     * @param success      执行是否成功
     * @param errorMessage 错误信息（失败时）
     */
    public void callbackNodeComplete(String jobParam, String nodeType, String executorType,
                                     boolean success, String errorMessage) {
        try {
            // 解析任务参数获取回调URL
            JsonNode paramNode = objectMapper.readTree(jobParam);
            callbackNodeComplete(paramNode, nodeType, executorType, success, errorMessage);

        } catch (Exception e) {
            log.error("解析任务参数失败，无法回调调度中心", e);
            XxlJobHelper.log("解析任务参数失败，无法回调调度中心: " + e.getMessage());
        }
    }

    /**
     * 回调调度中心，通知节点执行完成
     *
     * @param paramNode    任务参数JsonNode
     * @param nodeType     节点类型 (DATAX, SQL, PYTHON, SHELL等)
     * @param executorType 执行器类型
     * @param success      执行是否成功
     * @param errorMessage 错误信息（失败时）
     */
    public void callbackNodeComplete(JsonNode paramNode, String nodeType, String executorType,
                                     boolean success, String errorMessage) {
        try {
            // 获取回调URL
            if (!paramNode.has("callbackUrl")) {
                log.warn("缺少callbackUrl参数，无法回调调度中心");
                return;
            }

            String callbackUrl = paramNode.get("callbackUrl").asText();
            if (callbackUrl == null || callbackUrl.trim().isEmpty()) {
                log.warn("callbackUrl为空，无法回调调度中心");
                return;
            }

            // 构建回调参数 - 使用与SQL节点相同的数据结构
            Map<String, Object> result = new HashMap<>();
            result.put("executionId", paramNode.has("executionId") ? paramNode.get("executionId").asText() : "");
            result.put("nodeCode", paramNode.has("nodeCode") ? paramNode.get("nodeCode").asText() : "");
            result.put("success", success);
            result.put("errorMessage", errorMessage);
            result.put("startTime", System.currentTimeMillis() - 5000); // 假设执行了5秒，实际可以记录真实开始时间
            result.put("endTime", System.currentTimeMillis());
            result.put("duration", 5000L); // 执行时长（毫秒）
            result.put("executorAddress", "localhost:9999"); // 执行器地址
            result.put("outputParams", new HashMap<>()); // 输出参数
            result.put("logContent", success ? (nodeType + "任务执行成功") : (nodeType + "任务执行失败: " + (errorMessage != null ? errorMessage : "未知错误")));
            result.put("jobId", null); // XXL-JOB任务ID
            result.put("jobLogId", null); // XXL-JOB日志ID
            result.put("retryCount", 0); // 重试次数

            String requestBody = objectMapper.writeValueAsString(result);

            log.info("开始回调调度中心: {}", callbackUrl);
            XxlJobHelper.log("开始回调调度中心: " + callbackUrl);

            // 构建HTTP请求，添加XXL-JOB认证token
            HttpRequest.Builder requestBuilder = HttpRequest.newBuilder()
                    .uri(java.net.URI.create(callbackUrl))
                    .header("Content-Type", "application/json")
                    .POST(HttpRequest.BodyPublishers.ofString(requestBody))
                    .timeout(Duration.ofSeconds(30));

            // 添加XXL-JOB认证token
            if (accessToken != null && !accessToken.trim().isEmpty()) {
                requestBuilder.header("XXL-JOB-ACCESS-TOKEN", accessToken);
                log.debug("添加XXL-JOB认证token到回调请求");
            }

            HttpRequest request = requestBuilder.build();

            // 发送回调请求
            HttpResponse<String> response = httpClient.send(request,
                    HttpResponse.BodyHandlers.ofString());

            if (response.statusCode() == 200) {
                log.info("回调调度中心成功，响应: {}", response.body());
                XxlJobHelper.log("回调调度中心成功");
            } else {
                log.error("回调调度中心失败，状态码: {}, 响应: {}", response.statusCode(), response.body());
                XxlJobHelper.log("回调调度中心失败，状态码: " + response.statusCode());
            }

        } catch (Exception e) {
            log.error("回调调度中心异常", e);
            XxlJobHelper.log("回调调度中心异常: " + e.getMessage());
        }
    }

    /**
     * 安全回调方法，捕获所有异常，不影响主任务执行
     */
    public void safeCallback(String jobParam, String nodeType, String executorType,
                             boolean success, String errorMessage) {
        try {
            callbackNodeComplete(jobParam, nodeType, executorType, success, errorMessage);
        } catch (Exception e) {
            log.error("回调调度中心失败，但不影响主任务执行", e);
            XxlJobHelper.log("回调调度中心失败: " + e.getMessage());
        }
    }
}
