# 通用告警系统设计方案

## 📋 方案概述

设计一个通用的告警系统，支持企业微信、飞书、钉钉、邮件等多种通知渠道，采用模板化配置。**本方案已调整为集成到admin模块中**，而非独立部署，以便更好地与现有系统融合。

## 🎯 设计原则

- **集成性**: 集成到admin模块，复用现有基础设施
- **兼容性**: 保持现有告警功能不变，渐进式扩展
- **通用性**: 支持任意业务场景的告警需求
- **简单性**: 提供简洁的API接口，易于集成
- **可扩展性**: 易于添加新的通知渠道和模板

## 🔄 架构调整说明

**原设计**: 独立部署的告警服务
**新设计**: 集成到admin模块的告警功能

### 主要调整点：
1. **部署方式**: 从独立服务改为admin模块功能
2. **数据库**: 复用admin模块数据库
3. **邮件配置**: 复用现有邮件配置
4. **现有告警**: 保持兼容，扩展功能

## 🏗️ 系统架构

### Admin模块集成架构

```
Admin模块
├── controller/
│   ├── AlertController.java           # 告警API控制器
│   ├── AlertTemplateController.java   # 模板管理控制器
│   └── AlertChannelController.java    # 通道配置控制器
├── service/
│   ├── AlertService.java             # 告警服务
│   ├── AlertTemplateService.java     # 模板服务
│   └── AlertChannelService.java      # 通道配置服务
├── core/
│   └── alarm/                        # 现有告警模块扩展
│       ├── JobAlarm.java            # 现有接口（保持不变）
│       ├── JobAlarmer.java          # 现有实现（保持不变）
│       ├── impl/
│       │   ├── EmailJobAlarm.java   # 现有邮件告警（保持不变）
│       │   └── UniversalJobAlarm.java # 新增：通用告警实现
│       ├── channel/                 # 新增：通道适配器
│       │   ├── NotificationChannel.java
│       │   ├── WechatChannel.java
│       │   ├── FeishuChannel.java
│       │   ├── DingtalkChannel.java
│       │   └── EmailChannel.java
│       ├── template/                # 新增：模板引擎
│       │   ├── TemplateEngine.java
│       │   └── DefaultTemplateEngine.java
│       └── model/                   # 新增：告警模型
│           ├── AlertRequest.java
│           ├── NotificationRequest.java
│           └── SendResult.java
├── dao/
│   ├── AlertTemplateDao.java
│   ├── AlertChannelConfigDao.java
│   └── AlertSendRecordDao.java
└── core/model/
    ├── AlertTemplate.java
    ├── AlertChannelConfig.java
    └── AlertSendRecord.java
```

### 数据流架构

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Admin模块调用  │───▶│   告警控制器     │───▶│   告警服务引擎   │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                                                       │
                       ┌─────────────────┐    ┌─────────────────┐
                       │   模板管理器     │◀───│   通道适配器     │
                       └─────────────────┘    └─────────────────┘
                                │                       │
                                ▼                       ▼
                       ┌─────────────────┐    ┌─────────────────┐
                       │  Admin数据库     │    │   异步执行器     │
                       └─────────────────┘    └─────────────────┘
```

## 📊 数据库设计

### 设计说明
基于现有admin模块的数据库设计风格，添加告警相关表。所有表都遵循现有设计规范：
- 统一的字段命名风格
- 包含 `create_time`, `update_time`, `create_user`, `update_user`, `deleted` 字段
- 使用 `bigint(20)` 作为主键类型

### 1. 告警模板表
```sql
CREATE TABLE `alert_template` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `template_code` varchar(50) NOT NULL COMMENT '模板编码',
  `template_name` varchar(100) NOT NULL COMMENT '模板名称',
  `channel_type` varchar(20) NOT NULL COMMENT '通道类型：WECHAT,FEISHU,DINGTALK,EMAIL',
  `template_content` text NOT NULL COMMENT '模板内容JSON',
  `variables` text COMMENT '模板变量说明JSON',
  `enabled` tinyint(4) NOT NULL DEFAULT 1 COMMENT '是否启用',
  `description` varchar(500) COMMENT '模板描述',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP,
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `create_user` varchar(50) DEFAULT NULL COMMENT '创建人',
  `update_user` varchar(50) DEFAULT NULL COMMENT '更新人',
  `deleted` tinyint(4) NOT NULL DEFAULT '0' COMMENT '删除标记：0-未删除，1-已删除',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_template_code_channel` (`template_code`, `channel_type`),
  KEY `idx_channel_type` (`channel_type`),
  KEY `idx_enabled` (`enabled`),
  KEY `idx_deleted` (`deleted`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='告警模板表';
```

### 2. 通道配置表
```sql
CREATE TABLE `alert_channel_config` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `config_name` varchar(100) NOT NULL COMMENT '配置名称',
  `channel_type` varchar(20) NOT NULL COMMENT '通道类型：WECHAT,FEISHU,DINGTALK,EMAIL',
  `auth_type` varchar(20) NOT NULL COMMENT '认证类型：WEBHOOK,TOKEN,OAUTH,SMTP',
  `auth_config` text NOT NULL COMMENT '认证配置JSON（加密存储）',
  `channel_config` text COMMENT '通道特定配置JSON',
  `enabled` tinyint(4) NOT NULL DEFAULT 1 COMMENT '是否启用',
  `description` varchar(500) COMMENT '配置描述',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP,
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `create_user` varchar(50) DEFAULT NULL,
  `update_user` varchar(50) DEFAULT NULL,
  `deleted` tinyint(4) NOT NULL DEFAULT '0' COMMENT '删除标记：0-未删除，1-已删除',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_config_name` (`config_name`),
  KEY `idx_channel_type` (`channel_type`),
  KEY `idx_auth_type` (`auth_type`),
  KEY `idx_enabled` (`enabled`),
  KEY `idx_deleted` (`deleted`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='通道配置表';
```

### 3. 告警发送记录表
```sql
CREATE TABLE `alert_send_record` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `request_id` varchar(100) NOT NULL COMMENT '请求ID',
  `template_code` varchar(50) NOT NULL COMMENT '模板编码',
  `channel_type` varchar(20) NOT NULL COMMENT '通道类型',
  `config_name` varchar(100) COMMENT '使用的配置名称',
  `recipients` text NOT NULL COMMENT '接收人列表JSON',
  `title` varchar(200) NOT NULL COMMENT '消息标题',
  `content` text NOT NULL COMMENT '消息内容',
  `variables` text COMMENT '模板变量JSON',
  `send_time` datetime NOT NULL COMMENT '发送时间',
  `send_status` varchar(20) NOT NULL COMMENT '发送状态：SUCCESS,FAILED,RETRY',
  `response_message` text COMMENT '响应消息',
  `retry_count` int(11) DEFAULT 0 COMMENT '重试次数',
  `source_system` varchar(50) COMMENT '来源系统',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_request_id` (`request_id`),
  KEY `idx_template_code` (`template_code`),
  KEY `idx_send_time` (`send_time`),
  KEY `idx_send_status` (`send_status`),
  KEY `idx_source_system` (`source_system`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='告警发送记录表';
```

## 🔧 核心组件设计

### 设计说明
保持现有告警接口兼容性，扩展新的通用告警功能。

### 1. 现有告警接口兼容
保持以下组件不变，确保现有功能正常：
- `JobAlarm` 接口
- `JobAlarmer` 实现
- `EmailJobAlarm` 实现

### 2. 新增通用告警实现
```java
@Component
public class UniversalJobAlarm implements JobAlarm {
    @Autowired
    private AlertService alertService;

    @Override
    public boolean doAlarm(JobInfo info, JobLog jobLog) {
        // 使用新的通用告警系统发送告警
        AlertRequest request = buildJobAlertRequest(info, jobLog);
        String requestId = alertService.sendAlert(request);
        return StringUtils.isNotBlank(requestId);
    }

    private AlertRequest buildJobAlertRequest(JobInfo info, JobLog jobLog) {
        // 构建告警请求...
    }
}
```

### 3. 告警API接口
```java
@RestController
@RequestMapping("/api/alert")
@Slf4j
public class AlertController {
    
    @Autowired
    private AlertService alertService;
    
    /**
     * 发送告警通知
     */
    @PostMapping("/send")
    public ReturnT<String> sendAlert(@RequestBody AlertRequest request) {
        try {
            String requestId = alertService.sendAlert(request);
            return new ReturnT<>(requestId);
        } catch (Exception e) {
            log.error("发送告警失败", e);
            return new ReturnT<>(ReturnT.FAIL_CODE, "发送失败: " + e.getMessage());
        }
    }
    
    /**
     * 批量发送告警通知
     */
    @PostMapping("/send/batch")
    public ReturnT<List<String>> sendAlertBatch(@RequestBody List<AlertRequest> requests) {
        try {
            List<String> requestIds = alertService.sendAlertBatch(requests);
            return new ReturnT<>(requestIds);
        } catch (Exception e) {
            log.error("批量发送告警失败", e);
            return new ReturnT<>(ReturnT.FAIL_CODE, "发送失败: " + e.getMessage());
        }
    }
    
    /**
     * 查询发送状态
     */
    @GetMapping("/status/{requestId}")
    public ReturnT<AlertSendStatus> getAlertStatus(@PathVariable String requestId) {
        AlertSendStatus status = alertService.getAlertStatus(requestId);
        return new ReturnT<>(status);
    }
}
```

### 2. 告警请求模型
```java
@Data
@Builder
public class AlertRequest {
    /**
     * 模板编码
     */
    private String templateCode;
    
    /**
     * 通道类型列表（支持多通道发送）
     */
    private List<ChannelType> channelTypes;
    
    /**
     * 接收人配置
     */
    private Recipients recipients;
    
    /**
     * 模板变量
     */
    private Map<String, Object> variables;
    
    /**
     * 来源系统（可选）
     */
    private String sourceSystem;
    
    /**
     * 优先级（可选）
     */
    private Priority priority;
}

@Data
public class Recipients {
    /**
     * 用户ID列表
     */
    private List<String> userIds;
    
    /**
     * 手机号列表
     */
    private List<String> phones;
    
    /**
     * 邮箱列表
     */
    private List<String> emails;
    
    /**
     * 企业微信用户ID列表
     */
    private List<String> wechatUserIds;
    
    /**
     * 钉钉用户ID列表
     */
    private List<String> dingtalkUserIds;
    
    /**
     * 飞书用户ID列表
     */
    private List<String> feishuUserIds;
}

public enum ChannelType {
    WECHAT("WECHAT", "企业微信"),
    FEISHU("FEISHU", "飞书"),
    DINGTALK("DINGTALK", "钉钉"),
    EMAIL("EMAIL", "邮件");
}

public enum Priority {
    LOW, NORMAL, HIGH, URGENT
}
```

### 3. 告警服务核心实现
```java
@Service
@Slf4j
public class AlertService {
    
    @Autowired
    private AlertTemplateService templateService;
    
    @Autowired
    private NotificationChannelManager channelManager;
    
    @Autowired
    private AlertRecordService recordService;
    
    /**
     * 发送告警
     */
    public String sendAlert(AlertRequest request) {
        String requestId = UUID.randomUUID().toString();
        
        // 异步处理告警发送
        CompletableFuture.runAsync(() -> {
            processAlert(requestId, request);
        }, alertExecutor);
        
        return requestId;
    }
    
    /**
     * 批量发送告警
     */
    public List<String> sendAlertBatch(List<AlertRequest> requests) {
        List<String> requestIds = new ArrayList<>();
        
        for (AlertRequest request : requests) {
            String requestId = sendAlert(request);
            requestIds.add(requestId);
        }
        
        return requestIds;
    }
    
    private void processAlert(String requestId, AlertRequest request) {
        try {
            // 验证请求参数
            validateRequest(request);
            
            // 处理每个通道类型
            for (ChannelType channelType : request.getChannelTypes()) {
                processChannelAlert(requestId, request, channelType);
            }
            
        } catch (Exception e) {
            log.error("处理告警失败: requestId={}", requestId, e);
            recordService.recordFailure(requestId, e.getMessage());
        }
    }
    
    private void processChannelAlert(String requestId, AlertRequest request, ChannelType channelType) {
        try {
            // 渲染模板
            String content = templateService.renderTemplate(
                request.getTemplateCode(), 
                channelType, 
                request.getVariables()
            );
            
            // 获取接收人
            List<String> recipients = extractRecipients(request.getRecipients(), channelType);
            
            if (recipients.isEmpty()) {
                log.warn("没有找到接收人: requestId={}, channelType={}", requestId, channelType);
                return;
            }
            
            // 构建通知请求
            NotificationRequest notificationRequest = NotificationRequest.builder()
                .requestId(requestId)
                .channelType(channelType)
                .recipients(recipients)
                .content(content)
                .build();
            
            // 发送通知
            SendResult result = channelManager.sendNotification(notificationRequest);
            
            // 记录发送结果
            recordService.recordSendResult(requestId, request, channelType, result);
            
        } catch (Exception e) {
            log.error("处理通道告警失败: requestId={}, channelType={}", requestId, channelType, e);
            recordService.recordChannelFailure(requestId, channelType, e.getMessage());
        }
    }
}
```

### 4. 认证配置模型
```java
public enum AuthType {
    WEBHOOK("WEBHOOK", "Webhook认证"),
    TOKEN("TOKEN", "Token认证"),
    OAUTH("OAUTH", "OAuth认证"),
    SMTP("SMTP", "SMTP认证"),
    APP_SECRET("APP_SECRET", "应用密钥认证");
}

// 企业微信认证配置
@Data
public class WechatAuthConfig {
    private String webhookUrl;      // Webhook地址
    private String corpId;          // 企业ID（可选，用于高级功能）
    private String agentId;         // 应用ID（可选）
    private String corpSecret;      // 应用密钥（可选）
}

// 飞书认证配置
@Data
public class FeishuAuthConfig {
    private String webhookUrl;      // Webhook地址
    private String appId;           // 应用ID（可选）
    private String appSecret;       // 应用密钥（可选）
    private String tenantToken;     // 租户访问凭证（可选）
}

// 钉钉认证配置
@Data
public class DingtalkAuthConfig {
    private String webhookUrl;      // Webhook地址
    private String accessToken;     // 访问令牌
    private String secret;          // 加签密钥（可选）
    private String appKey;          // 应用Key（可选）
    private String appSecret;       // 应用密钥（可选）
}

// 邮件认证配置
@Data
public class EmailAuthConfig {
    private String smtpHost;        // SMTP服务器
    private Integer smtpPort;       // SMTP端口
    private String username;        // 用户名
    private String password;        // 密码
    private String fromAddress;     // 发件人地址
    private String fromName;        // 发件人名称
    private Boolean enableTls;      // 是否启用TLS
    private Boolean enableSsl;      // 是否启用SSL
}
```

### 5. 通道适配器接口
```java
public interface NotificationChannel {
    ChannelType getChannelType();
    SendResult sendNotification(NotificationRequest request);
    boolean validateAuthConfig(AuthConfig authConfig);
    AuthType getSupportedAuthType();
}

@Data
@Builder
public class NotificationRequest {
    private String requestId;
    private ChannelType channelType;
    private List<String> recipients;
    private String content;
    private AuthConfig authConfig;      // 认证配置
    private Map<String, Object> channelConfig;  // 通道特定配置
}

@Data
public class AuthConfig {
    private AuthType authType;
    private Map<String, Object> authData;   // 认证数据（解密后）

    public <T> T getAuthData(Class<T> clazz) {
        ObjectMapper mapper = new ObjectMapper();
        return mapper.convertValue(authData, clazz);
    }
}

@Data
public class SendResult {
    private boolean success;
    private String message;
    private String responseCode;
    private Map<String, Object> responseData;

    public static SendResult success(String message) {
        SendResult result = new SendResult();
        result.setSuccess(true);
        result.setMessage(message);
        return result;
    }

    public static SendResult failure(String message) {
        SendResult result = new SendResult();
        result.setSuccess(false);
        result.setMessage(message);
        return result;
    }
}
```

### 6. 通道适配器实现示例

#### 企业微信适配器
```java
@Component
public class WechatNotificationChannel implements NotificationChannel {

    @Override
    public ChannelType getChannelType() {
        return ChannelType.WECHAT;
    }

    @Override
    public AuthType getSupportedAuthType() {
        return AuthType.WEBHOOK;
    }

    @Override
    public SendResult sendNotification(NotificationRequest request) {
        try {
            WechatAuthConfig authConfig = request.getAuthConfig().getAuthData(WechatAuthConfig.class);

            // 构建企业微信消息
            Map<String, Object> message = new HashMap<>();
            message.put("msgtype", "markdown");
            message.put("markdown", Map.of("content", request.getContent()));

            // 发送HTTP请求
            String response = httpClient.post(authConfig.getWebhookUrl(), message);

            return SendResult.success("发送成功: " + response);
        } catch (Exception e) {
            return SendResult.failure("发送失败: " + e.getMessage());
        }
    }

    @Override
    public boolean validateAuthConfig(AuthConfig authConfig) {
        try {
            WechatAuthConfig config = authConfig.getAuthData(WechatAuthConfig.class);
            return config.getWebhookUrl() != null && config.getWebhookUrl().startsWith("https://qyapi.weixin.qq.com");
        } catch (Exception e) {
            return false;
        }
    }
}
```

#### 钉钉适配器
```java
@Component
public class DingtalkNotificationChannel implements NotificationChannel {

    @Override
    public ChannelType getChannelType() {
        return ChannelType.DINGTALK;
    }

    @Override
    public AuthType getSupportedAuthType() {
        return AuthType.TOKEN;
    }

    @Override
    public SendResult sendNotification(NotificationRequest request) {
        try {
            DingtalkAuthConfig authConfig = request.getAuthConfig().getAuthData(DingtalkAuthConfig.class);

            // 构建钉钉消息
            Map<String, Object> message = new HashMap<>();
            message.put("msgtype", "markdown");
            message.put("markdown", Map.of(
                "title", "系统告警",
                "text", request.getContent()
            ));

            // 如果配置了加签，需要计算签名
            String url = authConfig.getWebhookUrl();
            if (authConfig.getSecret() != null) {
                url = addSignature(url, authConfig.getSecret());
            }

            String response = httpClient.post(url, message);

            return SendResult.success("发送成功: " + response);
        } catch (Exception e) {
            return SendResult.failure("发送失败: " + e.getMessage());
        }
    }

    private String addSignature(String webhookUrl, String secret) {
        // 实现钉钉加签逻辑
        long timestamp = System.currentTimeMillis();
        String stringToSign = timestamp + "\n" + secret;
        String sign = hmacSha256(stringToSign, secret);
        return webhookUrl + "&timestamp=" + timestamp + "&sign=" + URLEncoder.encode(sign, "UTF-8");
    }
}
```

#### 邮件适配器
```java
@Component
public class EmailNotificationChannel implements NotificationChannel {

    @Override
    public ChannelType getChannelType() {
        return ChannelType.EMAIL;
    }

    @Override
    public AuthType getSupportedAuthType() {
        return AuthType.SMTP;
    }

    @Override
    public SendResult sendNotification(NotificationRequest request) {
        try {
            EmailAuthConfig authConfig = request.getAuthConfig().getAuthData(EmailAuthConfig.class);

            // 配置邮件会话
            Properties props = new Properties();
            props.put("mail.smtp.host", authConfig.getSmtpHost());
            props.put("mail.smtp.port", authConfig.getSmtpPort());
            props.put("mail.smtp.auth", "true");
            if (authConfig.getEnableTls()) {
                props.put("mail.smtp.starttls.enable", "true");
            }
            if (authConfig.getEnableSsl()) {
                props.put("mail.smtp.ssl.enable", "true");
            }

            Session session = Session.getInstance(props, new Authenticator() {
                @Override
                protected PasswordAuthentication getPasswordAuthentication() {
                    return new PasswordAuthentication(authConfig.getUsername(), authConfig.getPassword());
                }
            });

            // 构建邮件消息
            MimeMessage message = new MimeMessage(session);
            message.setFrom(new InternetAddress(authConfig.getFromAddress(), authConfig.getFromName()));

            // 设置收件人
            for (String recipient : request.getRecipients()) {
                message.addRecipient(Message.RecipientType.TO, new InternetAddress(recipient));
            }

            // 解析内容（假设是HTML格式）
            Map<String, String> contentMap = parseEmailContent(request.getContent());
            message.setSubject(contentMap.get("subject"));
            message.setContent(contentMap.get("body"), "text/html;charset=utf-8");

            // 发送邮件
            Transport.send(message);

            return SendResult.success("邮件发送成功");
        } catch (Exception e) {
            return SendResult.failure("邮件发送失败: " + e.getMessage());
        }
    }
}
```

### 7. 通道配置管理服务
```java
@Service
public class ChannelConfigService {

    @Autowired
    private AlertChannelConfigDao configDao;

    @Autowired
    private EncryptionService encryptionService;

    /**
     * 保存通道配置
     */
    public void saveChannelConfig(ChannelConfigDTO configDTO) {
        AlertChannelConfig config = new AlertChannelConfig();
        config.setConfigName(configDTO.getConfigName());
        config.setChannelType(configDTO.getChannelType());
        config.setAuthType(configDTO.getAuthType());

        // 加密认证配置
        String encryptedAuthConfig = encryptionService.encrypt(
            JsonUtils.toJson(configDTO.getAuthConfig())
        );
        config.setAuthConfig(encryptedAuthConfig);

        config.setChannelConfig(JsonUtils.toJson(configDTO.getChannelConfig()));
        config.setEnabled(configDTO.getEnabled());
        config.setDescription(configDTO.getDescription());

        configDao.save(config);
    }

    /**
     * 获取通道配置
     */
    public AuthConfig getAuthConfig(String configName) {
        AlertChannelConfig config = configDao.findByConfigName(configName);
        if (config == null) {
            throw new RuntimeException("通道配置不存在: " + configName);
        }

        // 解密认证配置
        String decryptedAuthConfig = encryptionService.decrypt(config.getAuthConfig());
        Map<String, Object> authData = JsonUtils.parseMap(decryptedAuthConfig);

        AuthConfig authConfig = new AuthConfig();
        authConfig.setAuthType(AuthType.valueOf(config.getAuthType()));
        authConfig.setAuthData(authData);

        return authConfig;
    }
}
```

## 🎨 使用示例

### 1. 简单告警发送
```java
// 在业务代码中调用
@Autowired
private AlertService alertService;

public void sendWorkflowFailedAlert(String workflowName, String errorMessage) {
    AlertRequest request = AlertRequest.builder()
        .templateCode("WORKFLOW_FAILED")
        .channelTypes(Arrays.asList(ChannelType.WECHAT, ChannelType.EMAIL))
        .recipients(Recipients.builder()
            .userIds(Arrays.asList("user1", "user2"))
            .emails(Arrays.asList("<EMAIL>"))
            .build())
        .variables(Map.of(
            "workflowName", workflowName,
            "errorMessage", errorMessage,
            "alertTime", LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"))
        ))
        .sourceSystem("IBDP")
        .priority(Priority.HIGH)
        .build();
    
    String requestId = alertService.sendAlert(request);
    log.info("告警发送请求已提交: requestId={}", requestId);
}
```

### 2. 模板配置示例
```json
// 企业微信模板
{
  "templateCode": "WORKFLOW_FAILED",
  "templateName": "工作流失败通知",
  "channelType": "WECHAT",
  "templateContent": {
    "msgtype": "markdown",
    "markdown": {
      "content": "## 🚨 工作流执行失败\n\n**工作流**: ${workflowName}\n**时间**: ${alertTime}\n**原因**: ${errorMessage}\n\n请及时处理！"
    }
  },
  "variables": [
    {"name": "workflowName", "description": "工作流名称"},
    {"name": "alertTime", "description": "告警时间"},
    {"name": "errorMessage", "description": "错误信息"}
  ]
}

// 邮件模板
{
  "templateCode": "WORKFLOW_FAILED",
  "templateName": "工作流失败通知",
  "channelType": "EMAIL",
  "templateContent": {
    "subject": "【告警】工作流执行失败 - ${workflowName}",
    "body": "<h2>工作流执行失败</h2><p><strong>工作流名称:</strong> ${workflowName}</p><p><strong>失败时间:</strong> ${alertTime}</p><p><strong>失败原因:</strong> ${errorMessage}</p><p>请登录系统查看详细信息并及时处理。</p>"
  }
}
```

## 🚀 部署和配置

### 1. 应用配置
基于现有admin模块配置，新增告警相关配置：

```properties
# 现有邮件配置保持不变
spring.mail.host=smtp.qq.com
spring.mail.port=25
spring.mail.username=<EMAIL>
spring.mail.from=<EMAIL>
spring.mail.password=xxx

# 新增告警系统配置
alert.enabled=true
alert.async.core-pool-size=5
alert.async.max-pool-size=20
alert.async.queue-capacity=1000
alert.async.thread-name-prefix=alert-
alert.retry.max-attempts=3
alert.retry.delay=5000
```

### 2. 默认数据初始化
```sql
-- 插入默认模板数据
INSERT INTO `alert_template` (`template_code`, `template_name`, `channel_type`, `template_content`, `variables`, `description`) VALUES
('JOB_FAILED', '任务执行失败通知', 'WECHAT', '{"msgtype":"markdown","markdown":{"content":"## 🚨 任务执行失败\\n\\n**任务名称**: ${jobDesc}\\n**任务组**: ${jobGroup}\\n**执行时间**: ${triggerTime}\\n**失败原因**: ${handleMsg}\\n\\n请及时处理！"}}', '[{"name":"jobDesc","description":"任务描述"},{"name":"jobGroup","description":"任务组"},{"name":"triggerTime","description":"触发时间"},{"name":"handleMsg","description":"处理消息"}]', '任务失败企业微信通知模板'),
('JOB_FAILED', '任务执行失败通知', 'EMAIL', '{"subject":"【告警】任务执行失败 - ${jobDesc}","body":"<h2>任务执行失败</h2><p><strong>任务名称:</strong> ${jobDesc}</p><p><strong>任务组:</strong> ${jobGroup}</p><p><strong>执行时间:</strong> ${triggerTime}</p><p><strong>失败原因:</strong> ${handleMsg}</p><p>请登录系统查看详细信息并及时处理。</p>"}', '[{"name":"jobDesc","description":"任务描述"},{"name":"jobGroup","description":"任务组"},{"name":"triggerTime","description":"触发时间"},{"name":"handleMsg","description":"处理消息"}]', '任务失败邮件通知模板'),
('WORKFLOW_FAILED', '工作流执行失败通知', 'WECHAT', '{"msgtype":"markdown","markdown":{"content":"## 🚨 工作流执行失败\\n\\n**工作流名称**: ${workflowName}\\n**项目**: ${projectName}\\n**失败时间**: ${failTime}\\n**失败原因**: ${errorMessage}\\n\\n请及时处理！"}}', '[{"name":"workflowName","description":"工作流名称"},{"name":"projectName","description":"项目名称"},{"name":"failTime","description":"失败时间"},{"name":"errorMessage","description":"错误信息"}]', '工作流失败企业微信通知模板'),
('SYSTEM_ERROR', '系统错误通知', 'WECHAT', '{"msgtype":"text","text":{"content":"系统错误：${errorMessage}\\n时间：${alertTime}\\n来源：${sourceSystem}"}}', '[{"name":"errorMessage","description":"错误信息"},{"name":"alertTime","description":"告警时间"},{"name":"sourceSystem","description":"来源系统"}]', '系统错误通知模板');
```

### 3. 认证配置示例
```json
// 企业微信Webhook配置
{
  "webhookUrl": "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx",
  "corpId": "ww1234567890abcdef",
  "agentId": "1000001"
}

// 钉钉Token配置
{
  "webhookUrl": "https://oapi.dingtalk.com/robot/send?access_token=xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx",
  "secret": "SECxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx",
  "accessToken": "xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx"
}

// 飞书Webhook配置
{
  "webhookUrl": "https://open.feishu.cn/open-apis/bot/v2/hook/xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx",
  "appId": "cli_xxxxxxxxxxxxxxxx",
  "appSecret": "xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx"
}

// 邮件SMTP配置
{
  "smtpHost": "smtp.exmail.qq.com",
  "smtpPort": 587,
  "username": "<EMAIL>",
  "password": "your_password",
  "fromAddress": "<EMAIL>",
  "fromName": "系统告警",
  "enableTls": true,
  "enableSsl": false
}
```

## 🔄 与现有系统的集成

### 1. 任务告警集成
在现有任务告警基础上，新增通用告警实现：

```java
@Component
public class UniversalJobAlarm implements JobAlarm {
    @Autowired
    private AlertService alertService;

    @Override
    public boolean doAlarm(JobInfo info, JobLog jobLog) {
        try {
            // 构建告警请求
            AlertRequest request = AlertRequest.builder()
                .templateCode("JOB_FAILED")
                .channelTypes(parseChannelTypes(info.getAlarmEmail()))
                .recipients(buildRecipients(info.getAlarmEmail()))
                .variables(buildJobVariables(info, jobLog))
                .sourceSystem("IBDP-ADMIN")
                .build();

            String requestId = alertService.sendAlert(request);
            return StringUtils.isNotBlank(requestId);
        } catch (Exception e) {
            logger.error("通用告警发送失败", e);
            return false;
        }
    }
}
```

### 2. 工作流告警集成
在工作流执行失败时调用告警服务：

```java
// 在WorkflowInstanceService中
public void handleWorkflowFailed(WorkflowInstance instance, String errorMessage) {
    // 现有的失败处理逻辑...

    // 发送告警通知
    try {
        alertService.sendAlert(AlertRequest.builder()
            .templateCode("WORKFLOW_FAILED")
            .channelTypes(Arrays.asList(ChannelType.WECHAT, ChannelType.EMAIL))
            .recipients(getWorkflowAlertRecipients(instance))
            .variables(Map.of(
                "workflowName", instance.getWorkflowName(),
                "projectName", instance.getProjectName(),
                "failTime", LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")),
                "errorMessage", errorMessage
            ))
            .sourceSystem("IBDP-WORKFLOW")
            .build());
    } catch (Exception e) {
        logger.error("工作流告警发送失败", e);
    }
}
```

## 🎨 前端配置界面设计

### 前端集成说明
在现有admin前端基础上添加告警管理模块：

```
告警管理菜单
├── 通道配置管理
│   ├── 企业微信配置
│   ├── 飞书配置
│   ├── 钉钉配置
│   └── 邮件配置（复用现有）
├── 模板管理
│   ├── 模板列表
│   ├── 模板编辑
│   └── 模板测试
└── 发送记录
    ├── 记录查询
    └── 状态统计
```

### 1. 通道配置管理界面
```vue
<template>
  <div class="channel-config">
    <el-card>
      <template #header>
        <div class="card-header">
          <span>通知渠道配置</span>
          <el-button type="primary" @click="showAddDialog">新增配置</el-button>
        </div>
      </template>

      <el-table :data="channelConfigs" stripe>
        <el-table-column prop="configName" label="配置名称" />
        <el-table-column prop="channelType" label="通道类型">
          <template #default="{ row }">
            <el-tag :type="getChannelTypeColor(row.channelType)">
              {{ getChannelTypeName(row.channelType) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="authType" label="认证类型" />
        <el-table-column prop="enabled" label="状态">
          <template #default="{ row }">
            <el-switch v-model="row.enabled" @change="toggleConfig(row)" />
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200">
          <template #default="{ row }">
            <el-button size="small" @click="editConfig(row)">编辑</el-button>
            <el-button size="small" @click="testConfig(row)">测试</el-button>
            <el-button size="small" type="danger" @click="deleteConfig(row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>

    <!-- 配置对话框 -->
    <ChannelConfigDialog
      v-model:visible="dialogVisible"
      :config="currentConfig"
      @success="loadConfigs"
    />
  </div>
</template>
```

### 2. 通道配置表单组件
```vue
<template>
  <el-dialog v-model="visible" :title="title" width="600px">
    <el-form ref="formRef" :model="form" :rules="rules" label-width="120px">
      <el-form-item label="配置名称" prop="configName">
        <el-input v-model="form.configName" placeholder="请输入配置名称" />
      </el-form-item>

      <el-form-item label="通道类型" prop="channelType">
        <el-select v-model="form.channelType" @change="handleChannelTypeChange">
          <el-option label="企业微信" value="WECHAT" />
          <el-option label="飞书" value="FEISHU" />
          <el-option label="钉钉" value="DINGTALK" />
          <el-option label="邮件" value="EMAIL" />
        </el-select>
      </el-form-item>

      <!-- 企业微信配置 -->
      <template v-if="form.channelType === 'WECHAT'">
        <el-form-item label="Webhook地址" prop="webhookUrl">
          <el-input v-model="form.authConfig.webhookUrl"
                    placeholder="https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=..." />
        </el-form-item>
        <el-form-item label="企业ID" prop="corpId">
          <el-input v-model="form.authConfig.corpId" placeholder="可选，用于高级功能" />
        </el-form-item>
      </template>

      <!-- 钉钉配置 -->
      <template v-if="form.channelType === 'DINGTALK'">
        <el-form-item label="Webhook地址" prop="webhookUrl">
          <el-input v-model="form.authConfig.webhookUrl"
                    placeholder="https://oapi.dingtalk.com/robot/send?access_token=..." />
        </el-form-item>
        <el-form-item label="加签密钥" prop="secret">
          <el-input v-model="form.authConfig.secret"
                    placeholder="可选，用于加签验证" type="password" />
        </el-form-item>
      </template>

      <!-- 邮件配置 -->
      <template v-if="form.channelType === 'EMAIL'">
        <el-row :gutter="20">
          <el-col :span="16">
            <el-form-item label="SMTP服务器" prop="smtpHost">
              <el-input v-model="form.authConfig.smtpHost" placeholder="smtp.exmail.qq.com" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="端口" prop="smtpPort">
              <el-input-number v-model="form.authConfig.smtpPort" :min="1" :max="65535" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="用户名" prop="username">
          <el-input v-model="form.authConfig.username" />
        </el-form-item>
        <el-form-item label="密码" prop="password">
          <el-input v-model="form.authConfig.password" type="password" />
        </el-form-item>
        <el-form-item label="发件人地址" prop="fromAddress">
          <el-input v-model="form.authConfig.fromAddress" />
        </el-form-item>
        <el-form-item label="发件人名称" prop="fromName">
          <el-input v-model="form.authConfig.fromName" />
        </el-form-item>
        <el-form-item label="安全设置">
          <el-checkbox v-model="form.authConfig.enableTls">启用TLS</el-checkbox>
          <el-checkbox v-model="form.authConfig.enableSsl">启用SSL</el-checkbox>
        </el-form-item>
      </template>

      <el-form-item label="描述">
        <el-input v-model="form.description" type="textarea" :rows="3" />
      </el-form-item>
    </el-form>

    <template #footer>
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="testConnection" :loading="testing">测试连接</el-button>
      <el-button type="primary" @click="handleSubmit" :loading="submitting">保存</el-button>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, reactive, computed } from 'vue'
import { ElMessage } from 'element-plus'

const props = defineProps({
  visible: Boolean,
  config: Object
})

const emit = defineEmits(['update:visible', 'success'])

const formRef = ref()
const testing = ref(false)
const submitting = ref(false)

const form = reactive({
  configName: '',
  channelType: 'WECHAT',
  authConfig: {},
  description: ''
})

const rules = {
  configName: [{ required: true, message: '请输入配置名称', trigger: 'blur' }],
  channelType: [{ required: true, message: '请选择通道类型', trigger: 'change' }],
  webhookUrl: [{ required: true, message: '请输入Webhook地址', trigger: 'blur' }]
}

// 测试连接
const testConnection = async () => {
  try {
    testing.value = true
    const result = await channelConfigApi.testConnection(form)
    if (result.success) {
      ElMessage.success('连接测试成功')
    } else {
      ElMessage.error('连接测试失败: ' + result.message)
    }
  } catch (error) {
    ElMessage.error('连接测试失败: ' + error.message)
  } finally {
    testing.value = false
  }
}
</script>
```

## 🚀 实施步骤

### 阶段一：基础框架搭建
1. **数据库表创建**
   - 执行告警相关表的DDL脚本
   - 插入默认模板数据

2. **实体类和DAO层**
   - 创建AlertTemplate、AlertChannelConfig、AlertSendRecord实体类
   - 创建对应的DAO接口和XML映射文件

3. **基础Service层**
   - 实现AlertTemplateService
   - 实现AlertChannelService
   - 实现AlertSendRecordService

### 阶段二：核心功能实现
1. **通道适配器**
   - 实现NotificationChannel接口
   - 实现各通道适配器（微信、飞书、钉钉、邮件）

2. **模板引擎**
   - 实现TemplateEngine接口
   - 支持变量替换和JSON模板渲染

3. **告警服务**
   - 实现AlertService核心逻辑
   - 支持异步发送和重试机制

4. **Controller层**
   - 实现告警API接口
   - 实现管理界面API

### 阶段三：前端界面开发
1. **通道配置管理**
   - 配置列表页面
   - 配置编辑表单
   - 连接测试功能

2. **模板管理**
   - 模板列表页面
   - 模板编辑器
   - 模板预览功能

3. **发送记录**
   - 记录查询页面
   - 状态统计图表

### 阶段四：系统集成
1. **现有告警扩展**
   - 实现UniversalJobAlarm
   - 配置告警策略

2. **工作流集成**
   - 在工作流失败处添加告警调用
   - 配置工作流告警模板

3. **API提供**
   - 为其他模块提供告警API
   - 完善文档和示例

## 📈 扩展性设计

### 1. 新增通知渠道
只需实现 `NotificationChannel` 接口并注册为Spring Bean：

```java
@Component
public class SmsNotificationChannel implements NotificationChannel {
    @Override
    public ChannelType getChannelType() {
        return ChannelType.SMS;
    }

    @Override
    public SendResult sendNotification(NotificationRequest request) {
        // 实现短信发送逻辑
        return SendResult.success("短信发送成功");
    }
}
```

### 2. 自定义模板引擎
可以替换默认的模板引擎实现：

```java
@Component
public class CustomTemplateEngine implements TemplateEngine {
    @Override
    public String render(String template, Map<String, Object> variables) {
        // 自定义模板渲染逻辑
        return processedContent;
    }
}
```

## 🎯 总结

### 集成到Admin模块的告警系统特点：

1. **集成性**: 集成到admin模块，复用现有基础设施和配置
2. **兼容性**: 保持现有告警功能不变，渐进式扩展
3. **简单易用**: 提供简洁的API接口，业务模块只需调用一个方法
4. **模板化**: 支持灵活的消息模板配置，适应不同场景
5. **多通道**: 支持企业微信、飞书、钉钉、邮件等多种通知方式
6. **认证安全**: 支持多种认证方式，配置信息加密存储
7. **异步处理**: 异步发送告警，不影响业务系统性能
8. **可扩展**: 易于添加新的通知渠道和认证方式

### 与原独立设计的对比：

| 方面 | 原独立设计 | Admin模块集成设计 |
|------|------------|-------------------|
| 部署方式 | 独立服务 | 集成到admin模块 |
| 数据库 | 独立数据库 | 复用admin数据库 |
| 邮件配置 | 独立配置 | 复用现有配置 |
| 前端界面 | 独立前端 | 集成到admin前端 |
| API访问 | 独立API | admin模块API |
| 现有告警 | 完全替换 | 兼容扩展 |
| 维护成本 | 独立维护 | 统一维护 |

### 实施优势：

1. **最小侵入性**: 保持现有代码不变，只是扩展功能
2. **渐进式迁移**: 可以逐步从现有邮件告警迁移到通用告警
3. **配置复用**: 复用现有的邮件配置和数据库连接
4. **统一管理**: 在一个系统中管理所有告警配置
5. **易于维护**: 遵循现有的代码结构和设计规范

### 业务使用流程：

1. **管理员配置**: 在admin管理界面配置各种通知渠道的认证信息
2. **模板配置**: 配置不同场景的消息模板
3. **业务调用**: 业务模块调用 `alertService.sendAlert(request)` 方法
4. **自动处理**: 系统自动处理认证、模板渲染、多通道发送、失败重试等逻辑

这样设计的好处是业务模块完全不需要关心各种通知渠道的认证细节，只需要指定使用哪个模板和发送给谁即可，同时充分利用了现有admin模块的基础设施。
