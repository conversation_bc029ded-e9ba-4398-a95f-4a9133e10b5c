# 调度中心编排实施方案

## 📋 项目概述

将现有的执行器编排模式改造为调度中心编排模式，实现工作流在调度中心的统一编排和管理，提升系统的可观测性、可控性和扩展性。

## 🎯 实施目标

- **统一编排**：工作流编排逻辑从执行器迁移到调度中心
- **细粒度监控**：实现节点级别的状态监控和管理
- **故障恢复**：支持节点级别的重试和故障转移
- **负载均衡**：节点可以分发到不同执行器执行
- **向后兼容**：保持现有功能不受影响

## 🗂️ 实施阶段

### 阶段一：数据库表结构调整
### 阶段二：调度中心编排引擎开发
### 阶段三：执行器节点处理器改造
### 阶段四：前端界面适配
### 阶段五：测试和上线

---

## 🗄️ 阶段一：数据库表结构调整

### 1.1 现有表字段扩展

```sql
-- 1. workflow表添加编排模式字段
ALTER TABLE `workflow` 
ADD COLUMN `orchestration_mode` varchar(20) DEFAULT 'EXECUTOR' COMMENT '编排模式：CENTRAL-调度中心编排，EXECUTOR-执行器编排' AFTER `job_id`,
ADD COLUMN `last_execution_id` varchar(100) DEFAULT NULL COMMENT '最后执行ID' AFTER `orchestration_mode`,
ADD COLUMN `execution_count` bigint(20) DEFAULT '0' COMMENT '执行次数' AFTER `last_execution_id`,
ADD COLUMN `success_count` bigint(20) DEFAULT '0' COMMENT '成功次数' AFTER `execution_count`,
ADD COLUMN `failure_count` bigint(20) DEFAULT '0' COMMENT '失败次数' AFTER `success_count`,
ADD INDEX `idx_orchestration_mode` (`orchestration_mode`);

-- 2. task_instance表添加编排相关字段
ALTER TABLE `task_instance` 
ADD COLUMN `execution_id` varchar(100) DEFAULT NULL COMMENT '执行实例ID（UUID）' AFTER `id`,
ADD COLUMN `orchestration_mode` varchar(20) DEFAULT 'EXECUTOR' COMMENT '编排模式' AFTER `execution_id`,
ADD COLUMN `progress` decimal(5,2) DEFAULT '0.00' COMMENT '执行进度（百分比）' AFTER `orchestration_mode`,
ADD COLUMN `completed_nodes` int(11) DEFAULT '0' COMMENT '已完成节点数' AFTER `progress`,
ADD COLUMN `total_nodes` int(11) DEFAULT '0' COMMENT '总节点数' AFTER `completed_nodes`,
ADD COLUMN `running_nodes` text COMMENT '运行中的节点列表（JSON）' AFTER `total_nodes`,
ADD COLUMN `failed_nodes` text COMMENT '失败的节点列表（JSON）' AFTER `running_nodes`,
ADD UNIQUE KEY `uk_execution_id` (`execution_id`),
ADD INDEX `idx_orchestration_mode` (`orchestration_mode`);

-- 3. node_instance表添加执行状态字段
ALTER TABLE `node_instance` 
ADD COLUMN `execution_id` varchar(100) DEFAULT NULL COMMENT '工作流执行ID' AFTER `task_instance_id`,
ADD COLUMN `job_id` int(11) DEFAULT NULL COMMENT 'XXL-JOB任务ID' AFTER `node_type`,
ADD COLUMN `job_log_id` bigint(20) DEFAULT NULL COMMENT 'XXL-JOB日志ID' AFTER `job_id`,
ADD COLUMN `executor_address` varchar(255) DEFAULT NULL COMMENT '执行器地址' AFTER `job_log_id`,
ADD COLUMN `input_params` text COMMENT '输入参数JSON' AFTER `executor_address`,
ADD COLUMN `output_params` text COMMENT '输出参数JSON' AFTER `input_params`,
ADD COLUMN `callback_url` varchar(500) DEFAULT NULL COMMENT '回调URL' AFTER `output_params`,
ADD INDEX `idx_execution_id` (`execution_id`),
ADD INDEX `idx_job_id` (`job_id`),
ADD INDEX `idx_job_log_id` (`job_log_id`);

-- 4. job_info表添加工作流关联字段
ALTER TABLE `job_info` 
ADD COLUMN `workflow_id` bigint(20) DEFAULT NULL COMMENT '关联的工作流ID' AFTER `job_json`,
ADD COLUMN `node_code` varchar(50) DEFAULT NULL COMMENT '关联的节点编码' AFTER `workflow_id`,
ADD COLUMN `orchestration_mode` varchar(20) DEFAULT 'EXECUTOR' COMMENT '编排模式' AFTER `node_code`,
ADD INDEX `idx_workflow_id` (`workflow_id`),
ADD INDEX `idx_node_code` (`node_code`);
```

### 1.2 新增工作流调度配置表

```sql
-- 工作流调度配置表
CREATE TABLE `workflow_schedule_config` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '配置ID',
  `workflow_id` bigint(20) NOT NULL COMMENT '工作流ID',
  `orchestration_mode` varchar(20) NOT NULL DEFAULT 'CENTRAL' COMMENT '编排模式',
  `schedule_enabled` tinyint(4) NOT NULL DEFAULT '1' COMMENT '调度是否启用',
  `cron_expression` varchar(128) DEFAULT NULL COMMENT 'Cron表达式',
  `timezone` varchar(50) DEFAULT 'Asia/Shanghai' COMMENT '时区',
  `last_trigger_time` datetime DEFAULT NULL COMMENT '上次触发时间',
  `next_trigger_time` datetime DEFAULT NULL COMMENT '下次触发时间',
  `trigger_count` bigint(20) DEFAULT '0' COMMENT '触发次数',
  `success_count` bigint(20) DEFAULT '0' COMMENT '成功次数',
  `failure_count` bigint(20) DEFAULT '0' COMMENT '失败次数',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `create_user` varchar(50) NOT NULL COMMENT '创建人',
  `update_user` varchar(50) NOT NULL COMMENT '更新人',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_workflow_id` (`workflow_id`),
  INDEX `idx_schedule_enabled` (`schedule_enabled`),
  INDEX `idx_next_trigger_time` (`next_trigger_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='工作流调度配置表';
```

### 1.3 数据迁移脚本

```sql
-- 为现有工作流设置默认编排模式
UPDATE `workflow` SET `orchestration_mode` = 'EXECUTOR' WHERE `orchestration_mode` IS NULL;

-- 为现有task_instance设置默认编排模式
UPDATE `task_instance` SET `orchestration_mode` = 'EXECUTOR' WHERE `orchestration_mode` IS NULL;

-- 为现有job_info记录关联工作流信息
UPDATE `job_info` j 
INNER JOIN `workflow` w ON j.id = w.job_id 
SET j.workflow_id = w.id, j.orchestration_mode = w.orchestration_mode
WHERE j.workflow_id IS NULL;
```

---

## 🏗️ 阶段二：调度中心编排引擎开发

### 2.1 核心服务类

#### CentralWorkflowOrchestrationService.java
```java
@Service
@Slf4j
public class CentralWorkflowOrchestrationService {
    
    @Autowired
    private WorkflowDao workflowDao;
    
    @Autowired
    private WorkflowNodeDao workflowNodeDao;
    
    @Autowired
    private WorkflowInstanceDao workflowInstanceDao;
    
    @Autowired
    private NodeInstanceDao nodeInstanceDao;
    
    @Autowired
    private NodeTaskExecutionService nodeTaskService;
    
    /**
     * 执行工作流 - 调度中心编排模式
     */
    public ReturnT<String> executeWorkflow(Long workflowId, String executeUser) {
        try {
            log.info("调度中心开始编排执行工作流: workflowId={}, executeUser={}", workflowId, executeUser);
            
            // 1. 创建工作流执行实例
            WorkflowExecution execution = createWorkflowExecution(workflowId, executeUser);
            
            // 2. 解析工作流节点和依赖关系
            List<WorkflowNode> nodes = workflowNodeDao.listByWorkflow(workflowId);
            Map<String, List<String>> dependencies = parseDependencies(workflowId);
            
            // 3. 初始化执行状态
            execution.initializeNodeStates(nodes, dependencies);
            
            // 4. 启动入口节点
            List<WorkflowNode> entryNodes = findEntryNodes(nodes, dependencies);
            for (WorkflowNode node : entryNodes) {
                dispatchNodeToExecutor(execution, node);
            }
            
            return new ReturnT<>("工作流执行已启动，执行ID: " + execution.getExecutionId());
            
        } catch (Exception e) {
            log.error("工作流执行失败: workflowId={}", workflowId, e);
            return new ReturnT<>(ReturnT.FAIL_CODE, "执行失败: " + e.getMessage());
        }
    }
    
    /**
     * 处理节点执行完成回调
     */
    public ReturnT<String> handleNodeComplete(NodeExecutionResult result) {
        try {
            log.info("收到节点执行完成回调: executionId={}, nodeCode={}, success={}", 
                    result.getExecutionId(), result.getNodeCode(), result.isSuccess());
            
            WorkflowExecution execution = getWorkflowExecution(result.getExecutionId());
            if (execution == null) {
                return new ReturnT<>(ReturnT.FAIL_CODE, "工作流执行实例不存在");
            }
            
            if (result.isSuccess()) {
                // 节点执行成功，检查并启动下游节点
                execution.updateNodeState(result.getNodeCode(), NodeExecutionState.SUCCESS);
                
                List<WorkflowNode> readyNodes = findReadyNodes(execution);
                for (WorkflowNode node : readyNodes) {
                    dispatchNodeToExecutor(execution, node);
                }
                
                // 检查工作流是否完成
                if (execution.isAllNodesCompleted()) {
                    completeWorkflowExecution(execution);
                }
                
            } else {
                // 节点执行失败
                execution.updateNodeState(result.getNodeCode(), NodeExecutionState.FAILED);
                handleNodeFailure(execution, result.getNodeCode(), result.getErrorMessage());
            }
            
            return ReturnT.SUCCESS;
            
        } catch (Exception e) {
            log.error("处理节点完成回调失败", e);
            return new ReturnT<>(ReturnT.FAIL_CODE, "处理回调失败: " + e.getMessage());
        }
    }
}
```

### 2.2 工作流调度器

#### CentralWorkflowScheduler.java
```java
@Component
@EnableScheduling
@Slf4j
public class CentralWorkflowScheduler {
    
    @Autowired
    private WorkflowDao workflowDao;
    
    @Autowired
    private WorkflowScheduleConfigDao scheduleConfigDao;
    
    @Autowired
    private CentralWorkflowOrchestrationService orchestrationService;
    
    private final Map<Long, ScheduledFuture<?>> scheduledTasks = new ConcurrentHashMap<>();
    private final TaskScheduler taskScheduler;
    
    public CentralWorkflowScheduler() {
        ThreadPoolTaskScheduler scheduler = new ThreadPoolTaskScheduler();
        scheduler.setPoolSize(20);
        scheduler.setThreadNamePrefix("central-workflow-scheduler-");
        scheduler.setWaitForTasksToCompleteOnShutdown(true);
        scheduler.setAwaitTerminationSeconds(60);
        scheduler.initialize();
        this.taskScheduler = scheduler;
    }
    
    /**
     * 系统启动时加载所有调度中心编排的工作流
     */
    @PostConstruct
    public void initializeScheduledWorkflows() {
        try {
            log.info("初始化调度中心工作流调度任务...");
            
            List<WorkflowScheduleConfig> configs = scheduleConfigDao.findEnabledCentralConfigs();
            
            for (WorkflowScheduleConfig config : configs) {
                scheduleWorkflow(config);
            }
            
            log.info("调度中心工作流调度任务初始化完成，共加载{}个任务", configs.size());
            
        } catch (Exception e) {
            log.error("初始化调度中心工作流调度任务失败", e);
        }
    }
    
    /**
     * 为工作流创建定时调度
     */
    public void scheduleWorkflow(WorkflowScheduleConfig config) {
        try {
            if (StringUtils.isEmpty(config.getCronExpression())) {
                log.warn("工作流{}没有配置Cron表达式，跳过调度", config.getWorkflowId());
                return;
            }
            
            // 先取消现有调度
            unscheduleWorkflow(config.getWorkflowId());
            
            // 创建Cron触发器
            CronTrigger cronTrigger = new CronTrigger(config.getCronExpression());
            
            // 创建调度任务
            Runnable task = () -> {
                try {
                    log.info("定时触发工作流执行: workflowId={}", config.getWorkflowId());
                    
                    // 检查工作流状态
                    Workflow workflow = workflowDao.load(config.getWorkflowId());
                    if (workflow == null || !workflow.isOnline()) {
                        log.warn("工作流{}状态已变更，停止调度", config.getWorkflowId());
                        unscheduleWorkflow(config.getWorkflowId());
                        return;
                    }
                    
                    // 调用调度中心编排执行工作流
                    ReturnT<String> result = orchestrationService.executeWorkflow(
                            config.getWorkflowId(), "central-scheduler");
                    
                    // 更新调度统计
                    updateScheduleStats(config, result.getCode() == ReturnT.SUCCESS_CODE);
                    
                } catch (Exception e) {
                    log.error("定时工作流执行异常: workflowId={}", config.getWorkflowId(), e);
                    updateScheduleStats(config, false);
                }
            };
            
            // 调度任务
            ScheduledFuture<?> future = taskScheduler.schedule(task, cronTrigger);
            scheduledTasks.put(config.getWorkflowId(), future);
            
            log.info("工作流定时调度已启动: workflowId={}, cron={}", 
                    config.getWorkflowId(), config.getCronExpression());
            
        } catch (Exception e) {
            log.error("创建工作流定时调度失败: workflowId={}", config.getWorkflowId(), e);
        }
    }
}
```

### 2.3 节点任务执行服务

#### NodeTaskExecutionService.java
```java
@Service
@Slf4j
public class NodeTaskExecutionService {
    
    @Autowired
    private JobInfoDao jobInfoDao;
    
    @Autowired
    private WorkflowNodeDao workflowNodeDao;
    
    /**
     * 为节点创建或获取XXL-JOB任务
     */
    public Integer getOrCreateNodeTask(WorkflowNode node) {
        if (node.getJobId() != null && node.getJobId() > 0) {
            return node.getJobId().intValue();
        }
        
        // 动态创建节点执行任务
        JobInfo nodeJob = new JobInfo();
        nodeJob.setJobGroup(getNodeExecutorGroupId());
        nodeJob.setJobDesc("节点任务: " + node.getNodeName());
        nodeJob.setExecutorHandler("centralNodeExecutor"); // 统一的节点执行Handler
        nodeJob.setExecutorRouteStrategy(selectRouteStrategy(node));
        nodeJob.setExecutorBlockStrategy(ExecutorBlockStrategyEnum.SERIAL_EXECUTION.name());
        nodeJob.setExecutorTimeout(node.getTimeout() != null ? node.getTimeout() : 300);
        nodeJob.setExecutorFailRetryCount(node.getRetryTimes() != null ? node.getRetryTimes() : 0);
        nodeJob.setGlueType(GlueTypeEnum.BEAN.name());
        nodeJob.setTriggerStatus(0); // 暂停状态，只用于手动触发
        nodeJob.setWorkflowId(node.getWorkflowId());
        nodeJob.setNodeCode(node.getNodeCode());
        nodeJob.setOrchestrationMode("CENTRAL");
        nodeJob.setAddTime(new Date());
        nodeJob.setUpdateTime(new Date());
        nodeJob.setAuthor("system");
        
        int result = jobInfoDao.save(nodeJob);
        if (result > 0) {
            // 更新节点的jobId
            workflowNodeDao.updateJobId(node.getId(), Long.valueOf(nodeJob.getId()));
            return nodeJob.getId();
        } else {
            throw new RuntimeException("创建节点执行任务失败");
        }
    }
    
    /**
     * 触发节点执行
     */
    public void executeNodeTask(Integer jobId, NodeExecutionParam param) {
        String paramJson = JSON.toJSONString(param);
        
        // 使用XXL-JOB的触发机制
        JobTriggerPoolHelper.trigger(
            jobId,                           // 任务ID
            TriggerTypeEnum.MANUAL,          // 手动触发
            -1,                              // 使用任务配置的重试次数
            null,                            // 不使用分片
            paramJson,                       // 节点执行参数
            null                             // 使用默认执行器地址
        );
        
        log.info("节点任务触发成功: jobId={}, nodeCode={}", jobId, param.getNodeCode());
    }
}
```

---

## 🔧 阶段三：执行器节点处理器改造

### 3.1 统一节点执行器Handler

#### CentralNodeExecutorHandler.java
```java
@Component
@Slf4j
public class CentralNodeExecutorHandler {

    @Autowired
    private SqlNodeExecutor sqlNodeExecutor;

    @Autowired
    private ShellNodeExecutor shellNodeExecutor;

    @Autowired
    private PythonNodeExecutor pythonNodeExecutor;

    @Autowired
    private DataxNodeExecutor dataxNodeExecutor;

    @XxlJob("centralNodeExecutor")
    public void executeNode() throws Exception {
        String param = XxlJobHelper.getJobParam();
        log.info("调度中心节点执行器收到任务参数: {}", param);

        NodeExecutionResult result = null;
        try {
            // 解析节点执行参数
            NodeExecutionParam nodeParam = JSON.parseObject(param, NodeExecutionParam.class);

            // 记录节点开始执行
            XxlJobHelper.log("开始执行节点: " + nodeParam.getNodeCode());

            // 根据节点类型选择具体的执行器
            NodeExecutor executor = getNodeExecutor(nodeParam.getNodeType());

            // 执行节点
            result = executor.execute(nodeParam);

            // 记录执行结果
            if (result.isSuccess()) {
                XxlJobHelper.log("节点执行成功: " + nodeParam.getNodeCode());
                XxlJobHelper.handleSuccess("节点执行成功");
            } else {
                XxlJobHelper.log("节点执行失败: " + result.getErrorMessage());
                XxlJobHelper.handleFail("节点执行失败: " + result.getErrorMessage());
            }

        } catch (Exception e) {
            log.error("节点执行异常", e);
            XxlJobHelper.log("节点执行异常: " + e.getMessage());

            // 构建失败结果
            result = buildFailResult(param, e.getMessage());
            XxlJobHelper.handleFail("节点执行异常: " + e.getMessage());
        } finally {
            // 无论成功失败都要回调调度中心
            if (result != null) {
                callbackScheduleCenter(result);
            }
        }
    }

    /**
     * 根据节点类型获取执行器
     */
    private NodeExecutor getNodeExecutor(String nodeType) {
        return switch (nodeType) {
            case "SQL" -> sqlNodeExecutor;
            case "SHELL" -> shellNodeExecutor;
            case "PYTHON" -> pythonNodeExecutor;
            case "DATAX" -> dataxNodeExecutor;
            default -> throw new IllegalArgumentException("不支持的节点类型: " + nodeType);
        };
    }

    /**
     * 回调调度中心
     */
    private void callbackScheduleCenter(NodeExecutionResult result) {
        try {
            String callbackUrl = result.getCallbackUrl();
            if (StringUtils.hasText(callbackUrl)) {
                RestTemplate restTemplate = new RestTemplate();
                ResponseEntity<String> response = restTemplate.postForEntity(
                    callbackUrl, result, String.class);

                if (response.getStatusCode().is2xxSuccessful()) {
                    log.info("节点执行结果回调成功: nodeCode={}", result.getNodeCode());
                } else {
                    log.warn("节点执行结果回调失败: nodeCode={}, status={}",
                            result.getNodeCode(), response.getStatusCode());
                }
            }
        } catch (Exception e) {
            log.error("回调调度中心失败: nodeCode={}", result.getNodeCode(), e);
        }
    }
}
```

### 3.2 节点执行参数和结果类

#### NodeExecutionParam.java
```java
@Data
public class NodeExecutionParam {
    private String executionId;          // 工作流执行ID
    private Long workflowId;             // 工作流ID
    private String nodeCode;             // 节点编码
    private String nodeName;             // 节点名称
    private String nodeType;             // 节点类型
    private String nodeConfig;           // 节点配置JSON
    private Map<String, Object> globalParams;  // 全局参数
    private Map<String, Object> inputParams;   // 输入参数
    private String callbackUrl;          // 回调URL
    private Long timeout;                // 超时时间
    private Integer retryTimes;          // 重试次数
}
```

#### NodeExecutionResult.java
```java
@Data
public class NodeExecutionResult {
    private String executionId;          // 工作流执行ID
    private String nodeCode;             // 节点编码
    private boolean success;             // 是否成功
    private String errorMessage;         // 错误信息
    private Map<String, Object> outputParams;  // 输出参数
    private Long startTime;              // 开始时间
    private Long endTime;                // 结束时间
    private Long duration;               // 执行时长
    private String executorAddress;      // 执行器地址
    private String callbackUrl;          // 回调URL
    private String logContent;           // 执行日志
}
```

---

## 🖥️ 阶段四：前端界面适配

### 4.1 工作流管理界面调整

#### 工作流列表页面添加编排模式显示
```typescript
// src/views/workflow/WorkflowList.vue
interface Workflow {
  id: number
  name: string
  status: number
  orchestrationMode: 'CENTRAL' | 'EXECUTOR'  // 新增字段
  lastExecutionId?: string                   // 新增字段
  executionCount: number                     // 新增字段
  successCount: number                       // 新增字段
  failureCount: number                       // 新增字段
}

// 表格列配置
const columns = [
  { prop: 'name', label: '工作流名称' },
  { prop: 'orchestrationMode', label: '编排模式', formatter: formatOrchestrationMode },
  { prop: 'status', label: '状态' },
  { prop: 'executionCount', label: '执行次数' },
  { prop: 'successCount', label: '成功次数' },
  { prop: 'failureCount', label: '失败次数' }
]

function formatOrchestrationMode(row: Workflow) {
  return row.orchestrationMode === 'CENTRAL' ? '调度中心编排' : '执行器编排'
}
```

### 4.2 工作流执行监控页面

#### 实时监控组件
```typescript
// src/views/workflow/WorkflowMonitor.vue
interface WorkflowExecution {
  executionId: string
  workflowId: number
  workflowName: string
  orchestrationMode: string
  state: number
  progress: number
  completedNodes: number
  totalNodes: number
  runningNodes: string[]
  failedNodes: string[]
  startTime: string
  duration?: number
}

interface NodeExecution {
  nodeCode: string
  nodeName: string
  nodeType: string
  state: number
  startTime?: string
  endTime?: string
  duration?: number
  executorAddress?: string
  errorMessage?: string
}

// WebSocket连接实时监控
const ws = new WebSocket(`ws://localhost:8080/ws/workflow-monitor/${executionId}`)
ws.onmessage = (event) => {
  const data = JSON.parse(event.data)
  if (data.type === 'WORKFLOW_STATUS') {
    updateWorkflowStatus(data.payload)
  } else if (data.type === 'NODE_STATUS') {
    updateNodeStatus(data.payload)
  }
}
```

### 4.3 API接口调整

#### 工作流执行API
```typescript
// src/api/workflow.ts
export const workflowApi = {
  // 执行工作流（调度中心编排）
  executeCentral(workflowId: number): Promise<ApiResponse<string>> {
    return request.post(`/workflow/execute/central/${workflowId}`)
  },

  // 获取工作流执行状态
  getExecutionStatus(executionId: string): Promise<ApiResponse<WorkflowExecution>> {
    return request.get(`/workflow/execution/${executionId}`)
  },

  // 获取节点执行状态列表
  getNodeExecutions(executionId: string): Promise<ApiResponse<NodeExecution[]>> {
    return request.get(`/workflow/execution/${executionId}/nodes`)
  },

  // 重试失败节点
  retryFailedNode(executionId: string, nodeCode: string): Promise<ApiResponse<string>> {
    return request.post(`/workflow/execution/${executionId}/node/${nodeCode}/retry`)
  },

  // 终止工作流执行
  terminateExecution(executionId: string): Promise<ApiResponse<string>> {
    return request.post(`/workflow/execution/${executionId}/terminate`)
  }
}
```

---

## 🧪 阶段五：测试和上线

### 5.1 单元测试

#### 调度中心编排服务测试
```java
@SpringBootTest
@Transactional
class CentralWorkflowOrchestrationServiceTest {

    @Autowired
    private CentralWorkflowOrchestrationService orchestrationService;

    @Test
    void testExecuteWorkflow() {
        // 测试工作流执行
        Long workflowId = 1L;
        String executeUser = "test";

        ReturnT<String> result = orchestrationService.executeWorkflow(workflowId, executeUser);

        assertThat(result.getCode()).isEqualTo(ReturnT.SUCCESS_CODE);
        assertThat(result.getContent()).contains("执行ID:");
    }

    @Test
    void testHandleNodeComplete() {
        // 测试节点完成回调
        NodeExecutionResult result = new NodeExecutionResult();
        result.setExecutionId("test-execution-id");
        result.setNodeCode("node1");
        result.setSuccess(true);

        ReturnT<String> response = orchestrationService.handleNodeComplete(result);

        assertThat(response.getCode()).isEqualTo(ReturnT.SUCCESS_CODE);
    }
}
```

### 5.2 集成测试

#### 端到端工作流测试
```java
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
class WorkflowIntegrationTest {

    @Test
    void testCompleteWorkflowExecution() {
        // 1. 创建测试工作流
        // 2. 设置为调度中心编排模式
        // 3. 执行工作流
        // 4. 验证节点按依赖顺序执行
        // 5. 验证工作流最终状态
    }
}
```

### 5.3 性能测试

#### 并发执行测试
- 测试同时执行多个工作流的性能
- 测试大量节点工作流的执行效率
- 测试调度中心的并发处理能力

### 5.4 上线计划

#### 灰度发布策略
1. **第一阶段**：新建工作流使用调度中心编排
2. **第二阶段**：将部分现有工作流迁移到调度中心编排
3. **第三阶段**：全量迁移到调度中心编排

#### 回滚方案
- 保留执行器编排代码，支持快速回滚
- 数据库表结构向后兼容
- 配置开关控制编排模式

---

## 📊 预期收益

### 功能收益
- ✅ **细粒度监控**：节点级别的实时状态监控
- ✅ **故障恢复**：支持节点级别的重试和故障转移
- ✅ **负载均衡**：节点可分发到不同执行器
- ✅ **统一管控**：工作流执行完全可控

### 性能收益
- ✅ **资源优化**：更好的执行器资源利用
- ✅ **并发提升**：支持更大规模的并发执行
- ✅ **扩展性**：支持动态扩缩容

### 运维收益
- ✅ **可观测性**：完整的执行链路追踪
- ✅ **告警精准**：节点级别的告警通知
- ✅ **问题定位**：快速定位执行问题

---

## 📝 注意事项

1. **数据一致性**：确保工作流执行状态的数据一致性
2. **性能影响**：调度中心需要处理更多的状态管理
3. **网络依赖**：增加了调度中心与执行器的网络通信
4. **向后兼容**：保持现有功能的完全兼容
5. **监控告警**：完善的监控和告警机制

---

## 🎯 总结

本实施方案将现有的执行器编排模式升级为调度中心编排模式，在保持系统稳定性的前提下，大幅提升了工作流的可观测性、可控性和扩展性。通过分阶段实施，可以平滑地完成系统升级，为后续的功能扩展奠定坚实基础。
```
