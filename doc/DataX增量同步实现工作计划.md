# DataX增量同步实现工作计划

## 📋 项目概述

基于现有的增量同步设计方案，当前前端配置界面已基本完成（90%），但后端核心逻辑完全缺失。本文档详细规划了完整实现增量同步功能所需的所有工作任务。

## 🎯 当前状态分析

### ✅ 已完成功能
- **前端配置界面**：DataX节点增量同步配置界面（90%）
- **配置数据结构**：前端增量配置数据模型（100%）
- **工作流增量保存**：工作流设计器增量保存功能（100%）

### ❌ 缺失功能
- **数据库表结构**：增量状态管理表（0%）
- **后端服务层**：增量状态管理服务（0%）
- **配置生成器**：DataX增量配置生成（0%）
- **执行器支持**：执行器增量处理逻辑（0%）
- **监控告警**：增量同步监控（0%）

## 🚀 实施计划

### 阶段一：基础设施建设（高优先级）

#### 1.1 数据库表结构创建
**工作量**：1天  
**负责人**：后端开发  
**任务描述**：
- 创建增量同步状态表 `datax_sync_state`
- 创建增量同步日志表 `datax_sync_log`
- 添加必要的索引和约束

**具体任务**：
```sql
-- 1. 创建增量同步状态表
CREATE TABLE `datax_sync_state` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `node_id` bigint(20) NOT NULL COMMENT '节点ID',
  `workflow_id` bigint(20) NOT NULL COMMENT '工作流ID',
  `source_datasource_id` bigint(20) NOT NULL COMMENT '源数据源ID',
  `source_table` varchar(100) NOT NULL COMMENT '源表名',
  `target_datasource_id` bigint(20) NOT NULL COMMENT '目标数据源ID', 
  `target_table` varchar(100) NOT NULL COMMENT '目标表名',
  `incremental_column` varchar(100) NOT NULL COMMENT '增量字段名',
  `incremental_type` varchar(20) NOT NULL DEFAULT 'TIMESTAMP' COMMENT '增量类型',
  `last_sync_value` varchar(100) COMMENT '上次同步的最大值',
  `last_sync_time` datetime COMMENT '上次同步时间',
  `sync_count` bigint(20) DEFAULT 0 COMMENT '同步次数',
  `status` varchar(20) DEFAULT 'ACTIVE' COMMENT '状态：ACTIVE,INACTIVE',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `create_user` varchar(50) NOT NULL,
  `update_user` varchar(50) NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_node_tables` (`node_id`, `source_table`, `target_table`),
  KEY `idx_workflow_id` (`workflow_id`),
  KEY `idx_source_datasource` (`source_datasource_id`),
  KEY `idx_target_datasource` (`target_datasource_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='DataX增量同步状态表';

-- 2. 创建增量同步日志表
CREATE TABLE `datax_sync_log` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `sync_state_id` bigint(20) NOT NULL COMMENT '同步状态ID',
  `execution_id` varchar(100) COMMENT '执行ID',
  `start_time` datetime NOT NULL COMMENT '开始时间',
  `end_time` datetime COMMENT '结束时间',
  `status` varchar(20) NOT NULL COMMENT '状态：RUNNING,SUCCESS,FAILED',
  `sync_records` bigint(20) DEFAULT 0 COMMENT '同步记录数',
  `error_message` text COMMENT '错误信息',
  `incremental_value_start` varchar(100) COMMENT '增量起始值',
  `incremental_value_end` varchar(100) COMMENT '增量结束值',
  PRIMARY KEY (`id`),
  KEY `idx_sync_state_id` (`sync_state_id`),
  KEY `idx_execution_id` (`execution_id`),
  KEY `idx_start_time` (`start_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='DataX增量同步日志表';
```

#### 1.2 实体类和DAO层开发
**工作量**：1天  
**负责人**：后端开发  
**任务描述**：
- 创建 `DataxSyncState` 实体类
- 创建 `DataxSyncLog` 实体类
- 创建对应的DAO接口和XML映射文件

**具体任务**：
- [ ] 创建 `DataxSyncState.java` 实体类
- [ ] 创建 `DataxSyncLog.java` 实体类
- [ ] 创建 `DataxSyncStateDao.java` 接口
- [ ] 创建 `DataxSyncLogDao.java` 接口
- [ ] 创建 `DataxSyncStateDao.xml` 映射文件
- [ ] 创建 `DataxSyncLogDao.xml` 映射文件

#### 1.3 基础Service层实现
**工作量**：2天  
**负责人**：后端开发  
**任务描述**：
- 实现 `DataxSyncStateService` 服务
- 实现 `DataxSyncLogService` 服务
- 提供基础的CRUD操作

**具体任务**：
- [ ] 创建 `DataxSyncStateService` 接口
- [ ] 实现 `DataxSyncStateServiceImpl` 类
- [ ] 创建 `DataxSyncLogService` 接口
- [ ] 实现 `DataxSyncLogServiceImpl` 类
- [ ] 实现状态查询和更新方法
- [ ] 实现日志记录方法

### 阶段二：核心功能实现（高优先级）

#### 2.1 增量状态管理服务
**工作量**：3天  
**负责人**：后端开发  
**任务描述**：
- 实现增量状态的获取和更新逻辑
- 实现上次同步值的管理
- 实现源表最大值查询功能

**具体任务**：
- [ ] 实现 `getLastSyncValue()` 方法
- [ ] 实现 `updateSyncState()` 方法
- [ ] 实现 `getMaxIncrementalValue()` 方法
- [ ] 实现 `initializeSyncState()` 方法
- [ ] 实现 `cleanupHistoryStates()` 方法
- [ ] 添加事务管理和异常处理

#### 2.2 DataX配置生成器增强
**工作量**：3天  
**负责人**：后端开发  
**任务描述**：
- 增强现有的DataX配置生成器
- 实现增量配置的解析和处理
- 实现变量替换机制

**具体任务**：
- [ ] 创建 `DataxConfigGenerator` 组件
- [ ] 实现 `generateIncrementalDataxConfig()` 方法
- [ ] 实现 `resolveIncrementalValue()` 变量解析
- [ ] 实现 `buildIncrementalWhere()` WHERE条件构建
- [ ] 实现 `combineWhereConditions()` 条件合并
- [ ] 支持多种增量类型（时间戳、主键等）

#### 2.3 变量替换机制
**工作量**：2天  
**负责人**：后端开发  
**任务描述**：
- 实现动态变量替换功能
- 支持 `${lastSyncTime}`、`${currentTime}` 等变量
- 实现变量验证和错误处理

**具体任务**：
- [ ] 创建 `VariableResolver` 工具类
- [ ] 实现时间变量解析
- [ ] 实现自定义变量支持
- [ ] 添加变量格式验证
- [ ] 实现变量替换的单元测试

### 阶段三：执行器集成（高优先级）

#### 3.1 执行器增量支持
**工作量**：4天  
**负责人**：执行器开发  
**任务描述**：
- 在执行器中添加增量同步处理逻辑
- 实现增量配置的解析和应用
- 实现同步状态的回调更新

**具体任务**：
- [ ] 修改DataX节点执行器
- [ ] 实现增量配置解析
- [ ] 实现DataX配置动态生成
- [ ] 实现同步前状态检查
- [ ] 实现同步后状态更新
- [ ] 实现异常情况处理

#### 3.2 回调机制实现
**工作量**：2天  
**负责人**：后端开发  
**任务描述**：
- 实现执行器到调度中心的状态回调
- 更新增量同步状态和日志
- 处理同步成功和失败的不同情况

**具体任务**：
- [ ] 创建增量同步回调接口
- [ ] 实现状态更新回调处理
- [ ] 实现日志记录回调处理
- [ ] 添加回调失败重试机制
- [ ] 实现回调数据验证

### 阶段四：监控和管理功能（中优先级）

#### 4.1 增量状态监控
**工作量**：3天  
**负责人**：前后端开发  
**任务描述**：
- 实现增量同步状态的监控界面
- 提供同步历史查询功能
- 实现异常告警机制

**具体任务**：
- [ ] 创建增量状态查询接口
- [ ] 实现前端监控界面
- [ ] 实现同步历史展示
- [ ] 集成告警系统
- [ ] 实现状态统计功能

#### 4.2 管理界面增强
**工作量**：2天  
**负责人**：前端开发  
**任务描述**：
- 增强前端配置界面
- 添加变量提示和验证
- 实现配置预览功能

**具体任务**：
- [ ] 添加变量输入提示
- [ ] 实现配置验证增强
- [ ] 添加增量字段类型检查
- [ ] 实现配置预览功能
- [ ] 优化用户体验

### 阶段五：测试和优化（中优先级）

#### 5.1 功能测试
**工作量**：3天  
**负责人**：测试工程师  
**任务描述**：
- 编写增量同步功能测试用例
- 执行完整的功能测试
- 验证各种场景下的正确性

**具体任务**：
- [ ] 编写单元测试用例
- [ ] 编写集成测试用例
- [ ] 执行时间戳增量测试
- [ ] 执行主键增量测试
- [ ] 执行异常场景测试
- [ ] 性能测试和优化

#### 5.2 文档完善
**工作量**：1天  
**负责人**：技术文档  
**任务描述**：
- 更新用户使用文档
- 编写运维部署文档
- 完善API接口文档

**具体任务**：
- [ ] 更新用户操作手册
- [ ] 编写部署升级指南
- [ ] 完善API接口文档
- [ ] 编写故障排查手册

## 📅 时间计划

| 阶段 | 工作量 | 开始时间 | 结束时间 | 里程碑 |
|------|--------|----------|----------|--------|
| 阶段一：基础设施 | 4天 | 第1周 | 第1周 | 数据库和基础服务完成 |
| 阶段二：核心功能 | 8天 | 第2周 | 第3周 | 增量逻辑实现完成 |
| 阶段三：执行器集成 | 6天 | 第3周 | 第4周 | 端到端功能打通 |
| 阶段四：监控管理 | 5天 | 第4周 | 第5周 | 监控和管理功能完成 |
| 阶段五：测试优化 | 4天 | 第5周 | 第6周 | 功能测试和上线准备 |

**总工作量**：27天  
**预计完成时间**：6周

## 🎯 验收标准

### 功能验收
- [ ] 支持时间戳和主键两种增量策略
- [ ] 支持变量替换（${lastSyncTime}等）
- [ ] 增量状态正确管理和持久化
- [ ] 同步失败后能够断点续传
- [ ] 提供完整的监控和日志功能

### 性能验收
- [ ] 增量同步性能比全量同步提升50%以上
- [ ] 支持千万级数据表的增量同步
- [ ] 状态查询响应时间<100ms
- [ ] 支持并发增量同步任务

### 稳定性验收
- [ ] 连续运行7天无异常
- [ ] 异常恢复机制正常工作
- [ ] 数据一致性验证通过
- [ ] 内存和CPU使用稳定

## ⚠️ 风险评估

### 技术风险
- **数据一致性风险**：增量同步可能导致数据不一致
- **性能风险**：大表增量同步可能影响源数据库性能
- **兼容性风险**：不同数据库的增量实现差异

### 业务风险
- **数据丢失风险**：同步失败可能导致数据丢失
- **服务中断风险**：增量同步异常可能影响业务流程

### 缓解措施
- 完善的测试验证
- 实时监控告警
- 数据备份机制
- 快速回滚方案

## 📞 联系信息

**项目负责人**：[待指定]  
**技术负责人**：[待指定]  
**测试负责人**：[待指定]

## 🔧 技术实现细节

### 核心类设计

#### DataxSyncState 实体类
```java
@Data
@TableName("datax_sync_state")
public class DataxSyncState {
    private Long id;
    private Long nodeId;
    private Long workflowId;
    private Long sourceDatasourceId;
    private String sourceTable;
    private Long targetDatasourceId;
    private String targetTable;
    private String incrementalColumn;
    private String incrementalType;
    private String lastSyncValue;
    private LocalDateTime lastSyncTime;
    private Long syncCount;
    private String status;
    // ... 其他字段
}
```

#### DataxSyncStateService 接口
```java
public interface DataxSyncStateService {
    /**
     * 获取上次同步值
     */
    String getLastSyncValue(Long nodeId, String sourceTable, String targetTable);

    /**
     * 更新同步状态
     */
    void updateSyncState(Long nodeId, String sourceTable, String targetTable,
                        String newSyncValue, Long syncRecords);

    /**
     * 获取源表最大增量值
     */
    String getMaxIncrementalValue(Long datasourceId, String tableName,
                                String incrementalColumn, String incrementalType);

    /**
     * 初始化同步状态
     */
    void initializeSyncState(WorkflowNode node, Map<String, Object> syncConfig);
}
```

#### DataxConfigGenerator 配置生成器
```java
@Component
public class DataxConfigGenerator {

    @Autowired
    private DataxSyncStateService syncStateService;

    /**
     * 生成增量DataX配置
     */
    public String generateIncrementalDataxConfig(WorkflowNode node) {
        Map<String, Object> nodeConfig = parseNodeConfig(node.getConfigParams());
        Map<String, Object> syncStrategy = (Map<String, Object>) nodeConfig.get("syncStrategy");

        if ("INCREMENTAL".equals(syncStrategy.get("type"))) {
            return buildIncrementalConfig(node, nodeConfig, syncStrategy);
        } else {
            return buildFullConfig(nodeConfig);
        }
    }

    private String buildIncrementalConfig(WorkflowNode node, Map<String, Object> nodeConfig,
                                        Map<String, Object> syncStrategy) {
        // 获取增量配置
        String incrementalColumn = (String) syncStrategy.get("incrementalColumn");
        String incrementalValue = (String) syncStrategy.get("incrementalValue");

        // 解析变量
        String resolvedValue = resolveVariables(node, incrementalValue);

        // 构建WHERE条件
        String whereCondition = buildIncrementalWhere(incrementalColumn, resolvedValue);

        // 生成DataX配置JSON
        return generateDataxJson(nodeConfig, whereCondition);
    }
}
```

### 执行器集成方案

#### 增量同步执行流程
```java
@Component
public class DataxNodeExecutor {

    public void executeIncrementalSync(NodeExecutionParam param) {
        try {
            // 1. 解析节点配置
            Map<String, Object> config = parseNodeConfig(param.getNodeConfig());

            // 2. 检查是否为增量同步
            if (isIncrementalSync(config)) {
                // 3. 生成增量DataX配置
                String dataxConfig = generateIncrementalConfig(param);

                // 4. 执行DataX任务
                DataxResult result = executeDatax(dataxConfig);

                // 5. 更新同步状态
                updateSyncState(param, result);

                // 6. 回调调度中心
                callbackScheduler(param, result);
            } else {
                // 执行全量同步
                executeFullSync(param);
            }
        } catch (Exception e) {
            handleExecutionError(param, e);
        }
    }
}
```

### 监控界面设计

#### 增量状态监控API
```java
@RestController
@RequestMapping("/api/datax/sync")
public class DataxSyncController {

    @GetMapping("/states")
    public ReturnT<PageResult<DataxSyncState>> getSyncStates(
            @RequestParam(defaultValue = "1") int page,
            @RequestParam(defaultValue = "20") int size,
            @RequestParam(required = false) Long workflowId,
            @RequestParam(required = false) String status) {
        // 分页查询同步状态
    }

    @GetMapping("/logs/{stateId}")
    public ReturnT<List<DataxSyncLog>> getSyncLogs(@PathVariable Long stateId) {
        // 查询同步日志
    }

    @PostMapping("/reset/{stateId}")
    public ReturnT<String> resetSyncState(@PathVariable Long stateId) {
        // 重置同步状态
    }
}
```

#### 前端监控界面
```vue
<template>
  <div class="sync-monitor">
    <!-- 状态统计卡片 -->
    <el-row :gutter="20" class="stats-cards">
      <el-col :span="6">
        <el-card>
          <div class="stat-item">
            <div class="stat-value">{{ stats.totalSyncs }}</div>
            <div class="stat-label">总同步任务</div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card>
          <div class="stat-item">
            <div class="stat-value success">{{ stats.successSyncs }}</div>
            <div class="stat-label">成功同步</div>
          </div>
        </el-card>
      </el-col>
      <!-- 更多统计卡片 -->
    </el-row>

    <!-- 同步状态表格 -->
    <el-card class="sync-table">
      <el-table :data="syncStates" v-loading="loading">
        <el-table-column prop="sourceTable" label="源表" />
        <el-table-column prop="targetTable" label="目标表" />
        <el-table-column prop="lastSyncValue" label="上次同步值" />
        <el-table-column prop="lastSyncTime" label="上次同步时间" />
        <el-table-column prop="status" label="状态">
          <template #default="{ row }">
            <el-tag :type="getStatusType(row.status)">
              {{ getStatusText(row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作">
          <template #default="{ row }">
            <el-button size="small" @click="viewLogs(row)">查看日志</el-button>
            <el-button size="small" type="warning" @click="resetState(row)">重置状态</el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>
  </div>
</template>
```

## 📋 详细任务清单

### 阶段一任务清单
- [ ] **数据库设计**
  - [ ] 创建 `datax_sync_state` 表
  - [ ] 创建 `datax_sync_log` 表
  - [ ] 添加外键约束和索引
  - [ ] 编写数据库升级脚本

- [ ] **实体类开发**
  - [ ] `DataxSyncState.java` 实体类
  - [ ] `DataxSyncLog.java` 实体类
  - [ ] 添加字段验证注解
  - [ ] 编写实体类单元测试

- [ ] **DAO层开发**
  - [ ] `DataxSyncStateDao.java` 接口
  - [ ] `DataxSyncLogDao.java` 接口
  - [ ] `DataxSyncStateDao.xml` 映射文件
  - [ ] `DataxSyncLogDao.xml` 映射文件
  - [ ] DAO层单元测试

### 阶段二任务清单
- [ ] **状态管理服务**
  - [ ] `DataxSyncStateService` 接口定义
  - [ ] `DataxSyncStateServiceImpl` 实现类
  - [ ] 状态初始化逻辑
  - [ ] 状态更新逻辑
  - [ ] 最大值查询逻辑
  - [ ] 服务层单元测试

- [ ] **配置生成器**
  - [ ] `DataxConfigGenerator` 组件
  - [ ] 增量配置解析
  - [ ] WHERE条件构建
  - [ ] DataX JSON生成
  - [ ] 配置验证逻辑
  - [ ] 生成器单元测试

- [ ] **变量解析器**
  - [ ] `VariableResolver` 工具类
  - [ ] 时间变量解析
  - [ ] 自定义变量支持
  - [ ] 变量格式验证
  - [ ] 解析器单元测试

### 阶段三任务清单
- [ ] **执行器增强**
  - [ ] 修改 `DataxNodeExecutor`
  - [ ] 增量配置解析
  - [ ] DataX配置动态生成
  - [ ] 状态回调实现
  - [ ] 异常处理增强
  - [ ] 执行器集成测试

- [ ] **回调机制**
  - [ ] 回调接口定义
  - [ ] 状态更新回调
  - [ ] 日志记录回调
  - [ ] 重试机制实现
  - [ ] 回调单元测试

### 阶段四任务清单
- [ ] **监控接口**
  - [ ] `DataxSyncController` 控制器
  - [ ] 状态查询接口
  - [ ] 日志查询接口
  - [ ] 统计信息接口
  - [ ] 管理操作接口
  - [ ] 接口文档编写

- [ ] **前端监控界面**
  - [ ] 监控页面组件
  - [ ] 状态统计展示
  - [ ] 同步历史查询
  - [ ] 操作按钮实现
  - [ ] 界面响应式适配

### 阶段五任务清单
- [ ] **测试用例**
  - [ ] 单元测试用例
  - [ ] 集成测试用例
  - [ ] 端到端测试用例
  - [ ] 性能测试用例
  - [ ] 异常场景测试

- [ ] **文档完善**
  - [ ] 用户操作手册
  - [ ] 开发者文档
  - [ ] API接口文档
  - [ ] 部署运维文档
  - [ ] 故障排查手册

## 🎯 关键里程碑

### 里程碑1：基础设施完成（第1周末）
- 数据库表结构创建完成
- 基础实体类和DAO开发完成
- 基础服务层框架搭建完成

### 里程碑2：核心逻辑实现（第3周末）
- 增量状态管理服务完成
- DataX配置生成器完成
- 变量解析机制完成

### 里程碑3：端到端打通（第4周末）
- 执行器增量支持完成
- 回调机制实现完成
- 基础功能端到端验证通过

### 里程碑4：功能完善（第5周末）
- 监控界面开发完成
- 管理功能实现完成
- 用户体验优化完成

### 里程碑5：上线准备（第6周末）
- 所有测试用例通过
- 文档编写完成
- 生产环境部署就绪

---

*本文档将根据项目进展持续更新*
