-- data_service.datasource definition

CREATE TABLE `datasource` (
                              `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `name` varchar(100) NOT NULL COMMENT '数据源名称',
    `type` varchar(20) NOT NULL COMMENT '数据源类型：MYSQL、POSTGRESQL、ORACLE、DORIS、HIVE',
    `host` varchar(255) NOT NULL COMMENT '主机地址',
    `port` int(11) NOT NULL COMMENT '端口号',
    `database_name` varchar(100) NOT NULL COMMENT '数据库名称',
    `username` varchar(100) NOT NULL COMMENT '用户名',
    `password` varchar(255) NOT NULL COMMENT '密码（加密存储）',
    `connection_params` text COMMENT '连接参数（JSON格式）',
    `description` varchar(500) DEFAULT NULL COMMENT '描述',
    `status` tinyint(4) NOT NULL DEFAULT '1' COMMENT '状态：0-禁用，1-启用',
    `test_status` tinyint(4) NOT NULL DEFAULT '0' COMMENT '测试状态：0-未测试，1-连接成功，2-连接失败',
    `test_message` varchar(500) DEFAULT NULL COMMENT '测试结果信息',
    `test_time` datetime DEFAULT NULL COMMENT '最后测试时间',
    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `create_user` varchar(50) DEFAULT NULL COMMENT '创建人',
    `update_user` varchar(50) DEFAULT NULL COMMENT '更新人',
    `deleted` tinyint(4) NOT NULL DEFAULT '0' COMMENT '删除标记：0-未删除，1-已删除',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_name` (`name`) USING BTREE,
    KEY `idx_type` (`type`),
    KEY `idx_status` (`status`),
    KEY `idx_create_time` (`create_time`)
    ) ENGINE=InnoDB AUTO_INCREMENT=4 DEFAULT CHARSET=utf8mb4 COMMENT='数据源管理表';


-- data_service.job_group definition

CREATE TABLE `job_group` (
                             `id` int(11) NOT NULL AUTO_INCREMENT,
    `app_name` varchar(64) NOT NULL COMMENT '执行器AppName',
    `title` varchar(12) NOT NULL COMMENT '执行器名称',
    `address_type` tinyint(4) NOT NULL DEFAULT '0' COMMENT '执行器地址类型：0=自动注册、1=手动录入',
    `address_list` text COMMENT '执行器地址列表，多地址逗号分隔',
    `update_time` datetime DEFAULT NULL,
    PRIMARY KEY (`id`)
    ) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4;


-- data_service.job_info definition

CREATE TABLE `job_info` (
                            `id` int(11) NOT NULL AUTO_INCREMENT,
    `job_group` int(11) NOT NULL COMMENT '执行器主键ID',
    `job_desc` varchar(255) NOT NULL,
    `add_time` datetime DEFAULT NULL,
    `update_time` datetime DEFAULT NULL,
    `author` varchar(64) DEFAULT NULL COMMENT '作者',
    `alarm_email` varchar(255) DEFAULT NULL COMMENT '报警邮件',
    `schedule_type` varchar(50) NOT NULL DEFAULT 'NONE' COMMENT '调度类型',
    `schedule_conf` varchar(128) DEFAULT NULL COMMENT '调度配置，值含义取决于调度类型',
    `misfire_strategy` varchar(50) NOT NULL DEFAULT 'DO_NOTHING' COMMENT '调度过期策略',
    `executor_route_strategy` varchar(50) DEFAULT NULL COMMENT '执行器路由策略',
    `executor_handler` varchar(255) DEFAULT NULL COMMENT '执行器任务handler',
    `executor_param` varchar(512) DEFAULT NULL COMMENT '执行器任务参数',
    `executor_block_strategy` varchar(50) DEFAULT NULL COMMENT '阻塞处理策略',
    `executor_timeout` int(11) NOT NULL DEFAULT '0' COMMENT '任务执行超时时间，单位秒',
    `executor_fail_retry_count` int(11) NOT NULL DEFAULT '0' COMMENT '失败重试次数',
    `glue_type` varchar(50) NOT NULL COMMENT 'GLUE类型',
    `glue_source` mediumtext COMMENT 'GLUE源代码',
    `glue_remark` varchar(128) DEFAULT NULL COMMENT 'GLUE备注',
    `glue_updatetime` datetime DEFAULT NULL COMMENT 'GLUE更新时间',
    `child_jobid` varchar(255) DEFAULT NULL COMMENT '子任务ID，多个逗号分隔',
    `job_json` text COMMENT '工作流JSON配置，用于存储工作流定义、节点配置等信息',
    `workflow_id` bigint(20) DEFAULT NULL COMMENT '关联的工作流ID',
    `node_code` varchar(50) DEFAULT NULL COMMENT '关联的节点编码',
    `orchestration_mode` varchar(20) DEFAULT 'EXECUTOR' COMMENT '编排模式',
    `trigger_status` tinyint(4) NOT NULL DEFAULT '0' COMMENT '调度状态：0-停止，1-运行',
    `trigger_last_time` bigint(13) NOT NULL DEFAULT '0' COMMENT '上次调度时间',
    `trigger_next_time` bigint(13) NOT NULL DEFAULT '0' COMMENT '下次调度时间',
    PRIMARY KEY (`id`),
    KEY `idx_workflow_id` (`workflow_id`),
    KEY `idx_node_code` (`node_code`)
    ) ENGINE=InnoDB AUTO_INCREMENT=31 DEFAULT CHARSET=utf8mb4;


-- data_service.job_lock definition

CREATE TABLE `job_lock` (
                            `lock_name` varchar(50) NOT NULL COMMENT '锁名称',
    PRIMARY KEY (`lock_name`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;


-- data_service.job_log definition

CREATE TABLE `job_log` (
                           `id` bigint(20) NOT NULL AUTO_INCREMENT,
    `job_group` int(11) NOT NULL COMMENT '执行器主键ID',
    `job_id` int(11) NOT NULL COMMENT '任务，主键ID',
    `executor_address` varchar(255) DEFAULT NULL COMMENT '执行器地址，本次执行的地址',
    `executor_handler` varchar(255) DEFAULT NULL COMMENT '执行器任务handler',
    `executor_param` text COMMENT '执行器任务参数',
    `executor_sharding_param` varchar(20) DEFAULT NULL COMMENT '执行器任务分片参数，格式如 1/2',
    `executor_fail_retry_count` int(11) NOT NULL DEFAULT '0' COMMENT '失败重试次数',
    `trigger_time` datetime DEFAULT NULL COMMENT '调度-时间',
    `trigger_code` int(11) NOT NULL COMMENT '调度-结果',
    `trigger_msg` text COMMENT '调度-日志',
    `handle_time` datetime DEFAULT NULL COMMENT '执行-时间',
    `handle_code` int(11) NOT NULL COMMENT '执行-状态',
    `handle_msg` text COMMENT '执行-日志',
    `alarm_status` tinyint(4) NOT NULL DEFAULT '0' COMMENT '告警状态：0-默认、1-无需告警、2-告警成功、3-告警失败',
    PRIMARY KEY (`id`),
    KEY `I_trigger_time` (`trigger_time`),
    KEY `I_handle_code` (`handle_code`),
    KEY `I_jobid_jobgroup` (`job_id`,`job_group`),
    KEY `I_job_id` (`job_id`)
    ) ENGINE=InnoDB AUTO_INCREMENT=73 DEFAULT CHARSET=utf8mb4;


-- data_service.job_log_report definition

CREATE TABLE `job_log_report` (
                                  `id` int(11) NOT NULL AUTO_INCREMENT,
    `trigger_day` datetime DEFAULT NULL COMMENT '调度-时间',
    `running_count` int(11) NOT NULL DEFAULT '0' COMMENT '运行中-日志数量',
    `suc_count` int(11) NOT NULL DEFAULT '0' COMMENT '执行成功-日志数量',
    `fail_count` int(11) NOT NULL DEFAULT '0' COMMENT '执行失败-日志数量',
    `update_time` datetime DEFAULT NULL,
    PRIMARY KEY (`id`),
    UNIQUE KEY `i_trigger_day` (`trigger_day`) USING BTREE
    ) ENGINE=InnoDB AUTO_INCREMENT=17 DEFAULT CHARSET=utf8mb4;


-- data_service.job_logglue definition

CREATE TABLE `job_logglue` (
                               `id` int(11) NOT NULL AUTO_INCREMENT,
    `job_id` int(11) NOT NULL COMMENT '任务，主键ID',
    `glue_type` varchar(50) DEFAULT NULL COMMENT 'GLUE类型',
    `glue_source` mediumtext COMMENT 'GLUE源代码',
    `glue_remark` varchar(128) NOT NULL COMMENT 'GLUE备注',
    `add_time` datetime DEFAULT NULL,
    `update_time` datetime DEFAULT NULL,
    PRIMARY KEY (`id`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;


-- data_service.job_registry definition

CREATE TABLE `job_registry` (
                                `id` int(11) NOT NULL AUTO_INCREMENT,
    `registry_group` varchar(50) NOT NULL,
    `registry_key` varchar(255) NOT NULL,
    `registry_value` varchar(255) NOT NULL,
    `update_time` datetime DEFAULT NULL,
    PRIMARY KEY (`id`),
    UNIQUE KEY `i_g_k_v` (`registry_group`,`registry_key`,`registry_value`) USING BTREE
    ) ENGINE=InnoDB AUTO_INCREMENT=1762 DEFAULT CHARSET=utf8mb4;


-- data_service.job_user definition

CREATE TABLE `job_user` (
                            `id` int(11) NOT NULL AUTO_INCREMENT,
    `username` varchar(50) NOT NULL COMMENT '账号',
    `password` varchar(50) NOT NULL COMMENT '密码',
    `role` tinyint(4) NOT NULL COMMENT '角色：0-普通用户、1-管理员',
    `permission` varchar(255) DEFAULT NULL COMMENT '权限：执行器ID列表，多个逗号分割',
    PRIMARY KEY (`id`),
    UNIQUE KEY `i_username` (`username`) USING BTREE
    ) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4;


-- data_service.node_instance definition

CREATE TABLE `node_instance` (
                                 `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '节点实例ID',
    `workflow_id` bigint(20) DEFAULT NULL COMMENT '工作流ID',
    `execution_id` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '工作流执行ID',
    `node_id` bigint(20) DEFAULT NULL COMMENT '节点id',
    `node_code` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '节点编码，对应workflow_node.node_code',
    `node_name` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '节点名称',
    `node_type` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '节点类型',
    `job_id` int(11) DEFAULT NULL COMMENT 'XXL-JOB任务ID',
    `state` int(11) NOT NULL DEFAULT '0' COMMENT '节点状态：0-等待执行，1-运行中，2-成功，3-失败，4-跳过，5-终止',
    `submit_time` datetime DEFAULT NULL COMMENT '提交时间',
    `start_time` datetime DEFAULT NULL COMMENT '开始时间',
    `end_time` datetime DEFAULT NULL COMMENT '结束时间',
    `duration` bigint(20) DEFAULT NULL COMMENT '执行耗时（毫秒）',
    `error_message` text COLLATE utf8mb4_unicode_ci COMMENT '错误信息',
    `execute_path` varchar(200) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '执行路径',
    `log_path` varchar(200) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '日志路径',
    `alert_flag` tinyint(4) DEFAULT '0' COMMENT '告警标识',
    `retry_times` int(11) DEFAULT '0' COMMENT '重试次数',
    `pid` int(11) DEFAULT NULL COMMENT '进程ID',
    `app_link` text COLLATE utf8mb4_unicode_ci COMMENT '应用链接',
    `node_params` longtext COLLATE utf8mb4_unicode_ci COMMENT '节点参数',
    `retry_interval` int(11) DEFAULT '1' COMMENT '重试间隔',
    `max_retry_times` int(11) DEFAULT '0' COMMENT '最大重试次数',
    `priority` int(11) DEFAULT '2' COMMENT '优先级',
    `worker_group` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT 'default' COMMENT '工作组',
    `environment_code` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '环境变量编码',
    `executor_id` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '执行用户ID',
    `first_submit_time` datetime DEFAULT NULL COMMENT '首次提交时间',
    `delay_time` int(11) DEFAULT '0' COMMENT '延迟时间',
    `var_pool` text COLLATE utf8mb4_unicode_ci COMMENT '变量池',
    `dry_run` tinyint(4) DEFAULT '0' COMMENT '试运行标识',
    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    KEY `idx_state` (`state`) USING BTREE,
    KEY `idx_start_time` (`start_time`) USING BTREE,
    KEY `idx_execution_id` (`execution_id`),
    KEY `idx_job_id` (`job_id`)
    ) ENGINE=InnoDB AUTO_INCREMENT=34 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='节点实例表';


-- data_service.node_type definition

CREATE TABLE `node_type` (
                             `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '节点类型ID',
    `type_code` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '节点类型编码',
    `type_name` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '节点类型名称',
    `category` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '节点分类：DATA_SYNC-数据同步，COMPUTE-计算处理，CONTROL-流程控制',
    `icon` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '节点图标',
    `color` varchar(20) COLLATE utf8mb4_unicode_ci DEFAULT '#1890ff' COMMENT '节点颜色',
    `description` text COLLATE utf8mb4_unicode_ci COMMENT '节点描述',
    `config_schema` longtext COLLATE utf8mb4_unicode_ci COMMENT '配置参数Schema(JSON)',
    `executor_class` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '执行器类名',
    `sort_order` int(11) DEFAULT '0' COMMENT '排序序号',
    `status` tinyint(4) NOT NULL DEFAULT '1' COMMENT '状态：0-禁用，1-启用',
    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_type_code` (`type_code`) USING BTREE,
    KEY `idx_category` (`category`) USING BTREE,
    KEY `idx_status` (`status`) USING BTREE
    ) ENGINE=InnoDB AUTO_INCREMENT=9 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='节点类型表';


-- data_service.project definition

CREATE TABLE `project` (
                           `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '项目ID',
    `name` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '项目名称',
    `code` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '项目编码',
    `type` varchar(20) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'PRIVATE' COMMENT '项目类型：PUBLIC-公共项目，PRIVATE-私有项目',
    `description` text COLLATE utf8mb4_unicode_ci COMMENT '项目描述',
    `status` tinyint(4) NOT NULL DEFAULT '1' COMMENT '状态：0-禁用，1-启用',
    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `create_user` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '创建人',
    `update_user` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '更新人',
    `deleted` tinyint(4) NOT NULL DEFAULT '0' COMMENT '删除标记：0-未删除，1-已删除',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_project_code` (`code`) USING BTREE COMMENT '项目编码唯一索引',
    KEY `idx_project_type` (`type`) USING BTREE COMMENT '项目类型索引',
    KEY `idx_project_status` (`status`) USING BTREE COMMENT '项目状态索引',
    KEY `idx_project_create_time` (`create_time`) USING BTREE COMMENT '创建时间索引',
    KEY `idx_project_deleted` (`deleted`) USING BTREE COMMENT '删除标记索引'
    ) ENGINE=InnoDB AUTO_INCREMENT=15 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='项目表';


-- data_service.task_instance definition

CREATE TABLE `task_instance` (
                                 `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '任务实例ID',
    `execution_id` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '执行实例ID（UUID）',
    `orchestration_mode` varchar(20) COLLATE utf8mb4_unicode_ci DEFAULT 'EXECUTOR' COMMENT '编排模式',
    `progress` decimal(5,2) DEFAULT '0.00' COMMENT '执行进度（百分比）',
    `completed_nodes` int(11) DEFAULT '0' COMMENT '已完成节点数',
    `total_nodes` int(11) DEFAULT '0' COMMENT '总节点数',
    `running_nodes` text COLLATE utf8mb4_unicode_ci COMMENT '运行中的节点列表（JSON）',
    `failed_nodes` text COLLATE utf8mb4_unicode_ci COMMENT '失败的节点列表（JSON）',
    `workflow_id` bigint(20) NOT NULL COMMENT '工作流ID',
    `job_id` int(11) DEFAULT NULL COMMENT 'XXL-JOB任务ID',
    `job_log_id` bigint(20) DEFAULT NULL COMMENT '任务日志ID',
    `instance_name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '实例名称',
    `workflow_version` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT '1.0' COMMENT '工作流版本，默认1.0',
    `state` int(11) NOT NULL DEFAULT '0' COMMENT '执行状态：0-提交成功，1-正在运行，2-准备暂停，3-暂停，4-准备停止，5-停止，6-失败，7-成功，8-需要容错，9-kill，10-等待线程，11-等待依赖完成',
    `trigger_type` varchar(20) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'MANUAL' COMMENT '触发类型：CRON-定时触发，MANUAL-手动触发，API-接口触发，RETRY-重试触发',
    `recovery` tinyint(4) DEFAULT '0' COMMENT '恢复标识：0-正常，1-恢复',
    `start_time` datetime DEFAULT NULL COMMENT '开始时间',
    `end_time` datetime DEFAULT NULL COMMENT '结束时间',
    `duration` bigint(20) DEFAULT NULL COMMENT '执行耗时（毫秒）',
    `run_times` int(11) DEFAULT '1' COMMENT '运行次数',
    `execute_user` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '执行用户',
    `host` varchar(135) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '执行主机',
    `executor_sharding_param` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '执行器分片参数',
    `command_type` int(11) DEFAULT NULL COMMENT '命令类型',
    `command_param` text COLLATE utf8mb4_unicode_ci COMMENT '命令参数',
    `execute_result` text COLLATE utf8mb4_unicode_ci COMMENT '执行结果',
    `error_message` text COLLATE utf8mb4_unicode_ci COMMENT '错误信息',
    `max_try_times` int(11) DEFAULT '0' COMMENT '最大尝试次数',
    `failure_strategy` int(11) DEFAULT '0' COMMENT '失败策略',
    `warning_type` int(11) DEFAULT '0' COMMENT '告警类型',
    `warning_group_id` bigint(20) DEFAULT NULL COMMENT '告警组ID',
    `schedule_time` datetime DEFAULT NULL COMMENT '调度时间',
    `command_start_time` datetime DEFAULT NULL COMMENT '命令开始时间',
    `global_params` text COLLATE utf8mb4_unicode_ci COMMENT '全局参数',
    `dag_json` longtext COLLATE utf8mb4_unicode_ci COMMENT 'DAG JSON',
    `executor_id` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '执行用户ID',
    `priority` int(11) DEFAULT '2' COMMENT '优先级',
    `worker_group` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT 'default' COMMENT '工作组',
    `environment_code` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '环境变量编码',
    `timeout` int(11) DEFAULT '0' COMMENT '超时时间',
    `var_pool` text COLLATE utf8mb4_unicode_ci COMMENT '变量池',
    `dry_run` tinyint(4) DEFAULT '0' COMMENT '试运行标识：0-否，1-是',
    `restart_time` datetime DEFAULT NULL COMMENT '重启时间',
    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_execution_id` (`execution_id`),
    KEY `idx_workflow_id` (`workflow_id`) USING BTREE,
    KEY `idx_job_id` (`job_id`) USING BTREE,
    KEY `idx_state` (`state`) USING BTREE,
    KEY `idx_start_time` (`start_time`) USING BTREE,
    KEY `idx_schedule_time` (`schedule_time`) USING BTREE,
    KEY `idx_trigger_type` (`trigger_type`),
    KEY `idx_orchestration_mode` (`orchestration_mode`)
    ) ENGINE=InnoDB AUTO_INCREMENT=32 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='任务实例表';


-- data_service.workflow definition

CREATE TABLE `workflow` (
                            `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '工作流ID',
    `project_id` bigint(20) NOT NULL COMMENT '项目ID',
    `name` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '工作流名称',
    `code` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '工作流编码',
    `description` text COLLATE utf8mb4_unicode_ci COMMENT '工作流描述',
    `tags` varchar(500) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '标签，逗号分隔',
    `category` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '分类：ETL-数据处理，REPORT-报表生成，ANALYSIS-数据分析，SYNC-数据同步',
    `version` int(11) NOT NULL DEFAULT '1' COMMENT '版本号',
    `global_params` text COLLATE utf8mb4_unicode_ci COMMENT '全局参数JSON',
    `warning_type` varchar(20) COLLATE utf8mb4_unicode_ci DEFAULT 'NONE' COMMENT '告警类型：NONE-不告警，SUCCESS-成功告警，FAILURE-失败告警，ALL-全部告警',
    `warning_group_id` bigint(20) DEFAULT NULL COMMENT '告警组ID',
    `job_id` int(11) DEFAULT NULL COMMENT 'XXL-JOB任务ID',
    `orchestration_mode` varchar(20) COLLATE utf8mb4_unicode_ci DEFAULT 'EXECUTOR' COMMENT '编排模式：CENTRAL-调度中心编排，EXECUTOR-执行器编排',
    `last_execution_id` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '最后执行ID',
    `execution_count` bigint(20) DEFAULT '0' COMMENT '执行次数',
    `success_count` bigint(20) DEFAULT '0' COMMENT '成功次数',
    `failure_count` bigint(20) DEFAULT '0' COMMENT '失败次数',
    `cron_expression` varchar(128) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'Cron表达式',
    `last_trigger_time` datetime DEFAULT NULL COMMENT '最后触发时间',
    `status` tinyint(4) NOT NULL DEFAULT '0' COMMENT '状态：0-草稿，1-已发布，2-已上线，3-已下线',
    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `create_user` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '创建人',
    `update_user` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '更新人',
    `deleted` tinyint(4) NOT NULL DEFAULT '0' COMMENT '删除标记：0-未删除，1-已删除',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_project_code` (`project_id`,`code`) USING BTREE,
    KEY `idx_project_id` (`project_id`) USING BTREE,
    KEY `idx_status` (`status`) USING BTREE,
    KEY `idx_create_time` (`create_time`) USING BTREE,
    KEY `idx_deleted` (`deleted`) USING BTREE,
    KEY `idx_workflow_category` (`category`) USING BTREE,
    KEY `idx_workflow_tags` (`tags`(255)) USING BTREE,
    KEY `idx_job_id` (`job_id`) USING BTREE,
    KEY `idx_orchestration_mode` (`orchestration_mode`)
    ) ENGINE=InnoDB AUTO_INCREMENT=11 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='工作流定义表';


-- data_service.workflow_node definition

CREATE TABLE `workflow_node` (
                                 `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '节点ID',
    `workflow_id` bigint(20) NOT NULL COMMENT '工作流ID',
    `node_code` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '节点编码',
    `node_name` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '节点名称',
    `node_type` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '节点类型编码',
    `color` varchar(20) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '节点颜色（从节点类型继承）',
    `description` text COLLATE utf8mb4_unicode_ci COMMENT '节点描述',
    `config_params` longtext COLLATE utf8mb4_unicode_ci COMMENT '节点配置参数JSON',
    `position_x` int(11) DEFAULT '0' COMMENT 'X坐标位置',
    `position_y` int(11) DEFAULT '0' COMMENT 'Y坐标位置',
    `timeout` int(11) DEFAULT '0' COMMENT '超时时间(分钟)，0表示不限制',
    `retry_times` int(11) DEFAULT '0' COMMENT '重试次数',
    `retry_interval` int(11) DEFAULT '1' COMMENT '重试间隔(分钟)',
    `max_retry_times` int(11) DEFAULT '3' COMMENT '最大重试次数',
    `failure_strategy` varchar(20) COLLATE utf8mb4_unicode_ci DEFAULT 'FAIL' COMMENT '失败策略：FAIL-失败，SKIP-跳过',
    `priority` int(11) DEFAULT '2' COMMENT '优先级：1-最高，2-高，3-中，4-低，5-最低',
    `worker_group` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT 'default' COMMENT '工作组',
    `environment_code` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '环境变量编码',
    `datasource_id` bigint(20) DEFAULT NULL COMMENT '关联数据源ID',
    `job_id` bigint(20) DEFAULT NULL COMMENT 'XXL-JOB任务ID，关联job_info表的id字段，用于节点级别的任务调度',
    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `create_user` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '创建人',
    `update_user` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '更新人',
    `deleted` tinyint(4) NOT NULL DEFAULT '0' COMMENT '删除标记：0-未删除，1-已删除',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_workflow_node_code` (`workflow_id`,`node_code`) USING BTREE,
    KEY `idx_workflow_id` (`workflow_id`) USING BTREE,
    KEY `idx_node_type` (`node_type`) USING BTREE,
    KEY `idx_datasource_id` (`datasource_id`) USING BTREE,
    KEY `idx_deleted` (`deleted`) USING BTREE,
    KEY `idx_job_id` (`job_id`) USING BTREE COMMENT 'job_id索引'
    ) ENGINE=InnoDB AUTO_INCREMENT=25 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='工作流节点表';


-- data_service.workflow_node_relation definition

CREATE TABLE `workflow_node_relation` (
                                          `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '关系ID',
    `workflow_id` bigint(20) NOT NULL COMMENT '工作流ID',
    `pre_node_code` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '前置节点编码，NULL表示起始节点',
    `post_node_code` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '后置节点编码',
    `condition_type` varchar(20) COLLATE utf8mb4_unicode_ci DEFAULT 'NONE' COMMENT '条件类型：NONE-无条件，SUCCESS-成功条件，FAILURE-失败条件',
    `condition_params` text COLLATE utf8mb4_unicode_ci COMMENT '条件参数JSON',
    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    KEY `idx_workflow_id` (`workflow_id`) USING BTREE,
    KEY `idx_pre_node_code` (`pre_node_code`) USING BTREE,
    KEY `idx_post_node_code` (`post_node_code`) USING BTREE
    ) ENGINE=InnoDB AUTO_INCREMENT=29 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='工作流节点关系表';


-- data_service.workflow_schedule_config definition

CREATE TABLE `workflow_schedule_config` (
                                            `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '配置ID',
    `workflow_id` bigint(20) NOT NULL COMMENT '工作流ID',
    `orchestration_mode` varchar(20) NOT NULL DEFAULT 'CENTRAL' COMMENT '编排模式',
    `schedule_enabled` tinyint(4) NOT NULL DEFAULT '1' COMMENT '调度是否启用',
    `cron_expression` varchar(128) DEFAULT NULL COMMENT 'Cron表达式',
    `timezone` varchar(50) DEFAULT 'Asia/Shanghai' COMMENT '时区',
    `last_trigger_time` datetime DEFAULT NULL COMMENT '上次触发时间',
    `next_trigger_time` datetime DEFAULT NULL COMMENT '下次触发时间',
    `trigger_count` bigint(20) DEFAULT '0' COMMENT '触发次数',
    `success_count` bigint(20) DEFAULT '0' COMMENT '成功次数',
    `failure_count` bigint(20) DEFAULT '0' COMMENT '失败次数',
    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    `create_user` varchar(50) NOT NULL COMMENT '创建人',
    `update_user` varchar(50) NOT NULL COMMENT '更新人',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_workflow_id` (`workflow_id`),
    KEY `idx_schedule_enabled` (`schedule_enabled`),
    KEY `idx_next_trigger_time` (`next_trigger_time`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='工作流调度配置表';


-- data_service.project_member definition

CREATE TABLE `project_member` (
                                  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '成员ID',
    `project_id` bigint(20) NOT NULL COMMENT '项目ID',
    `user_id` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '用户ID',
    `username` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '用户名',
    `role` varchar(20) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'VIEWER' COMMENT '角色：ADMIN-项目管理员，DEVELOPER-开发者，VIEWER-查看者',
    `join_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '加入时间',
    `status` tinyint(4) NOT NULL DEFAULT '1' COMMENT '状态：0-禁用，1-启用',
    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `create_user` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '创建人',
    `update_user` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '更新人',
    `deleted` tinyint(4) NOT NULL DEFAULT '0' COMMENT '删除标记：0-未删除，1-已删除',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_project_user` (`project_id`,`user_id`) USING BTREE COMMENT '项目用户唯一索引',
    KEY `idx_member_project_id` (`project_id`) USING BTREE COMMENT '项目ID索引',
    KEY `idx_member_user_id` (`user_id`) USING BTREE COMMENT '用户ID索引',
    KEY `idx_member_role` (`role`) USING BTREE COMMENT '角色索引',
    KEY `idx_member_status` (`status`) USING BTREE COMMENT '状态索引',
    KEY `idx_member_deleted` (`deleted`) USING BTREE COMMENT '删除标记索引',
    CONSTRAINT `fk_project_member_project` FOREIGN KEY (`project_id`) REFERENCES `project` (`id`) ON DELETE CASCADE
    ) ENGINE=InnoDB AUTO_INCREMENT=44 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='项目成员表';