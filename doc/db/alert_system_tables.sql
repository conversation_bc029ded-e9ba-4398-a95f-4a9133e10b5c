-- 告警系统数据库表结构
-- 集成到admin模块的完整数据库设计
-- 基于现有admin模块的设计规范

-- 1. 告警模板表
CREATE TABLE `alert_template` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `template_code` varchar(50) NOT NULL COMMENT '模板编码',
  `template_name` varchar(100) NOT NULL COMMENT '模板名称',
  `channel_type` varchar(20) NOT NULL COMMENT '通道类型：WECHAT,FEISHU,DINGTALK,EMAIL',
  `template_content` text NOT NULL COMMENT '模板内容JSON',
  `variables` text COMMENT '模板变量说明JSON',
  `enabled` tinyint(4) NOT NULL DEFAULT 1 COMMENT '是否启用：0-禁用，1-启用',
  `description` varchar(500) COMMENT '模板描述',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `create_user` varchar(50) DEFAULT NULL COMMENT '创建人',
  `update_user` varchar(50) DEFAULT NULL COMMENT '更新人',
  `deleted` tinyint(4) NOT NULL DEFAULT '0' COMMENT '删除标记：0-未删除，1-已删除',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_template_code_channel_deleted` (`template_code`, `channel_type`, `deleted`),
  KEY `idx_channel_type` (`channel_type`),
  KEY `idx_enabled` (`enabled`),
  KEY `idx_deleted` (`deleted`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='告警模板表';

-- 2. 通道配置表
CREATE TABLE `alert_channel_config` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `config_name` varchar(100) NOT NULL COMMENT '配置名称',
  `channel_type` varchar(20) NOT NULL COMMENT '通道类型：WECHAT,FEISHU,DINGTALK,EMAIL',
  `auth_type` varchar(20) NOT NULL COMMENT '认证类型：WEBHOOK,TOKEN,OAUTH,SMTP',
  `auth_config` text NOT NULL COMMENT '认证配置JSON（加密存储）',
  `channel_config` text COMMENT '通道特定配置JSON',
  `enabled` tinyint(4) NOT NULL DEFAULT 1 COMMENT '是否启用：0-禁用，1-启用',
  `description` varchar(500) COMMENT '配置描述',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `create_user` varchar(50) DEFAULT NULL COMMENT '创建人',
  `update_user` varchar(50) DEFAULT NULL COMMENT '更新人',
  `deleted` tinyint(4) NOT NULL DEFAULT '0' COMMENT '删除标记：0-未删除，1-已删除',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_config_name_deleted` (`config_name`, `deleted`),
  KEY `idx_channel_type` (`channel_type`),
  KEY `idx_auth_type` (`auth_type`),
  KEY `idx_enabled` (`enabled`),
  KEY `idx_deleted` (`deleted`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='通道配置表';

-- 3. 告警发送记录表
CREATE TABLE `alert_send_record` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `request_id` varchar(100) NOT NULL COMMENT '请求ID',
  `template_code` varchar(50) NOT NULL COMMENT '模板编码',
  `channel_type` varchar(20) NOT NULL COMMENT '通道类型',
  `config_name` varchar(100) COMMENT '使用的配置名称',
  `recipients` text NOT NULL COMMENT '接收人列表JSON',
  `title` varchar(200) COMMENT '消息标题',
  `content` text NOT NULL COMMENT '消息内容',
  `variables` text COMMENT '模板变量JSON',
  `send_time` datetime NOT NULL COMMENT '发送时间',
  `send_status` varchar(20) NOT NULL COMMENT '发送状态：PENDING,SUCCESS,FAILED,RETRY',
  `response_code` varchar(50) COMMENT '响应状态码',
  `response_message` text COMMENT '响应消息',
  `retry_count` int(11) NOT NULL DEFAULT 0 COMMENT '重试次数',
  `max_retry_count` int(11) NOT NULL DEFAULT 3 COMMENT '最大重试次数',
  `next_retry_time` datetime COMMENT '下次重试时间',
  `source_system` varchar(50) COMMENT '来源系统',
  `business_id` varchar(100) COMMENT '业务ID（如任务ID、工作流ID等）',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_request_id` (`request_id`),
  KEY `idx_template_code` (`template_code`),
  KEY `idx_channel_type` (`channel_type`),
  KEY `idx_send_time` (`send_time`),
  KEY `idx_send_status` (`send_status`),
  KEY `idx_source_system` (`source_system`),
  KEY `idx_business_id` (`business_id`),
  KEY `idx_create_time` (`create_time`),
  KEY `idx_next_retry_time` (`next_retry_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='告警发送记录表';

-- 插入默认模板数据
INSERT INTO `alert_template` (`template_code`, `template_name`, `channel_type`, `template_content`, `variables`, `description`, `create_user`) VALUES
-- 任务失败通知模板
('JOB_FAILED', '任务执行失败通知', 'WECHAT', 
'{"msgtype":"markdown","markdown":{"content":"## 🚨 任务执行失败\\n\\n**任务名称**: ${jobDesc}\\n**任务组**: ${jobGroup}\\n**执行时间**: ${triggerTime}\\n**失败原因**: ${handleMsg}\\n\\n请及时处理！"}}', 
'[{"name":"jobDesc","description":"任务描述","required":true},{"name":"jobGroup","description":"任务组","required":true},{"name":"triggerTime","description":"触发时间","required":true},{"name":"handleMsg","description":"处理消息","required":true}]', 
'任务失败企业微信通知模板', 'system'),

('JOB_FAILED', '任务执行失败通知', 'EMAIL', 
'{"subject":"【告警】任务执行失败 - ${jobDesc}","body":"<h2>🚨 任务执行失败</h2><table border=\\"1\\" cellpadding=\\"5\\" style=\\"border-collapse:collapse;width:100%;\\"><tr><td><strong>任务名称</strong></td><td>${jobDesc}</td></tr><tr><td><strong>任务组</strong></td><td>${jobGroup}</td></tr><tr><td><strong>执行时间</strong></td><td>${triggerTime}</td></tr><tr><td><strong>失败原因</strong></td><td>${handleMsg}</td></tr></table><p>请登录系统查看详细信息并及时处理。</p>"}', 
'[{"name":"jobDesc","description":"任务描述","required":true},{"name":"jobGroup","description":"任务组","required":true},{"name":"triggerTime","description":"触发时间","required":true},{"name":"handleMsg","description":"处理消息","required":true}]', 
'任务失败邮件通知模板', 'system'),

-- 工作流失败通知模板
('WORKFLOW_FAILED', '工作流执行失败通知', 'WECHAT', 
'{"msgtype":"markdown","markdown":{"content":"## 🚨 工作流执行失败\\n\\n**工作流名称**: ${workflowName}\\n**项目**: ${projectName}\\n**失败时间**: ${failTime}\\n**失败原因**: ${errorMessage}\\n\\n请及时处理！"}}', 
'[{"name":"workflowName","description":"工作流名称","required":true},{"name":"projectName","description":"项目名称","required":true},{"name":"failTime","description":"失败时间","required":true},{"name":"errorMessage","description":"错误信息","required":true}]', 
'工作流失败企业微信通知模板', 'system'),

-- 系统错误通知模板
('SYSTEM_ERROR', '系统错误通知', 'WECHAT', 
'{"msgtype":"text","text":{"content":"⚠️ 系统错误通知\\n\\n错误信息：${errorMessage}\\n发生时间：${alertTime}\\n来源系统：${sourceSystem}\\n\\n请及时处理！"}}', 
'[{"name":"errorMessage","description":"错误信息","required":true},{"name":"alertTime","description":"告警时间","required":true},{"name":"sourceSystem","description":"来源系统","required":true}]', 
'系统错误通知模板', 'system'),

-- 数据源连接失败通知模板
('DATASOURCE_FAILED', '数据源连接失败通知', 'WECHAT', 
'{"msgtype":"markdown","markdown":{"content":"## ⚠️ 数据源连接失败\\n\\n**数据源名称**: ${datasourceName}\\n**数据源类型**: ${datasourceType}\\n**失败时间**: ${failTime}\\n**错误信息**: ${errorMessage}\\n\\n请检查数据源配置！"}}', 
'[{"name":"datasourceName","description":"数据源名称","required":true},{"name":"datasourceType","description":"数据源类型","required":true},{"name":"failTime","description":"失败时间","required":true},{"name":"errorMessage","description":"错误信息","required":true}]', 
'数据源连接失败通知模板', 'system');
