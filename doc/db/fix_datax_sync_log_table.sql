-- 修复datax_sync_log表结构
-- 检查并添加缺失的字段

-- 1. 检查job_log_id字段是否存在
SET @column_exists = (
    SELECT COUNT(*) 
    FROM information_schema.COLUMNS 
    WHERE TABLE_SCHEMA = DATABASE() 
    AND TABLE_NAME = 'datax_sync_log' 
    AND COLUMN_NAME = 'job_log_id'
);

-- 2. 如果job_log_id字段不存在，则添加
SET @sql = IF(@column_exists = 0, 
    'ALTER TABLE datax_sync_log ADD COLUMN job_log_id bigint(20) COMMENT "XXL-JOB日志ID" AFTER execution_id',
    'SELECT "job_log_id字段已存在" as message'
);

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 3. 检查其他可能缺失的字段
SET @column_exists = (
    SELECT COUNT(*) 
    FROM information_schema.COLUMNS 
    WHERE TABLE_SCHEMA = DATABASE() 
    AND TABLE_NAME = 'datax_sync_log' 
    AND COLUMN_NAME = 'duration'
);

SET @sql = IF(@column_exists = 0, 
    'ALTER TABLE datax_sync_log ADD COLUMN duration bigint(20) COMMENT "执行时长(毫秒)" AFTER end_time',
    'SELECT "duration字段已存在" as message'
);

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 4. 检查error_code字段
SET @column_exists = (
    SELECT COUNT(*) 
    FROM information_schema.COLUMNS 
    WHERE TABLE_SCHEMA = DATABASE() 
    AND TABLE_NAME = 'datax_sync_log' 
    AND COLUMN_NAME = 'error_code'
);

SET @sql = IF(@column_exists = 0, 
    'ALTER TABLE datax_sync_log ADD COLUMN error_code varchar(50) COMMENT "错误代码" AFTER error_message',
    'SELECT "error_code字段已存在" as message'
);

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 5. 检查performance_info字段
SET @column_exists = (
    SELECT COUNT(*) 
    FROM information_schema.COLUMNS 
    WHERE TABLE_SCHEMA = DATABASE() 
    AND TABLE_NAME = 'datax_sync_log' 
    AND COLUMN_NAME = 'performance_info'
);

SET @sql = IF(@column_exists = 0, 
    'ALTER TABLE datax_sync_log ADD COLUMN performance_info text COMMENT "性能信息JSON" AFTER datax_config',
    'SELECT "performance_info字段已存在" as message'
);

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 6. 检查retry_times字段
SET @column_exists = (
    SELECT COUNT(*) 
    FROM information_schema.COLUMNS 
    WHERE TABLE_SCHEMA = DATABASE() 
    AND TABLE_NAME = 'datax_sync_log' 
    AND COLUMN_NAME = 'retry_times'
);

SET @sql = IF(@column_exists = 0, 
    'ALTER TABLE datax_sync_log ADD COLUMN retry_times int(11) DEFAULT 0 COMMENT "重试次数" AFTER performance_info',
    'SELECT "retry_times字段已存在" as message'
);

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 7. 添加缺失的索引
-- 检查job_log_id索引
SET @index_exists = (
    SELECT COUNT(*) 
    FROM information_schema.STATISTICS 
    WHERE TABLE_SCHEMA = DATABASE() 
    AND TABLE_NAME = 'datax_sync_log' 
    AND INDEX_NAME = 'idx_job_log_id'
);

SET @sql = IF(@index_exists = 0, 
    'ALTER TABLE datax_sync_log ADD INDEX idx_job_log_id (job_log_id)',
    'SELECT "idx_job_log_id索引已存在" as message'
);

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 8. 显示当前表结构
SELECT 
    COLUMN_NAME,
    COLUMN_TYPE,
    IS_NULLABLE,
    COLUMN_DEFAULT,
    COLUMN_COMMENT
FROM information_schema.COLUMNS 
WHERE TABLE_SCHEMA = DATABASE() 
AND TABLE_NAME = 'datax_sync_log'
ORDER BY ORDINAL_POSITION;

-- 9. 显示索引信息
SELECT 
    INDEX_NAME,
    COLUMN_NAME,
    NON_UNIQUE
FROM information_schema.STATISTICS 
WHERE TABLE_SCHEMA = DATABASE() 
AND TABLE_NAME = 'datax_sync_log'
ORDER BY INDEX_NAME, SEQ_IN_INDEX;

SELECT '✅ datax_sync_log表结构修复完成！' as message;
