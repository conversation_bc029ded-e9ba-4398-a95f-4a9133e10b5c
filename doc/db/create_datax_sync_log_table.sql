-- =====================================================
-- DataX同步日志表完整建表脚本
-- =====================================================

-- 删除已存在的表（谨慎使用）
-- DROP TABLE IF EXISTS `datax_sync_log`;

-- 创建DataX同步日志表
CREATE TABLE `datax_sync_log` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `sync_state_id` bigint(20) NOT NULL COMMENT '同步状态ID，关联datax_sync_state表',
  `execution_id` varchar(100) NOT NULL COMMENT '执行ID，关联工作流执行',
  `job_log_id` bigint(20) DEFAULT NULL COMMENT 'XXL-JOB日志ID',
  `start_time` datetime NOT NULL COMMENT '开始时间',
  `end_time` datetime DEFAULT NULL COMMENT '结束时间',
  `duration` bigint(20) DEFAULT NULL COMMENT '执行时长(毫秒)',
  `status` varchar(20) NOT NULL DEFAULT 'RUNNING' COMMENT '执行状态：RUNNING-运行中，SUCCESS-成功，FAILED-失败，CANCELLED-已取消',
  `sync_records` bigint(20) DEFAULT NULL COMMENT '同步记录数',
  `error_message` text COMMENT '错误信息',
  `error_code` varchar(50) DEFAULT NULL COMMENT '错误代码',
  `incremental_value_start` varchar(100) DEFAULT NULL COMMENT '增量起始值',
  `incremental_value_end` varchar(100) DEFAULT NULL COMMENT '增量结束值',
  `datax_config` longtext COMMENT 'DataX配置JSON',
  `performance_info` text COMMENT '性能信息JSON',
  `retry_times` int(11) DEFAULT 0 COMMENT '重试次数',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_sync_state_id` (`sync_state_id`),
  KEY `idx_execution_id` (`execution_id`),
  KEY `idx_job_log_id` (`job_log_id`),
  KEY `idx_status` (`status`),
  KEY `idx_start_time` (`start_time`),
  KEY `idx_create_time` (`create_time`),
  KEY `idx_sync_state_status` (`sync_state_id`, `status`),
  KEY `idx_execution_status` (`execution_id`, `status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='DataX同步执行日志表';

-- =====================================================
-- 插入示例数据（可选）
-- =====================================================

-- 示例：运行中的同步任务
INSERT INTO `datax_sync_log` (
  `sync_state_id`,
  `execution_id`,
  `start_time`,
  `status`,
  `incremental_value_start`,
  `create_time`
) VALUES (
  1,
  'example-execution-001',
  NOW(),
  'RUNNING',
  '2025-08-20 00:00:00',
  NOW()
);

-- 示例：成功完成的同步任务
INSERT INTO `datax_sync_log` (
  `sync_state_id`,
  `execution_id`,
  `start_time`,
  `end_time`,
  `duration`,
  `status`,
  `sync_records`,
  `incremental_value_start`,
  `incremental_value_end`,
  `create_time`
) VALUES (
  1,
  'example-execution-002',
  DATE_SUB(NOW(), INTERVAL 1 HOUR),
  DATE_SUB(NOW(), INTERVAL 55 MINUTE),
  300000,
  'SUCCESS',
  1500,
  '2025-08-20 00:00:00',
  '2025-08-20 01:00:00',
  DATE_SUB(NOW(), INTERVAL 1 HOUR)
);

-- 示例：失败的同步任务
INSERT INTO `datax_sync_log` (
  `sync_state_id`,
  `execution_id`,
  `start_time`,
  `end_time`,
  `duration`,
  `status`,
  `error_message`,
  `error_code`,
  `incremental_value_start`,
  `retry_times`,
  `create_time`
) VALUES (
  1,
  'example-execution-003',
  DATE_SUB(NOW(), INTERVAL 2 HOUR),
  DATE_SUB(NOW(), INTERVAL 2 HOUR) + INTERVAL 30 SECOND,
  30000,
  'FAILED',
  '连接数据库超时',
  'DB_TIMEOUT',
  '2025-08-20 02:00:00',
  1,
  DATE_SUB(NOW(), INTERVAL 2 HOUR)
);

-- =====================================================
-- 查询验证
-- =====================================================

-- 查看表结构
DESCRIBE `datax_sync_log`;

-- 查看索引
SHOW INDEX FROM `datax_sync_log`;

-- 查看示例数据
SELECT 
  id,
  sync_state_id,
  execution_id,
  start_time,
  end_time,
  status,
  sync_records,
  error_message,
  incremental_value_start,
  incremental_value_end,
  retry_times,
  create_time
FROM `datax_sync_log`
ORDER BY create_time DESC;

-- =====================================================
-- 常用查询示例
-- =====================================================

-- 1. 查询某个同步状态的所有日志
-- SELECT * FROM datax_sync_log WHERE sync_state_id = 1 ORDER BY create_time DESC;

-- 2. 查询正在运行的任务
-- SELECT * FROM datax_sync_log WHERE status = 'RUNNING' ORDER BY start_time ASC;

-- 3. 查询失败的任务
-- SELECT * FROM datax_sync_log WHERE status = 'FAILED' ORDER BY create_time DESC LIMIT 10;

-- 4. 查询某个执行ID的日志
-- SELECT * FROM datax_sync_log WHERE execution_id = 'your-execution-id';

-- 5. 统计成功率
-- SELECT 
--   sync_state_id,
--   COUNT(*) as total_count,
--   SUM(CASE WHEN status = 'SUCCESS' THEN 1 ELSE 0 END) as success_count,
--   ROUND(SUM(CASE WHEN status = 'SUCCESS' THEN 1 ELSE 0 END) * 100.0 / COUNT(*), 2) as success_rate
-- FROM datax_sync_log 
-- GROUP BY sync_state_id;

-- 6. 查询平均执行时长
-- SELECT 
--   sync_state_id,
--   AVG(duration) as avg_duration_ms,
--   AVG(duration)/1000 as avg_duration_seconds
-- FROM datax_sync_log 
-- WHERE status = 'SUCCESS' AND duration IS NOT NULL
-- GROUP BY sync_state_id;

-- 7. 查询最近的同步记录
-- SELECT * FROM datax_sync_log ORDER BY create_time DESC LIMIT 20;

-- =====================================================
-- 维护脚本
-- =====================================================

-- 清理30天前的日志（谨慎使用）
-- DELETE FROM datax_sync_log WHERE create_time < DATE_SUB(NOW(), INTERVAL 30 DAY);

-- 清理失败且重试次数超过3次的日志（谨慎使用）
-- DELETE FROM datax_sync_log WHERE status = 'FAILED' AND retry_times > 3 AND create_time < DATE_SUB(NOW(), INTERVAL 7 DAY);

SELECT '✅ datax_sync_log表创建完成！' as message;
