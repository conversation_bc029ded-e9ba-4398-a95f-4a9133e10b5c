-- 修复DataX增量同步视图创建问题
-- 问题：视图创建时引用了不存在的表或字段

-- 1. 删除可能存在的有问题的视图
DROP VIEW IF EXISTS `v_datax_sync_overview`;

-- 2. 检查datax_sync_state表是否存在所有必要字段
-- 如果表不存在，先创建表
CREATE TABLE IF NOT EXISTS `datax_sync_state` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `node_id` bigint(20) NOT NULL COMMENT '节点ID',
  `workflow_id` bigint(20) NOT NULL COMMENT '工作流ID',
  `source_datasource_id` bigint(20) NOT NULL COMMENT '源数据源ID',
  `source_table` varchar(100) NOT NULL COMMENT '源表名',
  `target_datasource_id` bigint(20) NOT NULL COMMENT '目标数据源ID', 
  `target_table` varchar(100) NOT NULL COMMENT '目标表名',
  `incremental_column` varchar(100) NOT NULL COMMENT '增量字段名',
  `incremental_type` varchar(20) NOT NULL DEFAULT 'TIMESTAMP' COMMENT '增量类型：TIMESTAMP,PRIMARY_KEY,CDC',
  `last_sync_value` varchar(100) COMMENT '上次同步的最大值',
  `last_sync_time` datetime COMMENT '上次同步时间',
  `sync_count` bigint(20) DEFAULT 0 COMMENT '同步次数',
  `total_records` bigint(20) DEFAULT 0 COMMENT '累计同步记录数',
  `status` varchar(20) DEFAULT 'ACTIVE' COMMENT '状态：ACTIVE-活跃,INACTIVE-非活跃,ERROR-错误',
  `error_message` text COMMENT '错误信息',
  `config_snapshot` text COMMENT '配置快照JSON',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `create_user` varchar(50) NOT NULL COMMENT '创建人',
  `update_user` varchar(50) NOT NULL COMMENT '更新人',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_node_tables` (`node_id`, `source_table`, `target_table`),
  KEY `idx_workflow_id` (`workflow_id`),
  KEY `idx_source_datasource` (`source_datasource_id`),
  KEY `idx_target_datasource` (`target_datasource_id`),
  KEY `idx_status` (`status`),
  KEY `idx_last_sync_time` (`last_sync_time`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='DataX增量同步状态表';

-- 3. 检查是否缺少total_records字段，如果缺少则添加
SET @column_exists = (
    SELECT COUNT(*) 
    FROM information_schema.COLUMNS 
    WHERE TABLE_SCHEMA = DATABASE() 
    AND TABLE_NAME = 'datax_sync_state' 
    AND COLUMN_NAME = 'total_records'
);

SET @sql = IF(@column_exists = 0, 
    'ALTER TABLE datax_sync_state ADD COLUMN total_records bigint(20) DEFAULT 0 COMMENT "累计同步记录数" AFTER sync_count',
    'SELECT "total_records字段已存在" as message'
);

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 4. 重新创建视图（简化版本，不依赖其他可能不存在的表）
CREATE VIEW `v_datax_sync_overview` AS
SELECT 
    dss.id,
    dss.workflow_id,
    dss.node_id,
    dss.source_table,
    dss.target_table,
    dss.incremental_column,
    dss.incremental_type,
    dss.last_sync_value,
    dss.last_sync_time,
    dss.sync_count,
    dss.total_records,
    dss.status,
    dss.error_message,
    dss.create_time,
    dss.update_time,
    dss.create_user,
    dss.update_user,
    CASE 
        WHEN dss.last_sync_time IS NULL THEN '从未同步'
        WHEN dss.last_sync_time < DATE_SUB(NOW(), INTERVAL 1 DAY) THEN '超过1天未同步'
        WHEN dss.last_sync_time < DATE_SUB(NOW(), INTERVAL 1 HOUR) THEN '超过1小时未同步'
        ELSE '正常'
    END as sync_health_status,
    CASE 
        WHEN dss.status = 'ACTIVE' THEN '活跃'
        WHEN dss.status = 'INACTIVE' THEN '非活跃'
        WHEN dss.status = 'ERROR' THEN '错误'
        ELSE dss.status
    END as status_name
FROM datax_sync_state dss;

-- 5. 验证视图创建成功
SELECT 
    TABLE_NAME,
    TABLE_TYPE,
    TABLE_COMMENT
FROM information_schema.TABLES 
WHERE TABLE_SCHEMA = DATABASE() 
AND TABLE_NAME = 'v_datax_sync_overview';

-- 6. 测试视图查询
SELECT COUNT(*) as view_record_count FROM v_datax_sync_overview;

SELECT '✅ DataX增量同步视图修复完成！' as message;
