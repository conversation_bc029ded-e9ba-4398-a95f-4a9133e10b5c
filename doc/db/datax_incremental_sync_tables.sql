-- DataX增量同步相关表结构
-- 创建时间: 2025-08-19
-- 版本: 1.0

-- 1. 创建增量同步状态表
CREATE TABLE `datax_sync_state` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `node_id` bigint(20) NOT NULL COMMENT '节点ID',
  `workflow_id` bigint(20) NOT NULL COMMENT '工作流ID',
  `source_datasource_id` bigint(20) NOT NULL COMMENT '源数据源ID',
  `source_table` varchar(100) NOT NULL COMMENT '源表名',
  `target_datasource_id` bigint(20) NOT NULL COMMENT '目标数据源ID', 
  `target_table` varchar(100) NOT NULL COMMENT '目标表名',
  `incremental_column` varchar(100) NOT NULL COMMENT '增量字段名',
  `incremental_type` varchar(20) NOT NULL DEFAULT 'TIMESTAMP' COMMENT '增量类型：TIMESTAMP,PRIMARY_KEY,CDC',
  `last_sync_value` varchar(100) COMMENT '上次同步的最大值',
  `last_sync_time` datetime COMMENT '上次同步时间',
  `sync_count` bigint(20) DEFAULT 0 COMMENT '同步次数',
  `total_records` bigint(20) DEFAULT 0 COMMENT '累计同步记录数',
  `status` varchar(20) DEFAULT 'ACTIVE' COMMENT '状态：ACTIVE-活跃,INACTIVE-非活跃,ERROR-错误',
  `error_message` text COMMENT '错误信息',
  `config_snapshot` text COMMENT '配置快照JSON',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `create_user` varchar(50) NOT NULL COMMENT '创建人',
  `update_user` varchar(50) NOT NULL COMMENT '更新人',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_node_tables` (`node_id`, `source_table`, `target_table`),
  KEY `idx_workflow_id` (`workflow_id`),
  KEY `idx_source_datasource` (`source_datasource_id`),
  KEY `idx_target_datasource` (`target_datasource_id`),
  KEY `idx_status` (`status`),
  KEY `idx_last_sync_time` (`last_sync_time`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='DataX增量同步状态表';

-- 2. 创建增量同步日志表
CREATE TABLE `datax_sync_log` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `sync_state_id` bigint(20) NOT NULL COMMENT '同步状态ID',
  `execution_id` varchar(100) COMMENT '执行ID',
  `job_log_id` bigint(20) COMMENT 'XXL-JOB日志ID',
  `start_time` datetime NOT NULL COMMENT '开始时间',
  `end_time` datetime COMMENT '结束时间',
  `duration` bigint(20) COMMENT '执行时长(毫秒)',
  `status` varchar(20) NOT NULL COMMENT '状态：RUNNING-运行中,SUCCESS-成功,FAILED-失败,CANCELLED-取消',
  `sync_records` bigint(20) DEFAULT 0 COMMENT '本次同步记录数',
  `error_message` text COMMENT '错误信息',
  `error_code` varchar(50) COMMENT '错误代码',
  `incremental_value_start` varchar(100) COMMENT '增量起始值',
  `incremental_value_end` varchar(100) COMMENT '增量结束值',
  `datax_config` longtext COMMENT 'DataX配置JSON',
  `performance_info` text COMMENT '性能信息JSON',
  `retry_times` int(11) DEFAULT 0 COMMENT '重试次数',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_sync_state_id` (`sync_state_id`),
  KEY `idx_execution_id` (`execution_id`),
  KEY `idx_job_log_id` (`job_log_id`),
  KEY `idx_start_time` (`start_time`),
  KEY `idx_status` (`status`),
  CONSTRAINT `fk_sync_log_state` FOREIGN KEY (`sync_state_id`) REFERENCES `datax_sync_state` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='DataX增量同步日志表';

-- 3. 创建增量同步配置表（用于存储增量策略配置）
CREATE TABLE `datax_incremental_config` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `node_id` bigint(20) NOT NULL COMMENT '节点ID',
  `incremental_type` varchar(20) NOT NULL DEFAULT 'TIMESTAMP' COMMENT '增量类型',
  `incremental_column` varchar(100) NOT NULL COMMENT '增量字段',
  `incremental_value` varchar(200) COMMENT '增量值表达式',
  `batch_size` int(11) DEFAULT 10000 COMMENT '批次大小',
  `max_retry_times` int(11) DEFAULT 3 COMMENT '最大重试次数',
  `custom_condition` text COMMENT '自定义增量条件',
  `enable_checkpoint` tinyint(4) DEFAULT 1 COMMENT '是否启用断点续传：0-否，1-是',
  `checkpoint_interval` int(11) DEFAULT 1000 COMMENT '断点保存间隔（记录数）',
  `enabled` tinyint(4) DEFAULT 1 COMMENT '是否启用：0-否，1-是',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `create_user` varchar(50) NOT NULL COMMENT '创建人',
  `update_user` varchar(50) NOT NULL COMMENT '更新人',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_node_id` (`node_id`),
  KEY `idx_incremental_type` (`incremental_type`),
  KEY `idx_enabled` (`enabled`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='DataX增量同步配置表';

-- 4. 插入初始化数据
INSERT INTO `datax_incremental_config` (`node_id`, `incremental_type`, `incremental_column`, `incremental_value`, `create_user`, `update_user`) VALUES
(0, 'TIMESTAMP', 'update_time', '${lastSyncTime}', 'system', 'system') ON DUPLICATE KEY UPDATE `update_time` = NOW();

-- 5. 创建视图：增量同步状态概览（先检查表是否存在必要字段）
-- 注意：如果workflow_node表不存在，可以去掉相关的JOIN
CREATE VIEW `v_datax_sync_overview` AS
SELECT
    dss.id,
    dss.workflow_id,
    dss.node_id,
    dss.source_table,
    dss.target_table,
    dss.incremental_column,
    dss.incremental_type,
    dss.last_sync_value,
    dss.last_sync_time,
    dss.sync_count,
    dss.total_records,
    dss.status,
    dss.error_message,
    dss.create_time,
    dss.update_time,
    CASE
        WHEN dss.last_sync_time IS NULL THEN '从未同步'
        WHEN dss.last_sync_time < DATE_SUB(NOW(), INTERVAL 1 DAY) THEN '超过1天未同步'
        WHEN dss.last_sync_time < DATE_SUB(NOW(), INTERVAL 1 HOUR) THEN '超过1小时未同步'
        ELSE '正常'
    END as sync_health_status,
    CASE
        WHEN dss.status = 'ACTIVE' THEN '活跃'
        WHEN dss.status = 'INACTIVE' THEN '非活跃'
        WHEN dss.status = 'ERROR' THEN '错误'
        ELSE dss.status
    END as status_name
FROM datax_sync_state dss
WHERE dss.status IN ('ACTIVE', 'INACTIVE', 'ERROR');

-- 6. 创建存储过程：清理历史日志
DELIMITER $$
CREATE PROCEDURE `sp_cleanup_datax_sync_logs`(IN days_to_keep INT)
BEGIN
    DECLARE done INT DEFAULT FALSE;
    DECLARE log_count INT DEFAULT 0;
    
    -- 删除指定天数之前的日志记录
    DELETE FROM datax_sync_log 
    WHERE create_time < DATE_SUB(NOW(), INTERVAL days_to_keep DAY)
    AND status IN ('SUCCESS', 'FAILED', 'CANCELLED');
    
    -- 获取删除的记录数
    SET log_count = ROW_COUNT();
    
    -- 记录清理日志
    INSERT INTO datax_sync_log (sync_state_id, execution_id, start_time, end_time, status, sync_records, error_message)
    VALUES (0, CONCAT('CLEANUP_', UNIX_TIMESTAMP()), NOW(), NOW(), 'SUCCESS', log_count, CONCAT('清理了', log_count, '条历史日志记录'));
    
END$$
DELIMITER ;

-- 7. 创建定时清理事件（可选，需要开启事件调度器）
-- SET GLOBAL event_scheduler = ON;
-- CREATE EVENT IF NOT EXISTS `evt_cleanup_datax_sync_logs`
-- ON SCHEDULE EVERY 1 DAY
-- STARTS CURRENT_TIMESTAMP
-- DO
--   CALL sp_cleanup_datax_sync_logs(30);
