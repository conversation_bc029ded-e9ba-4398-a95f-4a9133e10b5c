package com.sevb.core.biz.model;

import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

/**
 * common return
 * <AUTHOR> 2015-12-4 16:32:31
 * @param <T>
 */
@Data
public class ReturnT<T> implements Serializable {

	@Serial
    private static final long serialVersionUID = 42L;

	public static final int SUCCESS_CODE = 200;
	public static final int FAIL_CODE = 500;

	public static final ReturnT<String> SUCCESS = new ReturnT<String>(null);
	public static final ReturnT<String> FAIL = new ReturnT<String>(FAIL_CODE, null);

	private int code;
	private String msg;
	private T content;

	public ReturnT(){}

	public ReturnT(int code, String msg) {
		this.code = code;
		this.msg = msg;
	}

	public ReturnT(T content) {
		this.code = SUCCESS_CODE;
		this.content = content;
	}

	public ReturnT(int code, String msg, T content) {
		this.code = code;
		this.msg = msg;
		this.content = content;
	}

	@Override
	public String toString() {
		return "ReturnT [code=" + code + ", msg=" + msg + ", content=" + content + "]";
	}

}
