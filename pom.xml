<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <groupId>com.sevb</groupId>
    <artifactId>data-development</artifactId>
    <version>3.0.0</version>
    <packaging>pom</packaging>

    <parent>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-parent</artifactId>
        <version>3.4.2</version>
    </parent>

    <name>${project.artifactId}</name>
    <description>A distributed task scheduling framework.</description>

    <modules>
        <module>ibdp-service-core</module>
        <module>ibdp-service-admin</module>
        <module>ibdp-service-executor</module>
    </modules>

    <properties>
        <!-- env -->
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
        <maven.compiler.encoding>UTF-8</maven.compiler.encoding>
        <maven.compiler.source>17</maven.compiler.source>
        <maven.compiler.target>17</maven.compiler.target>
        <maven.test.skip>true</maven.test.skip>
        <!-- plugin -->
        <maven-source-plugin.version>3.3.1</maven-source-plugin.version>
        <maven-javadoc-plugin.version>3.11.2</maven-javadoc-plugin.version>
        <maven-gpg-plugin.version>3.2.7</maven-gpg-plugin.version>
        <!-- base -->
        <slf4j-api.version>2.0.16</slf4j-api.version>
        <junit-jupiter.version>5.11.4</junit-jupiter.version>
        <jakarta.annotation-api.version>3.0.0</jakarta.annotation-api.version>
        <!-- net -->
        <netty.version>4.1.117.Final</netty.version>
        <gson.version>2.12.1</gson.version>
        <!-- spring -->
        <spring-boot.version>3.4.2</spring-boot.version>
        <spring.version>6.2.2</spring.version>
        <!-- db -->
        <mybatis-spring-boot-starter.version>3.0.4</mybatis-spring-boot-starter.version>
        <mysql-connector-j.version>9.2.0</mysql-connector-j.version>
        <!-- dynamic language -->
        <groovy.version>4.0.25</groovy.version>
    </properties>
</project>