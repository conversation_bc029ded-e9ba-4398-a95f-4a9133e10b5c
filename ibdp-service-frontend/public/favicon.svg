<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 32 32" width="32" height="32">
  <!-- 背景圆形 -->
  <circle cx="16" cy="16" r="15" fill="#409eff" stroke="#ffffff" stroke-width="1"/>
  
  <!-- 数据库图标 -->
  <g fill="#ffffff">
    <!-- 数据库顶部椭圆 -->
    <ellipse cx="16" cy="10" rx="8" ry="3"/>
    
    <!-- 数据库主体 -->
    <rect x="8" y="10" width="16" height="12" rx="0"/>
    
    <!-- 数据库底部椭圆 -->
    <ellipse cx="16" cy="22" rx="8" ry="3"/>
    
    <!-- 数据库中间分隔线 -->
    <ellipse cx="16" cy="16" rx="8" ry="2" fill="#409eff"/>
    <ellipse cx="16" cy="16" rx="8" ry="1.5" fill="#ffffff"/>
  </g>
  
  <!-- 流程箭头 -->
  <g fill="#ffffff" opacity="0.9">
    <!-- 箭头线条 -->
    <rect x="4" y="15.5" width="6" height="1" rx="0.5"/>
    <rect x="22" y="15.5" width="6" height="1" rx="0.5"/>
    
    <!-- 箭头头部 -->
    <polygon points="26,14 28,16 26,18"/>
  </g>
</svg>
