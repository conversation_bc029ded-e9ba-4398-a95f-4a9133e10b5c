<!DOCTYPE html>
<html>
<head>
    <title>Favicon Generator</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; }
        .preview { margin: 20px 0; }
        canvas { border: 1px solid #ccc; margin: 10px; }
        .download { margin: 10px 0; }
    </style>
</head>
<body>
    <h1>IBDP Favicon Generator</h1>
    
    <div class="preview">
        <h3>预览:</h3>
        <canvas id="favicon16" width="16" height="16"></canvas>
        <canvas id="favicon32" width="32" height="32"></canvas>
        <canvas id="favicon48" width="48" height="48"></canvas>
    </div>
    
    <div class="download">
        <button onclick="downloadFavicon()">下载 favicon.ico</button>
        <button onclick="downloadPNG(16)">下载 16x16 PNG</button>
        <button onclick="downloadPNG(32)">下载 32x32 PNG</button>
    </div>

    <script>
        // 绘制favicon图标
        function drawFavicon(canvas, size) {
            const ctx = canvas.getContext('2d');
            const scale = size / 32;
            
            // 清空画布
            ctx.clearRect(0, 0, size, size);
            
            // 背景圆形
            ctx.fillStyle = '#409eff';
            ctx.beginPath();
            ctx.arc(size/2, size/2, size/2 - 1, 0, 2 * Math.PI);
            ctx.fill();
            
            // 白色边框
            ctx.strokeStyle = '#ffffff';
            ctx.lineWidth = 1;
            ctx.stroke();
            
            // 数据库图标
            ctx.fillStyle = '#ffffff';
            
            // 数据库顶部椭圆
            ctx.beginPath();
            ctx.ellipse(size/2, size * 0.3125, size * 0.25, size * 0.09375, 0, 0, 2 * Math.PI);
            ctx.fill();
            
            // 数据库主体
            ctx.fillRect(size * 0.25, size * 0.3125, size * 0.5, size * 0.375);
            
            // 数据库底部椭圆
            ctx.beginPath();
            ctx.ellipse(size/2, size * 0.6875, size * 0.25, size * 0.09375, 0, 0, 2 * Math.PI);
            ctx.fill();
            
            // 数据库中间分隔线
            ctx.fillStyle = '#409eff';
            ctx.beginPath();
            ctx.ellipse(size/2, size * 0.5, size * 0.25, size * 0.0625, 0, 0, 2 * Math.PI);
            ctx.fill();
            
            ctx.fillStyle = '#ffffff';
            ctx.beginPath();
            ctx.ellipse(size/2, size * 0.5, size * 0.25, size * 0.046875, 0, 0, 2 * Math.PI);
            ctx.fill();
            
            // 流程箭头
            ctx.fillStyle = '#ffffff';
            ctx.globalAlpha = 0.9;
            
            // 左侧箭头线
            ctx.fillRect(size * 0.125, size * 0.484375, size * 0.1875, size * 0.03125);
            
            // 右侧箭头线
            ctx.fillRect(size * 0.6875, size * 0.484375, size * 0.1875, size * 0.03125);
            
            // 箭头头部
            ctx.beginPath();
            ctx.moveTo(size * 0.8125, size * 0.4375);
            ctx.lineTo(size * 0.875, size * 0.5);
            ctx.lineTo(size * 0.8125, size * 0.5625);
            ctx.closePath();
            ctx.fill();
            
            ctx.globalAlpha = 1;
        }
        
        // 初始化画布
        drawFavicon(document.getElementById('favicon16'), 16);
        drawFavicon(document.getElementById('favicon32'), 32);
        drawFavicon(document.getElementById('favicon48'), 48);
        
        // 下载PNG
        function downloadPNG(size) {
            const canvas = document.createElement('canvas');
            canvas.width = size;
            canvas.height = size;
            drawFavicon(canvas, size);
            
            const link = document.createElement('a');
            link.download = `favicon-${size}x${size}.png`;
            link.href = canvas.toDataURL();
            link.click();
        }
        
        // 下载ICO (简化版本，实际使用建议用专业工具转换)
        function downloadFavicon() {
            downloadPNG(32);
            alert('已下载32x32 PNG格式。建议使用在线工具将PNG转换为ICO格式，如：https://www.favicon-generator.org/');
        }
    </script>
</body>
</html>
