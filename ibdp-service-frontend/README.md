# IBDP数据开发平台前端

基于Vue 3 + TypeScript + Element Plus构建的数据开发平台前端应用。

## 技术栈

- **框架**: Vue.js 3.4+
- **语言**: TypeScript
- **UI库**: Element Plus
- **状态管理**: Pinia
- **路由**: Vue Router 4
- **HTTP客户端**: Axios
- **构建工具**: Vite
- **代码规范**: ESLint

## 功能特性

- 🏠 **首页**: 系统概览、数据统计、快捷入口
- 🗄️ **数据源管理**: 支持MySQL、PostgreSQL、Oracle、Doris、Hive等数据库
- 🔧 **连接测试**: 实时测试数据源连接状态
- 📱 **响应式设计**: 适配桌面端和移动端
- 🎨 **蓝色主题**: 统一的视觉风格

## 开发环境要求

- Node.js >= 16.0.0
- npm >= 8.0.0

## 安装依赖

```bash
npm install
```

## 开发运行

```bash
npm run dev
```

访问 http://localhost:3000

## 构建部署

```bash
npm run build
```

## 代码检查

```bash
npm run lint
```

## 项目结构

```
src/
├── api/                # API接口
├── components/         # 公共组件
├── router/            # 路由配置
├── stores/            # Pinia状态管理
├── styles/            # 样式文件
├── types/             # TypeScript类型定义
├── utils/             # 工具函数
└── views/             # 页面组件
    ├── Home.vue       # 首页
    └── datasource/    # 数据源管理
```

## API接口

后端服务地址: http://localhost:18080/ibdp

主要接口:
- `/datasource/pageList` - 分页查询数据源
- `/datasource/add` - 新增数据源
- `/datasource/update` - 更新数据源
- `/datasource/delete/{id}` - 删除数据源
- `/datasource/test/{id}` - 测试连接

## 开发规范

- 组件命名使用PascalCase
- 文件名使用kebab-case
- 所有文本和注释使用中文
- 遵循Vue 3 Composition API规范
- 使用TypeScript进行类型检查

## 浏览器支持

- Chrome >= 87
- Firefox >= 78
- Safari >= 14
- Edge >= 88
