{"name": "ibdp-service-frontend", "version": "1.0.0", "description": "IBDP数据开发平台前端", "type": "module", "scripts": {"dev": "vite", "build": "vue-tsc && vite build", "preview": "vite preview", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts --fix --ignore-path .gitignore", "type-check": "vue-tsc --noEmit"}, "dependencies": {"vue": "^3.4.0", "vue-router": "^4.2.5", "pinia": "^2.1.7", "element-plus": "^2.4.4", "axios": "^1.6.2", "@element-plus/icons-vue": "^2.3.1"}, "devDependencies": {"@vitejs/plugin-vue": "^4.5.2", "@vue/tsconfig": "^0.5.1", "typescript": "~5.3.0", "vue-tsc": "^1.8.25", "vite": "^5.0.10", "@types/node": "^20.10.5", "@typescript-eslint/eslint-plugin": "^6.15.0", "@typescript-eslint/parser": "^6.15.0", "eslint": "^8.56.0", "eslint-plugin-vue": "^9.19.2", "unplugin-auto-import": "^0.17.2", "unplugin-vue-components": "^0.26.0"}}