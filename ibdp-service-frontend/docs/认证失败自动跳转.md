# 认证失败自动跳转功能

## 功能概述

当前端发起HTTP请求时，如果后端返回认证失败的状态码（401未授权或302重定向），系统会自动清除用户登录状态并跳转到登录页面。

## 实现机制

### 1. HTTP响应拦截器处理

在 `src/api/request.ts` 中的响应拦截器会处理以下情况：

- **401 Unauthorized**: 未授权访问，token无效或已过期
- **302 Found**: 后端重定向到登录页面（由PermissionInterceptor触发）
- **HTML响应检测**: 检测到包含'toLogin'的HTML响应（重定向后的页面）
- **非JSON响应**: 检测到非JSON格式的响应数据

### 2. 重定向处理策略

为了正确捕获302重定向，系统采用了两种策略：

1. **禁止自动跟随重定向**: 设置 `maxRedirects: 0` 来捕获真正的302状态码
2. **HTML响应检测**: 检测响应内容是否包含登录页面标识

### 3. 自动跳转流程

当检测到认证失败时，系统会执行以下步骤：

1. **清除用户数据**: 调用 `userStore.clearUserData()` 清除本地存储的用户信息
2. **避免重复跳转**: 检查当前路由是否已经是登录页面
3. **跳转到登录页**: 使用 `router.push('/login')` 进行页面跳转
4. **显示提示消息**: 向用户显示相应的错误提示

### 4. 代码实现

```typescript
// 处理认证失败
const handleAuthFailure = async () => {
  // 动态导入用户Store避免循环依赖
  const { useUserStore } = await import('@/stores/user')
  const userStore = useUserStore()
  
  // 清除用户数据
  userStore.clearUserData()
  
  // 跳转到登录页面，避免重复跳转
  if (router.currentRoute.value.path !== '/login') {
    router.push('/login')
  }
}
```

## 触发场景

### 1. Token过期
- 用户长时间未操作，session或token过期
- 后端返回401状态码

### 2. 无效Token
- Token被篡改或格式错误
- 后端返回401状态码

### 3. 权限拦截
- 后端PermissionInterceptor检测到未登录用户
- 返回302重定向状态码

### 4. 手动登出
- 用户在其他标签页登出
- 当前页面发起请求时会触发认证失败

## 用户体验

### 1. 无感知跳转
- 用户无需手动刷新页面
- 自动清理过期的登录状态
- 平滑跳转到登录页面

### 2. 友好提示
- 显示明确的错误信息
- 区分不同的失败原因
- 引导用户重新登录

### 3. 状态一致性
- 前端状态与后端认证状态保持同步
- 避免出现"假登录"状态

## 配合机制

### 1. 路由守卫
- 在路由跳转时检查登录状态
- 与HTTP拦截器形成双重保护

### 2. 用户Store
- 统一管理用户登录状态
- 提供清除用户数据的方法

### 3. 后端拦截器
- PermissionInterceptor负责权限检查
- 返回标准的HTTP状态码

## 测试方法

### 手动测试场景
1. 登录后等待session过期，然后操作页面
2. 在开发者工具中清除cookie，然后发起请求
3. 在多个标签页中登出，然后在其他标签页操作

## 注意事项

### 1. 循环依赖避免
- 使用动态导入避免模块循环依赖
- 确保router和store的正确初始化顺序

### 2. 重复跳转防护
- 检查当前路由避免重复跳转到登录页
- 防止无限重定向循环

### 3. 错误处理
- 区分网络错误和认证错误
- 提供适当的用户反馈

## 扩展功能

### 1. 记住跳转前页面
可以在跳转到登录页时记录当前页面，登录成功后自动返回：

```typescript
// 记录来源页面
const from = router.currentRoute.value.fullPath
router.push(`/login?redirect=${encodeURIComponent(from)}`)
```

### 2. 自动重试机制
可以在token刷新后自动重试失败的请求：

```typescript
// 在请求拦截器中添加重试逻辑
if (isTokenRefreshed) {
  return request(originalRequest)
}
```
