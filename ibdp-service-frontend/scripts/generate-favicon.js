import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// 创建一个简单的16x16 ICO文件
// 这是一个基础的ICO文件头和数据
const createSimpleICO = () => {
  // ICO文件头 (6 bytes)
  const header = Buffer.from([
    0x00, 0x00, // Reserved (must be 0)
    0x01, 0x00, // Image type (1 = ICO)
    0x01, 0x00  // Number of images (1)
  ]);

  // 图像目录条目 (16 bytes)
  const dirEntry = Buffer.from([
    0x10,       // Width (16 pixels)
    0x10,       // Height (16 pixels)
    0x00,       // Color palette (0 = no palette)
    0x00,       // Reserved (must be 0)
    0x01, 0x00, // Color planes (1)
    0x20, 0x00, // Bits per pixel (32)
    0x00, 0x04, 0x00, 0x00, // Image data size (1024 bytes)
    0x16, 0x00, 0x00, 0x00  // Offset to image data (22 bytes)
  ]);

  // 创建16x16的RGBA图像数据
  const imageData = Buffer.alloc(1024); // 16*16*4 = 1024 bytes

  // 绘制简单的数据库图标
  for (let y = 0; y < 16; y++) {
    for (let x = 0; x < 16; x++) {
      const offset = (y * 16 + x) * 4;
      
      // 计算距离中心的距离
      const centerX = 8;
      const centerY = 8;
      const distance = Math.sqrt((x - centerX) ** 2 + (y - centerY) ** 2);
      
      if (distance <= 7.5) {
        // 背景圆形 - 蓝色
        imageData[offset] = 0x40;     // B
        imageData[offset + 1] = 0x9e; // G
        imageData[offset + 2] = 0xff; // R
        imageData[offset + 3] = 0xff; // A
        
        // 数据库形状
        if ((y >= 3 && y <= 5) || (y >= 7 && y <= 9) || (y >= 11 && y <= 13)) {
          if (x >= 4 && x <= 12) {
            // 数据库层
            imageData[offset] = 0xff;     // B - 白色
            imageData[offset + 1] = 0xff; // G
            imageData[offset + 2] = 0xff; // R
            imageData[offset + 3] = 0xff; // A
          }
        }
        
        // 箭头
        if (y === 8) {
          if (x === 2 || x === 14) {
            imageData[offset] = 0xff;     // B - 白色
            imageData[offset + 1] = 0xff; // G
            imageData[offset + 2] = 0xff; // R
            imageData[offset + 3] = 0xff; // A
          }
        }
      } else {
        // 透明背景
        imageData[offset] = 0x00;     // B
        imageData[offset + 1] = 0x00; // G
        imageData[offset + 2] = 0x00; // R
        imageData[offset + 3] = 0x00; // A
      }
    }
  }

  // 合并所有数据
  return Buffer.concat([header, dirEntry, imageData]);
};

// 生成favicon.ico文件
const icoData = createSimpleICO();
const outputPath = path.join(__dirname, '../public/favicon.ico');

fs.writeFileSync(outputPath, icoData);
console.log('✅ favicon.ico 生成成功:', outputPath);

// 使用说明
console.log(`
📝 使用说明:
1. 已生成 favicon.svg (矢量图标)
2. 已生成 favicon.ico (位图图标)
3. 已更新 index.html 引用

🎨 图标设计:
- 蓝色圆形背景 (#409eff)
- 白色数据库图标
- 流程箭头元素
- 适合数据开发平台主题

🔧 如需自定义:
1. 修改 favicon.svg 文件
2. 使用在线工具转换: https://www.favicon-generator.org/
3. 或运行 favicon-generator.html 生成不同尺寸
`);
