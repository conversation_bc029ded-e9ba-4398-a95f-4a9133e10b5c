import request from './request'
import type { Workflow, WorkflowQuery, WorkflowForm, WorkflowScheduleConfig } from '@/types/workflow'

/**
 * 工作流管理API
 */
export const workflowApi = {
  /**
   * 分页查询工作流列表
   */
  pageList(params: WorkflowQuery) {
    return request.get('/workflow/pageList', { params })
  },

  /**
   * 根据项目ID查询所有工作流
   */
  listByProject(projectId: number) {
    return request.get('/workflow/listByProject', {
      params: { projectId }
    })
  },

  /**
   * 根据项目ID和状态查询工作流
   */
  listByProjectAndStatus(projectId: number, status: number) {
    return request.get('/workflow/listByProjectAndStatus', {
      params: { projectId, status }
    })
  },

  /**
   * 获取工作流详情
   */
  getById(id: number) {
    return request.get(`/workflow/${id}`)
  },

  /**
   * 根据项目ID和编码获取工作流
   */
  getByCode(projectId: number, code: string) {
    return request.get('/workflow/getByCode', {
      params: { projectId, code }
    })
  },

  /**
   * 添加工作流
   */
  add(data: WorkflowForm) {
    return request.post('/workflow/add', data)
  },

  /**
   * 更新工作流
   */
  update(data: WorkflowForm) {
    return request.post('/workflow/update', data)
  },

  /**
   * 删除工作流
   */
  delete(id: number) {
    return request.post('/workflow/delete', null, {
      params: { id }
    })
  },

  /**
   * 发布工作流
   */
  publish(id: number) {
    return request.post('/workflow/publish', null, {
      params: { id }
    })
  },

  /**
   * 上线工作流
   */
  online(id: number) {
    return request.post('/workflow/online', null, {
      params: { id }
    })
  },

  /**
   * 下线工作流
   */
  offline(id: number) {
    return request.post('/workflow/offline', null, {
      params: { id }
    })
  },

  /**
   * 获取工作流调度配置
   */
  getScheduleConfig(id: number) {
    return request.get(`/workflow/schedule-config/${id}`)
  },

  /**
   * 更新工作流调度配置
   */
  updateScheduleConfig(config: WorkflowScheduleConfig) {
    return request.post('/workflow/schedule-config', config)
  },

  /**
   * 启动工作流调度
   */
  startSchedule(id: number) {
    return request.post(`/workflow/schedule/start/${id}`)
  },

  /**
   * 停止工作流调度
   */
  stopSchedule(id: number) {
    return request.post(`/workflow/schedule/stop/${id}`)
  },

  /**
   * 获取工作流调度状态
   */
  getScheduleStatus(id: number) {
    return request.get(`/workflow/schedule/status/${id}`)
  },

  /**
   * 手动执行工作流（调度中心编排）
   */
  execute(id: number, executeUser?: string) {
    const user = executeUser || 'admin' // 默认用户
    return request.post(`/workflow/execute/central?id=${id}&executeUser=${user}`)
  },

  /**
   * 获取工作流执行实例详情
   */
  getExecution(executionId: string) {
    return request.get(`/workflow/execution/${executionId}`)
  },

  /**
   * 重试失败的节点
   */
  retryFailedNode(executionId: string, nodeCode: string) {
    return request.post('/workflow/callback/retryNode', null, {
      params: { executionId, nodeCode }
    })
  },

  /**
   * 终止工作流执行
   */
  terminateExecution(executionId: string, reason?: string) {
    return request.post('/workflow/callback/terminate', null, {
      params: {
        executionId,
        reason: reason || '用户手动终止'
      }
    })
  },

  /**
   * 检查工作流下线安全性
   */
  checkOfflineSafety(id: number) {
    return request.get(`/workflow/offline/check/${id}`)
  },

  /**
   * 获取工作流分类列表
   */
  getCategoryList() {
    return request.get('/workflow/categoryList')
  },

  /**
   * 获取工作流状态列表
   */
  getStatusList() {
    return request.get('/workflow/statusList')
  },

  /**
   * 统计项目下的工作流数量
   */
  countByProject(projectId: number) {
    return request.get('/workflow/count', {
      params: { projectId }
    })
  },

  /**
   * 根据分类统计工作流数量
   */
  countByCategory(projectId: number) {
    return request.get('/workflow/countByCategory', {
      params: { projectId }
    })
  }
}
