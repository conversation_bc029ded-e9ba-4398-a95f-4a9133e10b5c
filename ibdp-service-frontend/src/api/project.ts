import request from './request'
import type { ApiResponse, PageParams, PageResponse, Project, ProjectTypeOption } from '@/types'

// 项目管理API
export const projectApi = {
  // 分页查询项目列表
  pageList: (params: PageParams & {
    name?: string
    type?: string
    status?: number
  }): Promise<ApiResponse<PageResponse<Project>>> => {
    return request.get('/project/pageList', { params })
  },

  // 新增项目
  add: (data: Project): Promise<ApiResponse<string>> => {
    return request.post('/project/add', data)
  },

  // 更新项目
  update: (data: Project): Promise<ApiResponse<string>> => {
    return request.post('/project/update', data)
  },

  // 删除项目
  delete: (id: number): Promise<ApiResponse<string>> => {
    return request.post(`/project/delete/${id}`)
  },

  // 查询项目详情
  load: (id: number): Promise<ApiResponse<Project>> => {
    return request.get(`/project/load/${id}`)
  },

  // 根据编码查询项目
  loadByCode: (code: string): Promise<ApiResponse<Project>> => {
    return request.get(`/project/loadByCode/${code}`)
  },

  // 查询可访问项目列表
  findAccessibleProjects: (): Promise<ApiResponse<Project[]>> => {
    return request.get('/project/accessible')
  },

  // 查询公共项目列表
  findPublicProjects: (): Promise<ApiResponse<Project[]>> => {
    return request.get('/project/public')
  },

  // 查询用户参与的项目
  findUserProjects: (): Promise<ApiResponse<Project[]>> => {
    return request.get('/project/user')
  },

  // 更新项目状态
  updateStatus: (id: number, status: number): Promise<ApiResponse<string>> => {
    return request.post(`/project/updateStatus/${id}`, null, {
      params: { status }
    })
  },

  // 更新项目类型
  updateType: (id: number, type: string): Promise<ApiResponse<string>> => {
    return request.post(`/project/updateType/${id}`, null, {
      params: { type }
    })
  },

  // 检查项目访问权限
  hasAccess: (id: number): Promise<ApiResponse<boolean>> => {
    return request.get(`/project/hasAccess/${id}`)
  },

  // 检查是否为项目管理员
  isAdmin: (id: number): Promise<ApiResponse<boolean>> => {
    return request.get(`/project/isAdmin/${id}`)
  },

  // 获取用户项目角色
  getUserRole: (id: number): Promise<ApiResponse<string>> => {
    return request.get(`/project/userRole/${id}`)
  },

  // 获取项目类型列表
  getProjectTypes: (): Promise<ApiResponse<ProjectTypeOption[]>> => {
    return request.get('/project/types')
  },

  // 查询启用的项目列表
  findAllEnabled: (): Promise<ApiResponse<Project[]>> => {
    return request.get('/project/enabled')
  },

  // 根据类型查询项目
  findByType: (type: string): Promise<ApiResponse<Project[]>> => {
    return request.get(`/project/byType/${type}`)
  }
}
