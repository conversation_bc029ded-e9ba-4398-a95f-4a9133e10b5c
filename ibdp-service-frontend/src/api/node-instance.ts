import request from './request'

/**
 * 节点实例API
 */
export const nodeInstanceApi = {
  /**
   * 分页查询节点实例
   */
  pageList(params: {
    page: number
    size: number
    workflowInstanceId?: number
    workflowId?: number
    nodeId?: number
    nodeName?: string
    nodeType?: string
    status?: number
    startTime?: string
    endTime?: string
  }) {
    return request.get('/api/node-instance/pageList', { params })
  },

  /**
   * 查询节点实例详情
   */
  getById(id: number) {
    return request.get(`/api/node-instance/${id}`)
  },

  /**
   * 根据工作流实例ID查询节点实例
   */
  getByWorkflowInstanceId(workflowInstanceId: number) {
    return request.get(`/api/node-instance/workflow-instance/${workflowInstanceId}`)
  },

  /**
   * 重试节点实例
   */
  retryNodeInstance(id: number) {
    return request.post(`/api/node-instance/retry/${id}`)
  },

  /**
   * 停止节点实例
   */
  stopNodeInstance(id: number) {
    return request.post(`/api/node-instance/stop/${id}`)
  },

  /**
   * 查询节点实例日志
   */
  getNodeInstanceLog(id: number) {
    return request.get(`/api/node-instance/${id}/log`)
  },

  /**
   * 查询节点统计信息
   */
  getNodeStats(nodeId: number, days: number = 7) {
    return request.get(`/api/node-instance/stats/${nodeId}`, {
      params: { days }
    })
  },

  /**
   * 查询节点的最近实例
   */
  getRecentNodeInstances(nodeId: number, limit: number = 10) {
    return request.get(`/api/node-instance/recent/${nodeId}`, {
      params: { limit }
    })
  }
}

export default nodeInstanceApi
