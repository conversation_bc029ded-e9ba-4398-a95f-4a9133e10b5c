import request from '@/api/request'

// 告警模板相关接口
export const alertTemplateApi = {
  // 获取模板列表
  getList: (params: any) => {
    return request({
      url: '/api/alert/template/list',
      method: 'get',
      params
    })
  },

  // 获取模板详情
  getDetail: (id: number) => {
    return request({
      url: `/api/alert/template/detail/${id}`,
      method: 'get'
    })
  },

  // 保存模板
  save: (data: any) => {
    return request({
      url: '/api/alert/template/save',
      method: 'post',
      data
    })
  },

  // 更新模板
  update: (data: any) => {
    return request({
      url: '/api/alert/template/update',
      method: 'put',
      data
    })
  },

  // 删除模板
  delete: (id: number) => {
    return request({
      url: `/api/alert/template/delete/${id}`,
      method: 'delete'
    })
  },

  // 批量删除模板
  batchDelete: (ids: number[]) => {
    return request({
      url: '/api/alert/template/batchDelete',
      method: 'delete',
      data: ids
    })
  },

  // 更新模板状态
  updateStatus: (id: number, enabled: number) => {
    return request({
      url: '/api/alert/template/updateEnabled',
      method: 'put',
      params: { id, enabled }
    })
  },

  // 预览模板
  preview: (templateCode: string, channelType: string, variables: any) => {
    return request({
      url: '/api/alert/template/preview',
      method: 'post',
      params: { templateCode, channelType },
      data: variables
    })
  },

  // 验证模板
  validate: (templateContent: string, variables?: string) => {
    return request({
      url: '/api/alert/template/validate',
      method: 'post',
      params: { templateContent, variables }
    })
  }
}

// 告警通道相关接口
export const alertChannelApi = {
  // 获取通道配置列表
  getList: (params: any) => {
    return request({
      url: '/api/alert/channel/list',
      method: 'get',
      params
    })
  },

  // 获取通道配置详情
  getDetail: (id: number) => {
    return request({
      url: `/api/alert/channel/detail/${id}`,
      method: 'get'
    })
  },

  // 保存通道配置
  save: (data: any) => {
    return request({
      url: '/api/alert/channel/save',
      method: 'post',
      data
    })
  },

  // 更新通道配置
  update: (data: any) => {
    return request({
      url: '/api/alert/channel/update',
      method: 'put',
      data
    })
  },

  // 删除通道配置
  delete: (id: number) => {
    return request({
      url: `/api/alert/channel/delete/${id}`,
      method: 'delete'
    })
  },

  // 批量删除通道配置
  batchDelete: (ids: number[]) => {
    return request({
      url: '/api/alert/channel/batchDelete',
      method: 'delete',
      data: ids
    })
  },

  // 更新通道状态
  updateStatus: (id: number, enabled: number) => {
    return request({
      url: '/api/alert/channel/updateEnabled',
      method: 'put',
      params: { id, enabled }
    })
  },

  // 测试发送
  testSend: (id: number) => {
    return request({
      url: `/api/alert/channel/testSend/${id}`,
      method: 'post'
    })
  },

  // 测试发送（指定收件人）
  testSendWithRecipients: (id: number, recipients: string[]) => {
    return request({
      url: `/api/alert/channel/testSend/${id}`,
      method: 'post',
      data: { recipients }
    })
  },

  // 获取支持的通道类型
  getSupportedTypes: () => {
    return request({
      url: '/api/alert/channel/supportedTypes',
      method: 'get'
    })
  },

  // 获取通道信息
  getChannelInfo: (channelType: string) => {
    return request({
      url: '/api/alert/channel/channelInfo',
      method: 'get',
      params: { channelType }
    })
  }
}

// 告警发送记录相关接口
export const alertRecordApi = {
  // 获取发送记录列表
  getList: (params: any) => {
    return request({
      url: '/api/alert/record/list',
      method: 'get',
      params
    })
  },

  // 获取发送记录详情
  getDetail: (id: number) => {
    return request({
      url: `/api/alert/record/detail/${id}`,
      method: 'get'
    })
  },

  // 根据请求ID查询记录
  getByRequestId: (requestId: string) => {
    return request({
      url: '/api/alert/record/listByRequestId',
      method: 'get',
      params: { requestId }
    })
  },

  // 根据业务ID查询记录
  getByBusinessId: (businessId: string) => {
    return request({
      url: '/api/alert/record/listByBusinessId',
      method: 'get',
      params: { businessId }
    })
  },

  // 批量删除记录
  batchDelete: (ids: number[]) => {
    return request({
      url: '/api/alert/record/batchDelete',
      method: 'delete',
      data: ids
    })
  },

  // 按时间范围删除记录
  deleteByTimeRange: (startTime: string, endTime: string) => {
    return request({
      url: '/api/alert/record/deleteByTimeRange',
      method: 'delete',
      params: { startTime, endTime }
    })
  },

  // 获取统计信息
  getStats: (hours: number = 24) => {
    return request({
      url: '/api/alert/record/stats',
      method: 'get',
      params: { hours }
    })
  },

  // 按状态统计
  getStatsByStatus: (startTime?: string, endTime?: string) => {
    return request({
      url: '/api/alert/record/statsByStatus',
      method: 'get',
      params: { startTime, endTime }
    })
  },

  // 按通道类型统计
  getStatsByChannelType: (startTime?: string, endTime?: string) => {
    return request({
      url: '/api/alert/record/statsByChannelType',
      method: 'get',
      params: { startTime, endTime }
    })
  },

  // 获取成功率统计
  getSuccessRateStats: (startTime?: string, endTime?: string) => {
    return request({
      url: '/api/alert/record/successRateStats',
      method: 'get',
      params: { startTime, endTime }
    })
  }
}

// 告警发送相关接口
export const alertSendApi = {
  // 发送告警
  send: (data: any) => {
    return request({
      url: '/api/alert/send',
      method: 'post',
      data
    })
  },

  // 批量发送告警
  sendBatch: (data: any[]) => {
    return request({
      url: '/api/alert/send/batch',
      method: 'post',
      data
    })
  },

  // 查询告警状态
  getStatus: (requestId: string) => {
    return request({
      url: `/api/alert/status/${requestId}`,
      method: 'get'
    })
  },

  // 重试告警
  retry: (recordId: number) => {
    return request({
      url: `/api/alert/retry/${recordId}`,
      method: 'post'
    })
  },

  // 取消告警
  cancel: (requestId: string) => {
    return request({
      url: `/api/alert/cancel/${requestId}`,
      method: 'post'
    })
  },

  // 测试告警
  test: (data: any) => {
    return request({
      url: '/api/alert/test',
      method: 'post',
      data
    })
  },

  // 获取告警统计
  getStats: (hours: number = 24) => {
    return request({
      url: '/api/alert/stats',
      method: 'get',
      params: { hours }
    })
  },

  // 处理重试队列
  processRetryQueue: () => {
    return request({
      url: '/api/alert/retry/process',
      method: 'post'
    })
  },

  // 清理历史记录
  cleanHistory: (days: number = 30) => {
    return request({
      url: '/api/alert/clean',
      method: 'post',
      params: { days }
    })
  },

  // 健康检查
  healthCheck: () => {
    return request({
      url: '/api/alert/health',
      method: 'get'
    })
  }
}

// 导出便捷方法
export const getTemplateList = alertTemplateApi.getList
export const deleteTemplate = alertTemplateApi.delete
export const updateTemplateStatus = alertTemplateApi.updateStatus

export const getChannelList = alertChannelApi.getList
export const deleteChannel = alertChannelApi.delete
export const updateChannelStatus = alertChannelApi.updateStatus

export const getRecordList = alertRecordApi.getList
export const deleteRecord = alertRecordApi.batchDelete
