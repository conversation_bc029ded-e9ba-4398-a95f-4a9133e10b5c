import axios from 'axios'
import type { AxiosInstance, InternalAxiosRequestConfig, AxiosResponse } from 'axios'
import { ElMessage } from 'element-plus'
import type { ApiResponse } from '@/types'
import router from '@/router'

// 处理认证失败
const handleAuthFailure = async () => {
  // 动态导入用户Store避免循环依赖
  const { useUserStore } = await import('@/stores/user')
  const userStore = useUserStore()

  // 清除用户数据
  userStore.clearUserData()

  // 跳转到登录页面，避免重复跳转
  if (router.currentRoute.value.path !== '/login') {
    router.push('/login')
  }
}

// 创建axios实例
const request: AxiosInstance = axios.create({
  baseURL: '/ibdp',
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json;charset=UTF-8'
  },
  // 禁止自动跟随重定向，这样我们可以捕获302状态码
  maxRedirects: 0
})

// 请求拦截器
request.interceptors.request.use(
  (config: InternalAxiosRequestConfig) => {
    // 在这里可以添加token等认证信息
    // const token = localStorage.getItem('token')
    // if (token) {
    //   config.headers!.Authorization = `Bearer ${token}`
    // }
    return config
  },
  (error) => {
    console.error('请求错误:', error)
    return Promise.reject(error)
  }
)

// 响应拦截器
request.interceptors.response.use(
  (response: AxiosResponse<ApiResponse>) => {
    const { data } = response

    // 检查是否是重定向到登录页面的HTML响应
    if (typeof data === 'string' && data.includes('toLogin')) {
      handleAuthFailure()
      return Promise.reject(new Error('会话已过期，请重新登录'))
    }

    // 检查是否是有效的JSON响应
    if (typeof data !== 'object' || data === null) {
      handleAuthFailure()
      return Promise.reject(new Error('认证失败，请重新登录'))
    }

    // 检查业务状态码
    if (data.code === 200) {
      return data as any
    } else if (data.code === undefined) {
      // XXL-JOB接口直接返回数据，没有code字段，包装为标准格式
      console.log('✅ XXL-JOB直接响应，包装为标准格式:', data)
      return {
        code: 200,
        content: data,
        msg: 'success'
      } as any
    } else {
      // 业务错误
      ElMessage.error(data.msg || '请求失败')
      return Promise.reject(new Error(data.msg || '请求失败'))
    }
  },
  (error) => {
    console.error('响应错误:', error)
    
    let message = '网络错误'
    if (error.response) {
      const { status, data } = error.response
      switch (status) {
        case 400:
          message = data?.msg || '请求参数错误'
          break
        case 401:
          message = '未授权，请重新登录'
          // 处理登录跳转
          handleAuthFailure()
          break
        case 302:
          // 处理后端重定向到登录页面的情况
          message = '会话已过期，请重新登录'
          handleAuthFailure()
          break
        case 403:
          message = '拒绝访问'
          break
        case 404:
          message = '请求地址不存在'
          break
        case 500:
          message = '服务器内部错误'
          break
        default:
          message = data?.msg || `连接错误${status}`
      }
    } else if (error.code === 'ECONNABORTED') {
      message = '请求超时'
    } else if (error.message) {
      message = error.message
    }
    
    ElMessage.error(message)
    return Promise.reject(error)
  }
)

export default request
