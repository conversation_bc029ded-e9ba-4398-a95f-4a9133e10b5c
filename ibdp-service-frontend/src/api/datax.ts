import { request } from '@/utils/request'

/**
 * DataX增量同步相关API
 */

// 增量配置相关
export const dataxConfigApi = {
  // 保存增量配置
  saveConfig: (data: any) => {
    return request.post('/api/datax/incremental-config/save', data)
  },

  // 获取节点的增量配置
  getNodeConfig: (nodeId: number) => {
    return request.get(`/api/datax/incremental-config/node/${nodeId}`)
  },

  // 测试增量配置
  testConfig: (data: any) => {
    return request.post('/api/datax/incremental-config/test', data)
  },

  // 生成WHERE条件预览
  previewCondition: (data: any) => {
    return request.post('/api/datax/incremental-config/preview', data)
  },

  // 删除增量配置
  deleteConfig: (nodeId: number) => {
    return request.delete(`/api/datax/incremental-config/node/${nodeId}`)
  }
}

// 同步状态相关
export const syncStateApi = {
  // 分页查询同步状态
  getPage: (params: any) => {
    return request.get('/api/datax/sync-state/page', { params })
  },

  // 获取统计信息
  getStatistics: () => {
    return request.get('/api/datax/sync-state/statistics')
  },

  // 根据节点ID获取同步状态
  getByNodeId: (nodeId: number) => {
    return request.get(`/api/datax/sync-state/node/${nodeId}`)
  },

  // 重置同步状态
  reset: (id: number) => {
    return request.post(`/api/datax/sync-state/reset/${id}`)
  },

  // 禁用同步状态
  disable: (id: number) => {
    return request.post(`/api/datax/sync-state/disable/${id}`)
  },

  // 启用同步状态
  enable: (id: number) => {
    return request.post(`/api/datax/sync-state/enable/${id}`)
  },

  // 删除同步状态
  delete: (id: number) => {
    return request.delete(`/api/datax/sync-state/${id}`)
  },

  // 批量启用
  batchEnable: (ids: number[]) => {
    return request.post('/api/datax/sync-state/batch-enable', { ids })
  },

  // 批量禁用
  batchDisable: (ids: number[]) => {
    return request.post('/api/datax/sync-state/batch-disable', { ids })
  },

  // 批量删除
  batchDelete: (ids: number[]) => {
    return request.post('/api/datax/sync-state/batch-delete', { ids })
  }
}

// 同步日志相关
export const syncLogApi = {
  // 分页查询同步日志
  getPage: (params: any) => {
    return request.get('/api/datax/sync-log/page', { params })
  },

  // 根据同步状态ID获取日志列表
  getBySyncStateId: (syncStateId: number) => {
    return request.get('/api/datax/sync-log/list', { 
      params: { syncStateId } 
    })
  },

  // 获取日志详情
  getDetail: (id: number) => {
    return request.get(`/api/datax/sync-log/${id}`)
  },

  // 删除日志
  delete: (id: number) => {
    return request.delete(`/api/datax/sync-log/${id}`)
  },

  // 批量删除日志
  batchDelete: (ids: number[]) => {
    return request.post('/api/datax/sync-log/batch-delete', { ids })
  },

  // 清理过期日志
  cleanExpired: (days: number) => {
    return request.post('/api/datax/sync-log/clean-expired', { days })
  }
}

// 数据源相关（复用现有API）
export const datasourceApi = {
  // 获取数据源列表
  getList: () => {
    return request.get('/api/datasource/list')
  },

  // 获取表列表
  getTables: (datasourceId: number) => {
    return request.get(`/api/datasource/${datasourceId}/tables`)
  },

  // 获取表字段
  getColumns: (datasourceId: number, tableName: string) => {
    return request.get(`/api/datasource/${datasourceId}/table/${tableName}/columns`)
  },

  // 测试连接
  testConnection: (datasourceId: number) => {
    return request.post(`/api/datasource/${datasourceId}/test`)
  }
}

// 工作流相关（复用现有API）
export const workflowApi = {
  // 创建工作流
  create: (data: any) => {
    return request.post('/api/workflow/create', data)
  },

  // 执行工作流
  execute: (data: any) => {
    return request.post('/api/workflow/execute', data)
  },

  // 获取工作流列表
  getList: () => {
    return request.get('/api/workflow/list')
  },

  // 获取工作流详情
  getDetail: (id: number) => {
    return request.get(`/api/workflow/${id}`)
  },

  // 获取执行历史
  getExecutionHistory: (workflowId: number, params?: any) => {
    return request.get(`/api/workflow/${workflowId}/executions`, { params })
  }
}

// 变量相关
export const variableApi = {
  // 解析变量
  resolve: (data: any) => {
    return request.post('/api/datax/variable/resolve', data)
  },

  // 获取变量列表
  getList: () => {
    return request.get('/api/datax/variable/list')
  },

  // 验证变量表达式
  validate: (expression: string) => {
    return request.post('/api/datax/variable/validate', { expression })
  }
}

// 监控相关
export const monitorApi = {
  // 获取实时监控数据
  getRealTimeData: () => {
    return request.get('/api/datax/monitor/realtime')
  },

  // 获取性能指标
  getPerformanceMetrics: (params: any) => {
    return request.get('/api/datax/monitor/performance', { params })
  },

  // 获取健康检查结果
  getHealthCheck: () => {
    return request.get('/api/datax/monitor/health')
  }
}

// 导出所有API
export default {
  config: dataxConfigApi,
  syncState: syncStateApi,
  syncLog: syncLogApi,
  datasource: datasourceApi,
  workflow: workflowApi,
  variable: variableApi,
  monitor: monitorApi
}
