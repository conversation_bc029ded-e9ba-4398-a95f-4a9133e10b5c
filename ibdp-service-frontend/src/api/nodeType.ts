import request from './request'
import type { NodeType, NodeTypeQuery } from '@/types/nodeType'

/**
 * 节点类型管理API
 */
export const nodeTypeApi = {
  /**
   * 分页查询节点类型列表
   */
  pageList(params: NodeTypeQuery) {
    return request.get('/node-type/pageList', { params })
  },

  /**
   * 查询所有启用的节点类型
   */
  list() {
    return request.get('/node-type/list')
  },

  /**
   * 根据分类查询节点类型
   */
  listByCategory(category: string) {
    return request.get('/node-type/listByCategory', {
      params: { category }
    })
  },

  /**
   * 获取节点类型详情
   */
  getById(id: number) {
    return request.get(`/node-type/${id}`)
  },

  /**
   * 根据类型编码获取节点类型
   */
  getByCode(typeCode: string) {
    return request.get(`/node-type/getByCode/${typeCode}`)
  },

  /**
   * 添加节点类型
   */
  add(data: NodeType) {
    return request.post('/node-type/add', data)
  },

  /**
   * 更新节点类型
   */
  update(data: NodeType) {
    return request.post('/node-type/update', data)
  },

  /**
   * 删除节点类型
   */
  delete(id: number) {
    return request.post('/node-type/delete', null, {
      params: { id }
    })
  },

  /**
   * 启用/禁用节点类型
   */
  updateStatus(id: number, status: number) {
    return request.post('/node-type/updateStatus', null, {
      params: { id, status }
    })
  },

  /**
   * 获取节点分类列表
   */
  getCategoryList() {
    return request.get('/node-type/categoryList')
  }
}
