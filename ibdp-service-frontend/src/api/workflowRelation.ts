import request from './request'
import type { WorkflowRelation } from '@/types/workflowNode'

/**
 * 工作流节点关系管理API
 */
export const workflowRelationApi = {
  /**
   * 根据工作流ID查询所有节点关系
   */
  listByWorkflow(workflowId: number) {
    return request.get('/workflow-relation/listByWorkflow', {
      params: { workflowId }
    })
  },

  /**
   * 根据ID获取关系详情
   */
  getById(id: string) {
    return request.get(`/workflow-relation/${id}`)
  },

  /**
   * 根据前置节点查询关系
   */
  listByPreNode(workflowId: number, preNodeCode: string) {
    return request.get('/workflow-relation/listByPreNode', {
      params: { workflowId, preNodeCode }
    })
  },

  /**
   * 根据后置节点查询关系
   */
  listByPostNode(workflowId: number, postNodeCode: string) {
    return request.get('/workflow-relation/listByPostNode', {
      params: { workflowId, postNodeCode }
    })
  },

  /**
   * 添加工作流节点关系
   */
  add(data: WorkflowRelation) {
    return request.post('/workflow-relation/add', data)
  },

  /**
   * 批量添加工作流节点关系
   */
  batchAdd(data: WorkflowRelation[]) {
    return request.post('/workflow-relation/batchAdd', data)
  },

  /**
   * 更新工作流节点关系
   */
  update(data: WorkflowRelation) {
    return request.post('/workflow-relation/update', data)
  },

  /**
   * 批量更新工作流节点关系
   */
  batchUpdate(data: WorkflowRelation[]) {
    return request.post('/workflow-relation/batchUpdate', data)
  },

  /**
   * 删除工作流节点关系
   */
  delete(id: string) {
    return request.post('/workflow-relation/delete', null, {
      params: { id }
    })
  },

  /**
   * 批量删除工作流节点关系
   */
  batchDelete(ids: string[]) {
    return request.post('/workflow-relation/batchDelete', { ids })
  },

  /**
   * 根据工作流ID删除所有关系
   */
  deleteByWorkflow(workflowId: number) {
    return request.post('/workflow-relation/deleteByWorkflow', null, {
      params: { workflowId }
    })
  },

  /**
   * 根据节点删除相关关系
   */
  deleteByNode(workflowId: number, nodeCode: string) {
    return request.post('/workflow-relation/deleteByNode', null, {
      params: { workflowId, nodeCode }
    })
  },

  /**
   * 保存工作流的所有关系（先删除再新增）
   */
  saveWorkflowRelations(workflowId: number, relations: WorkflowRelation[]) {
    return request.post('/workflow-relation/saveWorkflowRelations', {
      workflowId,
      relations
    })
  },

  /**
   * 检查是否存在循环依赖
   */
  checkCircularDependency(workflowId: number, preNodeCode: string, postNodeCode: string) {
    return request.get('/workflow-relation/checkCircularDependency', {
      params: { workflowId, preNodeCode, postNodeCode }
    })
  },

  /**
   * 获取节点的所有上游依赖
   */
  getUpstreamNodes(workflowId: number, nodeCode: string) {
    return request.get('/workflow-relation/getUpstreamNodes', {
      params: { workflowId, nodeCode }
    })
  },

  /**
   * 获取节点的所有下游依赖
   */
  getDownstreamNodes(workflowId: number, nodeCode: string) {
    return request.get('/workflow-relation/getDownstreamNodes', {
      params: { workflowId, nodeCode }
    })
  },

  /**
   * 验证关系是否已存在
   */
  validateRelation(workflowId: number, preNodeCode: string, postNodeCode: string) {
    return request.get('/workflow-relation/validateRelation', {
      params: { workflowId, preNodeCode, postNodeCode }
    })
  }
}
