import request from './request'
import type { ApiResponse, PageParams, PageResponse, ProjectMember, ProjectRoleOption } from '@/types'

// 项目成员管理API
export const projectMemberApi = {
  // 分页查询项目成员列表
  pageList: (params: PageParams & {
    projectId?: number
    username?: string
    role?: string
    status?: number
  }): Promise<ApiResponse<PageResponse<ProjectMember>>> => {
    return request.get('/project/member/pageList', { params })
  },

  // 添加项目成员
  add: (data: ProjectMember): Promise<ApiResponse<string>> => {
    return request.post('/project/member/add', data)
  },

  // 批量添加项目成员
  batchAdd: (data: ProjectMember[]): Promise<ApiResponse<string>> => {
    return request.post('/project/member/batchAdd', data)
  },

  // 移除项目成员
  remove: (id: number): Promise<ApiResponse<string>> => {
    return request.post(`/project/member/remove/${id}`)
  },

  // 根据项目和用户移除成员
  removeByUser: (projectId: number, userId: string): Promise<ApiResponse<string>> => {
    return request.post('/project/member/removeByUser', null, {
      params: { projectId, userId }
    })
  },

  // 更新成员角色
  updateRole: (id: number, role: string): Promise<ApiResponse<string>> => {
    return request.post(`/project/member/updateRole/${id}`, null, {
      params: { role }
    })
  },

  // 更新成员状态
  updateStatus: (id: number, status: number): Promise<ApiResponse<string>> => {
    return request.post(`/project/member/updateStatus/${id}`, null, {
      params: { status }
    })
  },

  // 查询项目成员详情
  load: (id: number): Promise<ApiResponse<ProjectMember>> => {
    return request.get(`/project/member/load/${id}`)
  },

  // 根据项目和用户查询成员
  loadByProjectAndUser: (projectId: number, userId: string): Promise<ApiResponse<ProjectMember>> => {
    return request.get('/project/member/loadByProjectAndUser', {
      params: { projectId, userId }
    })
  },

  // 查询项目成员列表
  findByProjectId: (projectId: number): Promise<ApiResponse<ProjectMember[]>> => {
    return request.get(`/project/member/byProject/${projectId}`)
  },

  // 查询用户参与的项目
  findByUserId: (userId: string): Promise<ApiResponse<ProjectMember[]>> => {
    return request.get(`/project/member/byUser/${userId}`)
  },

  // 查询项目管理员
  findAdmins: (projectId: number): Promise<ApiResponse<ProjectMember[]>> => {
    return request.get(`/project/member/admins/${projectId}`)
  },

  // 检查是否为项目成员
  isMember: (projectId: number, userId: string): Promise<ApiResponse<boolean>> => {
    return request.get('/project/member/isMember', {
      params: { projectId, userId }
    })
  },

  // 统计项目成员数量
  count: (projectId: number): Promise<ApiResponse<number>> => {
    return request.get(`/project/member/count/${projectId}`)
  },

  // 获取项目角色列表
  getProjectRoles: (): Promise<ApiResponse<ProjectRoleOption[]>> => {
    return request.get('/project/member/roles')
  }
}
