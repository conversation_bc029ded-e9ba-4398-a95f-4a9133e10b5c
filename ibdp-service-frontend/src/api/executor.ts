import request from './request'

// 执行器信息接口（基于JobGroup）
export interface ExecutorInfo {
  id: number
  appname: string
  title: string
  addressType: number
  addressList: string | null
  updateTime: string
  registryList: string[] | null
}

// 执行器统计信息接口
export interface ExecutorStats {
  totalCount: number
  onlineCount: number
  offlineCount: number
}

// 执行器分页查询参数
export interface ExecutorPageParams {
  start?: number
  length?: number
  appname?: string
  title?: string
}

// XXL-JOB执行器分页响应（直接返回，不包装在ApiResponse中）
export interface ExecutorPageResponse {
  recordsTotal: number
  recordsFiltered: number
  data: ExecutorInfo[]
}

// 直接调用XXL-JOB接口的API函数
export const getExecutorList = async () => {
  try {
    const response = await request.get('/jobgroup/pageList', {
      params: {
        start: 0,
        length: 1000,
        appname: '',
        title: ''
      }
    })

    // 处理响应数据
    let executorData = []

    if ((response as any).content && (response as any).content.data) {
      // 封装格式：{code: 200, content: {recordsFiltered: 1, data: [...]}}
      executorData = (response as any).content.data
    } else if ((response as any).data) {
      // 直接格式：{recordsFiltered: 1, data: [...]}
      executorData = (response as any).data
    }

    return {
      data: executorData
    }

  } catch (error) {
    console.error('获取执行器列表失败:', error)
    throw error
  }
}

export const getExecutorStats = async () => {
  try {
    const { data: executors } = await getExecutorList()

    const totalCount = executors.length
    const onlineCount = executors.filter((executor: ExecutorInfo) =>
      executor.registryList && executor.registryList.length > 0
    ).length
    const offlineCount = totalCount - onlineCount

    return {
      data: {
        totalCount,
        onlineCount,
        offlineCount
      }
    }
  } catch (error) {
    console.error('获取统计信息失败:', error)
    throw error
  }
}
export const deleteExecutor = async (id: number) => {
  try {
    return await request.post('/jobgroup/remove', null, {
      params: {id}
    })
  } catch (error) {
    console.error('删除执行器失败:', error)
    throw error
  }
}

export const updateExecutor = async (data: ExecutorInfo) => {
  try {
    return await request.post('/jobgroup/update', data)
  } catch (error) {
    console.error('更新执行器失败:', error)
    throw error
  }
}

export const createExecutor = async (data: Omit<ExecutorInfo, 'id' | 'updateTime' | 'registryList'>) => {
  try {
    return await request.post('/jobgroup/save', data)
  } catch (error) {
    console.error('创建执行器失败:', error)
    throw error
  }
}


