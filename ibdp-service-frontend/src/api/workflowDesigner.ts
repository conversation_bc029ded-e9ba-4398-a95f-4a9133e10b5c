import request from './request'
import type { WorkflowNode, WorkflowRelation } from '@/types/workflowNode'

/**
 * 工作流设计器数据管理API
 * 提供工作流设计器所需的综合数据操作接口
 */
export const workflowDesignerApi = {
  /**
   * 加载工作流设计数据（节点+关系）
   */
  loadWorkflowDesign(workflowId: number) {
    return request.get('/workflow-designer/loadDesign', {
      params: { workflowId }
    })
  },

  /**
   * 保存工作流设计数据（节点+关系）
   * 使用事务确保数据一致性
   */
  saveWorkflowDesign(data: {
    workflowId: number
    nodes: WorkflowNode[]
    relations: WorkflowRelation[]
  }) {
    return request.post('/workflow-designer/saveDesign', data)
  },

  /**
   * 增量保存工作流设计数据
   * 只保存有变更的数据
   */
  saveWorkflowDesignIncremental(data: {
    workflowId: number
    addedNodes: WorkflowNode[]
    updatedNodes: WorkflowNode[]
    deletedNodeIds: string[]
    addedRelations: WorkflowRelation[]
    updatedRelations: WorkflowRelation[]
    deletedRelationIds: string[]
  }) {
    return request.post('/workflow-designer/saveDesignIncremental', data)
  },

  /**
   * 验证工作流设计
   * 检查节点配置、循环依赖等
   */
  validateWorkflowDesign(workflowId: number) {
    return request.get('/workflow-designer/validateDesign', {
      params: { workflowId }
    })
  },

  /**
   * 获取工作流的DAG结构
   */
  getWorkflowDAG(workflowId: number) {
    return request.get('/workflow-designer/getDAG', {
      params: { workflowId }
    })
  },

  /**
   * 复制工作流设计
   */
  copyWorkflowDesign(sourceWorkflowId: number, targetWorkflowId: number) {
    return request.post('/workflow-designer/copyDesign', {
      sourceWorkflowId,
      targetWorkflowId
    })
  },

  /**
   * 清空工作流设计
   */
  clearWorkflowDesign(workflowId: number) {
    return request.post('/workflow-designer/clearDesign', null, {
      params: { workflowId }
    })
  },

  /**
   * 获取工作流设计统计信息
   */
  getWorkflowDesignStats(workflowId: number) {
    return request.get('/workflow-designer/getStats', {
      params: { workflowId }
    })
  },

  /**
   * 导出工作流设计
   */
  exportWorkflowDesign(workflowId: number) {
    return request.get('/workflow-designer/export', {
      params: { workflowId }
    })
  },

  /**
   * 导入工作流设计
   */
  importWorkflowDesign(workflowId: number, designData: any) {
    return request.post('/workflow-designer/import', {
      workflowId,
      designData
    })
  }
}
