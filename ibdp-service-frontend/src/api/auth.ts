import request from './request'
import type { ApiResponse } from '@/types'

// 登录请求参数
export interface LoginParams {
  userName: string
  password: string
  ifRemember?: boolean
}

// 用户信息
export interface UserInfo {
  id: number
  username: string
  password?: string
  role: number
  permission?: string
}

export const authApi = {
  // 用户登录
  login(params: LoginParams): Promise<ApiResponse<string>> {
    const formData = new URLSearchParams()
    formData.append('userName', params.userName)
    formData.append('password', params.password)
    if (params.ifRemember) {
      formData.append('ifRemember', 'on')
    }

    return request.post('/login', formData, {
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded'
      }
    })
  },

  // 用户登出
  logout(): Promise<ApiResponse<string>> {
    return request.post('/logout')
  },

  // 获取当前用户信息
  getCurrentUser(): Promise<ApiResponse<UserInfo>> {
    return request.get('/user/current')
  }
}
