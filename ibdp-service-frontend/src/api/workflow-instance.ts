import request from './request'

/**
 * 工作流实例API
 */
export const workflowInstanceApi = {
  /**
   * 分页查询工作流实例
   */
  pageList(params: {
    page: number
    size: number
    workflowId?: number
    workflowName?: string
    status?: number
    triggerType?: string
    startTime?: string
    endTime?: string
  }) {
    return request.get('/api/workflow-instance/pageList', { params })
  },

  /**
   * 查询工作流实例详情
   */
  getById(id: number) {
    return request.get(`/api/workflow-instance/${id}`)
  },

  /**
   * 停止工作流实例
   */
  stopInstance(id: number) {
    return request.post(`/api/workflow-instance/stop/${id}`)
  },

  /**
   * 重试工作流实例
   */
  retryInstance(id: number) {
    return request.post(`/api/workflow-instance/retry/${id}`)
  },

  /**
   * 查询工作流实例统计信息
   */
  getInstanceStats(workflowId: number, days: number = 7) {
    return request.get(`/api/workflow-instance/stats/${workflowId}`, {
      params: { days }
    })
  },

  /**
   * 查询工作流的最近实例
   */
  getRecentInstances(workflowId: number, limit: number = 10) {
    return request.get(`/api/workflow-instance/recent/${workflowId}`, {
      params: { limit }
    })
  },

  /**
   * 删除工作流实例
   */
  deleteInstance(id: number) {
    return request.delete(`/api/workflow-instance/${id}`)
  },

  /**
   * 批量删除工作流实例
   */
  batchDeleteInstances(ids: number[]) {
    return request.delete('/api/workflow-instance/batch', { data: ids })
  }
}

export default workflowInstanceApi
