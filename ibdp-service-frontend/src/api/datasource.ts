import request from './request'
import type { ApiResponse, PageResponse, Datasource, DatasourceTypeOption } from '@/types'

// 数据源API接口
export const datasourceApi = {
  // 分页查询数据源列表
  pageList(params: {
    pageNum: number
    pageSize: number
    name?: string
    type?: string
    status?: number
  }): Promise<ApiResponse<PageResponse<Datasource>>> {
    return request.get('/datasource/pageList', { params })
  },

  // 查询所有启用的数据源
  list(): Promise<ApiResponse<Datasource[]>> {
    return request.get('/datasource/list')
  },

  // 根据ID查询数据源详情
  load(id: number): Promise<ApiResponse<Datasource>> {
    return request.get(`/datasource/load/${id}`)
  },

  // 新增数据源
  add(data: Datasource): Promise<ApiResponse<string>> {
    return request.post('/datasource/add', data)
  },

  // 更新数据源
  update(data: Datasource): Promise<ApiResponse<string>> {
    return request.post('/datasource/update', data)
  },

  // 删除数据源
  delete(id: number): Promise<ApiResponse<string>> {
    return request.post(`/datasource/delete/${id}`)
  },

  // 测试数据源连接
  testConnection(id: number): Promise<ApiResponse<string>> {
    return request.post(`/datasource/test/${id}`)
  },

  // 仅测试连接（不保存结果）
  testConnectionOnly(data: Datasource): Promise<ApiResponse<string>> {
    return request.post('/datasource/testOnly', data)
  },

  // 获取数据源类型列表
  getTypes(): Promise<ApiResponse<DatasourceTypeOption[]>> {
    return request.get('/datasource/types')
  },

  // 获取默认端口
  getDefaultPort(type: string): Promise<ApiResponse<number>> {
    return request.get(`/datasource/defaultPort/${type}`)
  },

  // 获取默认连接参数
  getDefaultParams(type: string): Promise<ApiResponse<string>> {
    return request.get(`/datasource/defaultParams/${type}`)
  },

  // 根据类型查询数据源
  findByType(type: string): Promise<ApiResponse<Datasource[]>> {
    return request.get(`/datasource/findByType/${type}`)
  },

  // 获取数据库列表
  getDatabases(id: number): Promise<ApiResponse<string[]>> {
    return request.get(`/datasource/databases/${id}`)
  },

  // 获取表列表
  getTables(id: number, database: string): Promise<ApiResponse<string[]>> {
    return request.get(`/datasource/tables/${id}`, {
      params: { database }
    })
  },

  // 获取字段列表
  getColumns(id: number, database: string, table: string): Promise<ApiResponse<any[]>> {
    return request.get(`/datasource/columns/${id}`, {
      params: { database, table }
    })
  },

  // 执行SQL查询
  executeQuery(id: number, sql: string, limit: number = 100): Promise<ApiResponse<any>> {
    return request.post(`/datasource/query/${id}`, null, {
      params: { sql, limit }
    })
  }
}
