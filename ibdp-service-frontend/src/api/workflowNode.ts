import request from './request'
import type { WorkflowNode, WorkflowNodeQuery } from '@/types/workflowNode'

/**
 * 工作流节点管理API
 */
export const workflowNodeApi = {
  /**
   * 根据工作流ID查询所有节点
   */
  listByWorkflow(workflowId: number) {
    return request.get('/workflow-node/listByWorkflow', {
      params: { workflowId }
    })
  },

  /**
   * 分页查询工作流节点
   */
  pageList(params: WorkflowNodeQuery) {
    return request.get('/workflow-node/pageList', { params })
  },

  /**
   * 根据ID获取节点详情
   */
  getById(id: string) {
    return request.get(`/workflow-node/${id}`)
  },

  /**
   * 根据工作流ID和节点编码获取节点
   */
  getByCode(workflowId: number, nodeCode: string) {
    return request.get('/workflow-node/getByCode', {
      params: { workflowId, nodeCode }
    })
  },

  /**
   * 添加工作流节点
   */
  add(data: WorkflowNode) {
    return request.post('/workflow-node/add', data)
  },

  /**
   * 批量添加工作流节点
   */
  batchAdd(data: WorkflowNode[]) {
    return request.post('/workflow-node/batchAdd', data)
  },

  /**
   * 更新工作流节点
   */
  update(data: WorkflowNode) {
    return request.post('/workflow-node/update', data)
  },

  /**
   * 批量更新工作流节点
   */
  batchUpdate(data: WorkflowNode[]) {
    return request.post('/workflow-node/batchUpdate', data)
  },

  /**
   * 删除工作流节点
   */
  delete(id: string) {
    return request.post('/workflow-node/delete', null, {
      params: { id }
    })
  },

  /**
   * 批量删除工作流节点
   */
  batchDelete(ids: string[]) {
    return request.post('/workflow-node/batchDelete', { ids })
  },

  /**
   * 根据工作流ID删除所有节点
   */
  deleteByWorkflow(workflowId: number) {
    return request.post('/workflow-node/deleteByWorkflow', null, {
      params: { workflowId }
    })
  },

  /**
   * 保存工作流的所有节点（先删除再新增）
   */
  saveWorkflowNodes(workflowId: number, nodes: WorkflowNode[]) {
    return request.post('/workflow-node/saveWorkflowNodes', {
      workflowId,
      nodes
    })
  },

  /**
   * 更新节点位置
   */
  updatePosition(id: string, positionX: number, positionY: number) {
    return request.post('/workflow-node/updatePosition', {
      id,
      positionX,
      positionY
    })
  },

  /**
   * 批量更新节点位置
   */
  batchUpdatePosition(updates: Array<{ id: string, positionX: number, positionY: number }>) {
    return request.post('/workflow-node/batchUpdatePosition', { updates })
  },

  /**
   * 复制节点
   */
  copy(id: string, newNodeCode: string, newNodeName: string) {
    return request.post('/workflow-node/copy', {
      id,
      newNodeCode,
      newNodeName
    })
  },

  /**
   * 验证节点编码是否唯一
   */
  validateNodeCode(workflowId: number, nodeCode: string, excludeId?: string) {
    return request.get('/workflow-node/validateNodeCode', {
      params: { workflowId, nodeCode, excludeId }
    })
  }
}
