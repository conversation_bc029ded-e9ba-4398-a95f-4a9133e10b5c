/**
 * 节点实例类型定义
 */

/**
 * 节点实例
 */
export interface NodeInstance {
  id: number
  workflowInstanceId: number
  workflowId: number
  nodeId: number
  nodeName: string
  nodeType: string
  status: number
  startTime?: string
  endTime?: string
  duration?: number
  executeParams?: string
  executeResult?: string
  errorMessage?: string
  executeLog?: string
  executorAddress?: string
  retryCount?: number
  maxRetryCount?: number
  executeOrder?: number
  createTime: string
  updateTime: string
}

/**
 * 节点实例状态枚举
 * 对应数据库node_instance.state字段
 */
export enum NodeInstanceStatus {
  WAITING = 0,    // 等待执行
  RUNNING = 1,    // 运行中
  SUCCESS = 2,    // 成功
  FAILED = 3,     // 失败
  SKIPPED = 4,    // 跳过
  TERMINATED = 5  // 终止
}

/**
 * 节点类型枚举
 */
export enum NodeType {
  SQL = 'SQL',
  DATAX = 'DATAX',
  SHELL = 'SHELL',
  PYTHON = 'PYTHON'
}

/**
 * 节点实例查询参数
 */
export interface NodeInstanceQuery {
  page: number
  size: number
  projectId: number
  workflowInstanceId?: number
  workflowId?: number
  nodeId?: number
  nodeName?: string
  nodeType?: string
  status?: number
  startTime?: string
  endTime?: string
}

/**
 * 节点实例统计信息
 */
export interface NodeInstanceStats {
  totalCount: number
  successCount: number
  failedCount: number
  runningCount: number
  successRate: number
  avgDuration: number
}

/**
 * 分页查询结果
 */
export interface NodeInstancePageResult {
  data: NodeInstance[]
  recordsTotal: number
  recordsFiltered: number
}
