// 执行器相关类型定义（基于JobGroup）

export interface ExecutorInfo {
  id: number
  appname: string
  title: string
  addressType: number
  addressList: string | null
  updateTime: string
  registryList: string[] | null
}

export interface ExecutorStats {
  totalCount: number
  onlineCount: number
  offlineCount: number
}

export interface ExecutorCreateForm {
  appname: string
  title: string
  addressType: number
  addressList: string
}

export interface ExecutorUpdateForm {
  title: string
  addressType: number
  addressList: string
}

export interface ExecutorPageParams {
  start?: number
  length?: number
  appname?: string
  title?: string
}

// XXL-JOB执行器分页响应（直接返回，不包装在ApiResponse中）
export interface ExecutorPageResponse {
  recordsTotal: number
  recordsFiltered: number
  data: ExecutorInfo[]
}
