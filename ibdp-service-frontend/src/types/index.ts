// 通用类型定义

// API响应类型
export interface ApiResponse<T = any> {
  code: number
  msg: string
  content: T
}

// 分页参数
export interface PageParams {
  pageNum: number
  pageSize: number
}

// 用户信息
export interface UserInfo {
  id: number
  username: string
  password?: string
  role: number
  permission?: string
}

// 登录参数
export interface LoginParams {
  userName: string
  password: string
  ifRemember?: boolean
}

// 分页响应
export interface PageResponse<T = any> {
  recordsTotal: number
  recordsFiltered: number
  data: T[]
}

// 数据源类型枚举
export enum DatasourceType {
  MYSQL = 'MYSQL',
  POSTGRESQL = 'POSTGRESQL',
  ORACLE = 'ORACLE',
  DORIS = 'DORIS',
  HIVE = 'HIVE'
}

// 数据源状态枚举
export enum DatasourceStatus {
  DISABLED = 0,
  ENABLED = 1
}

// 测试状态枚举
export enum TestStatus {
  NOT_TESTED = 0,
  SUCCESS = 1,
  FAILED = 2
}

// 数据源实体
export interface Datasource {
  id?: number
  name: string
  type: DatasourceType
  host: string
  port: number
  databaseName: string
  username: string
  password: string
  connectionParams?: string
  description?: string
  status: DatasourceStatus
  testStatus?: TestStatus
  testMessage?: string
  testTime?: string
  createTime?: string
  updateTime?: string
  createUser?: string
  updateUser?: string
}

// 数据源类型选项
export interface DatasourceTypeOption {
  code: string
  name: string
  defaultPort: number
  driverClass: string
  urlTemplate: string
}

// 表单验证规则
export interface FormRule {
  required?: boolean
  message?: string
  trigger?: string | string[]
  min?: number
  max?: number
  pattern?: RegExp
  validator?: (rule: any, value: any, callback: any) => void
}

// 菜单项
export interface MenuItem {
  path: string
  name: string
  title: string
  icon?: string
  children?: MenuItem[]
}

// 项目类型枚举
export enum ProjectType {
  PUBLIC = 'PUBLIC',
  PRIVATE = 'PRIVATE'
}

// 项目状态枚举
export enum ProjectStatus {
  DISABLED = 0,
  ENABLED = 1
}

// 项目成员角色枚举
export enum ProjectMemberRole {
  ADMIN = 'ADMIN',
  DEVELOPER = 'DEVELOPER',
  VIEWER = 'VIEWER'
}

// 项目成员状态枚举
export enum ProjectMemberStatus {
  DISABLED = 0,
  ENABLED = 1
}

// 项目实体
export interface Project {
  id?: number
  name: string
  code: string
  type: ProjectType
  description?: string
  status: ProjectStatus
  createTime?: string
  updateTime?: string
  createUser?: string
  updateUser?: string
  deleted?: number
  // 扩展字段
  members?: ProjectMember[]
  memberCount?: number
  currentUserRole?: string
}

// 项目成员实体
export interface ProjectMember {
  id?: number
  projectId: number
  userId: string
  username: string
  role: ProjectMemberRole
  joinTime?: string
  status: ProjectMemberStatus
  createTime?: string
  updateTime?: string
  createUser?: string
  updateUser?: string
  deleted?: number
  // 扩展字段
  projectName?: string
  projectCode?: string
  roleName?: string
}

// 项目类型选项
export interface ProjectTypeOption {
  code: string
  name: string
}

// 项目角色选项
export interface ProjectRoleOption {
  code: string
  name: string
}
