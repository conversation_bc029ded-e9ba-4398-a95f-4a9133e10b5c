/**
 * 工作流相关类型定义
 */

// 工作流实体
export interface Workflow {
  id?: number
  projectId: number
  name: string
  code: string
  description?: string
  tags?: string
  category?: string
  version?: number

  globalParams?: string
  warningType?: string
  warningGroupId?: number
  jobId?: number
  // 调度中心编排相关字段
  lastExecutionId?: string
  executionCount?: number
  successCount?: number
  failureCount?: number
  cronExpression?: string
  lastTriggerTime?: string
  status?: number
  createTime?: string
  updateTime?: string
  createUser?: string
  updateUser?: string
  deleted?: number
}

// 工作流表单
export interface WorkflowForm {
  id?: number
  projectId: number
  name: string
  code: string
  description?: string
  tags?: string
  category?: string

  globalParams?: string
  warningType?: string
  warningGroupId?: number
  cronExpression?: string
}

// 工作流查询参数
export interface WorkflowQuery {
  pageNum?: number
  pageSize?: number
  projectId: number
  name?: string
  category?: string
  status?: number
}

// 工作流分类选项
export interface WorkflowCategoryOption {
  code: string
  name: string
}

// 工作流状态选项
export interface WorkflowStatusOption {
  code: number
  name: string
}

// 工作流状态枚举
export enum WorkflowStatus {
  DRAFT = 0,      // 草稿
  PUBLISHED = 1,  // 已发布
  ONLINE = 2,     // 已上线
  OFFLINE = 3     // 已下线
}

// 工作流分类枚举
export enum WorkflowCategory {
  ETL = 'ETL',           // 数据处理
  REPORT = 'REPORT',     // 报表生成
  ANALYSIS = 'ANALYSIS', // 数据分析
  SYNC = 'SYNC'          // 数据同步
}

// 告警类型枚举
export enum WarningType {
  NONE = 'NONE',         // 不告警
  SUCCESS = 'SUCCESS',   // 成功告警
  FAILURE = 'FAILURE',   // 失败告警
  ALL = 'ALL'            // 全部告警
}

// DAG节点
export interface DagNode {
  id: string
  type: string
  name: string
  x?: number
  y?: number
  [key: string]: any
}

// DAG边
export interface DagEdge {
  source: string
  target: string
  [key: string]: any
}

// DAG图
export interface DagGraph {
  nodes: DagNode[]
  edges: DagEdge[]
}

// 工作流统计
export interface WorkflowStats {
  total: number
  draft: number
  published: number
  online: number
  offline: number
  categoryStats: Record<string, number>
}

/**
 * 工作流执行实例
 */
export interface WorkflowExecution {
  executionId: string
  workflowId: number
  workflowName: string
  state: number
  progress: number
  completedNodes: number
  totalNodes: number
  runningNodes: string[]
  failedNodes: string[]
  startTime: string
  endTime?: string
  duration?: number
  executeUser: string
  errorMessage?: string
}

/**
 * 节点执行状态
 */
export interface NodeExecution {
  nodeCode: string
  nodeName: string
  nodeType: string
  state: number
  startTime?: string
  endTime?: string
  duration?: number
  executorAddress?: string
  errorMessage?: string
  jobId?: number
  jobLogId?: number
}

/**
 * 工作流执行状态枚举（内存中的执行状态）
 */
export enum WorkflowExecutionState {
  INITIALIZING = 0,
  RUNNING = 1,
  PAUSED = 2,
  SUCCESS = 3,
  FAILED = 4,
  TERMINATED = 5
}

/**
 * 工作流实例状态枚举（数据库中的状态）
 */
export enum WorkflowInstanceStatus {
  SUBMIT_SUCCESS = 0,    // 提交成功
  RUNNING = 1,           // 正在运行
  READY_PAUSE = 2,       // 准备暂停
  PAUSE = 3,             // 暂停
  READY_STOP = 4,        // 准备停止
  STOP = 5,              // 停止
  FAILURE = 6,           // 失败
  SUCCESS = 7            // 成功
}

/**
 * 节点执行状态枚举
 * 对应数据库node_instance.state字段和后端NodeExecutionState枚举
 */
export enum NodeExecutionState {
  WAITING = 0,      // 等待执行
  RUNNING = 1,      // 运行中
  SUCCESS = 2,      // 成功
  FAILED = 3,       // 失败
  SKIPPED = 4,      // 跳过
  TERMINATED = 5    // 终止
}

/**
 * 工作流调度配置
 */
export interface WorkflowScheduleConfig {
  workflowId: number
  cronExpression: string
  executorRouteStrategy?: string
  executorBlockStrategy?: string
  executorTimeout?: number
  executorFailRetryCount?: number
  misfireStrategy?: string
  jobDesc?: string
  alarmEmail?: string
}
