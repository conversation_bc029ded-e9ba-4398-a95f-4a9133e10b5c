/**
 * 工作流节点相关类型定义
 */

// 工作流节点实体
export interface WorkflowNode {
  id?: number
  workflowId: number
  nodeCode: string
  nodeName: string
  nodeType: string
  description?: string
  configParams: string
  positionX: number
  positionY: number
  timeout: number
  retryTimes: number
  retryInterval: number
  maxRetryTimes: number
  failureStrategy: string
  priority: number
  workerGroup: string
  jobId?: number  // XXL-JOB任务ID，关联job_info表
  createTime?: string
  updateTime?: string
  createUser: string
  updateUser: string
  status?: string
  color?: string  // 节点颜色
}

// 工作流节点关系实体
export interface WorkflowRelation {
  id?: number
  workflowId: number
  preNodeCode: string
  postNodeCode: string
  conditionType: string
  conditionParams?: string
  createTime?: string
  updateTime?: string
  createUser: string
  updateUser: string
}

// 工作流节点表单
export interface WorkflowNodeForm {
  id?: number
  workflowId: number
  nodeCode: string
  nodeName: string
  nodeType: string
  description?: string
  configParams: string
  timeout: number
  retryTimes: number
  retryInterval: number
  maxRetryTimes: number
  failureStrategy: string
  priority: number
  workerGroup: string
  jobId?: number  // XXL-JOB任务ID，关联job_info表
}

// 工作流节点查询参数
export interface WorkflowNodeQuery {
  workflowId: number
  nodeType?: string
  status?: string
}

// 失败策略枚举
export enum FailureStrategy {
  CONTINUE = 'CONTINUE',  // 继续
  END = 'END',           // 结束
  FAIL = 'FAIL'          // 失败
}

// 条件类型枚举
export enum ConditionType {
  NONE = 'NONE',         // 无条件
  SUCCESS = 'SUCCESS',   // 成功
  FAILURE = 'FAILURE'    // 失败
}

// 节点状态枚举
export enum NodeStatus {
  WAITING = 'WAITING',           // 等待
  RUNNING = 'RUNNING',           // 运行中
  SUCCESS = 'SUCCESS',           // 成功
  FAILURE = 'FAILURE',           // 失败
  PAUSE = 'PAUSE',               // 暂停
  KILL = 'KILL'                  // 终止
}

// 数据变更类型枚举
export enum ChangeType {
  ADDED = 'ADDED',               // 新增
  UPDATED = 'UPDATED',           // 更新
  DELETED = 'DELETED'            // 删除
}

// 工作流设计数据
export interface WorkflowDesignData {
  workflowId: number
  nodes: WorkflowNode[]
  relations: WorkflowRelation[]
}

// 工作流设计变更数据
export interface WorkflowDesignChanges {
  workflowId: number
  addedNodes: WorkflowNode[]
  updatedNodes: WorkflowNode[]
  deletedNodeIds: number[]
  addedRelations: WorkflowRelation[]
  updatedRelations: WorkflowRelation[]
  deletedRelationIds: number[]
}

// 节点变更跟踪
export interface NodeChange {
  node: WorkflowNode
  changeType: ChangeType
  originalNode?: WorkflowNode
}

// 关系变更跟踪
export interface RelationChange {
  relation: WorkflowRelation
  changeType: ChangeType
  originalRelation?: WorkflowRelation
}
