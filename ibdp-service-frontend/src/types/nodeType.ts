/**
 * 节点类型相关类型定义
 */

// 节点类型实体
export interface NodeType {
  id?: number
  typeCode: string
  typeName: string
  category: string
  icon?: string
  color?: string
  description?: string
  configSchema?: string
  executorClass: string
  sortOrder?: number
  status?: number
  createTime?: string
  updateTime?: string
}

// 节点类型查询参数
export interface NodeTypeQuery {
  pageNum?: number
  pageSize?: number
  typeName?: string
  category?: string
  status?: number
}

// 节点类型分类选项
export interface NodeTypeCategoryOption {
  code: string
  name: string
}

// 节点类型状态选项
export interface NodeTypeStatusOption {
  code: number
  name: string
}

// 节点类型分类枚举
export enum NodeTypeCategory {
  DATA_SYNC = 'DATA_SYNC',     // 数据同步
  COMPUTE = 'COMPUTE',         // 计算处理
  CONTROL = 'CONTROL'          // 流程控制
}

// 节点类型状态枚举
export enum NodeTypeStatus {
  DISABLED = 0,  // 禁用
  ENABLED = 1    // 启用
}
