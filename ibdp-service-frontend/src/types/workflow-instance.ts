/**
 * 工作流实例类型定义
 */

/**
 * 工作流实例
 */
export interface WorkflowInstance {
  id: number
  workflowId: number
  workflowName: string
  workflowVersion?: string
  executionId: string
  jobId?: number
  jobLogId?: number
  status: number
  triggerType: string
  triggerTime: string
  startTime?: string
  endTime?: string
  duration?: number
  executeParams?: string
  executeResult?: string
  errorMessage?: string
  executorAddress?: string
  executorShardingParam?: string
  retryCount?: number
  createTime: string
  updateTime: string
}

/**
 * 工作流实例状态枚举（对应数据库state字段）
 */
export enum WorkflowInstanceStatus {
  SUBMIT_SUCCESS = 0,        // 提交成功
  RUNNING = 1,              // 正在运行
  READY_PAUSE = 2,          // 准备暂停
  PAUSE = 3,                // 暂停
  READY_STOP = 4,           // 准备停止
  STOP = 5,                 // 停止
  FAILURE = 6,              // 失败
  SUCCESS = 7,              // 成功
  NEED_FAULT_TOLERANCE = 8, // 需要容错
  KILL = 9,                 // kill
  WAITING_THREAD = 10,      // 等待线程
  WAITING_DEPEND = 11       // 等待依赖完成
}

/**
 * 触发类型枚举（与后端 TriggerType 保持一致）
 */
export enum TriggerType {
  MANUAL = 'MANUAL',         // 手动触发
  SCHEDULE = 'SCHEDULE',     // 定时调度
  API = 'API',               // API触发
  EVENT = 'EVENT',           // 事件触发
  DEPENDENCY = 'DEPENDENCY'  // 依赖触发
}

/**
 * 触发类型常量（包含向后兼容）
 */
export const TRIGGER_TYPE_CODES = {
  MANUAL: 'MANUAL',
  SCHEDULE: 'SCHEDULE',
  API: 'API',
  EVENT: 'EVENT',
  DEPENDENCY: 'DEPENDENCY',
  // 向后兼容
  CRON: 'CRON',
  RETRY: 'RETRY'
} as const

/**
 * 工作流实例查询参数
 */
export interface WorkflowInstanceQuery {
  page: number
  size: number
  projectId: number
  workflowId?: number
  workflowName?: string
  status?: number
  triggerType?: string
  startTime?: string
  endTime?: string
}

/**
 * 工作流实例统计信息
 */
export interface WorkflowInstanceStats {
  totalCount: number
  successCount: number
  failedCount: number
  runningCount: number
  successRate: number
}

/**
 * 分页查询结果
 */
export interface WorkflowInstancePageResult {
  data: WorkflowInstance[]
  recordsTotal: number
  recordsFiltered: number
}
