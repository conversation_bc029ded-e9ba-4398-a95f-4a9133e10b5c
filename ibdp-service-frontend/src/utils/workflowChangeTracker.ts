import type {
  WorkflowNode,
  WorkflowRelation,
  WorkflowDesignChanges,
  NodeChange,
  RelationChange
} from '@/types/workflowNode'
import { ChangeType } from '@/types/workflowNode'

/**
 * 工作流设计变更跟踪器
 * 用于跟踪节点和关系的变更，支持增量保存
 */
export class WorkflowChangeTracker {
  private originalNodes: Map<number, WorkflowNode> = new Map()
  private originalRelations: Map<number, WorkflowRelation> = new Map()
  private nodeChanges: Map<number, NodeChange> = new Map()
  private relationChanges: Map<number, RelationChange> = new Map()

  /**
   * 初始化原始数据
   */
  initializeData(nodes: WorkflowNode[], relations: WorkflowRelation[]) {
    this.originalNodes.clear()
    this.originalRelations.clear()
    this.nodeChanges.clear()
    this.relationChanges.clear()

    // 存储原始节点数据
    nodes.forEach(node => {
      if (node.id) {
        this.originalNodes.set(node.id, { ...node })
      }
    })

    // 存储原始关系数据
    relations.forEach(relation => {
      if (relation.id) {
        this.originalRelations.set(relation.id, { ...relation })
      }
    })
  }

  /**
   * 跟踪节点变更
   */
  trackNodeChange(node: WorkflowNode, changeType?: ChangeType) {
    if (!node.id) return

    const originalNode = this.originalNodes.get(node.id)
    let detectedChangeType = changeType

    if (!detectedChangeType) {
      if (!originalNode) {
        detectedChangeType = ChangeType.ADDED
      } else if (this.isNodeChanged(node, originalNode)) {
        detectedChangeType = ChangeType.UPDATED
      } else {
        // 没有变更，移除跟踪
        this.nodeChanges.delete(node.id)
        return
      }
    }

    this.nodeChanges.set(node.id, {
      node: { ...node },
      changeType: detectedChangeType,
      originalNode: originalNode ? { ...originalNode } : undefined
    })
  }

  /**
   * 跟踪节点删除
   */
  trackNodeDeletion(nodeId: number) {
    const originalNode = this.originalNodes.get(nodeId)
    if (originalNode) {
      this.nodeChanges.set(nodeId, {
        node: originalNode,
        changeType: ChangeType.DELETED,
        originalNode: { ...originalNode }
      })
    }
  }

  /**
   * 跟踪关系变更
   */
  trackRelationChange(relation: WorkflowRelation, changeType?: ChangeType) {
    if (!relation.id) return

    const originalRelation = this.originalRelations.get(relation.id)
    let detectedChangeType = changeType

    if (!detectedChangeType) {
      if (!originalRelation) {
        detectedChangeType = ChangeType.ADDED
      } else if (this.isRelationChanged(relation, originalRelation)) {
        detectedChangeType = ChangeType.UPDATED
      } else {
        // 没有变更，移除跟踪
        this.relationChanges.delete(relation.id)
        return
      }
    }

    this.relationChanges.set(relation.id, {
      relation: { ...relation },
      changeType: detectedChangeType,
      originalRelation: originalRelation ? { ...originalRelation } : undefined
    })
  }

  /**
   * 跟踪关系删除
   */
  trackRelationDeletion(relationId: number) {
    const originalRelation = this.originalRelations.get(relationId)
    if (originalRelation) {
      this.relationChanges.set(relationId, {
        relation: originalRelation,
        changeType: ChangeType.DELETED,
        originalRelation: { ...originalRelation }
      })
    }
  }

  /**
   * 获取所有变更
   */
  getChanges(workflowId: number): WorkflowDesignChanges {
    const addedNodes: WorkflowNode[] = []
    const updatedNodes: WorkflowNode[] = []
    const deletedNodeIds: string[] = []
    const addedRelations: WorkflowRelation[] = []
    const updatedRelations: WorkflowRelation[] = []
    const deletedRelationIds: string[] = []

    // 处理节点变更
    this.nodeChanges.forEach((change, nodeId) => {
      switch (change.changeType) {
        case ChangeType.ADDED:
          addedNodes.push(change.node)
          break
        case ChangeType.UPDATED:
          updatedNodes.push(change.node)
          break
        case ChangeType.DELETED:
          deletedNodeIds.push(nodeId)
          break
      }
    })

    // 处理关系变更
    this.relationChanges.forEach((change, relationId) => {
      switch (change.changeType) {
        case ChangeType.ADDED:
          addedRelations.push(change.relation)
          break
        case ChangeType.UPDATED:
          updatedRelations.push(change.relation)
          break
        case ChangeType.DELETED:
          deletedRelationIds.push(relationId)
          break
      }
    })

    return {
      workflowId,
      addedNodes,
      updatedNodes,
      deletedNodeIds,
      addedRelations,
      updatedRelations,
      deletedRelationIds
    }
  }

  /**
   * 检查是否有变更
   */
  hasChanges(): boolean {
    return this.nodeChanges.size > 0 || this.relationChanges.size > 0
  }

  /**
   * 清空变更跟踪
   */
  clearChanges() {
    this.nodeChanges.clear()
    this.relationChanges.clear()
  }

  /**
   * 提交变更（更新原始数据）
   */
  commitChanges(nodes: WorkflowNode[], relations: WorkflowRelation[]) {
    this.initializeData(nodes, relations)
  }

  /**
   * 检查节点是否有变更
   */
  private isNodeChanged(current: WorkflowNode, original: WorkflowNode): boolean {
    const fieldsToCompare = [
      'nodeName', 'nodeType', 'description', 'configParams',
      'positionX', 'positionY', 'timeout', 'retryTimes', 'retryInterval',
      'maxRetryTimes', 'failureStrategy', 'priority', 'workerGroup'
    ]

    return fieldsToCompare.some(field => {
      return current[field as keyof WorkflowNode] !== original[field as keyof WorkflowNode]
    })
  }

  /**
   * 检查关系是否有变更
   */
  private isRelationChanged(current: WorkflowRelation, original: WorkflowRelation): boolean {
    const fieldsToCompare = [
      'preNodeCode', 'postNodeCode', 'conditionType', 'conditionParams'
    ]

    return fieldsToCompare.some(field => {
      return current[field as keyof WorkflowRelation] !== original[field as keyof WorkflowRelation]
    })
  }
}
