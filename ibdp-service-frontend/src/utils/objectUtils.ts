/**
 * 对象操作工具函数
 * 用于解决Vue组件中对象引用共享的问题
 */

/**
 * 深拷贝函数
 * @param obj 要拷贝的对象
 * @returns 深拷贝后的新对象
 */
export const deepClone = <T>(obj: T): T => {
  if (obj === null || typeof obj !== 'object') {
    return obj
  }
  
  if (obj instanceof Date) {
    return new Date(obj.getTime()) as T
  }
  
  if (obj instanceof Array) {
    return obj.map(item => deepClone(item)) as T
  }
  
  if (typeof obj === 'object') {
    const clonedObj: any = {}
    for (const key in obj) {
      if (obj.hasOwnProperty(key)) {
        clonedObj[key] = deepClone((obj as any)[key])
      }
    }
    return clonedObj as T
  }
  
  return obj
}

/**
 * 深度合并函数
 * 将源对象合并到目标对象，保持对象独立性
 * @param target 目标对象
 * @param source 源对象
 * @returns 合并后的新对象
 */
export const deepMerge = <T>(target: T, source: Partial<T>): T => {
  if (!source || typeof source !== 'object') {
    return target
  }
  
  const result = deepClone(target)
  
  for (const key in source) {
    if (source.hasOwnProperty(key)) {
      const sourceValue = source[key]
      const targetValue = (result as any)[key]
      
      if (sourceValue && typeof sourceValue === 'object' && !Array.isArray(sourceValue)) {
        // 递归合并对象
        ;(result as any)[key] = deepMerge(targetValue || {}, sourceValue)
      } else {
        // 直接赋值（包括数组和基本类型）
        ;(result as any)[key] = deepClone(sourceValue)
      }
    }
  }
  
  return result
}

/**
 * 检查两个对象是否深度相等
 * @param obj1 对象1
 * @param obj2 对象2
 * @returns 是否相等
 */
export const deepEqual = (obj1: any, obj2: any): boolean => {
  if (obj1 === obj2) {
    return true
  }
  
  if (obj1 == null || obj2 == null) {
    return obj1 === obj2
  }
  
  if (typeof obj1 !== typeof obj2) {
    return false
  }
  
  if (typeof obj1 !== 'object') {
    return obj1 === obj2
  }
  
  if (Array.isArray(obj1) !== Array.isArray(obj2)) {
    return false
  }
  
  const keys1 = Object.keys(obj1)
  const keys2 = Object.keys(obj2)
  
  if (keys1.length !== keys2.length) {
    return false
  }
  
  for (const key of keys1) {
    if (!keys2.includes(key)) {
      return false
    }
    
    if (!deepEqual(obj1[key], obj2[key])) {
      return false
    }
  }
  
  return true
}

/**
 * 安全的JSON解析
 * @param jsonStr JSON字符串
 * @param defaultValue 解析失败时的默认值
 * @returns 解析结果
 */
export const safeJsonParse = <T>(jsonStr: string, defaultValue: T): T => {
  try {
    return JSON.parse(jsonStr) as T
  } catch (error) {
    console.warn('JSON解析失败:', error)
    return defaultValue
  }
}

/**
 * 安全的JSON字符串化
 * @param obj 要序列化的对象
 * @param defaultValue 序列化失败时的默认值
 * @returns JSON字符串
 */
export const safeJsonStringify = (obj: any, defaultValue: string = '{}'): string => {
  try {
    return JSON.stringify(obj, null, 2)
  } catch (error) {
    console.warn('JSON序列化失败:', error)
    return defaultValue
  }
}

/**
 * 创建响应式配置对象的工厂函数
 * 用于确保每个组件实例都有独立的配置对象
 * @param defaultConfig 默认配置
 * @param initialConfig 初始配置
 * @returns 合并后的独立配置对象
 */
export const createReactiveConfig = <T>(defaultConfig: T, initialConfig?: Partial<T>): T => {
  const baseConfig = deepClone(defaultConfig)
  
  if (initialConfig) {
    return deepMerge(baseConfig, initialConfig)
  }
  
  return baseConfig
}

/**
 * 节点配置工厂函数
 * 专门用于工作流节点配置的创建
 */
export const createNodeConfig = {
  /**
   * 创建DataX节点默认配置
   */
  datax: () => ({
    source: {
      datasourceId: null,
      database: '',
      tableName: '',
      whereCondition: '',
      splitPk: ''
    },
    target: {
      datasourceId: null,
      database: '',
      tableName: '',
      writeMode: 'insert',
      preSql: '',
      postSql: ''
    },
    columnMappings: [],
    syncStrategy: {
      type: 'FULL',
      incrementalColumn: '',
      incrementalValue: '${lastSyncTime}',
      incrementalType: 'TIMESTAMP',
      operator: '>',
      customCondition: ''
    },
    performance: {
      channel: 1,
      batchSize: 1000,
      byteLimit: 64,
      recordLimit: 100000,
      speedLimit: 10,
      errorLimit: 0
    }
  }),
  
  /**
   * 创建SQL节点默认配置
   */
  sql: () => ({
    datasourceId: null,
    sqlContent: '',
    sqlType: 'SELECT',
    resultHandling: 'LOG',
    outputPath: '',
    parameters: []
  }),
  
  /**
   * 创建Shell节点默认配置
   */
  shell: () => ({
    scriptContent: '',
    scriptType: 'SHELL',
    scriptParams: '',
    environmentVariables: {}
  })
}
