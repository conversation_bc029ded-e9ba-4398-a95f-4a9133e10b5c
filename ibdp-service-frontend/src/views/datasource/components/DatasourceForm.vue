<template>
  <el-dialog
    v-model="dialogVisible"
    :title="mode === 'add' ? '新增数据源' : '编辑数据源'"
    width="700px"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="100px"
      label-position="right"
    >
      <el-form-item label="数据源名称" prop="name">
        <el-input
          v-model="formData.name"
          placeholder="请输入数据源名称"
          maxlength="100"
          show-word-limit
        />
      </el-form-item>

      <el-form-item label="数据源类型" prop="type">
        <el-select
          v-model="formData.type"
          placeholder="请选择数据源类型"
          style="width: 100%"
          @change="handleTypeChange"
        >
          <el-option
            v-for="type in datasourceTypes"
            :key="type.code"
            :label="type.name"
            :value="type.code"
          />
        </el-select>
      </el-form-item>

      <el-row :gutter="20">
        <el-col :span="14">
          <el-form-item label="主机地址" prop="host">
            <el-input
              v-model="formData.host"
              placeholder="请输入主机地址"
            />
          </el-form-item>
        </el-col>
        <el-col :span="10">
          <el-form-item label="端口" prop="port">
            <el-input-number
              v-model="formData.port"
              :min="1"
              :max="65535"
              style="width: 100%"
              placeholder="端口号"
              controls-position="right"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-form-item label="数据库名称" prop="databaseName">
        <el-input
          v-model="formData.databaseName"
          placeholder="请输入数据库名称"
        />
      </el-form-item>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="用户名" prop="username">
            <el-input
              v-model="formData.username"
              placeholder="请输入用户名"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="密码" prop="password">
            <el-input
              v-model="formData.password"
              type="password"
              placeholder="请输入密码"
              show-password
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-form-item label="连接参数">
        <el-input
          v-model="formData.connectionParams"
          type="textarea"
          :rows="3"
          placeholder="请输入连接参数（JSON格式），如：{&quot;useSSL&quot;:&quot;false&quot;,&quot;serverTimezone&quot;:&quot;Asia/Shanghai&quot;}"
        />
      </el-form-item>

      <el-form-item label="描述">
        <el-input
          v-model="formData.description"
          type="textarea"
          :rows="2"
          placeholder="请输入数据源描述"
          maxlength="500"
          show-word-limit
        />
      </el-form-item>

      <el-form-item label="状态" prop="status">
        <el-radio-group v-model="formData.status">
          <el-radio :label="1">启用</el-radio>
          <el-radio :label="0">禁用</el-radio>
        </el-radio-group>
      </el-form-item>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button
          type="primary"
          @click="handleTestConnection"
          :loading="testing"
        >
          测试连接
        </el-button>
        <el-button
          type="primary"
          @click="handleSubmit"
          :loading="submitting"
        >
          确定
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch, nextTick } from 'vue'
import { ElMessage } from 'element-plus'
import type { FormInstance, FormRules } from 'element-plus'
import { useDatasourceStore } from '@/stores/datasource'
import type { Datasource, DatasourceType, DatasourceStatus } from '@/types'

// Props
interface Props {
  visible: boolean
  datasource?: Datasource | null
  mode: 'add' | 'edit'
}

const props = withDefaults(defineProps<Props>(), {
  datasource: null,
  mode: 'add'
})

// Emits
const emit = defineEmits<{
  'update:visible': [value: boolean]
  success: []
}>()

const datasourceStore = useDatasourceStore()

// 响应式数据
const formRef = ref<FormInstance>()
const submitting = ref(false)
const testing = ref(false)

// 表单数据
const formData = reactive<Datasource>({
  name: '',
  type: 'MYSQL' as DatasourceType,
  host: '',
  port: 3306,
  databaseName: '',
  username: '',
  password: '',
  connectionParams: '',
  description: '',
  status: 1 as DatasourceStatus
})

// 计算属性
const dialogVisible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
})

const datasourceTypes = computed(() => datasourceStore.datasourceTypes)

// 表单验证规则
const formRules: FormRules = {
  name: [
    { required: true, message: '请输入数据源名称', trigger: 'blur' },
    { min: 2, max: 100, message: '长度在 2 到 100 个字符', trigger: 'blur' }
  ],
  type: [
    { required: true, message: '请选择数据源类型', trigger: 'change' }
  ],
  host: [
    { required: true, message: '请输入主机地址', trigger: 'blur' }
  ],
  port: [
    { required: true, message: '请输入端口号', trigger: 'blur' },
    { type: 'number', min: 1, max: 65535, message: '端口号范围为 1-65535', trigger: 'blur' }
  ],
  databaseName: [
    { required: true, message: '请输入数据库名称', trigger: 'blur' }
  ],
  username: [
    { required: true, message: '请输入用户名', trigger: 'blur' }
  ],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' }
  ],
  status: [
    { required: true, message: '请选择状态', trigger: 'change' }
  ]
}

// 方法
const resetForm = () => {
  Object.assign(formData, {
    name: '',
    type: 'MYSQL' as DatasourceType,
    host: '',
    port: 3306,
    databaseName: '',
    username: '',
    password: '',
    connectionParams: '',
    description: '',
    status: 1 as DatasourceStatus
  })
}

const loadFormData = () => {
  if (props.datasource) {
    // 编辑模式：复制数据但保留原始密码字段为空，让用户重新输入
    Object.assign(formData, {
      ...props.datasource,
      password: '' // 编辑时清空密码，要求用户重新输入
    })
  } else {
    resetForm()
  }
}

// 数据源类型改变
const handleTypeChange = async (type: string) => {
  try {
    // 获取默认端口
    const defaultPort = await datasourceStore.getDefaultPort(type)
    if (defaultPort > 0) {
      formData.port = defaultPort
    }
    
    // 获取默认连接参数
    const defaultParams = await datasourceStore.getDefaultParams(type)
    if (defaultParams) {
      formData.connectionParams = defaultParams
    }
  } catch (error) {
    console.error('获取默认配置失败:', error)
  }
}

// 测试连接
const handleTestConnection = async () => {
  try {
    // 先验证表单
    const valid = await formRef.value?.validate()
    if (!valid) return

    // 检查密码是否为空
    if (!formData.password || formData.password.trim() === '') {
      ElMessage.warning('请输入密码后再测试连接')
      return
    }

    testing.value = true
    await datasourceStore.testConnectionOnly(formData)
  } catch (error) {
    console.error('测试连接失败:', error)
  } finally {
    testing.value = false
  }
}

// 提交表单
const handleSubmit = async () => {
  try {
    const valid = await formRef.value?.validate()
    if (!valid) return
    
    submitting.value = true
    
    if (props.mode === 'add') {
      await datasourceStore.addDatasource(formData)
    } else {
      await datasourceStore.updateDatasource(formData)
    }
    
    emit('success')
  } catch (error) {
    console.error('保存数据源失败:', error)
  } finally {
    submitting.value = false
  }
}

// 关闭对话框
const handleClose = () => {
  formRef.value?.resetFields()
  emit('update:visible', false)
}

// 监听对话框显示状态
watch(
  () => props.visible,
  (visible) => {
    if (visible) {
      nextTick(() => {
        loadFormData()
      })
    }
  }
)
</script>

<style scoped>
.dialog-footer {
  text-align: right;
}

.dialog-footer .el-button {
  margin-left: 10px;
}
</style>
