<template>
  <div class="page-container">
    <!-- 页面标题 -->
    <div class="page-header">
      <h1 class="page-title">数据源管理</h1>
      <p class="page-description">管理系统中的数据源配置，支持MySQL、PostgreSQL、Oracle、Doris、Hive等数据库</p>
    </div>

    <!-- 数据源列表 -->
    <div class="table-container">
      <!-- 表格工具栏 -->
      <div class="table-header">
        <div class="table-search">
          <el-input
            v-model="searchForm.name"
            placeholder="请输入数据源名称"
            style="width: 200px"
            clearable
            @clear="handleSearch"
            @keyup.enter="handleSearch"
          >
            <template #prefix>
              <el-icon><Search /></el-icon>
            </template>
          </el-input>
          
          <el-select
            v-model="searchForm.type"
            placeholder="数据源类型"
            style="width: 150px"
            clearable
            @change="handleSearch"
          >
            <el-option
              v-for="type in datasourceTypes"
              :key="type.code"
              :label="type.name"
              :value="type.code"
            />
          </el-select>
          
          <el-select
            v-model="searchForm.status"
            placeholder="状态"
            style="width: 120px"
            clearable
            @change="handleSearch"
          >
            <el-option label="启用" :value="1" />
            <el-option label="禁用" :value="0" />
          </el-select>
          
          <el-button type="primary" @click="handleSearch">
            <el-icon><Search /></el-icon>
            搜索
          </el-button>
          
          <el-button @click="handleReset">
            <el-icon><Refresh /></el-icon>
            重置
          </el-button>
        </div>
        
        <div class="table-actions">
          <el-button type="primary" @click="handleAdd">
            <el-icon><Plus /></el-icon>
            新增数据源
          </el-button>
        </div>
      </div>

      <!-- 数据表格 -->
      <el-table
        v-loading="loading"
        :data="tableData"
        style="width: 100%"
        stripe
        border
      >
        <el-table-column prop="name" label="数据源名称" min-width="150" />
        <el-table-column prop="type" label="类型" width="120">
          <template #default="{ row }">
            <el-tag :type="getTypeTagType(row.type)">
              {{ getTypeName(row.type) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="host" label="主机地址" min-width="150" />
        <el-table-column prop="port" label="端口" width="80" />
        <el-table-column prop="databaseName" label="数据库" min-width="120" />
        <el-table-column prop="status" label="状态" width="80">
          <template #default="{ row }">
            <el-tag :type="row.status === 1 ? 'success' : 'danger'">
              {{ row.status === 1 ? '启用' : '禁用' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="testStatus" label="连接状态" width="100">
          <template #default="{ row }">
            <el-tag
              v-if="row.testStatus === 1"
              type="success"
              size="small"
            >
              正常
            </el-tag>
            <el-tag
              v-else-if="row.testStatus === 2"
              type="danger"
              size="small"
            >
              异常
            </el-tag>
            <el-tag
              v-else
              type="info"
              size="small"
            >
              未测试
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="createTime" label="创建时间" width="160">
          <template #default="{ row }">
            {{ formatDateTime(row.createTime) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="220" fixed="right">
          <template #default="{ row }">
            <el-button
              type="primary"
              size="small"
              @click="handleTest(row)"
              :loading="testingIds.includes(row.id)"
            >
              测试
            </el-button>
            <el-button
              type="warning"
              size="small"
              @click="handleEdit(row)"
            >
              编辑
            </el-button>
            <el-button
              type="danger"
              size="small"
              @click="handleDelete(row)"
            >
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="pagination.current"
          v-model:page-size="pagination.size"
          :page-sizes="[10, 20, 50, 100]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>

    <!-- 数据源表单对话框 -->
    <DatasourceForm
      v-model:visible="formVisible"
      :datasource="currentDatasource"
      :mode="formMode"
      @success="handleFormSuccess"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from 'vue'
import { useRoute } from 'vue-router'
import { ElMessageBox, ElMessage } from 'element-plus'
import { useDatasourceStore } from '@/stores/datasource'
import DatasourceForm from './components/DatasourceForm.vue'
import type { Datasource } from '@/types'
import {
  Search,
  Refresh,
  Plus
} from '@element-plus/icons-vue'

const route = useRoute()
const datasourceStore = useDatasourceStore()

// 响应式数据
const loading = ref(false)
const tableData = ref<Datasource[]>([])
const testingIds = ref<number[]>([])
const formVisible = ref(false)
const formMode = ref<'add' | 'edit'>('add')
const currentDatasource = ref<Datasource | null>(null)

// 搜索表单
const searchForm = reactive({
  name: '',
  type: '',
  status: undefined as number | undefined
})

// 分页
const pagination = reactive({
  current: 1,
  size: 20,
  total: 0
})

// 计算属性
const datasourceTypes = computed(() => datasourceStore.datasourceTypes)

// 方法
const getTypeName = (type: string) => {
  const typeMap: Record<string, string> = {
    MYSQL: 'MySQL',
    POSTGRESQL: 'PostgreSQL',
    ORACLE: 'Oracle',
    DORIS: 'Doris',
    HIVE: 'Hive'
  }
  return typeMap[type] || type
}

const getTypeTagType = (type: string) => {
  const typeMap: Record<string, string> = {
    MYSQL: 'primary',
    POSTGRESQL: 'success',
    ORACLE: 'warning',
    DORIS: 'info',
    HIVE: 'danger'
  }
  return typeMap[type] || 'default'
}

const formatDateTime = (dateTime: string) => {
  if (!dateTime) return '-'
  return new Date(dateTime).toLocaleString()
}

// 加载数据源列表
const loadDatasources = async () => {
  try {
    loading.value = true
    const params = {
      pageNum: pagination.current,
      pageSize: pagination.size,
      name: searchForm.name || undefined,
      type: searchForm.type || undefined,
      status: searchForm.status
    }
    
    const result = await datasourceStore.fetchDatasources(params)
    tableData.value = result.data || []
    pagination.total = result.recordsTotal || 0
  } catch (error) {
    console.error('加载数据源列表失败:', error)
  } finally {
    loading.value = false
  }
}

// 搜索
const handleSearch = () => {
  pagination.current = 1
  loadDatasources()
}

// 重置
const handleReset = () => {
  searchForm.name = ''
  searchForm.type = ''
  searchForm.status = undefined
  pagination.current = 1
  loadDatasources()
}

// 新增
const handleAdd = () => {
  currentDatasource.value = null
  formMode.value = 'add'
  formVisible.value = true
}

// 编辑
const handleEdit = (row: Datasource) => {
  currentDatasource.value = { ...row }
  formMode.value = 'edit'
  formVisible.value = true
}

// 删除
const handleDelete = async (row: Datasource) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除数据源"${row.name}"吗？`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    await datasourceStore.deleteDatasource(row.id!)
    loadDatasources()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除数据源失败:', error)
    }
  }
}

// 测试连接
const handleTest = async (row: Datasource) => {
  try {
    testingIds.value.push(row.id!)
    await datasourceStore.testConnection(row.id!)
    loadDatasources() // 重新加载以更新测试状态
  } catch (error) {
    console.error('测试连接失败:', error)
  } finally {
    testingIds.value = testingIds.value.filter(id => id !== row.id)
  }
}

// 分页大小改变
const handleSizeChange = (size: number) => {
  pagination.size = size
  pagination.current = 1
  loadDatasources()
}

// 当前页改变
const handleCurrentChange = (current: number) => {
  pagination.current = current
  loadDatasources()
}

// 表单成功回调
const handleFormSuccess = () => {
  formVisible.value = false
  loadDatasources()
}

// 生命周期
onMounted(async () => {
  // 加载数据源类型
  await datasourceStore.fetchDatasourceTypes()
  
  // 检查URL参数，如果有action=add则打开新增表单
  if (route.query.action === 'add') {
    handleAdd()
  }
  
  // 加载数据源列表
  loadDatasources()
})
</script>

<style scoped>
.page-container {
  padding: 20px;
  height: 100%;
}

.page-header {
  margin-bottom: 24px;
}

.page-title {
  font-size: 24px;
  font-weight: 600;
  color: #303133;
  margin-bottom: 8px;
}

.page-description {
  color: #606266;
  font-size: 14px;
}

.table-container {
  background: #fff;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.12), 0 0 6px rgba(0, 0, 0, 0.04);
}

.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  flex-wrap: wrap;
  gap: 10px;
}

.table-search {
  display: flex;
  gap: 10px;
  align-items: center;
  flex-wrap: wrap;
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: center;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .page-container {
    padding: 10px;
  }
  
  .table-header {
    flex-direction: column;
    align-items: stretch;
  }
  
  .table-search {
    flex-wrap: wrap;
  }
  
  .table-search .el-input,
  .table-search .el-select {
    width: 100% !important;
    margin-bottom: 10px;
  }
}
</style>
