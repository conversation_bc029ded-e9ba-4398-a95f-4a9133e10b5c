<template>
  <div class="executor-management">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-left">
        <h2>执行器管理</h2>
        <p>管理和监控XXL-JOB执行器的状态</p>
      </div>
      <div class="header-right">
        <el-button type="primary" @click="handleCreate" size="large" class="create-button">
          <el-icon><Plus /></el-icon>
          新增执行器
        </el-button>
        <el-button @click="handleRefresh" size="large">
          <el-icon><Refresh /></el-icon>
          刷新
        </el-button>
      </div>
    </div>

    <!-- 统计卡片 -->
    <div class="stats-cards">
      <el-row :gutter="20">
        <el-col :span="8">
          <el-card class="stats-card">
            <div class="stats-content">
              <div class="stats-icon total">
                <el-icon size="24"><Monitor /></el-icon>
              </div>
              <div class="stats-info">
                <div class="stats-value">{{ stats.totalCount }}</div>
                <div class="stats-label">总执行器数</div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="8">
          <el-card class="stats-card">
            <div class="stats-content">
              <div class="stats-icon online">
                <el-icon size="24"><SuccessFilled /></el-icon>
              </div>
              <div class="stats-info">
                <div class="stats-value">{{ stats.onlineCount }}</div>
                <div class="stats-label">在线执行器</div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="8">
          <el-card class="stats-card">
            <div class="stats-content">
              <div class="stats-icon offline">
                <el-icon size="24"><CircleCloseFilled /></el-icon>
              </div>
              <div class="stats-info">
                <div class="stats-value">{{ stats.offlineCount }}</div>
                <div class="stats-label">离线执行器</div>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 执行器列表 -->
    <el-card class="table-card">
      <template #header>
        <div class="card-header">
          <span>执行器列表</span>
          <div class="header-actions">
            <el-input
              v-model="searchKeyword"
              placeholder="搜索AppName或执行器名称"
              style="width: 250px"
              clearable
              @input="handleSearch"
            >
              <template #prefix>
                <el-icon><Search /></el-icon>
              </template>
            </el-input>
          </div>
        </div>
      </template>

      <el-table
        v-loading="loading"
        :data="filteredExecutorList"
        stripe
        style="width: 100%"
      >
        <el-table-column prop="id" label="ID" width="80" />
        <el-table-column prop="appname" label="AppName" min-width="150">
          <template #default="{ row }">
            <el-tag type="info">{{ row.appname }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="title" label="执行器名称" min-width="150" />
        <el-table-column label="注册方式" width="120">
          <template #default="{ row }">
            <el-tag :type="row.addressType === 0 ? 'success' : 'info'">
              {{ row.addressType === 0 ? '自动注册' : '手动录入' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="状态" width="100">
          <template #default="{ row }">
            <el-tag :type="getExecutorStatus(row) === 'ONLINE' ? 'success' : 'danger'">
              {{ getExecutorStatus(row) === 'ONLINE' ? '在线' : '离线' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="注册地址" min-width="200">
          <template #default="{ row }">
            <div v-if="row.registryList && row.registryList.length > 0">
              <el-tag
                v-for="address in row.registryList"
                :key="address"
                size="small"
                style="margin-right: 5px; margin-bottom: 2px"
                :type="getExecutorStatus(row) === 'ONLINE' ? 'success' : 'info'"
              >
                {{ address }}
              </el-tag>
            </div>
            <div v-else-if="row.addressList && row.addressList.trim()">
              <el-tag
                v-for="address in row.addressList.split(',')"
                :key="address"
                size="small"
                style="margin-right: 5px; margin-bottom: 2px"
                type="info"
              >
                {{ address.trim() }}
              </el-tag>
            </div>
            <span v-else class="text-muted">无注册地址</span>
          </template>
        </el-table-column>
        <el-table-column prop="updateTime" label="更新时间" width="180">
          <template #default="{ row }">
            {{ formatTime(row.updateTime) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="150" fixed="right">
          <template #default="{ row }">
            <el-button
              type="primary"
              size="small"
              @click="handleEdit(row)"
            >
              编辑
            </el-button>
            <el-button
              type="danger"
              size="small"
              @click="handleDelete(row)"
            >
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>

    <!-- 创建/编辑执行器对话框 -->
    <ExecutorDialog
      v-model:visible="dialogVisible"
      :executor="currentExecutor"
      :mode="dialogMode"
      @success="handleDialogSuccess"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Plus,
  Refresh,
  Search,
  Monitor,
  SuccessFilled,
  CircleCloseFilled
} from '@element-plus/icons-vue'
import { getExecutorList, getExecutorStats, deleteExecutor } from '@/api/executor'
import type { ExecutorInfo, ExecutorStats } from '@/types/executor'
import ExecutorDialog from './components/ExecutorDialog.vue'

// 响应式数据
const loading = ref(false)
const searchKeyword = ref('')
const executorList = ref<ExecutorInfo[]>([])
const stats = reactive<ExecutorStats>({
  totalCount: 0,
  onlineCount: 0,
  offlineCount: 0
})

// 对话框相关
const dialogVisible = ref(false)
const dialogMode = ref<'create' | 'edit'>('create')
const currentExecutor = ref<ExecutorInfo | null>(null)

// 计算属性
const filteredExecutorList = computed(() => {
  if (!searchKeyword.value) {
    return executorList.value
  }
  return executorList.value.filter(executor =>
    executor.appname.toLowerCase().includes(searchKeyword.value.toLowerCase()) ||
    executor.title.toLowerCase().includes(searchKeyword.value.toLowerCase())
  )
})

// 方法
const loadExecutorList = async () => {
  console.log('=== loadExecutorList 开始执行 ===')
  try {
    loading.value = true
    console.log('设置loading为true')

    console.log('准备调用getExecutorList API...')
    const response = await getExecutorList()
    console.log('=== API调用完成 ===')
    console.log('完整API响应:', response)
    console.log('response.data:', response.data)
    console.log('response.data类型:', typeof response.data)
    console.log('response.data是否为数组:', Array.isArray(response.data))

    if (response && response.data) {
      const newData = response.data
      console.log('准备设置的数据:', newData)
      console.log('数据长度:', newData.length)

      // 强制触发响应式更新
      executorList.value = [...newData]

      console.log('设置后的executorList.value:', executorList.value)
      console.log('设置后的长度:', executorList.value.length)

      // 验证响应式是否工作
      setTimeout(() => {
        console.log('延迟检查executorList.value:', executorList.value.length)
      }, 100)
    } else {
      console.error('响应格式错误:', response)
      ElMessage.error('响应数据格式错误')
    }

  } catch (error) {
    console.error('=== 加载执行器列表失败 ===', error)
    ElMessage.error('加载执行器列表失败: ' + error.message)
  } finally {
    loading.value = false
    console.log('设置loading为false')
    console.log('=== loadExecutorList 执行完成 ===')
  }
}

const loadStats = async () => {
  try {
    const response = await getExecutorStats()
    Object.assign(stats, response.data)
  } catch (error) {
    console.error('加载统计信息失败:', error)
  }
}

const handleCreate = () => {
  currentExecutor.value = null
  dialogMode.value = 'create'
  dialogVisible.value = true
}

const handleEdit = (executor: ExecutorInfo) => {
  currentExecutor.value = { ...executor }
  dialogMode.value = 'edit'
  dialogVisible.value = true
}

const handleDelete = async (executor: ExecutorInfo) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除执行器 "${executor.title}" 吗？`,
      '确认删除',
      {
        type: 'warning'
      }
    )
    
    await deleteExecutor(executor.id)
    ElMessage.success('删除成功')
    await loadData()
  } catch (error: any) {
    if (error !== 'cancel') {
      console.error('删除执行器失败:', error)
      ElMessage.error('删除失败')
    }
  }
}

const handleRefresh = () => {
  loadData()
}

const testApiCall = async () => {
  console.log('=== 手动测试API调用 ===')
  try {
    // 直接测试API
    const response = await getExecutorList()
    console.log('手动测试 - API响应:', response)

    // 直接设置数据
    if (response && response.data && Array.isArray(response.data)) {
      executorList.value = response.data
      console.log('手动测试 - 数据设置成功:', executorList.value.length)
      ElMessage.success(`手动测试成功，获取到 ${response.data.length} 条数据`)
    } else {
      console.error('手动测试 - 数据格式错误')
      ElMessage.error('数据格式错误')
    }
  } catch (error) {
    console.error('手动测试 - API调用失败:', error)
    ElMessage.error('API调用失败')
  }
}

const handleSearch = () => {
  // 搜索逻辑已在计算属性中处理
}

const handleDialogSuccess = () => {
  dialogVisible.value = false
  loadData()
}

const formatTime = (time: string) => {
  if (!time) return '-'
  return new Date(time).toLocaleString('zh-CN')
}

const getExecutorStatus = (executor: ExecutorInfo) => {
  // 如果有注册地址列表且不为空，则认为在线
  if (executor.registryList && executor.registryList.length > 0) {
    return 'ONLINE'
  }
  // 如果是手动录入且有地址列表，也认为可能在线（需要进一步检测）
  if (executor.addressType === 1 && executor.addressList && executor.addressList.trim()) {
    return 'ONLINE'
  }
  return 'OFFLINE'
}

const loadData = async () => {
  await Promise.all([
    loadExecutorList(),
    loadStats()
  ])
}

// 生命周期
onMounted(() => {
  loadData()
})
</script>

<style scoped>
.executor-management {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 20px;
}

.header-left h2 {
  margin: 0 0 8px 0;
  font-size: 24px;
  font-weight: 600;
  color: #303133;
}

.header-left p {
  margin: 0;
  color: #909399;
  font-size: 14px;
}

.header-right {
  display: flex;
  gap: 12px;
}

.create-button {
  background: linear-gradient(135deg, #409eff 0%, #667eea 100%);
  border: none;
  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.3);
  transition: all 0.3s ease;
  font-weight: 500;
}

.create-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 16px rgba(64, 158, 255, 0.4);
}

.create-button:active {
  transform: translateY(0);
}

.stats-cards {
  margin-bottom: 20px;
}

.stats-card {
  border: none;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.stats-content {
  display: flex;
  align-items: center;
  gap: 16px;
}

.stats-icon {
  width: 48px;
  height: 48px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
}

.stats-icon.total {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.stats-icon.online {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.stats-icon.offline {
  background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
}

.stats-info {
  flex: 1;
}

.stats-value {
  font-size: 24px;
  font-weight: 600;
  color: #303133;
  line-height: 1;
  margin-bottom: 4px;
}

.stats-label {
  font-size: 14px;
  color: #909399;
}

.table-card {
  border: none;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.text-muted {
  color: #909399;
  font-style: italic;
}
</style>
