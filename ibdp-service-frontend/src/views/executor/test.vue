<template>
  <div class="test-page">
    <h2>执行器API测试页面</h2>
    
    <div class="test-section">
      <h3>测试按钮</h3>
      <el-button @click="testGetList" type="primary">测试获取列表</el-button>
      <el-button @click="testGetStats" type="success">测试获取统计</el-button>
    </div>
    
    <div class="test-section">
      <h3>测试结果</h3>
      <pre>{{ testResult }}</pre>
    </div>
    
    <div class="test-section">
      <h3>执行器列表</h3>
      <ul>
        <li v-for="executor in executors" :key="executor.id">
          {{ executor.id }} - {{ executor.appname }} - {{ executor.title }}
        </li>
      </ul>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { getExecutorList, getExecutorStats } from '@/api/executor-simple'
import type { ExecutorInfo } from '@/api/executor-simple'

const testResult = ref('')
const executors = ref<ExecutorInfo[]>([])

const testGetList = async () => {
  try {
    console.log('开始测试获取列表...')
    testResult.value = '正在获取列表...'
    
    const response = await getExecutorList()
    console.log('获取列表响应:', response)
    
    executors.value = response.data
    testResult.value = `获取列表成功，共 ${response.data.length} 条数据:\n${JSON.stringify(response.data, null, 2)}`
  } catch (error) {
    console.error('获取列表失败:', error)
    testResult.value = `获取列表失败: ${error}`
  }
}

const testGetStats = async () => {
  try {
    console.log('开始测试获取统计...')
    testResult.value = '正在获取统计...'
    
    const response = await getExecutorStats()
    console.log('获取统计响应:', response)
    
    testResult.value = `获取统计成功:\n${JSON.stringify(response.data, null, 2)}`
  } catch (error) {
    console.error('获取统计失败:', error)
    testResult.value = `获取统计失败: ${error}`
  }
}
</script>

<style scoped>
.test-page {
  padding: 20px;
}

.test-section {
  margin-bottom: 30px;
  padding: 20px;
  border: 1px solid #ddd;
  border-radius: 8px;
}

.test-section h3 {
  margin-top: 0;
  color: #333;
}

pre {
  background: #f5f5f5;
  padding: 15px;
  border-radius: 4px;
  white-space: pre-wrap;
  word-wrap: break-word;
  max-height: 400px;
  overflow-y: auto;
}

ul {
  list-style-type: none;
  padding: 0;
}

li {
  padding: 8px;
  margin: 4px 0;
  background: #f9f9f9;
  border-radius: 4px;
}
</style>
