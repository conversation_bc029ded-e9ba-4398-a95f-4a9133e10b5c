<template>
  <el-dialog
    :model-value="visible"
    :title="mode === 'create' ? '新增执行器' : '编辑执行器'"
    width="680px"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    @update:model-value="$emit('update:visible', $event)"
    @close="handleClose"
    class="executor-dialog"
  >


    <!-- 表单内容 -->
    <div class="dialog-content">
      <el-form
        ref="formRef"
        :model="form"
        :rules="rules"
        label-width="120px"
        label-position="right"
        class="executor-form"
      >
        <!-- 基本信息卡片 -->
        <div class="form-section">
          <div class="section-header">
            <el-icon class="section-icon"><Setting /></el-icon>
            <span class="section-title">基本信息</span>
          </div>

          <el-form-item label="执行器名称" prop="appname" class="form-item-enhanced">
            <el-input
              v-model="form.appname"
              placeholder="请输入执行器名称（英文）"
              :disabled="mode === 'edit'"
              size="large"
              clearable
            >
              <template #prefix>
                <el-icon><Key /></el-icon>
              </template>
            </el-input>

          </el-form-item>

          <el-form-item label="执行器标题" prop="title" class="form-item-enhanced">
            <el-input
              v-model="form.title"
              placeholder="请输入执行器标题（中文描述）"
              size="large"
              clearable
            >
              <template #prefix>
                <el-icon><Document /></el-icon>
              </template>
            </el-input>

          </el-form-item>
        </div>

        <!-- 注册配置卡片 -->
        <div class="form-section">
          <div class="section-header">
            <el-icon class="section-icon"><Connection /></el-icon>
            <span class="section-title">注册配置</span>
          </div>

          <el-form-item label="注册方式" prop="addressType" class="form-item-enhanced">
            <el-radio-group v-model="form.addressType" size="large" class="radio-group-enhanced">
              <el-radio :label="0" class="radio-card">
                <div class="radio-content">
                  <div class="radio-header">
                    <el-icon><Refresh /></el-icon>
                    <span>自动注册</span>
                  </div>
                  <div class="radio-desc">执行器自动注册到调度中心</div>
                </div>
              </el-radio>
              <el-radio :label="1" class="radio-card">
                <div class="radio-content">
                  <div class="radio-header">
                    <el-icon><Edit /></el-icon>
                    <span>手动录入</span>
                  </div>
                  <div class="radio-desc">手动指定执行器地址列表</div>
                </div>
              </el-radio>
            </el-radio-group>
          </el-form-item>

          <el-form-item
            v-if="form.addressType === 1"
            label="机器地址"
            prop="addressList"
            class="form-item-enhanced address-item"
          >
            <el-input
              v-model="form.addressList"
              type="textarea"
              :rows="4"
              placeholder="请输入执行器地址，多个地址用逗号分隔&#10;例如：*************:9999,*************:9999"
              size="large"
              class="address-textarea"
            />

          </el-form-item>
        </div>
      </el-form>
    </div>

    <!-- 对话框底部 -->
    <template #footer>
      <div class="dialog-footer">
        <el-button size="large" @click="handleClose">
          <el-icon><Close /></el-icon>
          取消
        </el-button>
        <el-button
          type="primary"
          size="large"
          :loading="loading"
          @click="handleSubmit"
          class="submit-button"
        >
          <el-icon v-if="!loading">
            <Check v-if="mode === 'create'" />
            <Edit v-else />
          </el-icon>
          {{ mode === 'create' ? '创建执行器' : '保存修改' }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, watch } from 'vue'
import { ElMessage } from 'element-plus'
import type { FormInstance, FormRules } from 'element-plus'
import {
  Setting, Key, Document, Connection, Refresh, Edit,
  InfoFilled, Close, Check
} from '@element-plus/icons-vue'
import { createExecutor, updateExecutor } from '@/api/executor'
import type { ExecutorInfo } from '@/types/executor'

// Props
interface Props {
  visible: boolean
  executor?: ExecutorInfo | null
  mode: 'create' | 'edit'
}

const props = withDefaults(defineProps<Props>(), {
  executor: null
})

// Emits
const emit = defineEmits<{
  'update:visible': [value: boolean]
  success: []
}>()

// 响应式数据
const formRef = ref<FormInstance>()
const loading = ref(false)

const form = reactive({
  appname: '',
  title: '',
  addressType: 0,
  addressList: ''
})

// 方法定义（需要在watch之前定义）
const resetForm = () => {
  form.appname = ''
  form.title = ''
  form.addressType = 0
  form.addressList = ''
  formRef.value?.clearValidate()
}

// 表单验证规则
const rules: FormRules = {
  appname: [
    { required: true, message: '请输入执行器名称', trigger: 'blur' },
    { pattern: /^[a-zA-Z][a-zA-Z0-9_-]*$/, message: '执行器名称只能包含字母、数字、下划线和横线，且必须以字母开头', trigger: 'blur' }
  ],
  title: [
    { required: true, message: '请输入执行器标题', trigger: 'blur' }
  ],
  addressType: [
    { required: true, message: '请选择注册方式', trigger: 'change' }
  ],
  addressList: [
    {
      validator: (rule, value, callback) => {
        if (form.addressType === 1) {
          if (!value || value.trim() === '') {
            callback(new Error('请输入机器地址'))
            return
          }

          const addresses = value.split(',').map(addr => addr.trim())
          const addressPattern = /^(\d{1,3}\.){3}\d{1,3}:\d{1,5}$/

          for (const addr of addresses) {
            if (!addressPattern.test(addr)) {
              callback(new Error(`地址格式不正确: ${addr}`))
              return
            }
          }
        }
        callback()
      },
      trigger: 'blur'
    }
  ]
}

// 监听props变化，初始化表单
watch(
  () => props.executor,
  (newExecutor) => {
    if (newExecutor) {
      form.appname = newExecutor.appname || ''
      form.title = newExecutor.title || ''
      form.addressType = newExecutor.addressType || 0
      form.addressList = newExecutor.addressList || ''
    } else {
      resetForm()
    }
  },
  { immediate: true }
)

const handleClose = () => {
  emit('update:visible', false)
  resetForm()
}

const handleSubmit = async () => {
  if (!formRef.value) return

  try {
    const valid = await formRef.value.validate()
    if (!valid) return

    loading.value = true

    const submitData = {
      appname: form.appname,
      title: form.title,
      addressType: form.addressType,
      addressList: form.addressType === 1 ? form.addressList : ''
    }

    if (props.mode === 'create') {
      await createExecutor(submitData)
      ElMessage.success('创建成功')
    } else {
      const updateData = {
        ...submitData,
        id: props.executor!.id,
        updateTime: props.executor!.updateTime,
        registryList: props.executor!.registryList || null
      }
      await updateExecutor(updateData)
      ElMessage.success('保存成功')
    }

    emit('success')
  } catch (error) {
    console.error('提交失败:', error)
    ElMessage.error(props.mode === 'create' ? '创建失败' : '保存失败')
  } finally {
    loading.value = false
  }
}
</script>

<style scoped>
/* 对话框整体样式 */
.executor-dialog :deep(.el-dialog) {
  border-radius: 12px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
}

.executor-dialog :deep(.el-dialog__header) {
  padding: 20px 24px;
  border-bottom: 1px solid #f0f0f0;
}

.executor-dialog :deep(.el-dialog__body) {
  padding: 0;
}

.executor-dialog :deep(.el-dialog__footer) {
  padding: 24px 32px;
  border-top: 1px solid #f0f0f0;
  background: #fafafa;
  border-radius: 0 0 12px 12px;
}



/* 对话框内容 */
.dialog-content {
  padding: 32px;
  background: #fafbfc;
}

.executor-form {
  max-width: none;
}

/* 表单分组 */
.form-section {
  background: white;
  border-radius: 8px;
  padding: 24px;
  margin-bottom: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  border: 1px solid #e8e9ea;
}

.form-section:last-child {
  margin-bottom: 0;
}

.section-header {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 12px;
  border-bottom: 2px solid #f0f0f0;
}

.section-icon {
  margin-right: 8px;
  color: #409eff;
  font-size: 18px;
}

.section-title {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

/* 表单项增强样式 */
.form-item-enhanced {
  margin-bottom: 24px;
}

.form-item-enhanced :deep(.el-form-item__label) {
  font-weight: 500;
  color: #606266;
  line-height: 1.6;
}

.form-item-enhanced :deep(.el-input__wrapper) {
  border-radius: 6px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.04);
  transition: all 0.3s ease;
}

.form-item-enhanced :deep(.el-input__wrapper:hover) {
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.08);
}

.form-item-enhanced :deep(.el-input__wrapper.is-focus) {
  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.2);
}

/* 提示文本样式 */
.form-tip {
  display: flex;
  align-items: center;
  font-size: 12px;
  color: #909399;
  margin-top: 8px;
  line-height: 1.4;
  padding: 8px 12px;
  background: #f8f9fa;
  border-radius: 4px;
  border-left: 3px solid #409eff;
}

.form-tip .el-icon {
  margin-right: 6px;
  color: #409eff;
  font-size: 14px;
}

/* 单选按钮组增强样式 */
.radio-group-enhanced {
  width: 100%;
  display: flex;
  gap: 16px;
}

.radio-card {
  flex: 1;
  margin-right: 0 !important;
}

.radio-card :deep(.el-radio__input) {
  display: none;
}

.radio-card :deep(.el-radio__label) {
  padding: 0;
  width: 100%;
}

.radio-content {
  padding: 16px;
  border: 2px solid #e4e7ed;
  border-radius: 8px;
  background: white;
  cursor: pointer;
  transition: all 0.3s ease;
  height: 100%;
}

.radio-content:hover {
  border-color: #409eff;
  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.15);
  transform: translateY(-2px);
}

.radio-card.is-checked .radio-content {
  border-color: #409eff;
  background: linear-gradient(135deg, #409eff 0%, #667eea 100%);
  color: white;
  box-shadow: 0 6px 16px rgba(64, 158, 255, 0.3);
}

.radio-header {
  display: flex;
  align-items: center;
  font-weight: 600;
  margin-bottom: 4px;
}

.radio-header .el-icon {
  margin-right: 8px;
  font-size: 16px;
}

.radio-desc {
  font-size: 12px;
  opacity: 0.8;
  line-height: 1.4;
}

/* 地址输入框样式 */
.address-item .address-textarea :deep(.el-textarea__inner) {
  border-radius: 6px;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 13px;
  line-height: 1.6;
  resize: vertical;
}

/* 对话框底部 */
.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

.dialog-footer .el-button {
  min-width: 100px;
  border-radius: 6px;
  font-weight: 500;
}

.submit-button {
  background: linear-gradient(135deg, #409eff 0%, #667eea 100%);
  border: none;
  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.3);
  transition: all 0.3s ease;
}

.submit-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 16px rgba(64, 158, 255, 0.4);
}

.submit-button:active {
  transform: translateY(0);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .executor-dialog :deep(.el-dialog) {
    width: 95% !important;
    margin: 5vh auto;
  }

  .dialog-content {
    padding: 20px;
  }

  .form-section {
    padding: 16px;
  }

  .radio-group-enhanced {
    flex-direction: column;
    gap: 12px;
  }
}
</style>
