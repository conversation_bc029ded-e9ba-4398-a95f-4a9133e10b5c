<template>
  <div class="record-container">
    <!-- 搜索筛选 -->
    <div class="search-container">
      <el-form :model="searchForm" class="search-form">
        <!-- 第一行 -->
        <div class="form-row">
          <el-form-item label="请求ID" class="form-item">
            <el-input
              v-model="searchForm.requestId"
              placeholder="请输入请求ID"
              clearable
            />
          </el-form-item>
          <el-form-item label="模板编码" class="form-item">
            <el-input
              v-model="searchForm.templateCode"
              placeholder="请输入模板编码"
              clearable
            />
          </el-form-item>
          <el-form-item label="通道类型" class="form-item">
            <el-select
              v-model="searchForm.channelType"
              placeholder="请选择通道类型"
              clearable
            >
              <el-option label="企业微信" value="WECHAT" />
              <el-option label="钉钉" value="DINGTALK" />
              <el-option label="飞书" value="FEISHU" />
              <el-option label="邮件" value="EMAIL" />
            </el-select>
          </el-form-item>
          <el-form-item label="发送状态" class="form-item">
            <el-select
              v-model="searchForm.sendStatus"
              placeholder="请选择状态"
              clearable
            >
              <el-option label="待发送" value="PENDING" />
              <el-option label="发送成功" value="SUCCESS" />
              <el-option label="发送失败" value="FAILED" />
              <el-option label="重试中" value="RETRY" />
              <el-option label="已取消" value="CANCELLED" />
            </el-select>
          </el-form-item>
        </div>

        <!-- 第二行 -->
        <div class="form-row">
          <el-form-item label="来源系统" class="form-item">
            <el-input
              v-model="searchForm.sourceSystem"
              placeholder="请输入来源系统"
              clearable
            />
          </el-form-item>
          <el-form-item label="时间范围" class="form-item-wide">
            <el-date-picker
              v-model="timeRange"
              type="datetimerange"
              range-separator="至"
              start-placeholder="开始时间"
              end-placeholder="结束时间"
              format="YYYY-MM-DD HH:mm:ss"
              value-format="YYYY-MM-DD HH:mm:ss"
            />
          </el-form-item>
          <el-form-item class="button-group">
            <el-button type="primary" @click="handleSearch">
              <el-icon><Search /></el-icon>
              搜索
            </el-button>
            <el-button @click="handleReset">
              <el-icon><Refresh /></el-icon>
              重置
            </el-button>
          </el-form-item>
        </div>
      </el-form>
    </div>

    <!-- 统计卡片 -->
    <div class="stats-cards">
      <el-row :gutter="16">
        <el-col :span="6">
          <div class="stat-card">
            <div class="stat-icon success">
              <el-icon><SuccessFilled /></el-icon>
            </div>
            <div class="stat-content">
              <div class="stat-value">{{ stats.successCount || 0 }}</div>
              <div class="stat-label">发送成功</div>
            </div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="stat-card">
            <div class="stat-icon danger">
              <el-icon><CircleCloseFilled /></el-icon>
            </div>
            <div class="stat-content">
              <div class="stat-value">{{ stats.failedCount || 0 }}</div>
              <div class="stat-label">发送失败</div>
            </div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="stat-card">
            <div class="stat-icon warning">
              <el-icon><Loading /></el-icon>
            </div>
            <div class="stat-content">
              <div class="stat-value">{{ stats.pendingCount || 0 }}</div>
              <div class="stat-label">待发送</div>
            </div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="stat-card">
            <div class="stat-icon info">
              <el-icon><DataAnalysis /></el-icon>
            </div>
            <div class="stat-content">
              <div class="stat-value">{{ stats.successRate || 0 }}%</div>
              <div class="stat-label">成功率</div>
            </div>
          </div>
        </el-col>
      </el-row>
    </div>

    <!-- 操作栏 -->
    <div class="action-bar">
      <div class="action-left">
        <el-button
          type="danger"
          @click="handleBatchDelete"
          :disabled="selectedRows.length === 0"
          :icon="Delete"
        >
          批量删除
        </el-button>
        <el-button type="warning" @click="handleCleanHistory" :icon="Delete">
          清理历史
        </el-button>
      </div>
      <div class="action-right">
        <el-button @click="handleRefresh" :icon="Refresh">
          刷新
        </el-button>
      </div>
    </div>

    <!-- 数据表格 -->
    <div class="table-container">
      <el-table
        v-loading="loading"
        :data="tableData"
        @selection-change="handleSelectionChange"
        stripe
        border
        style="width: 100%"
      >
        <el-table-column type="selection" width="55" />
        <el-table-column prop="requestId" label="请求ID" width="200" show-overflow-tooltip />
        <el-table-column prop="templateCode" label="模板编码" width="120" />
        <el-table-column prop="channelType" label="通道类型" width="100">
          <template #default="{ row }">
            <el-tag :type="getChannelTypeTagType(row.channelType)" size="small">
              {{ getChannelTypeName(row.channelType) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="sendStatus" label="发送状态" width="100">
          <template #default="{ row }">
            <el-tag :type="getStatusTagType(row.sendStatus)" size="small">
              {{ getStatusName(row.sendStatus) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="title" label="标题" width="150" show-overflow-tooltip />
        <el-table-column prop="sourceSystem" label="来源系统" width="120" />
        <el-table-column prop="retryCount" label="重试次数" width="80" align="center" />
        <el-table-column prop="sendTime" label="发送时间" width="160" />
        <el-table-column label="操作" width="180" fixed="right">
          <template #default="{ row }">
            <el-button type="primary" size="small" @click="handleViewDetail(row)" :icon="View">
              详情
            </el-button>
            <el-button
              v-if="row.sendStatus === 'FAILED'"
              type="warning"
              size="small"
              @click="handleRetry(row)"
              :icon="RefreshRight"
            >
              重试
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 分页 -->
    <div class="pagination-container">
      <el-pagination
        v-model:current-page="pagination.current"
        v-model:page-size="pagination.size"
        :total="pagination.total"
        :page-sizes="[10, 20, 50, 100]"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>

    <!-- 详情对话框 -->
    <RecordDetailDialog
      v-model:visible="detailVisible"
      :record-data="currentRecord"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, watch } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Search,
  Refresh,
  Delete,
  View,
  RefreshRight,
  SuccessFilled,
  CircleCloseFilled,
  Loading,
  DataAnalysis
} from '@element-plus/icons-vue'
import RecordDetailDialog from './components/RecordDetailDialog.vue'
import { getRecordList, alertRecordApi, alertSendApi } from '@/api/alert'

// 搜索表单
const searchForm = reactive({
  requestId: '',
  templateCode: '',
  channelType: '',
  sendStatus: '',
  sourceSystem: '',
  businessId: ''
})

// 时间范围
const timeRange = ref([])

// 表格数据
const loading = ref(false)
const tableData = ref([])
const selectedRows = ref([])

// 分页
const pagination = reactive({
  current: 1,
  size: 10,
  total: 0
})

// 统计数据
const stats = ref({
  totalCount: 0,
  successCount: 0,
  failedCount: 0,
  pendingCount: 0,
  retryCount: 0,
  successRate: 0
})

// 对话框
const detailVisible = ref(false)
const currentRecord = ref(null)

// 获取通道类型名称
const getChannelTypeName = (type: string) => {
  const typeMap = {
    WECHAT: '企微',
    DINGTALK: '钉钉',
    FEISHU: '飞书',
    EMAIL: '邮件'
  }
  return typeMap[type] || type
}

// 获取通道类型标签类型
const getChannelTypeTagType = (type: string) => {
  const typeMap = {
    WECHAT: 'success',
    DINGTALK: 'primary',
    FEISHU: 'warning',
    EMAIL: 'info'
  }
  return typeMap[type] || 'info'
}

// 获取状态名称
const getStatusName = (status: string) => {
  const statusMap = {
    PENDING: '待发送',
    SUCCESS: '成功',
    FAILED: '失败',
    RETRY: '重试中',
    CANCELLED: '已取消'
  }
  return statusMap[status] || status
}

// 获取状态标签类型
const getStatusTagType = (status: string) => {
  const statusMap = {
    PENDING: 'warning',
    SUCCESS: 'success',
    FAILED: 'danger',
    RETRY: 'primary',
    CANCELLED: 'info'
  }
  return statusMap[status] || 'info'
}

// 加载统计数据
const loadStats = async () => {
  try {
    const response = await alertRecordApi.getStats(24)
    if (response.code === 200) {
      stats.value = response.content
    }
  } catch (error) {
    console.error('加载统计数据失败', error)
  }
}

// 加载数据
const loadData = async () => {
  loading.value = true
  try {
    const params = {
      start: (pagination.current - 1) * pagination.size,
      length: pagination.size,
      ...searchForm
    }

    // 添加时间范围参数
    if (timeRange.value && timeRange.value.length === 2) {
      params.startTime = timeRange.value[0]
      params.endTime = timeRange.value[1]
    }

    const response = await getRecordList(params)
    if (response && response.code === 200) {
      tableData.value = response.content?.data || []
      pagination.total = response.content?.recordsTotal || 0
    } else {
      tableData.value = []
      pagination.total = 0
      if (response?.msg) {
        ElMessage.error(response.msg)
      }
    }
  } catch (error) {
    console.error('加载数据失败:', error)
    ElMessage.error('加载数据失败')
    tableData.value = []
    pagination.total = 0
  } finally {
    loading.value = false
  }
}

// 搜索
const handleSearch = () => {
  pagination.current = 1
  loadData()
}

// 重置
const handleReset = () => {
  Object.assign(searchForm, {
    requestId: '',
    templateCode: '',
    channelType: '',
    sendStatus: '',
    sourceSystem: '',
    businessId: ''
  })
  timeRange.value = []
  handleSearch()
}

// 刷新
const handleRefresh = () => {
  loadData()
  loadStats()
}

// 查看详情
const handleViewDetail = (row: any) => {
  currentRecord.value = row
  detailVisible.value = true
}

// 重试
const handleRetry = async (row: any) => {
  try {
    const response = await alertSendApi.retry(row.id)
    if (response.code === 200) {
      ElMessage.success('重试请求已提交')
      loadData()
    } else {
      ElMessage.error(response.msg || '重试失败')
    }
  } catch (error) {
    ElMessage.error('重试失败')
  }
}

// 批量删除
const handleBatchDelete = async () => {
  if (selectedRows.value.length === 0) {
    ElMessage.warning('请选择要删除的记录')
    return
  }
  
  try {
    await ElMessageBox.confirm(`确定要删除选中的 ${selectedRows.value.length} 条记录吗？`, '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    
    const ids = selectedRows.value.map((row: any) => row.id)
    const response = await alertRecordApi.batchDelete(ids)
    if (response.code === 200) {
      ElMessage.success('批量删除成功')
      loadData()
      loadStats()
    } else {
      ElMessage.error(response.msg || '批量删除失败')
    }
  } catch (error) {
    // 用户取消删除
  }
}

// 清理历史记录
const handleCleanHistory = async () => {
  try {
    await ElMessageBox.confirm('确定要清理30天前的历史记录吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    
    const response = await alertSendApi.cleanHistory(30)
    if (response.code === 200) {
      ElMessage.success('历史记录清理成功')
      loadData()
      loadStats()
    } else {
      ElMessage.error(response.msg || '清理失败')
    }
  } catch (error) {
    // 用户取消操作
  }
}

// 选择变化
const handleSelectionChange = (selection: any[]) => {
  selectedRows.value = selection
}

// 分页变化
const handleSizeChange = (size: number) => {
  pagination.size = size
  pagination.current = 1
  loadData()
}

const handleCurrentChange = (current: number) => {
  pagination.current = current
  loadData()
}

// 监听时间范围变化
watch(timeRange, () => {
  if (timeRange.value && timeRange.value.length === 2) {
    loadStats()
  }
})

// 初始化
onMounted(() => {
  loadData()
  loadStats()
})
</script>

<style scoped>
.record-container {
  padding: 20px;
  background: transparent;
}

.search-container {
  background: #fff;
  padding: 24px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  margin-bottom: 20px;
}

.search-form {
  margin: 0;
}

.form-row {
  display: flex;
  align-items: flex-end;
  gap: 20px;
  margin-bottom: 16px;
}

.form-row:last-child {
  margin-bottom: 0;
}

.form-item {
  flex: 1;
  min-width: 200px;
}

.form-item-wide {
  flex: 2;
  min-width: 300px;
}

.form-row :deep(.el-form-item) {
  margin-bottom: 0;
}

.form-row :deep(.el-form-item__label) {
  font-weight: 500;
  color: #606266;
  margin-bottom: 6px;
}

.form-row :deep(.el-form-item__content) {
  width: 100%;
}

.form-row :deep(.el-input),
.form-row :deep(.el-select) {
  width: 100%;
}

.form-row :deep(.el-date-editor) {
  width: 100%;
}

.button-group {
  flex-shrink: 0;
  margin-left: auto;
}

.button-group .el-button {
  margin-left: 12px;
}

.button-group .el-button:first-child {
  margin-left: 0;
}

.stats-cards {
  margin-bottom: 20px;
}

.stat-card {
  background: #fff;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.12), 0 0 6px rgba(0, 0, 0, 0.04);
  display: flex;
  align-items: center;
  gap: 16px;
}

.stat-icon {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  color: #fff;
}

.stat-icon.success {
  background: #67c23a;
}

.stat-icon.danger {
  background: #f56c6c;
}

.stat-icon.warning {
  background: #e6a23c;
}

.stat-icon.info {
  background: #409eff;
}

.stat-content {
  flex: 1;
}

.stat-value {
  font-size: 24px;
  font-weight: 600;
  color: #303133;
  line-height: 1;
}

.stat-label {
  font-size: 14px;
  color: #909399;
  margin-top: 4px;
}

.action-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: #fff;
  padding: 16px 20px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  margin-bottom: 20px;
}

.action-left {
  display: flex;
  gap: 12px;
}

.table-container {
  background: #fff;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.pagination-container {
  display: flex;
  justify-content: center;
  padding: 20px;
  background: transparent;
}

/* 表格样式优化 */
:deep(.el-table) {
  border: none;
}

:deep(.el-table th) {
  background: #fafafa;
  border-bottom: 1px solid #e4e7ed;
  font-weight: 600;
  color: #606266;
}

:deep(.el-table td) {
  border-bottom: 1px solid #f0f0f0;
}

:deep(.el-table tr:hover > td) {
  background-color: #f5f7fa;
}

:deep(.el-table__body tr.current-row > td) {
  background-color: #ecf5ff;
}

/* 响应式设计 */
@media (max-width: 1400px) {
  .form-item {
    min-width: 180px;
  }

  .form-item-wide {
    min-width: 280px;
  }
}

@media (max-width: 1200px) {
  .form-row {
    gap: 16px;
  }

  .form-item {
    min-width: 160px;
  }

  .form-item-wide {
    min-width: 260px;
  }
}

@media (max-width: 992px) {
  .form-row {
    flex-wrap: wrap;
  }

  .form-item {
    flex: 1 1 calc(50% - 10px);
    min-width: 200px;
  }

  .form-item-wide {
    flex: 1 1 100%;
    min-width: 300px;
  }

  .button-group {
    flex: 1 1 100%;
    margin-left: 0;
    margin-top: 8px;
  }
}

@media (max-width: 768px) {
  .record-container {
    padding: 10px;
  }

  .search-container {
    padding: 16px;
  }

  .form-row {
    flex-direction: column;
    align-items: stretch;
    gap: 12px;
  }

  .form-item,
  .form-item-wide {
    flex: none;
    min-width: auto;
  }

  .button-group {
    margin-left: 0;
    margin-top: 8px;
    display: flex;
    gap: 12px;
  }

  .button-group .el-button {
    margin-left: 0;
    flex: 1;
  }
}
</style>
