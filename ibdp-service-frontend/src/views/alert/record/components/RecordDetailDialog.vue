<template>
  <el-dialog
    v-model="dialogVisible"
    title="发送记录详情"
    width="900px"
    :before-close="handleClose"
  >
    <div v-if="recordData" class="record-detail">
      <!-- 基本信息 -->
      <div class="detail-section">
        <h4>基本信息</h4>
        <el-descriptions :column="2" border>
          <el-descriptions-item label="请求ID">
            <el-tag type="info">{{ recordData.requestId }}</el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="模板编码">
            {{ recordData.templateCode }}
          </el-descriptions-item>
          <el-descriptions-item label="通道类型">
            <el-tag :type="getChannelTypeTagType(recordData.channelType)">
              {{ getChannelTypeName(recordData.channelType) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="发送状态">
            <el-tag :type="getStatusTagType(recordData.sendStatus)">
              {{ getStatusName(recordData.sendStatus) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="配置名称">
            {{ recordData.configName || '默认配置' }}
          </el-descriptions-item>
          <el-descriptions-item label="来源系统">
            {{ recordData.sourceSystem }}
          </el-descriptions-item>
          <el-descriptions-item label="业务ID">
            {{ recordData.businessId || '无' }}
          </el-descriptions-item>
          <el-descriptions-item label="重试次数">
            {{ recordData.retryCount }} / {{ recordData.maxRetryCount }}
          </el-descriptions-item>
        </el-descriptions>
      </div>

      <!-- 时间信息 -->
      <div class="detail-section">
        <h4>时间信息</h4>
        <el-descriptions :column="2" border>
          <el-descriptions-item label="发送时间">
            {{ recordData.sendTime }}
          </el-descriptions-item>
          <el-descriptions-item label="下次重试时间">
            {{ recordData.nextRetryTime || '无' }}
          </el-descriptions-item>
        </el-descriptions>
      </div>

      <!-- 消息内容 -->
      <div class="detail-section">
        <h4>消息内容</h4>
        <div class="content-container">
          <div class="content-item">
            <label>标题：</label>
            <div class="content-value">{{ recordData.title || '无标题' }}</div>
          </div>
          <div class="content-item">
            <label>内容：</label>
            <div class="content-value">
              <div v-if="isJsonContent(recordData.content)" class="json-content">
                <pre>{{ formatJsonContent(recordData.content) }}</pre>
              </div>
              <div v-else class="text-content">
                {{ recordData.content }}
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 接收人信息 -->
      <div v-if="recordData.recipients" class="detail-section">
        <h4>接收人信息</h4>
        <div class="recipients-container">
          <el-tag
            v-for="(recipient, index) in getRecipientsList(recordData.recipients)"
            :key="index"
            class="recipient-tag"
            type="info"
          >
            {{ recipient }}
          </el-tag>
        </div>
      </div>

      <!-- 变量信息 -->
      <div v-if="recordData.variables" class="detail-section">
        <h4>变量信息</h4>
        <div class="variables-container">
          <el-table :data="getVariablesList(recordData.variables)" border>
            <el-table-column prop="name" label="变量名" width="200" />
            <el-table-column prop="value" label="变量值" show-overflow-tooltip />
          </el-table>
        </div>
      </div>

      <!-- 响应信息 -->
      <div class="detail-section">
        <h4>响应信息</h4>
        <el-descriptions :column="1" border>
          <el-descriptions-item label="响应码">
            <el-tag :type="recordData.responseCode === '200' ? 'success' : 'danger'">
              {{ recordData.responseCode || '无' }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="响应消息">
            <div class="response-message">
              {{ recordData.responseMessage || '无' }}
            </div>
          </el-descriptions-item>
        </el-descriptions>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">关闭</el-button>
        <el-button
          v-if="recordData?.sendStatus === 'FAILED'"
          type="warning"
          @click="handleRetry"
          :loading="retrying"
        >
          重试发送
        </el-button>
        <el-button type="primary" @click="handleCopyDetail">
          复制详情
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { ElMessage } from 'element-plus'
import { alertSendApi } from '@/api/alert'

interface Props {
  visible: boolean
  recordData?: any
}

interface Emits {
  (e: 'update:visible', value: boolean): void
  (e: 'retry-success'): void
}

const props = withDefaults(defineProps<Props>(), {
  visible: false,
  recordData: null
})

const emit = defineEmits<Emits>()

// 对话框显示状态
const dialogVisible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
})

// 重试状态
const retrying = ref(false)

// 获取通道类型名称
const getChannelTypeName = (type: string) => {
  const typeMap = {
    WECHAT: '企业微信',
    DINGTALK: '钉钉',
    FEISHU: '飞书',
    EMAIL: '邮件'
  }
  return typeMap[type] || type
}

// 获取通道类型标签类型
const getChannelTypeTagType = (type: string) => {
  const typeMap = {
    WECHAT: 'success',
    DINGTALK: 'primary',
    FEISHU: 'warning',
    EMAIL: 'info'
  }
  return typeMap[type] || 'info'
}

// 获取状态名称
const getStatusName = (status: string) => {
  const statusMap = {
    PENDING: '待发送',
    SUCCESS: '发送成功',
    FAILED: '发送失败',
    RETRY: '重试中',
    CANCELLED: '已取消'
  }
  return statusMap[status] || status
}

// 获取状态标签类型
const getStatusTagType = (status: string) => {
  const statusMap = {
    PENDING: 'warning',
    SUCCESS: 'success',
    FAILED: 'danger',
    RETRY: 'primary',
    CANCELLED: 'info'
  }
  return statusMap[status] || 'info'
}

// 判断是否为JSON内容
const isJsonContent = (content: string) => {
  if (!content) return false
  try {
    JSON.parse(content)
    return true
  } catch {
    return false
  }
}

// 格式化JSON内容
const formatJsonContent = (content: string) => {
  try {
    return JSON.stringify(JSON.parse(content), null, 2)
  } catch {
    return content
  }
}

// 获取接收人列表
const getRecipientsList = (recipients: string) => {
  try {
    return JSON.parse(recipients)
  } catch {
    return recipients.split(',').map(r => r.trim())
  }
}

// 获取变量列表
const getVariablesList = (variables: string) => {
  try {
    const vars = JSON.parse(variables)
    return Object.entries(vars).map(([name, value]) => ({
      name,
      value: String(value)
    }))
  } catch {
    return []
  }
}

// 重试发送
const handleRetry = async () => {
  if (!props.recordData) return

  retrying.value = true
  try {
    const response = await alertSendApi.retry(props.recordData.id)
    if (response.code === 200) {
      ElMessage.success('重试请求已提交')
      emit('retry-success')
      handleClose()
    } else {
      ElMessage.error(response.msg || '重试失败')
    }
  } catch (error) {
    ElMessage.error('重试失败')
  } finally {
    retrying.value = false
  }
}

// 复制详情
const handleCopyDetail = async () => {
  if (!props.recordData) return

  const detail = `
发送记录详情
============
请求ID: ${props.recordData.requestId}
模板编码: ${props.recordData.templateCode}
通道类型: ${getChannelTypeName(props.recordData.channelType)}
发送状态: ${getStatusName(props.recordData.sendStatus)}
配置名称: ${props.recordData.configName || '默认配置'}
来源系统: ${props.recordData.sourceSystem}
业务ID: ${props.recordData.businessId || '无'}
重试次数: ${props.recordData.retryCount}/${props.recordData.maxRetryCount}
发送时间: ${props.recordData.sendTime}
标题: ${props.recordData.title || '无标题'}
内容: ${props.recordData.content}
响应码: ${props.recordData.responseCode || '无'}
响应消息: ${props.recordData.responseMessage || '无'}
  `.trim()

  try {
    await navigator.clipboard.writeText(detail)
    ElMessage.success('详情已复制到剪贴板')
  } catch (error) {
    // 降级方案
    const textArea = document.createElement('textarea')
    textArea.value = detail
    document.body.appendChild(textArea)
    textArea.select()
    document.execCommand('copy')
    document.body.removeChild(textArea)
    ElMessage.success('详情已复制到剪贴板')
  }
}

// 关闭对话框
const handleClose = () => {
  emit('update:visible', false)
}
</script>

<style scoped>
.record-detail {
  max-height: 70vh;
  overflow-y: auto;
}

.detail-section {
  margin-bottom: 24px;
}

.detail-section h4 {
  margin: 0 0 16px 0;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
  border-left: 4px solid #409eff;
  padding-left: 12px;
}

.content-container {
  border: 1px solid #dcdfe6;
  border-radius: 6px;
  padding: 16px;
  background: #fafafa;
}

.content-item {
  margin-bottom: 16px;
}

.content-item:last-child {
  margin-bottom: 0;
}

.content-item label {
  font-weight: 600;
  color: #606266;
  margin-bottom: 8px;
  display: block;
}

.content-value {
  background: #fff;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  padding: 12px;
  min-height: 40px;
}

.json-content pre {
  margin: 0;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 13px;
  line-height: 1.5;
  color: #303133;
  white-space: pre-wrap;
  word-break: break-all;
}

.text-content {
  line-height: 1.6;
  word-break: break-all;
}

.recipients-container {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.recipient-tag {
  margin: 0;
}

.variables-container {
  border: 1px solid #dcdfe6;
  border-radius: 6px;
  overflow: hidden;
}

.response-message {
  line-height: 1.6;
  word-break: break-all;
  max-height: 100px;
  overflow-y: auto;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}
</style>
