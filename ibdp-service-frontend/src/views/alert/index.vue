<template>
  <div class="alert-container">
    <!-- 左侧菜单 -->
    <div class="sidebar">
      <div class="sidebar-header">
        <h2 class="sidebar-title">
          <el-icon><Bell /></el-icon>
          告警中心
        </h2>
      </div>
      <el-menu
        :default-active="activeMenu"
        class="sidebar-menu"
        @select="handleMenuSelect"
        :router="true"
      >
        <el-menu-item index="/alert/template">
          <el-icon><Document /></el-icon>
          <span>告警模板</span>
        </el-menu-item>
        <el-menu-item index="/alert/channel">
          <el-icon><Connection /></el-icon>
          <span>通道配置</span>
        </el-menu-item>
        <el-menu-item index="/alert/record">
          <el-icon><List /></el-icon>
          <span>发送记录</span>
        </el-menu-item>
      </el-menu>
    </div>

    <!-- 右侧内容区域 -->
    <div class="main-content">
      <router-view />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { Bell, Document, Connection, List } from '@element-plus/icons-vue'

const router = useRouter()
const route = useRoute()

// 当前激活的菜单
const activeMenu = ref('/alert/template')

// 监听路由变化，更新激活的菜单
watch(
  () => route.path,
  (newPath) => {
    if (newPath.includes('/alert/template')) {
      activeMenu.value = '/alert/template'
    } else if (newPath.includes('/alert/channel')) {
      activeMenu.value = '/alert/channel'
    } else if (newPath.includes('/alert/record')) {
      activeMenu.value = '/alert/record'
    }
  },
  { immediate: true }
)

// 处理菜单选择
const handleMenuSelect = (index: string) => {
  router.push(index)
}
</script>

<style scoped>
.alert-container {
  min-height: calc(100vh - 60px); /* 减去顶部导航栏高度 */
  display: flex;
  background: #f5f7fa;
}

.sidebar {
  width: 240px;
  height: calc(100vh - 60px); /* 设置为100%高度减去顶部导航栏 */
  background: #fff;
  border-right: 1px solid #e4e7ed;
  display: flex;
  flex-direction: column;
  box-shadow: 2px 0 4px rgba(0, 0, 0, 0.1);
  overflow-y: auto;
}

.sidebar-header {
  padding: 20px 16px;
  border-bottom: 1px solid #e4e7ed;
  background: #fafafa;
  flex-shrink: 0; /* 防止头部被压缩 */
}

.sidebar-title {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #303133;
  display: flex;
  align-items: center;
  gap: 8px;
}

.sidebar-title .el-icon {
  color: #409eff;
  font-size: 20px;
}

.sidebar-menu {
  flex: 1;
  border: none;
  background: transparent;
  height: 100%;
}

.sidebar-menu :deep(.el-menu-item) {
  height: 48px;
  line-height: 48px;
  padding: 0 20px;
  margin: 4px 8px;
  border-radius: 6px;
  color: #606266;
  font-size: 14px;
  transition: all 0.3s;
}

.sidebar-menu :deep(.el-menu-item:hover) {
  background-color: #f5f7fa;
  color: #409eff;
}

.sidebar-menu :deep(.el-menu-item.is-active) {
  background-color: #ecf5ff;
  color: #409eff;
  font-weight: 500;
}

.sidebar-menu :deep(.el-menu-item .el-icon) {
  margin-right: 8px;
  font-size: 16px;
}

.main-content {
  flex: 1;
  height: calc(100vh - 60px); /* 设置为100%高度减去顶部导航栏 */
  overflow: auto;
  background: #f5f7fa;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .alert-container {
    flex-direction: column;
    min-height: calc(100vh - 60px);
  }

  .sidebar {
    width: 100%;
    height: auto;
    border-right: none;
    border-bottom: 1px solid #e4e7ed;
    overflow-y: visible;
  }

  .sidebar-header {
    padding: 16px;
  }

  .sidebar-title {
    font-size: 16px;
  }

  .sidebar-menu :deep(.el-menu-item) {
    height: 44px;
    line-height: 44px;
    margin: 2px 4px;
  }

  .sidebar-menu {
    height: auto;
  }

  .main-content {
    flex: 1;
    height: auto;
    min-height: calc(100vh - 200px); /* 减去侧边栏高度 */
  }
}
</style>
