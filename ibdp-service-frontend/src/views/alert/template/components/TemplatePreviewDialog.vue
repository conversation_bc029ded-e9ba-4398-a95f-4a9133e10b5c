<template>
  <el-dialog
    v-model="dialogVisible"
    title="模板预览"
    width="900px"
    :before-close="handleClose"
  >
    <div class="preview-container">
      <!-- 模板信息 -->
      <div class="template-info">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="模板编码">
            {{ templateData?.templateCode }}
          </el-descriptions-item>
          <el-descriptions-item label="模板名称">
            {{ templateData?.templateName }}
          </el-descriptions-item>
          <el-descriptions-item label="通道类型">
            <el-tag :type="getChannelTypeTagType(templateData?.channelType)">
              {{ getChannelTypeName(templateData?.channelType) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="状态">
            <el-tag :type="templateData?.enabled === 1 ? 'success' : 'danger'">
              {{ templateData?.enabled === 1 ? '启用' : '禁用' }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="描述" :span="2">
            {{ templateData?.description || '无' }}
          </el-descriptions-item>
        </el-descriptions>
      </div>

      <!-- 变量输入 -->
      <div class="variables-section">
        <h4>变量设置</h4>
        <div class="variables-form">
          <el-row :gutter="16">
            <el-col
              v-for="variable in templateVariables"
              :key="variable.name"
              :span="8"
            >
              <el-form-item :label="variable.description || variable.name">
                <el-input
                  v-model="variableValues[variable.name]"
                  :placeholder="`请输入${variable.description || variable.name}`"
                />
              </el-form-item>
            </el-col>
          </el-row>
          <div class="preview-actions">
            <el-button type="primary" @click="handlePreview" :loading="previewing">
              预览效果
            </el-button>
            <el-button @click="handleReset">
              重置变量
            </el-button>
          </div>
        </div>
      </div>

      <!-- 原始模板 -->
      <div class="template-section">
        <h4>原始模板</h4>
        <div class="template-content">
          <pre>{{ templateData?.templateContent }}</pre>
        </div>
      </div>

      <!-- 预览结果 -->
      <div v-if="previewResult" class="preview-section">
        <h4>预览结果</h4>
        <div class="preview-result">
          <div v-if="templateData?.channelType === 'EMAIL'" class="email-preview">
            <div class="email-header">
              <strong>主题：</strong>{{ extractEmailSubject(previewResult) }}
            </div>
            <div class="email-body">
              <strong>内容：</strong>
              <div v-html="formatEmailContent(previewResult)"></div>
            </div>
          </div>
          <div v-else class="message-preview">
            <pre>{{ previewResult }}</pre>
          </div>
        </div>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">关闭</el-button>
        <el-button type="primary" @click="handleCopyResult" :disabled="!previewResult">
          复制结果
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, watch, computed } from 'vue'
import { ElMessage } from 'element-plus'
import { alertTemplateApi } from '@/api/alert'

interface Props {
  visible: boolean
  templateData?: any
}

interface Emits {
  (e: 'update:visible', value: boolean): void
}

const props = withDefaults(defineProps<Props>(), {
  visible: false,
  templateData: null
})

const emit = defineEmits<Emits>()

// 对话框显示状态
const dialogVisible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
})

// 模板变量
const templateVariables = ref([])
const variableValues = reactive({})

// 预览结果
const previewResult = ref('')
const previewing = ref(false)

// 获取通道类型名称
const getChannelTypeName = (type: string) => {
  const typeMap = {
    WECHAT: '企业微信',
    DINGTALK: '钉钉',
    FEISHU: '飞书',
    EMAIL: '邮件'
  }
  return typeMap[type] || type
}

// 获取通道类型标签类型
const getChannelTypeTagType = (type: string) => {
  const typeMap = {
    WECHAT: 'success',
    DINGTALK: 'primary',
    FEISHU: 'warning',
    EMAIL: 'info'
  }
  return typeMap[type] || 'info'
}

// 监听模板数据变化
watch(
  () => props.templateData,
  (newData) => {
    if (newData) {
      // 解析模板变量
      parseTemplateVariables(newData)
      // 重置预览结果
      previewResult.value = ''
    }
  },
  { immediate: true }
)

// 解析模板变量
const parseTemplateVariables = (templateData: any) => {
  templateVariables.value = []
  Object.keys(variableValues).forEach(key => {
    delete variableValues[key]
  })

  if (templateData.variables) {
    try {
      const variables = JSON.parse(templateData.variables)
      templateVariables.value = variables
      
      // 初始化变量值
      variables.forEach((variable: any) => {
        variableValues[variable.name] = getDefaultValue(variable)
      })
    } catch (error) {
      console.error('解析模板变量失败', error)
    }
  }

  // 如果没有定义变量，从模板内容中提取
  if (templateVariables.value.length === 0) {
    const content = templateData.templateContent || ''
    const matches = content.match(/\$\{([^}]+)\}/g)
    if (matches) {
      const uniqueVars = [...new Set(matches.map(match => match.slice(2, -1)))]
      templateVariables.value = uniqueVars.map(name => ({
        name,
        description: name,
        type: 'string'
      }))
      
      // 初始化变量值
      uniqueVars.forEach(name => {
        variableValues[name] = getDefaultValue({ name, type: 'string' })
      })
    }
  }
}

// 获取默认值
const getDefaultValue = (variable: any) => {
  switch (variable.type) {
    case 'number':
      return '0'
    case 'date':
      return new Date().toLocaleString()
    default:
      return `示例${variable.description || variable.name}`
  }
}

// 预览模板
const handlePreview = async () => {
  if (!props.templateData) return

  previewing.value = true
  try {
    const response = await alertTemplateApi.preview(
      props.templateData.templateCode,
      props.templateData.channelType,
      variableValues
    )
    
    if (response.code === 200) {
      previewResult.value = response.content
      ElMessage.success('预览生成成功')
    } else {
      ElMessage.error(response.msg || '预览失败')
    }
  } catch (error) {
    ElMessage.error('预览失败')
  } finally {
    previewing.value = false
  }
}

// 重置变量
const handleReset = () => {
  templateVariables.value.forEach((variable: any) => {
    variableValues[variable.name] = getDefaultValue(variable)
  })
}

// 提取邮件主题
const extractEmailSubject = (content: string) => {
  try {
    const parsed = JSON.parse(content)
    return parsed.subject || '无主题'
  } catch {
    return '无主题'
  }
}

// 格式化邮件内容
const formatEmailContent = (content: string) => {
  try {
    const parsed = JSON.parse(content)
    const body = parsed.body || content
    return body.replace(/\n/g, '<br>')
  } catch {
    return content.replace(/\n/g, '<br>')
  }
}

// 复制结果
const handleCopyResult = async () => {
  if (!previewResult.value) return

  try {
    await navigator.clipboard.writeText(previewResult.value)
    ElMessage.success('复制成功')
  } catch (error) {
    // 降级方案
    const textArea = document.createElement('textarea')
    textArea.value = previewResult.value
    document.body.appendChild(textArea)
    textArea.select()
    document.execCommand('copy')
    document.body.removeChild(textArea)
    ElMessage.success('复制成功')
  }
}

// 关闭对话框
const handleClose = () => {
  emit('update:visible', false)
  previewResult.value = ''
}
</script>

<style scoped>
.preview-container {
  max-height: 70vh;
  overflow-y: auto;
}

.template-info {
  margin-bottom: 24px;
}

.variables-section,
.template-section,
.preview-section {
  margin-bottom: 24px;
}

.variables-section h4,
.template-section h4,
.preview-section h4 {
  margin: 0 0 16px 0;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.variables-form {
  background: #f8f9fa;
  padding: 16px;
  border-radius: 6px;
}

.preview-actions {
  margin-top: 16px;
  text-align: center;
}

.template-content {
  background: #f5f7fa;
  border: 1px solid #dcdfe6;
  border-radius: 6px;
  padding: 16px;
  max-height: 200px;
  overflow-y: auto;
}

.template-content pre {
  margin: 0;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 13px;
  line-height: 1.5;
  color: #303133;
  white-space: pre-wrap;
  word-break: break-all;
}

.preview-result {
  background: #f0f9ff;
  border: 1px solid #b3d8ff;
  border-radius: 6px;
  padding: 16px;
  max-height: 300px;
  overflow-y: auto;
}

.email-preview {
  font-family: Arial, sans-serif;
}

.email-header {
  margin-bottom: 12px;
  padding-bottom: 8px;
  border-bottom: 1px solid #e4e7ed;
}

.email-body {
  line-height: 1.6;
}

.message-preview pre {
  margin: 0;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 13px;
  line-height: 1.5;
  color: #303133;
  white-space: pre-wrap;
  word-break: break-all;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}
</style>
