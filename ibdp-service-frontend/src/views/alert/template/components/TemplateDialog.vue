<template>
  <el-dialog
    v-model="dialogVisible"
    :title="isEdit ? '编辑模板' : '新增模板'"
    width="800px"
    :before-close="handleClose"
  >
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="100px"
      class="template-form"
    >
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="模板编码" prop="templateCode">
            <el-input
              v-model="formData.templateCode"
              placeholder="请输入模板编码"
              :disabled="isEdit"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="模板名称" prop="templateName">
            <el-input
              v-model="formData.templateName"
              placeholder="请输入模板名称"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="通道类型" prop="channelType">
            <el-select
              v-model="formData.channelType"
              placeholder="请选择通道类型"
              style="width: 100%"
              :disabled="isEdit"
            >
              <el-option label="企业微信" value="WECHAT" />
              <el-option label="钉钉" value="DINGTALK" />
              <el-option label="飞书" value="FEISHU" />
              <el-option label="邮件" value="EMAIL" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="状态" prop="enabled">
            <el-radio-group v-model="formData.enabled">
              <el-radio :label="1">启用</el-radio>
              <el-radio :label="0">禁用</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
      </el-row>

      <el-form-item label="描述">
        <el-input
          v-model="formData.description"
          type="textarea"
          :rows="2"
          placeholder="请输入模板描述"
        />
      </el-form-item>

      <el-form-item label="模板内容" prop="templateContent">
        <el-input
          v-model="formData.templateContent"
          type="textarea"
          :rows="8"
          placeholder="请输入模板内容，支持变量格式：${变量名}"
        />
        <div class="form-tip">
          <el-icon><InfoFilled /></el-icon>
          支持变量格式：${变量名}，例如：${jobDesc}、${alertTime}
        </div>
      </el-form-item>

      <el-form-item label="变量定义">
        <div class="variables-container">
          <div
            v-for="(variable, index) in variables"
            :key="index"
            class="variable-item"
          >
            <el-input
              v-model="variable.name"
              placeholder="变量名"
              style="width: 150px"
            />
            <el-input
              v-model="variable.description"
              placeholder="变量描述"
              style="width: 200px; margin-left: 10px"
            />
            <el-select
              v-model="variable.type"
              placeholder="类型"
              style="width: 100px; margin-left: 10px"
            >
              <el-option label="字符串" value="string" />
              <el-option label="数字" value="number" />
              <el-option label="日期" value="date" />
            </el-select>
            <el-button
              type="danger"
              size="small"
              @click="removeVariable(index)"
              style="margin-left: 10px"
              :icon="Delete"
            />
          </div>
          <el-button
            type="primary"
            size="small"
            @click="addVariable"
            :icon="Plus"
          >
            添加变量
          </el-button>
        </div>
      </el-form-item>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="handleValidate" :loading="validating">
          验证模板
        </el-button>
        <el-button type="success" @click="handleSubmit" :loading="submitting">
          {{ isEdit ? '更新' : '保存' }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, watch, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { InfoFilled, Delete, Plus } from '@element-plus/icons-vue'
import { alertTemplateApi } from '@/api/alert'

interface Props {
  visible: boolean
  templateData?: any
  isEdit: boolean
}

interface Emits {
  (e: 'update:visible', value: boolean): void
  (e: 'success'): void
}

const props = withDefaults(defineProps<Props>(), {
  visible: false,
  templateData: null,
  isEdit: false
})

const emit = defineEmits<Emits>()

// 对话框显示状态
const dialogVisible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
})

// 表单引用
const formRef = ref()

// 表单数据
const formData = reactive({
  templateCode: '',
  templateName: '',
  channelType: '',
  templateContent: '',
  description: '',
  enabled: 1,
  variables: ''
})

// 变量列表
const variables = ref([
  { name: '', description: '', type: 'string' }
])

// 表单验证规则
const formRules = {
  templateCode: [
    { required: true, message: '请输入模板编码', trigger: 'blur' },
    { pattern: /^[A-Z_][A-Z0-9_]*$/, message: '模板编码只能包含大写字母、数字和下划线，且以字母或下划线开头', trigger: 'blur' }
  ],
  templateName: [
    { required: true, message: '请输入模板名称', trigger: 'blur' }
  ],
  channelType: [
    { required: true, message: '请选择通道类型', trigger: 'change' }
  ],
  templateContent: [
    { required: true, message: '请输入模板内容', trigger: 'blur' }
  ]
}

// 加载状态
const submitting = ref(false)
const validating = ref(false)

// 重置表单
const resetForm = () => {
  Object.assign(formData, {
    templateCode: '',
    templateName: '',
    channelType: '',
    templateContent: '',
    description: '',
    enabled: 1,
    variables: ''
  })
  variables.value = [{ name: '', description: '', type: 'string' }]
  formRef.value?.clearValidate()
}

// 监听props变化，初始化表单数据
watch(
  () => props.templateData,
  (newData) => {
    if (newData) {
      Object.assign(formData, newData)
      // 解析变量定义
      if (newData.variables) {
        try {
          variables.value = JSON.parse(newData.variables)
        } catch (error) {
          variables.value = [{ name: '', description: '', type: 'string' }]
        }
      }
    } else {
      resetForm()
    }
  },
  { immediate: true }
)

// 添加变量
const addVariable = () => {
  variables.value.push({ name: '', description: '', type: 'string' })
}

// 删除变量
const removeVariable = (index: number) => {
  if (variables.value.length > 1) {
    variables.value.splice(index, 1)
  }
}

// 验证模板
const handleValidate = async () => {
  validating.value = true
  try {
    // 构建变量JSON
    const validVariables = variables.value.filter(v => v.name.trim())
    const variablesJson = JSON.stringify(validVariables)
    
    const response = await alertTemplateApi.validate(formData.templateContent, variablesJson)
    if (response.code === 200) {
      ElMessage.success('模板验证通过')
    } else {
      ElMessage.error(response.msg || '模板验证失败')
    }
  } catch (error) {
    ElMessage.error('模板验证失败')
  } finally {
    validating.value = false
  }
}

// 提交表单
const handleSubmit = async () => {
  try {
    await formRef.value.validate()
    
    submitting.value = true
    
    // 构建变量JSON
    const validVariables = variables.value.filter(v => v.name.trim())
    const submitData = {
      ...formData,
      variables: JSON.stringify(validVariables)
    }
    
    let response
    if (props.isEdit) {
      response = await alertTemplateApi.update(submitData)
    } else {
      response = await alertTemplateApi.save(submitData)
    }
    
    if (response.code === 200) {
      ElMessage.success(props.isEdit ? '更新成功' : '保存成功')
      emit('success')
      handleClose()
    } else {
      ElMessage.error(response.msg || '操作失败')
    }
  } catch (error) {
    console.error('表单验证失败', error)
  } finally {
    submitting.value = false
  }
}

// 关闭对话框
const handleClose = () => {
  emit('update:visible', false)
  setTimeout(() => {
    resetForm()
  }, 300)
}
</script>

<style scoped>
.template-form {
  padding: 0 20px;
}

.form-tip {
  display: flex;
  align-items: center;
  gap: 6px;
  margin-top: 8px;
  font-size: 12px;
  color: #909399;
}

.variables-container {
  width: 100%;
}

.variable-item {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}
</style>
