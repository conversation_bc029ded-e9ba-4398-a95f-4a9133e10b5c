<template>
  <div class="template-container">
    <!-- 搜索筛选 -->
    <div class="search-container">
      <el-form :model="searchForm" inline class="search-form">
        <el-form-item label="模板编码">
          <el-input
            v-model="searchForm.templateCode"
            placeholder="请输入模板编码"
            clearable
            style="width: 200px"
          />
        </el-form-item>
        <el-form-item label="模板名称">
          <el-input
            v-model="searchForm.templateName"
            placeholder="请输入模板名称"
            clearable
            style="width: 200px"
          />
        </el-form-item>
        <el-form-item label="通道类型">
          <el-select
            v-model="searchForm.channelType"
            placeholder="请选择通道类型"
            clearable
            style="width: 150px"
          >
            <el-option label="企业微信" value="WECHAT" />
            <el-option label="钉钉" value="DINGTALK" />
            <el-option label="飞书" value="FEISHU" />
            <el-option label="邮件" value="EMAIL" />
          </el-select>
        </el-form-item>
        <el-form-item label="状态">
          <el-select
            v-model="searchForm.enabled"
            placeholder="请选择状态"
            clearable
            style="width: 150px"
          >
            <el-option label="启用" :value="1" />
            <el-option label="禁用" :value="0" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">
            <el-icon><Search /></el-icon>
            搜索
          </el-button>
          <el-button @click="handleReset">
            <el-icon><Refresh /></el-icon>
            重置
          </el-button>
        </el-form-item>
        <el-form-item class="add-button-item">
          <el-button type="primary" @click="handleAdd">
            <el-icon><Plus /></el-icon>
            新增模板
          </el-button>
        </el-form-item>
      </el-form>
    </div>

    <!-- 数据表格 -->
    <div class="table-container">
      <el-table
        v-loading="loading"
        :data="tableData"
        @selection-change="handleSelectionChange"
        stripe
        style="width: 100%"
      >
        <el-table-column type="selection" width="55" />
        <el-table-column prop="templateCode" label="模板编码" width="200" />
        <el-table-column prop="templateName" label="模板名称" min-width="200">
          <template #default="{ row }">
            <div class="template-name">
              <span class="name">{{ row.templateName }}</span>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="channelType" label="通道类型" width="120">
          <template #default="{ row }">
            <el-tag :type="getChannelTypeTagType(row.channelType)">
              {{ getChannelTypeName(row.channelType) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="enabled" label="状态" width="100">
          <template #default="{ row }">
            <el-tag :type="row.enabled === 1 ? 'success' : 'danger'">
              {{ row.enabled === 1 ? '启用' : '禁用' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="description" label="描述" min-width="200" show-overflow-tooltip />
        <el-table-column prop="createTime" label="创建时间" width="180">
          <template #default="{ row }">
            {{ formatDateTime(row.createTime) }}
          </template>
        </el-table-column>
        <el-table-column prop="updateTime" label="更新时间" width="180">
          <template #default="{ row }">
            {{ formatDateTime(row.updateTime) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="280" fixed="right">
          <template #default="{ row }">
            <div class="action-buttons">
              <el-button
                link
                type="primary"
                @click="handlePreview(row)"
                title="预览模板"
              >
                <el-icon><View /></el-icon>
                预览
              </el-button>
              <el-button
                link
                type="primary"
                @click="handleEdit(row)"
                title="编辑模板"
              >
                <el-icon><Edit /></el-icon>
                编辑
              </el-button>
              <el-button
                link
                type="primary"
                @click="handleStatusChange(row)"
                :title="row.enabled === 1 ? '禁用模板' : '启用模板'"
              >
                <el-icon><Switch /></el-icon>
                {{ row.enabled === 1 ? '禁用' : '启用' }}
              </el-button>
              <el-button
                link
                type="danger"
                @click="handleDelete(row)"
                title="删除模板"
              >
                <el-icon><Delete /></el-icon>
                删除
              </el-button>
            </div>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="pagination.current"
          v-model:page-size="pagination.size"
          :total="pagination.total"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>

    <!-- 新增/编辑对话框 -->
    <TemplateDialog
      v-model:visible="dialogVisible"
      :template-data="currentTemplate"
      :is-edit="isEdit"
      @success="handleDialogSuccess"
    />

    <!-- 预览对话框 -->
    <TemplatePreviewDialog
      v-model:visible="previewVisible"
      :template-data="previewTemplate"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Search,
  Refresh,
  Plus,
  Delete,
  Edit,
  View,
  Switch
} from '@element-plus/icons-vue'
import TemplateDialog from './components/TemplateDialog.vue'
import TemplatePreviewDialog from './components/TemplatePreviewDialog.vue'
import { getTemplateList, deleteTemplate, updateTemplateStatus, alertTemplateApi } from '@/api/alert'

// 搜索表单
const searchForm = reactive({
  templateCode: '',
  templateName: '',
  channelType: '',
  enabled: undefined as number | undefined
})

// 表格数据
const loading = ref(false)
const tableData = ref([])
const selectedRows = ref([])

// 分页
const pagination = reactive({
  current: 1,
  size: 10,
  total: 0
})

// 对话框
const dialogVisible = ref(false)
const previewVisible = ref(false)
const currentTemplate = ref(null)
const previewTemplate = ref(null)
const isEdit = ref(false)

// 获取通道类型名称
const getChannelTypeName = (type: string) => {
  const typeMap = {
    WECHAT: '企业微信',
    DINGTALK: '钉钉',
    FEISHU: '飞书',
    EMAIL: '邮件'
  }
  return typeMap[type] || type
}

// 获取通道类型标签类型
const getChannelTypeTagType = (type: string) => {
  const typeMap = {
    WECHAT: 'success',
    DINGTALK: 'primary',
    FEISHU: 'warning',
    EMAIL: 'info'
  }
  return typeMap[type] || 'info'
}

// 格式化日期时间
const formatDateTime = (dateTime: string) => {
  if (!dateTime) return ''
  const date = new Date(dateTime)
  return date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  })
}

// 加载数据
const loadData = async () => {
  loading.value = true
  try {
    const params = {
      start: (pagination.current - 1) * pagination.size,
      length: pagination.size,
      ...searchForm
    }
    const response = await getTemplateList(params)
    if (response && response.code === 200) {
      tableData.value = response.content?.data || []
      pagination.total = response.content?.recordsTotal || 0
    } else {
      tableData.value = []
      pagination.total = 0
      if (response?.msg) {
        ElMessage.error(response.msg)
      }
    }
  } catch (error) {
    console.error('加载数据失败:', error)
    ElMessage.error('加载数据失败')
    tableData.value = []
    pagination.total = 0
  } finally {
    loading.value = false
  }
}

// 搜索
const handleSearch = () => {
  pagination.current = 1
  loadData()
}

// 重置
const handleReset = () => {
  Object.assign(searchForm, {
    templateCode: '',
    templateName: '',
    channelType: '',
    enabled: undefined
  })
  handleSearch()
}

// 刷新
const handleRefresh = () => {
  loadData()
}

// 新增
const handleAdd = () => {
  currentTemplate.value = null
  isEdit.value = false
  dialogVisible.value = true
}

// 编辑
const handleEdit = (row: any) => {
  currentTemplate.value = { ...row }
  isEdit.value = true
  dialogVisible.value = true
}

// 预览
const handlePreview = (row: any) => {
  previewTemplate.value = row
  previewVisible.value = true
}

// 删除
const handleDelete = async (row: any) => {
  try {
    await ElMessageBox.confirm('确定要删除这个模板吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    
    const response = await deleteTemplate(row.id)
    if (response.code === 200) {
      ElMessage.success('删除成功')
      loadData()
    } else {
      ElMessage.error(response.msg || '删除失败')
    }
  } catch (error) {
    // 用户取消删除
  }
}

// 批量删除
const handleBatchDelete = async () => {
  if (selectedRows.value.length === 0) {
    ElMessage.warning('请选择要删除的模板')
    return
  }
  
  try {
    await ElMessageBox.confirm(`确定要删除选中的 ${selectedRows.value.length} 个模板吗？`, '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    
    const ids = selectedRows.value.map((row: any) => row.id)
    const response = await alertTemplateApi.batchDelete(ids)
    if (response.code === 200) {
      ElMessage.success('批量删除成功')
      loadData()
    } else {
      ElMessage.error(response.msg || '批量删除失败')
    }
  } catch (error) {
    // 用户取消删除
  }
}

// 状态切换
const handleStatusChange = async (row: any) => {
  try {
    const newStatus = row.enabled === 1 ? 0 : 1
    const action = newStatus === 1 ? '启用' : '禁用'

    await ElMessageBox.confirm(`确定要${action}这个模板吗？`, '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })

    const response = await updateTemplateStatus(row.id, newStatus)
    if (response.code === 200) {
      row.enabled = newStatus
      ElMessage.success(`${action}成功`)
    } else {
      ElMessage.error(response.msg || `${action}失败`)
    }
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('操作失败')
    }
  }
}

// 选择变化
const handleSelectionChange = (selection: any[]) => {
  selectedRows.value = selection
}

// 分页变化
const handleSizeChange = (size: number) => {
  pagination.size = size
  pagination.current = 1
  loadData()
}

const handleCurrentChange = (current: number) => {
  pagination.current = current
  loadData()
}

// 对话框成功回调
const handleDialogSuccess = () => {
  loadData()
}

// 初始化
onMounted(() => {
  loadData()
})
</script>

<style scoped>
.template-container {
  padding: 20px;
  background: transparent;
}



.search-container {
  margin-bottom: 20px;
  padding: 20px;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.search-form {
  display: flex;
  flex-wrap: wrap;
  align-items: flex-end;
  gap: 16px;
}

.search-form :deep(.el-form-item) {
  margin-bottom: 0;
}

.add-button-item {
  margin-left: auto;
  margin-bottom: 0;
}

.table-container {
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.template-name {
  display: flex;
  align-items: center;
}

.template-name .name {
  font-weight: 500;
  color: #303133;
}

.action-buttons {
  display: flex;
  flex-wrap: nowrap;
  white-space: nowrap;
}

.action-buttons .el-button {
  font-size: 12px;
  min-width: auto;
}

.pagination-container {
  display: flex;
  justify-content: center;
  padding: 20px;
  background: transparent;
}

/* 表格样式优化 */
:deep(.el-table) {
  border: none;
}

:deep(.el-table th) {
  background: #fafafa;
  border-bottom: 1px solid #e4e7ed;
  font-weight: 600;
  color: #606266;
}

:deep(.el-table td) {
  border-bottom: 1px solid #f0f0f0;
}

:deep(.el-table tr:hover > td) {
  background-color: #f5f7fa;
}

:deep(.el-table__body tr.current-row > td) {
  background-color: #ecf5ff;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .template-container {
    padding: 10px;
  }

  .search-container {
    padding: 15px;
  }

  .search-form {
    flex-direction: column;
    align-items: stretch;
    gap: 12px;
  }

  .add-button-item {
    margin-left: 0;
    order: -1;
  }

  .action-buttons {
    flex-direction: column;
    gap: 4px;
  }
}
</style>
