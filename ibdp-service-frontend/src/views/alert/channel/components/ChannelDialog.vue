<template>
  <el-dialog
    v-model="dialogVisible"
    :title="isEdit ? '编辑通道配置' : '新增通道配置'"
    width="1000px"
    :before-close="handleClose"
    class="channel-dialog"
  >
    <el-form
      ref="formRef"
      :model="{ ...formData, ...authConfigData }"
      :rules="formRules"
      label-width="120px"
      class="channel-form"
    >
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="配置名称" prop="configName">
            <el-input
              v-model="formData.configName"
              placeholder="请输入配置名称"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="通道类型" prop="channelType">
            <el-select
              v-model="formData.channelType"
              placeholder="请选择通道类型"
              style="width: 100%"
              :disabled="isEdit"
              @change="handleChannelTypeChange"
            >
              <el-option label="企业微信" value="WECHAT" />
              <el-option label="钉钉" value="DINGTALK" />
              <el-option label="飞书" value="FEISHU" />
              <el-option label="邮件" value="EMAIL" />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="认证类型" prop="authType">
            <el-select
              v-model="formData.authType"
              placeholder="请选择认证类型"
              style="width: 100%"
              :disabled="isEdit"
            >
              <el-option
                v-for="authType in authTypes"
                :key="authType.value"
                :label="authType.label"
                :value="authType.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="状态" prop="enabled">
            <el-radio-group v-model="formData.enabled">
              <el-radio :value="1">启用</el-radio>
              <el-radio :value="0">禁用</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
      </el-row>

      <el-form-item label="描述">
        <el-input
          v-model="formData.description"
          type="textarea"
          :rows="2"
          placeholder="请输入配置描述"
        />
      </el-form-item>

      <!-- 认证配置 -->
      <el-form-item label="认证配置" prop="authConfig">
        <div class="auth-config-container">
          <!-- 企业微信配置 -->
          <div v-if="formData.channelType === 'WECHAT'" class="config-section">
            <el-form-item
              label="Webhook URL"
              required
              :rules="[
                { required: true, message: 'Webhook URL不能为空', trigger: 'blur' },
                { validator: validateWebhookUrl, trigger: 'blur' }
              ]"
              prop="webhookUrl"
            >
              <el-input
                v-model="authConfigData.webhookUrl"
                placeholder="https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=xxx"
              />
            </el-form-item>
          </div>

          <!-- 钉钉配置 -->
          <div v-if="formData.channelType === 'DINGTALK'" class="config-section">
            <el-form-item
              label="Access Token"
              required
              :rules="[
                { required: true, message: 'Access Token不能为空', trigger: 'blur' }
              ]"
              prop="accessToken"
            >
              <el-input
                v-model="authConfigData.accessToken"
                placeholder="请输入钉钉机器人的Access Token"
              />
            </el-form-item>
            <el-form-item label="Secret">
              <el-input
                v-model="authConfigData.secret"
                placeholder="请输入钉钉机器人的Secret（可选）"
              />
            </el-form-item>
          </div>

          <!-- 飞书配置 -->
          <div v-if="formData.channelType === 'FEISHU'" class="config-section">
            <el-form-item
              label="Webhook URL"
              required
              :rules="[
                { required: true, message: 'Webhook URL不能为空', trigger: 'blur' },
                { validator: validateWebhookUrl, trigger: 'blur' }
              ]"
              prop="webhookUrl"
            >
              <el-input
                v-model="authConfigData.webhookUrl"
                placeholder="https://open.feishu.cn/open-apis/bot/v2/hook/xxx"
              />
            </el-form-item>
          </div>

          <!-- 邮件配置 -->
          <div v-if="formData.channelType === 'EMAIL'" class="config-section email-config">
            <!-- 服务器配置 -->
            <div class="config-group">
              <h4 class="group-title">服务器配置</h4>
              <el-row :gutter="24">
                <el-col :span="16">
                  <el-form-item
                    label="SMTP主机"
                    required
                    :rules="[{ required: true, message: 'SMTP主机不能为空', trigger: 'blur' }]"
                    prop="smtpHost"
                    label-width="100px"
                  >
                    <el-input
                      v-model="authConfigData.smtpHost"
                      placeholder="例如：smtp.qq.com, smtp.163.com"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="端口" required label-width="50px">
                    <el-input-number
                      v-model="authConfigData.smtpPort"
                      :min="1"
                      :max="65535"
                      placeholder="587"
                      style="width: 100%"
                    />
                  </el-form-item>
                </el-col>
              </el-row>
            </div>

            <!-- 认证信息 -->
            <div class="config-group">
              <h4 class="group-title">认证信息</h4>
              <el-row :gutter="24">
                <el-col :span="12">
                  <el-form-item
                    label="用户名"
                    required
                    :rules="[{ required: true, message: '用户名不能为空', trigger: 'blur' }]"
                    prop="username"
                    label-width="100px"
                  >
                    <el-input
                      v-model="authConfigData.username"
                      placeholder="<EMAIL>"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item
                    label="密码/授权码"
                    required
                    prop="password"
                    label-width="100px"
                  >
                    <el-input
                      v-model="authConfigData.password"
                      type="password"
                      placeholder="请输入邮箱密码或授权码"
                      show-password
                    />
                  </el-form-item>
                </el-col>
              </el-row>
            </div>

            <!-- 可选配置 -->
            <div class="config-group">
              <h4 class="group-title">加密配置</h4>
              <el-row :gutter="24">
                <el-col :span="24">
                  <el-form-item label="加密方式" label-width="100px">
                    <div class="encryption-options">
                      <el-checkbox v-model="authConfigData.enableTls">启用TLS</el-checkbox>
                      <el-checkbox v-model="authConfigData.enableSsl">启用SSL</el-checkbox>
                    </div>
                    <div class="form-tip">
                      <el-text type="info" size="small">
                        TLS通常用于587端口，SSL通常用于465端口
                      </el-text>
                    </div>
                  </el-form-item>
                </el-col>
              </el-row>
            </div>
          </div>
        </div>
      </el-form-item>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="success" @click="handleSubmit" :loading="submitting">
          {{ isEdit ? '更新' : '保存' }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, watch, computed } from 'vue'
import { ElMessage } from 'element-plus'
import { alertChannelApi } from '@/api/alert'

interface Props {
  visible: boolean
  channelData?: any
  isEdit: boolean
}

interface Emits {
  (e: 'update:visible', value: boolean): void
  (e: 'success'): void
}

const props = withDefaults(defineProps<Props>(), {
  visible: false,
  channelData: null,
  isEdit: false
})

const emit = defineEmits<Emits>()

// 对话框显示状态
const dialogVisible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
})

// 表单引用
const formRef = ref()

// 表单数据
const formData = reactive({
  configName: '',
  channelType: '',
  authType: '',
  description: '',
  enabled: 1,
  authConfig: ''
})

// 认证配置数据
const authConfigData = reactive({
  // 通用
  webhookUrl: '',
  // 钉钉
  accessToken: '',
  secret: '',
  // 邮件
  smtpHost: '',
  smtpPort: 587,
  username: '',
  password: '',
  enableTls: true,
  enableSsl: false
})

// 认证类型选项
const authTypes = ref([])

// 表单验证规则
const formRules = reactive({
  configName: [
    { required: true, message: '请输入配置名称', trigger: 'blur' }
  ],
  channelType: [
    { required: true, message: '请选择通道类型', trigger: 'change' }
  ],
  authType: [
    { required: true, message: '请选择认证类型', trigger: 'change' }
  ]
})

// 加载状态
const submitting = ref(false)

// 重置表单
const resetForm = () => {
  Object.assign(formData, {
    configName: '',
    channelType: '',
    authType: '',
    description: '',
    enabled: 1,
    authConfig: ''
  })
  Object.assign(authConfigData, {
    webhookUrl: '',
    accessToken: '',
    secret: '',
    smtpHost: '',
    smtpPort: 587,
    username: '',
    password: '',
    enableTls: true,
    enableSsl: false
  })
  formRef.value?.clearValidate()
}

// 监听props变化，初始化表单数据
watch(
  () => props.channelData,
  (newData) => {
    if (newData) {
      Object.assign(formData, newData)
      // 先重置认证配置数据到默认值
      const defaultAuthConfig = {
        webhookUrl: '',
        accessToken: '',
        secret: '',
        smtpHost: '',
        smtpPort: 587,
        username: '',
        password: '',
        enableTls: true,
        enableSsl: false
      }

      // 重置数据
      Object.keys(defaultAuthConfig).forEach(key => {
        authConfigData[key] = defaultAuthConfig[key]
      })

      // 解析认证配置
      if (newData.authConfig) {
        try {
          let authConfig
          if (typeof newData.authConfig === 'string') {
            // 解析JSON字符串
            authConfig = JSON.parse(newData.authConfig)
          } else if (typeof newData.authConfig === 'object') {
            authConfig = newData.authConfig
          }

          if (authConfig && typeof authConfig === 'object') {
            Object.keys(authConfig).forEach(key => {
              if (key in authConfigData) {
                authConfigData[key] = authConfig[key]
              }
            })
            console.log('认证配置应用成功:', authConfig)
          }
        } catch (error) {
          console.error('解析认证配置失败', error, newData.authConfig)
        }
      }
      updateAuthTypes()
    } else {
      resetForm()
    }
  },
  { immediate: true, deep: false }
)

// 通道类型变化
const handleChannelTypeChange = () => {
  updateAuthTypes()
  // 只有在新增模式下才重置认证配置
  if (!props.isEdit) {
    Object.assign(authConfigData, {
      webhookUrl: '',
      accessToken: '',
      secret: '',
      smtpHost: '',
      smtpPort: 587,
      username: '',
      password: '',
      enableTls: true,
      enableSsl: false
    })
  }
}

// 更新认证类型选项
const updateAuthTypes = () => {
  const typeMap = {
    WECHAT: [{ label: 'Webhook', value: 'WEBHOOK' }],
    DINGTALK: [{ label: 'Token', value: 'TOKEN' }],
    FEISHU: [{ label: 'Webhook', value: 'WEBHOOK' }],
    EMAIL: [{ label: 'SMTP', value: 'SMTP' }]
  }
  authTypes.value = typeMap[formData.channelType] || []
  // 只有在新增模式下或者认证类型为空时才设置默认值
  if (authTypes.value.length > 0 && (!props.isEdit || !formData.authType)) {
    formData.authType = authTypes.value[0].value
  }
}



// 构建认证配置
const buildAuthConfig = () => {
  const config = {}
  
  switch (formData.channelType) {
    case 'WECHAT':
    case 'FEISHU':
      config.webhookUrl = authConfigData.webhookUrl
      break
    case 'DINGTALK':
      config.accessToken = authConfigData.accessToken
      if (authConfigData.secret) {
        config.secret = authConfigData.secret
      }
      break
    case 'EMAIL':
      Object.assign(config, {
        smtpHost: authConfigData.smtpHost,
        smtpPort: authConfigData.smtpPort,
        username: authConfigData.username,
        password: authConfigData.password,
        enableTls: authConfigData.enableTls,
        enableSsl: authConfigData.enableSsl
      })
      break
  }
  
  return config
}



// URL格式验证
const isValidUrl = (url) => {
  try {
    new URL(url)
    return true
  } catch {
    return false
  }
}

// Webhook URL验证器
const validateWebhookUrl = (rule, value, callback) => {
  if (!value || value.trim() === '') {
    callback(new Error('Webhook URL不能为空'))
  } else if (!isValidUrl(value)) {
    callback(new Error('请输入有效的URL格式'))
  } else {
    callback()
  }
}

// 提交表单
const handleSubmit = async () => {
  try {
    await formRef.value.validate()

    submitting.value = true

    // 构建认证配置
    const authConfig = buildAuthConfig()
    const submitData = {
      ...formData,
      authConfig: JSON.stringify(authConfig)
    }
    
    let response
    if (props.isEdit) {
      response = await alertChannelApi.update(submitData)
    } else {
      response = await alertChannelApi.save(submitData)
    }
    
    if (response.code === 200) {
      ElMessage.success(props.isEdit ? '更新成功' : '保存成功')
      emit('success')
      handleClose()
    } else {
      ElMessage.error(response.msg || '操作失败')
    }
  } catch (error) {
    console.error('表单验证失败', error)
  } finally {
    submitting.value = false
  }
}

// 关闭对话框
const handleClose = () => {
  emit('update:visible', false)
  setTimeout(() => {
    resetForm()
  }, 300)
}
</script>

<style scoped>
.channel-form {
  padding: 0 20px;
}

.auth-config-container {
  width: 100%;
  border: 1px solid #dcdfe6;
  border-radius: 6px;
  padding: 20px;
  background: #fafafa;
}

.config-section {
  width: 100%;
}

/* 邮件配置特殊样式 */
.email-config {
  padding: 0;
  background: transparent;
  border: none;
}

.config-group {
  margin-bottom: 20px;
  padding: 18px;
  background: #ffffff;
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.config-group:last-child {
  margin-bottom: 8px;
}

.group-title {
  margin: 0 0 16px 0;
  padding: 0 0 8px 0;
  font-size: 14px;
  font-weight: 600;
  color: #303133;
  border-bottom: 1px solid #ebeef5;
}

.encryption-options {
  display: flex;
  gap: 16px;
  align-items: center;
}

.encryption-options .el-checkbox {
  margin-right: 0;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

/* 表单项间距优化 */
.email-config :deep(.el-form-item) {
  margin-bottom: 16px;
}

.config-group :deep(.el-form-item:last-child) {
  margin-bottom: 0;
}

/* 全局表单项间距 */
.channel-form :deep(.el-form-item) {
  margin-bottom: 20px;
}

/* 输入框样式优化 */
.email-config :deep(.el-input__wrapper) {
  box-shadow: 0 0 0 1px #dcdfe6 inset;
  transition: box-shadow 0.2s ease;
}

.email-config :deep(.el-input__wrapper:hover) {
  box-shadow: 0 0 0 1px #c0c4cc inset;
}

.email-config :deep(.el-input__wrapper.is-focus) {
  box-shadow: 0 0 0 1px #409eff inset;
}

/* 表单提示样式 */
.form-tip {
  margin-top: 4px;
  line-height: 1.4;
}

.form-tip .el-text {
  font-size: 12px;
}
</style>
