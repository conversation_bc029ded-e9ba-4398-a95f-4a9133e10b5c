<template>
  <div class="channel-container">
    <!-- 搜索筛选 -->
    <div class="search-container">
      <el-form :model="searchForm" inline class="search-form">
        <el-form-item label="配置名称">
          <el-input
            v-model="searchForm.configName"
            placeholder="请输入配置名称"
            clearable
            style="width: 200px"
          />
        </el-form-item>
        <el-form-item label="通道类型">
          <el-select
            v-model="searchForm.channelType"
            placeholder="请选择通道类型"
            clearable
            style="width: 150px"
          >
            <el-option label="企业微信" value="WECHAT" />
            <el-option label="钉钉" value="DINGTALK" />
            <el-option label="飞书" value="FEISHU" />
            <el-option label="邮件" value="EMAIL" />
          </el-select>
        </el-form-item>
        <el-form-item label="状态">
          <el-select
            v-model="searchForm.enabled"
            placeholder="请选择状态"
            clearable
            style="width: 150px"
          >
            <el-option label="启用" :value="1" />
            <el-option label="禁用" :value="0" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">
            <el-icon><Search /></el-icon>
            搜索
          </el-button>
          <el-button @click="handleReset">
            <el-icon><Refresh /></el-icon>
            重置
          </el-button>
        </el-form-item>
        <el-form-item class="add-button-item">
          <el-button type="primary" @click="handleAdd">
            <el-icon><Plus /></el-icon>
            新增配置
          </el-button>
        </el-form-item>
      </el-form>
    </div>

    <!-- 数据表格 -->
    <div class="table-container">
      <el-table
        v-loading="loading"
        :data="tableData"
        @selection-change="handleSelectionChange"
        stripe
        style="width: 100%"
      >
        <el-table-column type="selection" width="55" />
        <el-table-column prop="configName" label="配置名称" min-width="180">
          <template #default="{ row }">
            <div class="config-name">
              <span class="name">{{ row.configName }}</span>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="channelType" label="通道类型" width="120">
          <template #default="{ row }">
            <el-tag :type="getChannelTypeTagType(row.channelType)">
              {{ getChannelTypeName(row.channelType) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="authType" label="认证类型" width="120">
          <template #default="{ row }">
            <el-tag type="info">{{ row.authType }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="enabled" label="状态" width="100">
          <template #default="{ row }">
            <el-tag :type="row.enabled === 1 ? 'success' : 'danger'">
              {{ row.enabled === 1 ? '启用' : '禁用' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="description" label="描述" min-width="200" show-overflow-tooltip />
        <el-table-column prop="createTime" label="创建时间" width="180">
          <template #default="{ row }">
            {{ formatDateTime(row.createTime) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="300" fixed="right">
          <template #default="{ row }">
            <div class="action-buttons">
              <el-button
                link
                type="primary"
                @click="handleEdit(row)"
                title="编辑配置"
              >
                <el-icon><Edit /></el-icon>
                编辑
              </el-button>
              <el-button
                link
                type="success"
                @click="handleTest(row)"
                title="测试发送"
              >
                <el-icon><Connection /></el-icon>
                测试
              </el-button>

              <el-button
                link
                type="primary"
                @click="handleStatusChange(row)"
                :title="row.enabled === 1 ? '禁用配置' : '启用配置'"
              >
                <el-icon><Switch /></el-icon>
                {{ row.enabled === 1 ? '禁用' : '启用' }}
              </el-button>
              <el-button
                link
                type="danger"
                @click="handleDelete(row)"
                title="删除配置"
              >
                <el-icon><Delete /></el-icon>
                删除
              </el-button>
            </div>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="pagination.current"
          v-model:page-size="pagination.size"
          :total="pagination.total"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>

    <!-- 新增/编辑对话框 -->
    <ChannelDialog
      v-model:visible="dialogVisible"
      :channel-data="currentChannel"
      :is-edit="isEdit"
      @success="handleDialogSuccess"
    />

    <!-- 测试发送对话框 -->
    <el-dialog
      v-model="testDialogVisible"
      title="测试发送"
      width="500px"
      :before-close="handleTestDialogClose"
    >
      <el-form
        ref="testFormRef"
        :model="testForm"
        :rules="testFormRules"
        label-width="100px"
      >
        <el-form-item label="通道名称">
          <el-input v-model="testForm.configName" disabled />
        </el-form-item>
        <el-form-item label="通道类型">
          <el-tag :type="getChannelTypeTagType(testForm.channelType)">
            {{ getChannelTypeName(testForm.channelType) }}
          </el-tag>
        </el-form-item>
        <el-form-item
          v-if="needsRecipients(testForm.channelType)"
          :label="getRecipientLabel(testForm.channelType)"
          prop="recipients"
          required
        >
          <el-input
            v-model="testForm.recipients"
            type="textarea"
            :rows="3"
            :placeholder="getRecipientPlaceholder(testForm.channelType)"
          />
          <div class="form-tip">
            <el-text type="info" size="small">
              {{ getRecipientTip(testForm.channelType) }}
            </el-text>
          </div>
        </el-form-item>
        <el-form-item v-else>
          <div class="webhook-tip">
            <el-text type="success" size="small">
              <el-icon><InfoFilled /></el-icon>
              {{ getWebhookTip(testForm.channelType) }}
            </el-text>
          </div>
        </el-form-item>
      </el-form>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="handleTestDialogClose">取消</el-button>
          <el-button type="primary" @click="handleTestSend" :loading="testSending">
            发送测试
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Search,
  Refresh,
  Plus,
  Delete,
  Edit,
  Connection,
  Switch,
  InfoFilled
} from '@element-plus/icons-vue'
import ChannelDialog from './components/ChannelDialog.vue'
import { getChannelList, deleteChannel, updateChannelStatus, alertChannelApi } from '@/api/alert'

// 搜索表单
const searchForm = reactive({
  configName: '',
  channelType: '',
  enabled: undefined as number | undefined
})

// 表格数据
const loading = ref(false)
const tableData = ref([])
const selectedRows = ref([])

// 分页
const pagination = reactive({
  current: 1,
  size: 10,
  total: 0
})

// 对话框
const dialogVisible = ref(false)
const currentChannel = ref(null)
const isEdit = ref(false)

// 测试发送对话框相关
const testDialogVisible = ref(false)
const testFormRef = ref()
const testSending = ref(false)
const testForm = reactive({
  id: null,
  configName: '',
  channelType: '',
  recipients: ''
})

// 测试表单验证规则
const testFormRules = computed(() => {
  if (needsRecipients(testForm.channelType)) {
    return {
      recipients: [
        { required: true, message: '请输入收件人邮箱', trigger: 'blur' }
      ]
    }
  }
  return {}
})

// 获取通道类型名称
const getChannelTypeName = (type: string) => {
  const typeMap = {
    WECHAT: '企业微信',
    DINGTALK: '钉钉',
    FEISHU: '飞书',
    EMAIL: '邮件'
  }
  return typeMap[type] || type
}

// 获取通道类型标签类型
const getChannelTypeTagType = (type: string) => {
  const typeMap = {
    WECHAT: 'success',
    DINGTALK: 'primary',
    FEISHU: 'warning',
    EMAIL: 'info'
  }
  return typeMap[type] || 'info'
}

// 格式化日期时间
const formatDateTime = (dateTime: string) => {
  if (!dateTime) return ''
  const date = new Date(dateTime)
  return date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  })
}

// 加载数据
const loadData = async () => {
  loading.value = true
  try {
    const params = {
      start: (pagination.current - 1) * pagination.size,
      length: pagination.size,
      ...searchForm
    }
    const response = await getChannelList(params)
    if (response && response.code === 200) {
      tableData.value = response.content?.data || []
      pagination.total = response.content?.recordsTotal || 0
    } else {
      tableData.value = []
      pagination.total = 0
      if (response?.msg) {
        ElMessage.error(response.msg)
      }
    }
  } catch (error) {
    console.error('加载数据失败:', error)
    ElMessage.error('加载数据失败')
    tableData.value = []
    pagination.total = 0
  } finally {
    loading.value = false
  }
}

// 搜索
const handleSearch = () => {
  pagination.current = 1
  loadData()
}

// 重置
const handleReset = () => {
  Object.assign(searchForm, {
    configName: '',
    channelType: '',
    enabled: undefined
  })
  handleSearch()
}

// 刷新
const handleRefresh = () => {
  loadData()
}

// 新增
const handleAdd = () => {
  currentChannel.value = null
  isEdit.value = false
  dialogVisible.value = true
}

// 编辑
const handleEdit = async (row: any) => {
  try {
    // 调用详情接口获取解密后的认证配置
    const response = await alertChannelApi.getDetail(row.id)
    if (response.code === 200) {
      currentChannel.value = response.content || response.data
      isEdit.value = true
      dialogVisible.value = true
    } else {
      ElMessage.error(response.msg || '获取配置详情失败')
    }
  } catch (error) {
    console.error('获取配置详情失败:', error)
    ElMessage.error('获取配置详情失败')
  }
}

// 测试发送
const handleTest = (row: any) => {
  // 填充测试表单数据
  testForm.id = row.id
  testForm.configName = row.configName
  testForm.channelType = row.channelType
  testForm.recipients = getDefaultRecipients(row.channelType)

  // 显示测试对话框
  testDialogVisible.value = true
}

// 删除
const handleDelete = async (row: any) => {
  try {
    await ElMessageBox.confirm('确定要删除这个通道配置吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    
    const response = await deleteChannel(row.id)
    if (response.code === 200) {
      ElMessage.success('删除成功')
      loadData()
    } else {
      ElMessage.error(response.msg || '删除失败')
    }
  } catch (error) {
    // 用户取消删除
  }
}

// 批量删除
const handleBatchDelete = async () => {
  if (selectedRows.value.length === 0) {
    ElMessage.warning('请选择要删除的配置')
    return
  }

  try {
    await ElMessageBox.confirm(`确定要删除选中的 ${selectedRows.value.length} 个配置吗？`, '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })

    const ids = selectedRows.value.map((row: any) => row.id)
    const response = await alertChannelApi.batchDelete(ids)
    if (response.code === 200) {
      ElMessage.success('批量删除成功')
      loadData()
    } else {
      ElMessage.error(response.msg || '批量删除失败')
    }
  } catch (error) {
    // 用户取消删除
  }
}

// 判断是否需要收件人
const needsRecipients = (channelType: string) => {
  // 只有邮件类型需要指定收件人，其他都是Webhook类型
  return channelType === 'EMAIL'
}

// 获取Webhook提示信息
const getWebhookTip = (channelType: string) => {
  switch (channelType) {
    case 'WECHAT':
      return '企业微信通过Webhook发送到指定群聊，无需指定收件人'
    case 'DINGTALK':
      return '钉钉通过Webhook发送到指定群聊，无需指定收件人'
    case 'FEISHU':
      return '飞书通过Webhook发送到指定群聊，无需指定收件人'
    default:
      return '通过Webhook发送，无需指定收件人'
  }
}

// 获取默认收件人
const getDefaultRecipients = (channelType: string) => {
  switch (channelType) {
    case 'EMAIL':
      return '<EMAIL>'
    default:
      return '' // Webhook类型不需要收件人
  }
}

// 获取收件人标签
const getRecipientLabel = (channelType: string) => {
  return '收件人邮箱'
}

// 获取收件人占位符
const getRecipientPlaceholder = (channelType: string) => {
  return '请输入邮箱地址，多个邮箱用换行分隔\n例如：\<EMAIL>\<EMAIL>'
}

// 获取收件人提示
const getRecipientTip = (channelType: string) => {
  return '支持多个邮箱地址，每行一个'
}

// 处理测试发送
const handleTestSend = async () => {
  try {
    // 只对需要收件人的通道进行表单验证
    if (needsRecipients(testForm.channelType)) {
      await testFormRef.value.validate()
    }

    testSending.value = true

    let recipients = null
    if (needsRecipients(testForm.channelType)) {
      // 处理收件人列表
      recipients = testForm.recipients.split('\n')
        .map(r => r.trim())
        .filter(r => r.length > 0)
    }

    const response = await alertChannelApi.testSendWithRecipients(testForm.id, recipients)
    if (response.code === 200) {
      ElMessage.success('测试发送成功')
      handleTestDialogClose()
    } else {
      ElMessage.error(response.msg || '测试发送失败')
    }
  } catch (error) {
    console.error('测试发送失败:', error)
    ElMessage.error('测试发送失败')
  } finally {
    testSending.value = false
  }
}

// 关闭测试对话框
const handleTestDialogClose = () => {
  testDialogVisible.value = false
  testForm.id = null
  testForm.configName = ''
  testForm.channelType = ''
  testForm.recipients = ''
  testFormRef.value?.clearValidate()
}

// 状态切换
const handleStatusChange = async (row: any) => {
  try {
    const newStatus = row.enabled === 1 ? 0 : 1
    const action = newStatus === 1 ? '启用' : '禁用'

    await ElMessageBox.confirm(`确定要${action}这个通道配置吗？`, '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })

    const response = await updateChannelStatus(row.id, newStatus)
    if (response.code === 200) {
      row.enabled = newStatus
      ElMessage.success(`${action}成功`)
    } else {
      ElMessage.error(response.msg || `${action}失败`)
    }
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('操作失败')
    }
  }
}

// 选择变化
const handleSelectionChange = (selection: any[]) => {
  selectedRows.value = selection
}

// 分页变化
const handleSizeChange = (size: number) => {
  pagination.size = size
  pagination.current = 1
  loadData()
}

const handleCurrentChange = (current: number) => {
  pagination.current = current
  loadData()
}

// 对话框成功回调
const handleDialogSuccess = () => {
  loadData()
}

// 初始化
onMounted(() => {
  loadData()
})
</script>

<style scoped>
.channel-container {
  padding: 20px;
  background: transparent;
}



.search-container {
  margin-bottom: 20px;
  padding: 20px;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.search-form {
  display: flex;
  flex-wrap: wrap;
  align-items: flex-end;
  gap: 16px;
}

.search-form :deep(.el-form-item) {
  margin-bottom: 0;
}

.add-button-item {
  margin-left: auto;
  margin-bottom: 0;
}

.table-container {
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.config-name {
  display: flex;
  align-items: center;
}

.config-name .name {
  font-weight: 500;
  color: #303133;
}

.action-buttons {
  display: flex;
  flex-wrap: nowrap;
  white-space: nowrap;
}

.action-buttons .el-button {
  padding: 4px 6px;
  font-size: 12px;
  min-width: auto;
}

.pagination-container {
  display: flex;
  justify-content: center;
  padding: 20px;
  background: transparent;
}

/* 表格样式优化 */
:deep(.el-table) {
  border: none;
}

:deep(.el-table th) {
  background: #fafafa;
  border-bottom: 1px solid #e4e7ed;
  font-weight: 600;
  color: #606266;
}

:deep(.el-table td) {
  border-bottom: 1px solid #f0f0f0;
}

:deep(.el-table tr:hover > td) {
  background-color: #f5f7fa;
}

:deep(.el-table__body tr.current-row > td) {
  background-color: #ecf5ff;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .channel-container {
    padding: 10px;
  }

  .search-container {
    padding: 15px;
  }

  .search-form {
    flex-direction: column;
    align-items: stretch;
    gap: 12px;
  }

  .add-button-item {
    margin-left: 0;
    order: -1;
  }

  .action-buttons {
    flex-direction: column;
    gap: 4px;
  }
}

/* 测试对话框样式 */
.form-tip {
  margin-top: 4px;
  line-height: 1.4;
}

.form-tip .el-text {
  font-size: 12px;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

.webhook-tip {
  padding: 12px 16px;
  background: #f0f9ff;
  border: 1px solid #e1f5fe;
  border-radius: 6px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.webhook-tip .el-text {
  display: flex;
  align-items: center;
  gap: 6px;
}
</style>
