<template>
  <div class="page-container">
    <!-- 页面标题 -->
    <div class="page-header">
      <h1 class="page-title">首页</h1>
      <p class="page-description">欢迎使用SEVB数据开发平台</p>
    </div>



    <!-- 数据源连接状态统计 -->
    <div class="stats-section">
      <h3 class="section-title">
        <el-icon><DataBoard /></el-icon>
        数据源连接状态
        <div class="section-actions">
          <el-button size="small" type="primary" @click="refreshStats">
            <el-icon><Refresh /></el-icon>
            刷新状态
          </el-button>
          <el-button size="small" @click="navigateTo('/datasource')">
            <el-icon><Plus /></el-icon>
            添加数据源
          </el-button>
        </div>
      </h3>


      <!-- 数据源监控图表 -->
      <el-row :gutter="20" class="mb-20">
        <el-col :xs="24" :sm="12" :md="8" :lg="8">
          <el-card class="chart-card">
            <template #header>
              <div class="card-header">
                <span>连接状态趋势</span>
                <el-button link size="small">
                  <el-icon><TrendCharts /></el-icon>
                </el-button>
              </div>
            </template>
            <div class="trend-chart">
              <div class="trend-item" v-for="(day, index) in connectionTrend" :key="index">
                <div class="trend-bar">
                  <div class="bar-success" :style="{ height: day.success + '%' }"></div>
                  <div class="bar-error" :style="{ height: day.error + '%' }"></div>
                </div>
                <div class="trend-label">{{ day.label }}</div>
              </div>
            </div>
            <div class="trend-legend">
              <div class="legend-item">
                <span class="legend-color success"></span>
                <span>正常连接</span>
              </div>
              <div class="legend-item">
                <span class="legend-color error"></span>
                <span>连接异常</span>
              </div>
            </div>
          </el-card>
        </el-col>

        <el-col :xs="24" :sm="12" :md="8" :lg="8">
          <el-card class="chart-card">
            <template #header>
              <div class="card-header">
                <span>响应时间监控</span>
                <el-button link size="small">
                  <el-icon><Timer /></el-icon>
                </el-button>
              </div>
            </template>
            <div class="response-time-chart">
              <div class="metric-row">
                <div class="metric-item">
                  <div class="metric-value">{{ performanceStats.avgResponseTime }}</div>
                  <div class="metric-label">平均响应时间</div>
                </div>
                <div class="metric-item">
                  <div class="metric-value">{{ performanceStats.maxResponseTime }}</div>
                  <div class="metric-label">最大响应时间</div>
                </div>
              </div>
              <div class="response-bars">
                <div class="response-bar" v-for="(time, index) in responseTimeData" :key="index">
                  <div class="bar-fill" :style="{ height: (time / 100) * 100 + '%', backgroundColor: getResponseTimeColor(time) }"></div>
                  <div class="bar-label">{{ index + 1 }}h</div>
                </div>
              </div>
            </div>
          </el-card>
        </el-col>

        <el-col :xs="24" :sm="24" :md="8" :lg="8">
          <el-card class="chart-card">
            <template #header>
              <div class="card-header">
                <span>实时告警</span>
                <el-badge :value="recentAlerts.length" type="danger">
                  <el-button link size="small">
                    <el-icon><Bell /></el-icon>
                  </el-button>
                </el-badge>
              </div>
            </template>
            <div class="alert-list">
              <div class="alert-item" v-for="alert in recentAlerts" :key="alert.id">
                <div class="alert-icon">
                  <el-icon size="16" :color="alert.level === 'error' ? '#f56c6c' : '#e6a23c'">
                    <Warning v-if="alert.level === 'error'" />
                    <InfoFilled v-else />
                  </el-icon>
                </div>
                <div class="alert-content">
                  <div class="alert-title">{{ alert.title }}</div>
                  <div class="alert-time">{{ alert.time }}</div>
                </div>
                <div class="alert-action">
                  <el-button size="small" link>处理</el-button>
                </div>
              </div>
            </div>
            <div class="alert-footer">
              <el-button link size="small" @click="navigateTo('/alert/record')">
                查看全部告警
                <el-icon><ArrowRight /></el-icon>
              </el-button>
            </div>
          </el-card>
        </el-col>
      </el-row>


    </div>

    <!-- 工作流执行统计 -->
    <div class="stats-section">
      <h3 class="section-title">
        <el-icon><Connection /></el-icon>
        工作流执行统计
      </h3>
      <el-row :gutter="20" class="mb-20">
        <el-col :xs="24" :sm="24" :md="16" :lg="16">
          <el-card class="chart-card">
            <template #header>
              <div class="card-header">
                <span>执行趋势</span>
              </div>
            </template>
            <div class="chart-container">
              <div class="line-chart">
                <div class="chart-grid">
                  <div class="chart-line">
                    <div class="line-point" v-for="(point, index) in trendData" :key="index"
                         :style="{ left: (index * 14.28) + '%', bottom: point + '%' }">
                      <div class="point-tooltip">{{ Math.round(point * 2) }}</div>
                    </div>
                    <svg class="line-path" viewBox="0 0 100 100" preserveAspectRatio="none">
                      <polyline
                        :points="getTrendPoints()"
                        fill="none"
                        stroke="#409eff"
                        stroke-width="2"
                      />
                    </svg>
                  </div>
                </div>
                <div class="chart-labels">
                  <span v-for="day in ['周一', '周二', '周三', '周四', '周五', '周六', '周日']" :key="day">{{ day }}</span>
                </div>
              </div>
            </div>
          </el-card>
        </el-col>

        <el-col :xs="24" :sm="24" :md="8" :lg="8">
          <el-row :gutter="20">
            <el-col :xs="12" :sm="12" :md="24" :lg="24">
              <el-card class="stat-card workflow">
                <div class="stat-content">
                  <div class="stat-icon">
                    <el-icon size="32"><DataBoard /></el-icon>
                  </div>
                  <div class="stat-info">
                    <div class="stat-value">{{ workflowStats.todayExecutions }}</div>
                    <div class="stat-label">今日执行次数</div>
                  </div>
                </div>
              </el-card>
            </el-col>

            <el-col :xs="12" :sm="12" :md="24" :lg="24">
              <el-card class="stat-card success">
                <div class="stat-content">
                  <div class="stat-icon">
                    <el-icon size="32"><CircleCheckFilled /></el-icon>
                  </div>
                  <div class="stat-info">
                    <div class="stat-value">{{ workflowStats.successRate }}%</div>
                    <div class="stat-label">成功率</div>
                  </div>
                </div>
              </el-card>
            </el-col>
          </el-row>
        </el-col>
      </el-row>
    </div>

    <!-- 系统状态概览 -->
    <div class="stats-section">
      <h3 class="section-title">
        <el-icon><Monitor /></el-icon>
        系统状态概览
      </h3>
      <el-row :gutter="20" class="mb-20">
        <el-col :xs="12" :sm="6" :md="4" :lg="4">
          <el-card class="stat-card system">
            <div class="stat-content">
              <div class="stat-icon">
                <el-icon size="32"><Cpu /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-value">{{ systemStats.cpuUsage }}%</div>
                <div class="stat-label">CPU使用率</div>
              </div>
            </div>
          </el-card>
        </el-col>

        <el-col :xs="12" :sm="6" :md="4" :lg="4">
          <el-card class="stat-card memory">
            <div class="stat-content">
              <div class="stat-icon">
                <el-icon size="32"><Coin /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-value">{{ systemStats.memoryUsage }}%</div>
                <div class="stat-label">内存使用率</div>
              </div>
            </div>
          </el-card>
        </el-col>

        <el-col :xs="12" :sm="6" :md="4" :lg="4">
          <el-card class="stat-card disk">
            <div class="stat-content">
              <div class="stat-icon">
                <el-icon size="32"><Files /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-value">{{ systemStats.diskUsage }}%</div>
                <div class="stat-label">磁盘使用率</div>
              </div>
            </div>
          </el-card>
        </el-col>

        <el-col :xs="12" :sm="6" :md="4" :lg="4">
          <el-card class="stat-card network">
            <div class="stat-content">
              <div class="stat-icon">
                <el-icon size="32"><Link /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-value">{{ systemStats.networkSpeed }}</div>
                <div class="stat-label">网络速度</div>
              </div>
            </div>
          </el-card>
        </el-col>

        <el-col :xs="12" :sm="6" :md="4" :lg="4">
          <el-card class="stat-card online">
            <div class="stat-content">
              <div class="stat-icon">
                <el-icon size="32"><User /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-value">{{ systemStats.onlineUsers }}</div>
                <div class="stat-label">在线用户</div>
              </div>
            </div>
          </el-card>
        </el-col>

        <el-col :xs="12" :sm="6" :md="4" :lg="4">
          <el-card class="stat-card uptime">
            <div class="stat-content">
              <div class="stat-icon">
                <el-icon size="32"><Clock /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-value">{{ systemStats.uptime }}</div>
                <div class="stat-label">系统运行时间</div>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 任务调度统计 -->
    <div class="stats-section">
      <h3 class="section-title">
        <el-icon><Timer /></el-icon>
        任务调度统计
      </h3>
      <el-row :gutter="20" class="mb-20">
        <el-col :xs="24" :sm="12" :md="8" :lg="8">
          <el-card class="chart-card">
            <template #header>
              <div class="card-header">
                <span>任务执行效率</span>
              </div>
            </template>
            <div class="chart-container">
              <div class="gauge-chart">
                <div class="gauge-container">
                  <div class="gauge-circle">
                    <div class="gauge-fill" :style="{ '--percentage': getTaskEfficiency() }"></div>
                    <div class="gauge-center">
                      <div class="gauge-value">{{ getTaskEfficiency() }}%</div>
                      <div class="gauge-label">执行效率</div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </el-card>
        </el-col>

        <el-col :xs="24" :sm="12" :md="16" :lg="16">
          <el-card class="chart-card">
            <template #header>
              <div class="card-header">
                <span>任务状态分布</span>
              </div>
            </template>
            <div class="chart-container">
              <div class="bar-chart">
                <div class="bar-item">
                  <div class="bar-label">运行中</div>
                  <div class="bar-container">
                    <div class="bar-fill running" :style="{ width: getPercentage(scheduleStats.runningTasks, scheduleStats.totalTasks) + '%' }"></div>
                  </div>
                  <div class="bar-value">{{ scheduleStats.runningTasks }}</div>
                </div>
                <div class="bar-item">
                  <div class="bar-label">待执行</div>
                  <div class="bar-container">
                    <div class="bar-fill pending" :style="{ width: getPercentage(scheduleStats.pendingTasks, scheduleStats.totalTasks) + '%' }"></div>
                  </div>
                  <div class="bar-value">{{ scheduleStats.pendingTasks }}</div>
                </div>
                <div class="bar-item">
                  <div class="bar-label">暂停</div>
                  <div class="bar-container">
                    <div class="bar-fill paused" :style="{ width: getPercentage(scheduleStats.pausedTasks, scheduleStats.totalTasks) + '%' }"></div>
                  </div>
                  <div class="bar-value">{{ scheduleStats.pausedTasks }}</div>
                </div>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'
import { useRouter } from 'vue-router'
import { useDatasourceStore } from '@/stores/datasource'
import {
  Coin,
  CircleCheckFilled,
  Connection,
  Warning,
  DataBoard,
  Timer,
  Clock,
  Cpu,
  Files,
  Link,
  User,
  Plus,
  View,
  Refresh,
  TrendCharts,
  Bell,
  InfoFilled,
  ArrowRight
} from '@element-plus/icons-vue'

const router = useRouter()
const datasourceStore = useDatasourceStore()

// 响应式数据 - 数据源统计
const datasourceStats = ref({
  total: 0,
  connected: 0,
  disconnected: 0,
  error: 0
})



// 工作流执行统计
const workflowStats = ref({
  todayExecutions: 156,
  successRate: 94.2,
  failedCount: 9,
  runningCount: 3
})

// 任务调度统计
const scheduleStats = ref({
  totalTasks: 28,
  runningTasks: 5,
  pendingTasks: 12,
  pausedTasks: 2
})

// 系统状态统计
const systemStats = ref({
  cpuUsage: 45,
  memoryUsage: 68,
  diskUsage: 32,
  networkSpeed: '125MB/s',
  onlineUsers: 24,
  uptime: '15天'
})





// 连接状态趋势数据（7天）
const connectionTrend = ref([
  { label: '周一', success: 85, error: 15 },
  { label: '周二', success: 92, error: 8 },
  { label: '周三', success: 88, error: 12 },
  { label: '周四', success: 95, error: 5 },
  { label: '周五', success: 90, error: 10 },
  { label: '周六', success: 87, error: 13 },
  { label: '周日', success: 93, error: 7 }
])

// 性能统计数据
const performanceStats = ref({
  avgResponseTime: '15ms',
  maxResponseTime: '45ms',
  minResponseTime: '3ms'
})

// 响应时间数据（24小时）
const responseTimeData = ref([12, 15, 18, 22, 25, 20, 16, 14, 13, 15, 18, 21, 24, 28, 32, 29, 25, 22, 19, 16, 14, 12, 10, 11])

// 最近告警数据
const recentAlerts = ref([
  {
    id: 1,
    title: 'Oracle生产库连接超时',
    time: '5分钟前',
    level: 'error'
  },
  {
    id: 2,
    title: 'MySQL主库响应时间过长',
    time: '15分钟前',
    level: 'warning'
  },
  {
    id: 3,
    title: 'Redis缓存连接数过高',
    time: '30分钟前',
    level: 'warning'
  }
])



// 趋势数据（7天）
const trendData = ref([65, 78, 82, 75, 88, 92, 85])

// 方法
const loadStatistics = async () => {
  try {
    // 获取数据源统计信息
    await datasourceStore.fetchDatasources({
      pageNum: 1,
      pageSize: 1000 // 获取所有数据用于统计
    })

    // 更新数据源统计
    datasourceStats.value = {
      total: datasourceStore.total,
      connected: datasourceStore.datasources.filter(ds => ds.testStatus === 1).length,
      disconnected: datasourceStore.datasources.filter(ds => ds.testStatus === 0).length,
      error: datasourceStore.datasources.filter(ds => ds.testStatus === 2).length
    }
  } catch (error) {
    console.error('加载统计信息失败:', error)
    // 设置默认值
    datasourceStats.value = {
      total: 0,
      connected: 0,
      disconnected: 0,
      error: 0
    }
  }
}

// 刷新统计数据
const refreshStats = () => {
  loadStatistics()
  // 模拟刷新其他统计数据
  workflowStats.value.todayExecutions = Math.floor(Math.random() * 200) + 100
  workflowStats.value.successRate = Math.floor(Math.random() * 10) + 90
  scheduleStats.value.runningTasks = Math.floor(Math.random() * 10) + 1

  // 模拟刷新系统状态数据
  systemStats.value.cpuUsage = Math.floor(Math.random() * 50) + 20
  systemStats.value.memoryUsage = Math.floor(Math.random() * 40) + 40
  systemStats.value.diskUsage = Math.floor(Math.random() * 30) + 20
  systemStats.value.onlineUsers = Math.floor(Math.random() * 20) + 10
}

// 导航方法
const navigateTo = (path: string) => {
  router.push(path)
}





// 响应时间颜色
const getResponseTimeColor = (time: number) => {
  if (time <= 10) return '#67c23a'
  if (time <= 20) return '#e6a23c'
  if (time <= 30) return '#409eff'
  return '#f56c6c'
}



// 辅助方法
// 计算百分比
const getPercentage = (value: number, total: number) => {
  return total > 0 ? Math.round((value / total) * 100) : 0
}

// 获取趋势图点坐标
const getTrendPoints = () => {
  return trendData.value.map((point, index) =>
    `${index * 14.28},${100 - point}`
  ).join(' ')
}

// 计算任务执行效率
const getTaskEfficiency = () => {
  const total = scheduleStats.value.totalTasks
  const running = scheduleStats.value.runningTasks
  const pending = scheduleStats.value.pendingTasks
  return total > 0 ? Math.round(((running + pending) / total) * 100) : 0
}

// 生命周期
onMounted(() => {
  loadStatistics()
})
</script>

<style scoped>
.page-container {
  padding: 20px;
  background: #f5f7fa;
  width: 100%;
  box-sizing: border-box;
}

.page-header {
  margin-bottom: 24px;
}

.page-title {
  font-size: 24px;
  font-weight: 600;
  color: #303133;
  margin-bottom: 8px;
}

.page-description {
  color: #606266;
  font-size: 14px;
}



/* 统计区域样式 */
.stats-section {
  margin-bottom: 32px;
}







/* 图表卡片样式 */
.chart-card {
  border: none;
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.chart-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-weight: 600;
}

.header-actions {
  display: flex;
  gap: 8px;
}

/* 趋势图表样式 */
.trend-chart {
  display: flex;
  justify-content: space-between;
  align-items: end;
  height: 120px;
  padding: 16px 0;
}

.trend-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  flex: 1;
}

.trend-bar {
  width: 20px;
  height: 80px;
  background: var(--el-fill-color-lighter);
  border-radius: 4px;
  position: relative;
  overflow: hidden;
}

.bar-success {
  position: absolute;
  bottom: 0;
  width: 100%;
  background: #67c23a;
  border-radius: 4px 4px 0 0;
}

.bar-error {
  position: absolute;
  bottom: 0;
  width: 100%;
  background: #f56c6c;
  border-radius: 4px 4px 0 0;
}

.trend-label {
  font-size: 12px;
  color: var(--el-text-color-regular);
  margin-top: 8px;
}

.trend-legend {
  display: flex;
  justify-content: center;
  gap: 16px;
  margin-top: 12px;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
}

.legend-color {
  width: 12px;
  height: 12px;
  border-radius: 2px;
}

.legend-color.success {
  background: #67c23a;
}

.legend-color.error {
  background: #f56c6c;
}

/* 响应时间图表样式 */
.response-time-chart {
  padding: 16px 0;
}

.response-bars {
  display: flex;
  justify-content: space-between;
  align-items: end;
  height: 80px;
  margin-top: 16px;
}

.response-bar {
  display: flex;
  flex-direction: column;
  align-items: center;
  flex: 1;
  max-width: 20px;
}

.bar-fill {
  width: 12px;
  border-radius: 2px 2px 0 0;
  min-height: 4px;
}

.bar-label {
  font-size: 10px;
  color: var(--el-text-color-regular);
  margin-top: 4px;
}

/* 告警列表样式 */
.alert-list {
  max-height: 200px;
  overflow-y: auto;
}

.alert-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px 0;
  border-bottom: 1px solid var(--el-border-color-lighter);
}

.alert-item:last-child {
  border-bottom: none;
}

.alert-icon {
  flex-shrink: 0;
}

.alert-content {
  flex: 1;
}

.alert-title {
  font-size: 14px;
  color: var(--el-text-color-primary);
  margin-bottom: 4px;
}

.alert-time {
  font-size: 12px;
  color: var(--el-text-color-regular);
}

.alert-action {
  flex-shrink: 0;
}

.alert-footer {
  text-align: center;
  padding-top: 12px;
  border-top: 1px solid var(--el-border-color-lighter);
}





.type-progress .progress-label {
  font-size: 12px;
  color: var(--el-text-color-regular);
  margin-bottom: 6px;
}

.type-progress .progress-text {
  font-size: 12px;
  font-weight: 600;
  color: var(--el-text-color-primary);
  margin-left: 8px;
}

.section-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 18px;
  font-weight: 600;
  color: #303133;
  margin-bottom: 16px;
  padding-left: 4px;
  border-left: 4px solid #409eff;
  justify-content: space-between;
}

.section-actions {
  display: flex;
  gap: 8px;
  margin-left: auto;
}

.section-title .el-icon {
  color: #409eff;
}

.stat-card {
  height: 120px;
  margin-bottom: 20px;
  transition: all 0.3s ease;
  cursor: pointer;
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

/* 系统状态卡片特殊样式 */
.stat-card.system {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.stat-card.memory {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
  color: white;
}

.stat-card.disk {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
  color: white;
}

.stat-card.network {
  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
  color: white;
}

.stat-card.online {
  background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
  color: white;
}

.stat-card.uptime {
  background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
  color: #333;
}

.stat-content {
  display: flex;
  align-items: center;
  height: 100%;
}

.stat-icon {
  margin-right: 16px;
}

.stat-info {
  flex: 1;
}

.stat-value {
  font-size: 28px;
  font-weight: 600;
  color: #303133;
  line-height: 1;
  margin-bottom: 8px;
}

.stat-label {
  font-size: 14px;
  color: #909399;
}

/* 不同类型统计卡片的图标颜色 */
.stat-card.datasource .stat-icon { color: #409eff; }
.stat-card.connected .stat-icon { color: #67c23a; }
.stat-card.disconnected .stat-icon { color: #e6a23c; }
.stat-card.error .stat-icon { color: #f56c6c; }

.stat-card.project .stat-icon { color: #409eff; }
.stat-card.active .stat-icon { color: #67c23a; }
.stat-card.public .stat-icon { color: #67c23a; }
.stat-card.private .stat-icon { color: #909399; }

.stat-card.workflow .stat-icon { color: #409eff; }
.stat-card.success .stat-icon { color: #67c23a; }
.stat-card.failed .stat-icon { color: #f56c6c; }
.stat-card.running .stat-icon { color: #e6a23c; }

.stat-card.schedule .stat-icon { color: #409eff; }
.stat-card.pending .stat-icon { color: #e6a23c; }
.stat-card.paused .stat-icon { color: #909399; }

/* 图表卡片样式 */
.chart-card {
  height: 320px;
  margin-bottom: 20px;
}

.chart-container {
  height: 240px;
  padding: 20px 0;
}

/* 饼图样式 */
.pie-chart {
  display: flex;
  flex-direction: column;
  align-items: center;
  height: 100%;
}

.pie-item {
  position: relative;
  width: 120px;
  height: 120px;
  margin-bottom: 20px;
}

.pie-segment {
  width: 100%;
  height: 100%;
  border-radius: 50%;
  background: conic-gradient(
    #67c23a 0deg calc(var(--percentage) * 3.6deg),
    #e6a23c calc(var(--percentage) * 3.6deg) calc((var(--percentage) + 20) * 3.6deg),
    #f56c6c calc((var(--percentage) + 20) * 3.6deg) 360deg
  );
  position: relative;
}

.pie-center {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  text-align: center;
  background: #fff;
  width: 70px;
  height: 70px;
  border-radius: 50%;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.pie-value {
  font-size: 20px;
  font-weight: 600;
  color: #303133;
}

.pie-label {
  font-size: 12px;
  color: #606266;
}

.pie-legend {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 12px;
  color: #606266;
}

.legend-color {
  width: 12px;
  height: 12px;
  border-radius: 2px;
}

.legend-color.connected { background: #67c23a; }
.legend-color.disconnected { background: #e6a23c; }
.legend-color.error { background: #f56c6c; }

/* 进度图表样式 */
.progress-chart {
  padding: 20px 0;
}

.progress-item {
  margin-bottom: 24px;
}

.progress-header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
}

.progress-label {
  font-size: 14px;
  color: #606266;
  font-weight: 500;
}

.progress-value {
  font-size: 14px;
  color: #303133;
  font-weight: 600;
}

/* 折线图样式 */
.line-chart {
  height: 100%;
  position: relative;
}

.chart-grid {
  height: 180px;
  position: relative;
  background: linear-gradient(to top, #f5f7fa 0%, transparent 100%);
  border-radius: 4px;
  overflow: hidden;
}

.chart-line {
  position: relative;
  width: 100%;
  height: 100%;
}

.line-point {
  position: absolute;
  width: 8px;
  height: 8px;
  background: #409eff;
  border-radius: 50%;
  transform: translate(-50%, 50%);
  cursor: pointer;
  transition: all 0.3s ease;
}

.line-point:hover {
  width: 12px;
  height: 12px;
  background: #337ecc;
}

.point-tooltip {
  position: absolute;
  bottom: 15px;
  left: 50%;
  transform: translateX(-50%);
  background: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  white-space: nowrap;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.line-point:hover .point-tooltip {
  opacity: 1;
}

.line-path {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
}

.chart-labels {
  display: flex;
  justify-content: space-between;
  margin-top: 12px;
  font-size: 12px;
  color: #909399;
}

/* 仪表盘样式 */
.gauge-chart {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
}

.gauge-container {
  position: relative;
}

.gauge-circle {
  position: relative;
  width: 140px;
  height: 140px;
}

.gauge-fill {
  width: 100%;
  height: 100%;
  border-radius: 50%;
  background: conic-gradient(
    from 0deg,
    #67c23a 0deg calc(var(--percentage) * 3.6deg),
    #e6e8eb calc(var(--percentage) * 3.6deg) 360deg
  );
  position: relative;
}

.gauge-fill::before {
  content: '';
  position: absolute;
  top: 10px;
  left: 10px;
  right: 10px;
  bottom: 10px;
  background: #fff;
  border-radius: 50%;
}

.gauge-center {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  text-align: center;
}

.gauge-value {
  font-size: 24px;
  font-weight: 600;
  color: #303133;
  margin-bottom: 4px;
}

.gauge-label {
  font-size: 12px;
  color: #606266;
}

/* 柱状图样式 */
.bar-chart {
  padding: 20px 0;
}

.bar-item {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
  gap: 12px;
}

.bar-label {
  width: 60px;
  font-size: 14px;
  color: #606266;
  text-align: right;
}

.bar-container {
  flex: 1;
  height: 20px;
  background: #f5f7fa;
  border-radius: 10px;
  overflow: hidden;
  position: relative;
}

.bar-fill {
  height: 100%;
  border-radius: 10px;
  transition: width 0.8s ease;
}

.bar-fill.running { background: #409eff; }
.bar-fill.pending { background: #e6a23c; }
.bar-fill.paused { background: #909399; }

.bar-value {
  width: 30px;
  font-size: 14px;
  color: #303133;
  font-weight: 600;
  text-align: center;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .page-container {
    padding: 16px;
  }

  .section-title {
    font-size: 16px;
  }

  .stat-card {
    height: 100px;
  }

  .stat-value {
    font-size: 24px;
  }

  .stat-label {
    font-size: 12px;
  }

  .chart-card {
    height: 280px;
  }

  .chart-container {
    height: 200px;
  }

  .pie-item {
    width: 100px;
    height: 100px;
  }

  .pie-center {
    width: 60px;
    height: 60px;
  }

  .gauge-circle {
    width: 120px;
    height: 120px;
  }

  .chart-grid {
    height: 140px;
  }
}

.info-label {
  color: #606266;
  font-size: 14px;
}

.info-value {
  color: #303133;
  font-size: 14px;
  font-weight: 500;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .page-container {
    padding: 10px;
  }
  
  .quick-actions {
    flex-direction: column;
  }
  
  .quick-btn {
    width: 100%;
  }
}
</style>
