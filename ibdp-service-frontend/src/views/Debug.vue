<template>
  <div style="
    background: red !important;
    color: white !important;
    padding: 50px !important;
    font-size: 24px !important;
    min-height: 100vh !important;
    position: fixed !important;
    top: 0 !important;
    left: 0 !important;
    width: 100% !important;
    z-index: 99999 !important;
    box-sizing: border-box !important;
  ">
    <h1 style="color: white !important; font-size: 32px !important;">🔴 调试页面</h1>
    <p style="color: white !important;">如果您能看到这个红色页面，说明Vue应用和路由工作正常</p>
    <p style="color: white !important;">当前时间：{{ currentTime }}</p>
    <p style="color: white !important;">当前路由：{{ currentRoute }}</p>
    <p style="color: white !important;">Vue版本：{{ vueVersion }}</p>
    <button @click="testClick" style="
      background: yellow !important;
      color: black !important;
      padding: 10px 20px !important;
      font-size: 16px !important;
      border: none !important;
      cursor: pointer !important;
    ">
      点击测试Vue响应性
    </button>
    <p v-if="clicked" style="color: yellow !important; font-weight: bold !important;">
      ✅ Vue响应性正常工作！点击次数：{{ clickCount }}
    </p>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { useRoute } from 'vue-router'

console.log('🔴 调试页面开始加载')

const route = useRoute()
const clicked = ref(false)
const clickCount = ref(0)

const currentTime = computed(() => new Date().toLocaleString())
const currentRoute = computed(() => route.path)
const vueVersion = computed(() => '3.x')

const testClick = () => {
  clicked.value = true
  clickCount.value++
  console.log('🔴 按钮被点击，Vue响应性正常')
}

console.log('🔴 调试页面加载完成')
</script>
