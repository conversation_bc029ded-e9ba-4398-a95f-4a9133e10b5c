<template>
  <div class="sync-monitor">
    <!-- 页面头部 -->
    <div class="page-header">
      <h2>DataX增量同步监控</h2>
      <p>实时监控DataX增量同步任务的执行状态和性能指标</p>
    </div>

    <!-- 统计卡片 -->
    <el-row :gutter="20" class="stats-row">
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-item">
            <div class="stat-value">{{ statistics.totalStates }}</div>
            <div class="stat-label">总同步任务</div>
            <div class="stat-icon">
              <el-icon><DataBoard /></el-icon>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stat-card active">
          <div class="stat-item">
            <div class="stat-value">{{ statistics.activeStates }}</div>
            <div class="stat-label">活跃任务</div>
            <div class="stat-icon">
              <el-icon><CircleCheck /></el-icon>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stat-card error">
          <div class="stat-item">
            <div class="stat-value">{{ statistics.errorStates }}</div>
            <div class="stat-label">错误任务</div>
            <div class="stat-icon">
              <el-icon><CircleClose /></el-icon>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-item">
            <div class="stat-value">{{ formatNumber(statistics.totalSyncRecords) }}</div>
            <div class="stat-label">累计同步记录</div>
            <div class="stat-icon">
              <el-icon><TrendCharts /></el-icon>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 搜索和操作栏 -->
    <el-card class="search-card">
      <el-form :inline="true" :model="searchForm" size="default">
        <el-form-item label="工作流">
          <el-select v-model="searchForm.workflowId" placeholder="请选择工作流" clearable style="width: 200px">
            <el-option
              v-for="workflow in workflows"
              :key="workflow.id"
              :label="workflow.workflowName"
              :value="workflow.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="状态">
          <el-select v-model="searchForm.status" placeholder="请选择状态" clearable style="width: 150px">
            <el-option label="活跃" value="ACTIVE" />
            <el-option label="非活跃" value="INACTIVE" />
            <el-option label="错误" value="ERROR" />
          </el-select>
        </el-form-item>
        <el-form-item label="源表">
          <el-input v-model="searchForm.sourceTable" placeholder="请输入源表名" clearable style="width: 200px" />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="loadSyncStates" :icon="Search">
            搜索
          </el-button>
          <el-button @click="resetSearch" :icon="Refresh">重置</el-button>
          <el-button type="success" @click="refreshAll" :icon="RefreshRight">
            刷新
          </el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 同步状态列表 -->
    <el-card class="table-card">
      <template #header>
        <div class="table-header">
          <span>同步状态列表</span>
          <div>
            <el-button type="primary" size="default" @click="showCreateDialog = true" :icon="Plus">
              新建同步任务
            </el-button>
          </div>
        </div>
      </template>

      <el-table 
        :data="syncStates" 
        v-loading="loading"
        stripe
        style="width: 100%"
      >
        <el-table-column prop="id" label="ID" width="80" />
        
        <el-table-column label="工作流/节点" width="200">
          <template #default="scope">
            <div>
              <div class="workflow-name">{{ scope.row.workflowName || '未知工作流' }}</div>
              <div class="node-name">{{ scope.row.nodeName || scope.row.nodeCode }}</div>
            </div>
          </template>
        </el-table-column>

        <el-table-column label="数据表" width="200">
          <template #default="scope">
            <div>
              <div class="table-info">
                <el-icon><Download /></el-icon>
                {{ scope.row.sourceTable }}
              </div>
              <div class="table-info">
                <el-icon><Upload /></el-icon>
                {{ scope.row.targetTable }}
              </div>
            </div>
          </template>
        </el-table-column>

        <el-table-column label="增量配置" width="180">
          <template #default="scope">
            <div>
              <el-tag size="small" type="info">{{ scope.row.incrementalType }}</el-tag>
              <div class="incremental-column">{{ scope.row.incrementalColumn }}</div>
            </div>
          </template>
        </el-table-column>

        <el-table-column label="状态" width="100">
          <template #default="scope">
            <el-tag 
              :type="getStatusType(scope.row.status)" 
              size="default"
            >
              {{ getStatusText(scope.row.status) }}
            </el-tag>
          </template>
        </el-table-column>

        <el-table-column label="同步信息" width="150">
          <template #default="scope">
            <div class="sync-info">
              <div>次数: {{ scope.row.syncCount || 0 }}</div>
              <div>记录: {{ formatNumber(scope.row.totalRecords || 0) }}</div>
            </div>
          </template>
        </el-table-column>

        <el-table-column label="最后同步" width="180">
          <template #default="scope">
            <div v-if="scope.row.lastSyncTime">
              <div class="sync-time">{{ formatTime(scope.row.lastSyncTime) }}</div>
              <div class="sync-value">{{ scope.row.lastSyncValue }}</div>
            </div>
            <span v-else class="no-sync">从未同步</span>
          </template>
        </el-table-column>

        <el-table-column label="操作" width="200" fixed="right">
          <template #default="scope">
            <el-button 
              type="primary" 
              size="small" 
              @click="viewDetails(scope.row)"
              :icon="View"
              link
            >
              详情
            </el-button>
            <el-button 
              type="primary" 
              size="small" 
              @click="viewLogs(scope.row)"
              :icon="Document"
              link
            >
              日志
            </el-button>
            <el-button 
              type="warning" 
              size="small" 
              @click="resetSyncState(scope.row)"
              :icon="RefreshLeft"
              link
            >
              重置
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-wrapper">
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="pagination.current"
          :page-sizes="[10, 20, 50, 100]"
          :page-size="pagination.size"
          layout="total, sizes, prev, pager, next, jumper"
          :total="pagination.total"
        />
      </div>
    </el-card>

    <!-- 详情对话框 -->
    <el-dialog 
      v-model="showDetailDialog" 
      title="同步状态详情" 
      width="800px"
    >
      <div v-if="selectedSyncState" class="sync-detail">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="节点ID">{{ selectedSyncState.nodeId }}</el-descriptions-item>
          <el-descriptions-item label="工作流ID">{{ selectedSyncState.workflowId }}</el-descriptions-item>
          <el-descriptions-item label="源表">{{ selectedSyncState.sourceTable }}</el-descriptions-item>
          <el-descriptions-item label="目标表">{{ selectedSyncState.targetTable }}</el-descriptions-item>
          <el-descriptions-item label="增量字段">{{ selectedSyncState.incrementalColumn }}</el-descriptions-item>
          <el-descriptions-item label="增量类型">{{ selectedSyncState.incrementalType }}</el-descriptions-item>
          <el-descriptions-item label="状态">
            <el-tag :type="getStatusType(selectedSyncState.status)">
              {{ getStatusText(selectedSyncState.status) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="同步次数">{{ selectedSyncState.syncCount || 0 }}</el-descriptions-item>
          <el-descriptions-item label="总记录数">{{ formatNumber(selectedSyncState.totalRecords || 0) }}</el-descriptions-item>
          <el-descriptions-item label="最后同步值">{{ selectedSyncState.lastSyncValue || '无' }}</el-descriptions-item>
          <el-descriptions-item label="创建时间">{{ formatTime(selectedSyncState.createTime) }}</el-descriptions-item>
          <el-descriptions-item label="更新时间">{{ formatTime(selectedSyncState.updateTime) }}</el-descriptions-item>
        </el-descriptions>
        
        <div v-if="selectedSyncState.errorMessage" class="error-info">
          <h4>错误信息：</h4>
          <el-alert type="error" :closable="false">
            {{ selectedSyncState.errorMessage }}
          </el-alert>
        </div>
      </div>
    </el-dialog>

    <!-- 日志对话框 -->
    <el-dialog 
      v-model="showLogDialog" 
      title="同步日志" 
      width="1000px"
    >
      <div v-if="selectedSyncState">
        <el-table :data="syncLogs" v-loading="loadingLogs" stripe>
          <el-table-column prop="executionId" label="执行ID" width="150" />
          <el-table-column label="状态" width="100">
            <template #default="scope">
              <el-tag :type="scope.row.status === 'SUCCESS' ? 'success' : 'danger'" size="small">
                {{ scope.row.status }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="syncRecords" label="同步记录数" width="120" />
          <el-table-column prop="duration" label="耗时(秒)" width="100">
            <template #default="scope">
              {{ Math.round((scope.row.duration || 0) / 1000) }}
            </template>
          </el-table-column>
          <el-table-column prop="incrementalValueEnd" label="增量值" width="150" />
          <el-table-column prop="createTime" label="执行时间" width="150">
            <template #default="scope">
              {{ formatTime(scope.row.createTime) }}
            </template>
          </el-table-column>
          <el-table-column prop="errorMessage" label="错误信息" min-width="200" />
        </el-table>
      </div>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  DataBoard,
  CircleCheck,
  CircleClose,
  TrendCharts,
  Search,
  Refresh,
  RefreshRight,
  RefreshLeft,
  Plus,
  View,
  Document,
  Download,
  Upload
} from '@element-plus/icons-vue'
import { request } from '@/utils/request'

// 数据定义
const statistics = ref({
  totalStates: 0,
  activeStates: 0,
  errorStates: 0,
  totalSyncRecords: 0
})

const searchForm = ref({
  workflowId: null,
  status: null,
  sourceTable: ''
})

const pagination = ref({
  current: 1,
  size: 20,
  total: 0
})

const syncStates = ref([])
const workflows = ref([])
const syncLogs = ref([])
const selectedSyncState = ref(null)

const loading = ref(false)
const loadingLogs = ref(false)
const showDetailDialog = ref(false)
const showLogDialog = ref(false)
const showCreateDialog = ref(false)

let timer: NodeJS.Timeout | null = null

// 生命周期
onMounted(() => {
  loadWorkflows()
  loadStatistics()
  loadSyncStates()
  
  // 定时刷新
  timer = setInterval(() => {
    loadStatistics()
    loadSyncStates()
  }, 30000) // 30秒刷新一次
})

onUnmounted(() => {
  if (timer) {
    clearInterval(timer)
  }
})

// 方法定义
const loadWorkflows = async () => {
  try {
    const response = await request.get('/api/workflow/list')
    if (response.code === 200) {
      workflows.value = response.data || []
    }
  } catch (error) {
    console.error('加载工作流列表失败:', error)
  }
}

const loadStatistics = async () => {
  try {
    const response = await request.get('/api/datax/sync-state/statistics')
    if (response.code === 200) {
      statistics.value = response.data || {}
    }
  } catch (error) {
    console.error('加载统计信息失败:', error)
  }
}

const loadSyncStates = async () => {
  loading.value = true
  try {
    const params = {
      current: pagination.value.current,
      size: pagination.value.size,
      ...searchForm.value
    }
    
    const response = await request.get('/api/datax/sync-state/page', { params })
    if (response.code === 200) {
      const data = response.data
      syncStates.value = data.records || []
      pagination.value.total = data.total || 0
    }
  } catch (error) {
    ElMessage.error('加载同步状态失败')
    console.error(error)
  } finally {
    loading.value = false
  }
}

const resetSearch = () => {
  searchForm.value = {
    workflowId: null,
    status: null,
    sourceTable: ''
  }
  pagination.value.current = 1
  loadSyncStates()
}

const refreshAll = () => {
  loadStatistics()
  loadSyncStates()
  ElMessage.success('数据已刷新')
}

const viewDetails = (row: any) => {
  selectedSyncState.value = row
  showDetailDialog.value = true
}

const viewLogs = async (row: any) => {
  selectedSyncState.value = row
  showLogDialog.value = true
  
  loadingLogs.value = true
  try {
    const response = await request.get(`/api/datax/sync-log/list?syncStateId=${row.id}`)
    if (response.code === 200) {
      syncLogs.value = response.data || []
    }
  } catch (error) {
    ElMessage.error('加载同步日志失败')
  } finally {
    loadingLogs.value = false
  }
}

const resetSyncState = async (row: any) => {
  try {
    await ElMessageBox.confirm('确定要重置此同步状态吗？', '确认操作', {
      type: 'warning'
    })
    
    const response = await request.post(`/api/datax/sync-state/reset/${row.id}`)
    if (response.code === 200) {
      ElMessage.success('重置成功')
      loadSyncStates()
    } else {
      ElMessage.error(response.msg || '重置失败')
    }
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('重置失败')
      console.error(error)
    }
  }
}

const handleSizeChange = (size: number) => {
  pagination.value.size = size
  pagination.value.current = 1
  loadSyncStates()
}

const handleCurrentChange = (current: number) => {
  pagination.value.current = current
  loadSyncStates()
}

// 工具方法
const getStatusType = (status: string) => {
  const types: Record<string, string> = {
    'ACTIVE': 'success',
    'INACTIVE': 'info',
    'ERROR': 'danger'
  }
  return types[status] || 'info'
}

const getStatusText = (status: string) => {
  const texts: Record<string, string> = {
    'ACTIVE': '活跃',
    'INACTIVE': '非活跃',
    'ERROR': '错误'
  }
  return texts[status] || status
}

const formatTime = (time: string) => {
  return time ? new Date(time).toLocaleString() : ''
}

const formatNumber = (num: number) => {
  return num ? num.toLocaleString() : '0'
}
</script>

<style scoped>
.sync-monitor {
  padding: 20px;
}

.page-header {
  margin-bottom: 20px;
}

.page-header h2 {
  margin: 0 0 8px 0;
  color: #333;
}

.page-header p {
  margin: 0;
  color: #666;
  font-size: 14px;
}

.stats-row {
  margin-bottom: 20px;
}

.stat-card {
  position: relative;
  overflow: hidden;
}

.stat-card.active {
  border-left: 4px solid #67c23a;
}

.stat-card.error {
  border-left: 4px solid #f56c6c;
}

.stat-item {
  padding: 20px;
  position: relative;
}

.stat-value {
  font-size: 28px;
  font-weight: bold;
  color: #333;
  margin-bottom: 8px;
}

.stat-label {
  font-size: 14px;
  color: #666;
}

.stat-icon {
  position: absolute;
  top: 20px;
  right: 20px;
  font-size: 32px;
  color: #ddd;
}

.search-card, .table-card {
  margin-bottom: 20px;
}

.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.workflow-name {
  font-weight: bold;
  color: #333;
  font-size: 14px;
}

.node-name {
  font-size: 12px;
  color: #666;
  margin-top: 4px;
}

.table-info {
  font-size: 12px;
  margin: 2px 0;
  display: flex;
  align-items: center;
}

.table-info .el-icon {
  margin-right: 4px;
  color: #666;
}

.incremental-column {
  font-size: 12px;
  color: #666;
  margin-top: 4px;
}

.sync-info div {
  font-size: 12px;
  margin: 2px 0;
}

.sync-time {
  font-size: 12px;
  color: #333;
}

.sync-value {
  font-size: 11px;
  color: #666;
  margin-top: 2px;
}

.no-sync {
  font-size: 12px;
  color: #999;
}

.pagination-wrapper {
  text-align: right;
  margin-top: 20px;
}

.sync-detail .error-info {
  margin-top: 20px;
}

.sync-detail .error-info h4 {
  margin-bottom: 10px;
  color: #f56c6c;
}
</style>
