<template>
  <div class="project-detail-container">


    <!-- 主要内容区域 -->
    <div class="project-content">
      <!-- 左侧菜单 -->
      <div class="project-sidebar">
        <el-menu
          :default-active="activeMenu"
          mode="vertical"
          @select="handleMenuSelect"
          class="project-menu"
        >
          <el-menu-item index="overview">
            <el-icon><DataAnalysis /></el-icon>
            <span>项目概览</span>
          </el-menu-item>
          <el-menu-item index="workflow">
            <el-icon><Share /></el-icon>
            <span>流程定义</span>
          </el-menu-item>
          <el-menu-item index="workflow-instance">
            <el-icon><Clock /></el-icon>
            <span>流程实例</span>
          </el-menu-item>
          <el-menu-item index="node-instance">
            <el-icon><List /></el-icon>
            <span>节点实例</span>
          </el-menu-item>
        </el-menu>
      </div>

      <!-- 右侧内容区域 -->
      <div class="project-main">
        <router-view />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import {
  DataAnalysis,
  Share,
  Clock,
  List
} from '@element-plus/icons-vue'

// Router
const route = useRoute()
const router = useRouter()



// 计算属性
const projectId = computed(() => {
  const id = route.params.projectId
  return typeof id === 'string' ? parseInt(id) : null
})

const activeMenu = computed(() => {
  const routeName = route.name as string
  const routePath = route.path as string

  // 调试信息（开发时可以打开）
  // console.log('Route Name:', routeName, 'Route Path:', routePath)

  // 根据路由名称或路径判断当前菜单（注意顺序，先匹配具体的）
  if (routeName?.includes('WorkflowInstance') || routePath?.includes('/workflow-instance')) {
    return 'workflow-instance'
  }
  if (routeName?.includes('NodeInstance') || routePath?.includes('/node-instance')) {
    return 'node-instance'
  }
  if (routeName?.includes('Overview') || routePath?.includes('/overview')) {
    return 'overview'
  }
  if (routeName?.includes('Workflow') || routePath?.includes('/workflow')) {
    return 'workflow'
  }

  // 默认选中概览
  return 'overview'
})



// 方法

const handleMenuSelect = (index: string) => {
  if (!projectId.value) return
  
  const routeMap: Record<string, string> = {
    overview: `/project/${projectId.value}/overview`,
    workflow: `/project/${projectId.value}/workflow`,
    'workflow-instance': `/project/${projectId.value}/workflow-instance`,
    'node-instance': `/project/${projectId.value}/node-instance`
  }
  
  const targetRoute = routeMap[index]
  if (targetRoute && route.path !== targetRoute) {
    router.push(targetRoute)
  }
}

const goBack = () => {
  router.push('/project')
}




</script>

<style scoped>
.project-detail-container {
  min-height: calc(100vh - 60px); /* 减去顶部导航栏高度 */
  background: #f5f7fa;
}



.project-content {
  display: flex;
  width: 100%;
  min-height: calc(100vh - 60px); /* 占满整个视口高度减去顶部导航栏 */
  position: relative;
}

.project-sidebar {
  position: fixed;
  left: 0;
  top: 60px; /* 顶部导航栏高度 */
  width: 200px;
  height: calc(100vh - 60px); /* 减去顶部导航栏高度 */
  background: #fff;
  border-right: 1px solid #e4e7ed;
  z-index: 1000;
  overflow-y: auto;
  box-shadow: 2px 0 8px rgba(0, 0, 0, 0.1);
}

.project-menu {
  border-right: none;
  height: 100%;
  width: 100%;
}

.project-menu .el-menu-item {
  height: 48px;
  line-height: 48px;
  border-radius: 0;
  margin: 0;
}

.project-menu .el-menu-item:hover {
  background-color: #ecf5ff;
}

.project-menu .el-menu-item.is-active {
  background-color: #409eff;
  color: #fff;
}

.project-menu .el-menu-item.is-active .el-icon {
  color: #fff;
}

.project-main {
  flex: 1;
  background: #fff;
  overflow: auto;
  min-height: 0;
  margin-left: 200px; /* 为固定菜单留出空间 */
  width: calc(100% - 200px);
}

/* 中等屏幕响应式设计 */
@media (max-width: 1024px) and (min-width: 769px) {
  .project-sidebar {
    width: 180px; /* 中等屏幕稍微缩小菜单宽度 */
  }

  .project-main {
    margin-left: 180px;
    width: calc(100% - 180px);
  }
}

/* 小屏幕响应式设计 */
@media (max-width: 768px) {
  .project-content {
    flex-direction: column;
  }

  .project-sidebar {
    position: fixed;
    left: 0;
    top: 60px;
    width: 100%;
    height: 60px; /* 移动端菜单高度 */
    border-right: none;
    border-bottom: 1px solid #e4e7ed;
    z-index: 1001; /* 移动端更高的层级 */
    overflow-x: auto;
    overflow-y: hidden;
  }

  .project-menu {
    display: flex;
    height: 60px;
    overflow-x: auto;
    overflow-y: hidden;
    white-space: nowrap;
  }

  .project-menu .el-menu-item {
    flex-shrink: 0;
    min-width: 120px;
    text-align: center;
    height: 60px;
    line-height: 60px;
  }

  .project-main {
    margin-left: 0;
    margin-top: 60px; /* 为移动端固定菜单留出空间 */
    width: 100%;
  }
}
</style>
