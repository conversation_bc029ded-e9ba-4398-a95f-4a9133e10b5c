<template>
  <div class="project-overview">
    <!-- 页面标题 -->
    <div class="page-header">
      <h2>项目概览</h2>
      <p>查看项目的基本信息和整体状况</p>
    </div>

    <!-- 项目基本信息 -->
    <div class="info-section">
      <el-card class="info-card" shadow="never">
        <template #header>
          <div class="card-header">
            <span>基本信息</span>
          </div>
        </template>
        
        <div class="info-content" v-loading="loading">
          <el-descriptions :column="2" border v-if="project">
            <el-descriptions-item label="项目名称">
              {{ project.name }}
            </el-descriptions-item>
            <el-descriptions-item label="项目编码">
              {{ project.code }}
            </el-descriptions-item>
            <el-descriptions-item label="项目类型">
              <el-tag :type="project.type === 'PUBLIC' ? 'success' : 'info'" size="small">
                {{ getProjectTypeName(project.type) }}
              </el-tag>
            </el-descriptions-item>
            <el-descriptions-item label="项目状态">
              <el-tag :type="project.status === 1 ? 'success' : 'danger'" size="small">
                {{ project.status === 1 ? '启用' : '禁用' }}
              </el-tag>
            </el-descriptions-item>
            <el-descriptions-item label="创建时间">
              {{ formatDateTime(project.createTime) }}
            </el-descriptions-item>
            <el-descriptions-item label="更新时间">
              {{ formatDateTime(project.updateTime) }}
            </el-descriptions-item>
            <el-descriptions-item label="项目描述" :span="2">
              {{ project.description || '暂无描述' }}
            </el-descriptions-item>
          </el-descriptions>
          
          <div v-else class="no-data">
            <el-empty description="暂无项目信息" />
          </div>
        </div>
      </el-card>
    </div>

    <!-- 统计信息 -->
    <div class="stats-section">
      <el-row :gutter="20">
        <el-col :xs="12" :sm="6" :md="6" :lg="6">
          <div class="stat-card">
            <div class="stat-icon workflows">
              <el-icon><Connection /></el-icon>
            </div>
            <div class="stat-content">
              <div class="stat-value">{{ statsData.workflowCount }}</div>
              <div class="stat-label">工作流数量</div>
            </div>
          </div>
        </el-col>
        
        <el-col :xs="12" :sm="6" :md="6" :lg="6">
          <div class="stat-card">
            <div class="stat-icon tasks">
              <el-icon><List /></el-icon>
            </div>
            <div class="stat-content">
              <div class="stat-value">{{ statsData.taskCount }}</div>
              <div class="stat-label">任务数量</div>
            </div>
          </div>
        </el-col>
        
        <el-col :xs="12" :sm="6" :md="6" :lg="6">
          <div class="stat-card">
            <div class="stat-icon members">
              <el-icon><User /></el-icon>
            </div>
            <div class="stat-content">
              <div class="stat-value">{{ statsData.memberCount }}</div>
              <div class="stat-label">项目成员</div>
            </div>
          </div>
        </el-col>
        
        <el-col :xs="12" :sm="6" :md="6" :lg="6">
          <div class="stat-card">
            <div class="stat-icon success">
              <el-icon><SuccessFilled /></el-icon>
            </div>
            <div class="stat-content">
              <div class="stat-value">{{ statsData.successRate }}%</div>
              <div class="stat-label">成功率</div>
            </div>
          </div>
        </el-col>
      </el-row>
    </div>


  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue'
import { useRoute } from 'vue-router'
import {
  Connection,
  List,
  User,
  SuccessFilled
} from '@element-plus/icons-vue'
import { useProjectStore } from '@/stores/project'
import { formatDateTime } from '@/utils/date'
import type { Project } from '@/types'

// 响应式数据
const route = useRoute()
const projectStore = useProjectStore()
const loading = ref(false)

// 响应式数据
const project = ref<Project | null>(null)

// 计算属性
const projectId = computed(() => {
  const id = route.params.projectId
  return typeof id === 'string' ? parseInt(id) : null
})

// 模拟统计数据
const statsData = ref({
  workflowCount: 12,
  taskCount: 45,
  memberCount: 8,
  successRate: 92.5
})



// 方法
const loadProjectData = async () => {
  if (!projectId.value) return

  try {
    loading.value = true
    // 直接从后端加载项目数据
    const projectData = await projectStore.loadProject(projectId.value)
    if (projectData) {
      project.value = projectData
    }
  } catch (error) {
    console.error('加载项目数据失败:', error)
  } finally {
    loading.value = false
  }
}

// 辅助方法
const getProjectTypeName = (type?: string) => {
  return type === 'PUBLIC' ? '公共项目' : '私有项目'
}

// 监听路由参数变化
watch(
  () => route.params.projectId,
  () => {
    loadProjectData()
  },
  { immediate: true }
)

// 生命周期
onMounted(() => {
  loadProjectData()
})
</script>

<style scoped>
.project-overview {
  padding: 24px;
  min-height: 100%;
  background: #f5f7fa;
}

.page-header {
  margin-bottom: 24px;
}

.page-header h2 {
  margin: 0 0 8px 0;
  color: #303133;
  font-size: 20px;
  font-weight: 600;
}

.page-header p {
  margin: 0;
  color: #606266;
  font-size: 14px;
}

.info-section {
  margin-bottom: 24px;
}

.info-card {
  border-radius: 8px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-weight: 600;
}

.info-content {
  min-height: 200px;
}

.no-data {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
}

.stats-section {
  margin-bottom: 24px;
}

.stat-card {
  background: #fff;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  gap: 16px;
  height: 100px;
}

.stat-icon {
  width: 48px;
  height: 48px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  color: #fff;
}

.stat-icon.workflows {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.stat-icon.tasks {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.stat-icon.members {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.stat-icon.success {
  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
}

.stat-content {
  flex: 1;
}

.stat-value {
  font-size: 28px;
  font-weight: 600;
  color: #303133;
  line-height: 1;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 14px;
  color: #606266;
}



/* 响应式设计 */
@media (max-width: 768px) {
  .project-overview {
    padding: 16px;
  }
  
  .stat-card {
    padding: 16px;
    height: 80px;
  }
  
  .stat-icon {
    width: 40px;
    height: 40px;
    font-size: 20px;
  }
  
  .stat-value {
    font-size: 24px;
  }
}
</style>
