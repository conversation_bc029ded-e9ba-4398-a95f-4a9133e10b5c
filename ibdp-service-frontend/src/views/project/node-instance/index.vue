<template>
  <div class="node-instance-container">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-left">
        <h2>节点实例</h2>
        <p class="page-description">查看和管理工作流节点的执行实例</p>
      </div>
      <div class="header-right">
        <el-button @click="handleRefresh" :loading="loading">
          <el-icon><Refresh /></el-icon>
          刷新
        </el-button>
      </div>
    </div>

    <!-- 搜索条件 -->
    <div class="search-container">
      <el-form :model="searchForm" inline>
        <el-form-item label="节点名称">
          <el-input
            v-model="searchForm.nodeName"
            placeholder="请输入节点名称"
            clearable
            style="width: 200px"
          />
        </el-form-item>
        <el-form-item label="节点类型">
          <el-select
            v-model="searchForm.nodeType"
            placeholder="请选择节点类型"
            clearable
            style="width: 150px"
          >
            <el-option label="SQL" value="SQL" />
            <el-option label="DataX" value="DATAX" />
            <el-option label="Shell" value="SHELL" />
            <el-option label="Python" value="PYTHON" />
          </el-select>
        </el-form-item>
        <el-form-item label="实例状态">
          <el-select
            v-model="searchForm.status"
            placeholder="请选择状态"
            clearable
            style="width: 150px"
          >
            <el-option label="等待中" :value="0" />
            <el-option label="运行中" :value="1" />
            <el-option label="成功" :value="2" />
            <el-option label="失败" :value="3" />
            <el-option label="跳过" :value="4" />
            <el-option label="超时" :value="5" />
          </el-select>
        </el-form-item>
        <el-form-item label="执行时间">
          <el-date-picker
            v-model="searchForm.dateRange"
            type="datetimerange"
            range-separator="至"
            start-placeholder="开始时间"
            end-placeholder="结束时间"
            format="YYYY-MM-DD HH:mm:ss"
            value-format="YYYY-MM-DD HH:mm:ss"
            style="width: 350px"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">
            <el-icon><Search /></el-icon>
            搜索
          </el-button>
          <el-button @click="handleReset">
            <el-icon><RefreshLeft /></el-icon>
            重置
          </el-button>
        </el-form-item>
      </el-form>
    </div>

    <!-- 数据表格 -->
    <div class="table-container">
      <el-table
        :data="tableData"
        v-loading="loading"
        stripe
        border
        style="width: 100%"
      >
        <el-table-column prop="id" label="节点实例ID" width="120" />
        <el-table-column prop="executionId" label="执行实例ID" width="280" show-overflow-tooltip>
          <template #default="{ row }">
            <span class="execution-id">{{ row.executionId }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="nodeName" label="节点名称" width="200" show-overflow-tooltip />
        <el-table-column prop="nodeType" label="节点类型" width="100">
          <template #default="{ row }">
            <el-tag :type="getNodeTypeTagType(row.nodeType)" size="small">
              {{ row.nodeType }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="status" label="状态" width="100">
          <template #default="{ row }">
            <el-tag :type="getStatusTagType(row.status)">
              {{ getStatusName(row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="startTime" label="开始时间" width="180">
          <template #default="{ row }">
            <span>{{ formatDateTime(row.startTime) }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="endTime" label="结束时间" width="180">
          <template #default="{ row }">
            <span>{{ formatDateTime(row.endTime) }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="duration" label="执行耗时" width="120">
          <template #default="{ row }">
            <span v-if="row.duration">{{ formatDuration(row.duration) }}</span>
            <span v-else>-</span>
          </template>
        </el-table-column>
        <el-table-column prop="retryCount" label="重试次数" width="100" />
        <el-table-column prop="executeOrder" label="执行顺序" width="100" />
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="{ row }">
            <div class="action-buttons">
              <el-button link type="primary" @click="handleView(row)">
                查看
              </el-button>
              <el-button link type="info" @click="handleViewLog(row)">
                日志
              </el-button>
              <el-button 
                v-if="row.status === 1"
                link 
                type="warning" 
                @click="handleStop(row)"
              >
                停止
              </el-button>
              <el-button 
                v-if="row.status === 3 || row.status === 5"
                link 
                type="success" 
                @click="handleRetry(row)"
              >
                重试
              </el-button>
            </div>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.size"
          :total="total"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>

    <!-- 日志查看对话框 -->
    <el-dialog
      v-model="logDialog.visible"
      :title="`节点实例日志 - ${logDialog.nodeName}`"
      width="80%"
      top="5vh"
    >
      <div class="log-container">
        <el-input
          v-model="logDialog.content"
          type="textarea"
          :rows="20"
          readonly
          placeholder="暂无日志内容"
        />
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="logDialog.visible = false">关闭</el-button>
          <el-button type="primary" @click="handleRefreshLog">刷新日志</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { useRoute } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Refresh, Search, RefreshLeft } from '@element-plus/icons-vue'
import nodeInstanceApi from '@/api/node-instance'
import type { NodeInstance, NodeInstanceQuery } from '@/types/node-instance'

// 路由信息
const route = useRoute()
const projectId = ref(route.params.projectId as string)

// 响应式数据
const loading = ref(false)
const tableData = ref<NodeInstance[]>([])
const total = ref(0)

// 搜索表单
const searchForm = reactive<Omit<NodeInstanceQuery, 'page' | 'size'> & { dateRange: string[] | null }>({
  nodeName: '',
  nodeType: '',
  status: undefined,
  dateRange: null
})

// 分页信息
const pagination = reactive({
  page: 1,
  size: 10
})

// 日志对话框
const logDialog = reactive({
  visible: false,
  nodeInstanceId: null as number | null,
  nodeName: '',
  content: ''
})

// 页面初始化
onMounted(() => {
  loadTableData()
})

// 加载表格数据
const loadTableData = async () => {
  loading.value = true
  try {
    const params: NodeInstanceQuery = {
      page: pagination.page,
      size: pagination.size,
      projectId: Number(projectId.value),
      nodeName: searchForm.nodeName || undefined,
      nodeType: searchForm.nodeType || undefined,
      status: searchForm.status,
      startTime: searchForm.dateRange?.[0],
      endTime: searchForm.dateRange?.[1]
    }

    const response = await nodeInstanceApi.pageList(params)

    if (response.code === 200) {
      const pageData = response.content
      tableData.value = pageData.data || []
      total.value = pageData.recordsTotal || 0
    } else {
      ElMessage.error(response.msg || '加载数据失败')
      tableData.value = []
      total.value = 0
    }
  } catch (error) {
    console.error('加载数据失败:', error)
    ElMessage.error('加载数据失败')
    tableData.value = []
    total.value = 0
  } finally {
    loading.value = false
  }
}

// 搜索
const handleSearch = () => {
  pagination.page = 1
  loadTableData()
}

// 重置
const handleReset = () => {
  Object.assign(searchForm, {
    nodeName: '',
    nodeType: '',
    status: null,
    dateRange: null
  })
  pagination.page = 1
  loadTableData()
}

// 刷新
const handleRefresh = () => {
  loadTableData()
}

// 分页处理
const handleSizeChange = (size: number) => {
  pagination.size = size
  pagination.page = 1
  loadTableData()
}

const handleCurrentChange = (page: number) => {
  pagination.page = page
  loadTableData()
}

// 操作处理
const handleView = (row: any) => {
  ElMessage.info('查看节点实例详情功能开发中...')
}

const handleViewLog = async (row: any) => {
  logDialog.nodeInstanceId = row.id
  logDialog.nodeName = row.nodeName
  logDialog.visible = true
  
  try {
    // TODO: 调用API获取日志
    // const response = await nodeInstanceApi.getLog(row.id)
    // logDialog.content = response.content || '暂无日志内容'
    
    logDialog.content = '节点实例日志功能开发中...'
  } catch (error) {
    console.error('获取日志失败:', error)
    logDialog.content = '获取日志失败'
  }
}

const handleRefreshLog = async () => {
  if (logDialog.nodeInstanceId) {
    try {
      // TODO: 刷新日志
      ElMessage.success('日志已刷新')
    } catch (error) {
      ElMessage.error('刷新日志失败')
    }
  }
}

const handleStop = async (row: any) => {
  try {
    await ElMessageBox.confirm('确定要停止此节点实例吗？', '确认停止', {
      type: 'warning'
    })
    ElMessage.info('停止节点实例功能开发中...')
  } catch (error) {
    // 用户取消
  }
}

const handleRetry = async (row: any) => {
  try {
    await ElMessageBox.confirm('确定要重试此节点实例吗？', '确认重试', {
      type: 'warning'
    })
    ElMessage.info('重试节点实例功能开发中...')
  } catch (error) {
    // 用户取消
  }
}

// 工具方法
const getStatusName = (status: number) => {
  const statusMap: Record<number, string> = {
    0: '等待中',
    1: '运行中',
    2: '成功',
    3: '失败',
    4: '跳过',
    5: '超时'
  }
  return statusMap[status] || '未知'
}

const getStatusTagType = (status: number) => {
  const typeMap: Record<number, string> = {
    0: 'info',
    1: 'warning',
    2: 'success',
    3: 'danger',
    4: 'info',
    5: 'danger'
  }
  return typeMap[status] || ''
}

const getNodeTypeTagType = (nodeType: string) => {
  const typeMap: Record<string, string> = {
    'SQL': 'primary',
    'DATAX': 'success',
    'SHELL': 'warning',
    'PYTHON': 'info'
  }
  return typeMap[nodeType] || ''
}

const formatDuration = (duration: number) => {
  if (duration < 1000) {
    return `${duration}ms`
  } else if (duration < 60000) {
    return `${(duration / 1000).toFixed(1)}s`
  } else {
    const minutes = Math.floor(duration / 60000)
    const seconds = Math.floor((duration % 60000) / 1000)
    return `${minutes}m${seconds}s`
  }
}

// 格式化日期时间
const formatDateTime = (dateTime: string | null | undefined) => {
  if (!dateTime) return '-'

  try {
    const date = new Date(dateTime)
    if (isNaN(date.getTime())) return '-'

    const year = date.getFullYear()
    const month = String(date.getMonth() + 1).padStart(2, '0')
    const day = String(date.getDate()).padStart(2, '0')
    const hours = String(date.getHours()).padStart(2, '0')
    const minutes = String(date.getMinutes()).padStart(2, '0')
    const seconds = String(date.getSeconds()).padStart(2, '0')

    return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`
  } catch (error) {
    return '-'
  }
}
</script>

<style scoped>
.node-instance-container {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.header-left h2 {
  margin: 0 0 5px 0;
  color: #303133;
  font-size: 20px;
}

.page-description {
  margin: 0;
  color: #909399;
  font-size: 14px;
}

.search-container {
  background: #f8f9fa;
  padding: 20px;
  border-radius: 8px;
  margin-bottom: 20px;
}

.table-container {
  background: white;
  border-radius: 8px;
  overflow: hidden;
}

.pagination-container {
  padding: 20px;
  text-align: right;
  border-top: 1px solid #ebeef5;
}

.action-buttons {
  display: flex;
  gap: 8px;
}

.log-container {
  max-height: 500px;
  overflow-y: auto;
}

.dialog-footer {
  text-align: right;
}

.execution-id {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  background: #f5f7fa;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  color: #606266;
  border: 1px solid #e4e7ed;
}
</style>
