<template>
  <el-dialog
    v-model="dialogVisible"
    :title="isEdit ? '编辑工作流' : '新建工作流'"
    width="800px"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <el-form
      ref="formRef"
      :model="form"
      :rules="rules"
      label-width="120px"
      label-position="right"
    >
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="工作流名称" prop="name">
            <el-input
              v-model="form.name"
              placeholder="请输入工作流名称"
              maxlength="100"
              show-word-limit
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="工作流编码" prop="code">
            <el-input
              v-model="form.code"
              placeholder="请输入工作流编码"
              maxlength="50"
              show-word-limit
              :disabled="isEdit"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="分类" prop="category">
            <el-select
              v-model="form.category"
              placeholder="请选择分类"
              style="width: 100%"
            >
              <el-option
                v-for="item in categoryOptions"
                :key="item.code"
                :label="item.name"
                :value="item.code"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="告警类型" prop="warningType">
            <el-select
              v-model="form.warningType"
              placeholder="请选择告警类型"
              style="width: 100%"
            >
              <el-option label="不告警" value="NONE" />
              <el-option label="成功告警" value="SUCCESS" />
              <el-option label="失败告警" value="FAILURE" />
              <el-option label="全部告警" value="ALL" />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-form-item label="标签">
        <el-input
          v-model="form.tags"
          placeholder="请输入标签，多个标签用逗号分隔"
          maxlength="500"
          show-word-limit
        />
        <div class="form-tip">多个标签用英文逗号分隔，如：数据同步,ETL,用户数据</div>
      </el-form-item>

      <el-form-item label="描述">
        <el-input
          v-model="form.description"
          type="textarea"
          :rows="3"
          placeholder="请输入工作流描述"
          maxlength="1000"
          show-word-limit
        />
      </el-form-item>

      <el-form-item label="全局参数">
        <el-input
          v-model="form.globalParams"
          type="textarea"
          :rows="4"
          placeholder="请输入全局参数JSON，如：{&quot;batchDate&quot;:&quot;${yyyyMMdd}&quot;}"
        />
        <div class="form-tip">JSON格式的全局参数，支持变量替换</div>
      </el-form-item>

      <!-- 新增：Cron表达式配置 -->
      <el-form-item label="定时调度" prop="cronExpression">
        <el-input
          v-model="form.cronExpression"
          placeholder="请输入Cron表达式，如：0 0 2 * * ?（每天凌晨2点执行）"
          maxlength="128"
        >
          <template #append>
            <el-button @click="showCronHelper">
              <el-icon><QuestionFilled /></el-icon>
              帮助
            </el-button>
          </template>
        </el-input>
        <div class="form-tip">
          <div>留空表示手动执行，填写Cron表达式可实现定时调度</div>
          <div class="cron-examples">
            <span class="example-item">每天2点: 0 0 2 * * ?</span>
            <span class="example-item">每小时: 0 0 * * * ?</span>
            <span class="example-item">每5分钟: 0 */5 * * * ?</span>
          </div>
        </div>
      </el-form-item>

    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" :loading="submitLoading" @click="handleSubmit">
          {{ isEdit ? '更新' : '创建' }}
        </el-button>
      </div>
    </template>
  </el-dialog>

  <!-- Cron表达式帮助对话框 -->
  <el-dialog
    v-model="cronHelpVisible"
    title="Cron表达式帮助"
    width="600px">

    <div class="cron-help">
      <div class="help-section">
        <h4>Cron表达式格式</h4>
        <div class="cron-format">
          <span class="format-item">秒</span>
          <span class="format-item">分</span>
          <span class="format-item">时</span>
          <span class="format-item">日</span>
          <span class="format-item">月</span>
          <span class="format-item">周</span>
        </div>
        <p class="format-desc">格式：秒 分 时 日 月 周，例如：0 0 2 * * ? 表示每天凌晨2点执行</p>
      </div>

      <div class="help-section">
        <h4>常用示例</h4>
        <div class="examples-grid">
          <div class="example-card" v-for="example in cronExamples" :key="example.expression">
            <div class="example-expression">{{ example.expression }}</div>
            <div class="example-desc">{{ example.description }}</div>
            <el-button
              size="small"
              type="primary"
              link
              @click="useCronExample(example.expression)">
              使用此表达式
            </el-button>
          </div>
        </div>
      </div>

      <div class="help-section">
        <h4>特殊字符说明</h4>
        <div class="special-chars">
          <div class="char-item">
            <span class="char">*</span>
            <span class="char-desc">匹配任意值</span>
          </div>
          <div class="char-item">
            <span class="char">?</span>
            <span class="char-desc">不指定值（用于日和周）</span>
          </div>
          <div class="char-item">
            <span class="char">-</span>
            <span class="char-desc">范围，如1-5表示1到5</span>
          </div>
          <div class="char-item">
            <span class="char">,</span>
            <span class="char-desc">列举，如1,3,5表示1、3、5</span>
          </div>
          <div class="char-item">
            <span class="char">/</span>
            <span class="char-desc">步长，如*/5表示每5个单位</span>
          </div>
        </div>
      </div>
    </div>

    <template #footer>
      <el-button @click="cronHelpVisible = false">关闭</el-button>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch, nextTick } from 'vue'
import { ElMessage, type FormInstance, type FormRules } from 'element-plus'
import { workflowApi } from '@/api/workflow'
import type { Workflow, WorkflowForm, WorkflowCategoryOption } from '@/types/workflow'

// Props
interface Props {
  visible: boolean
  formData: Workflow | null
  projectId: number
}

const props = withDefaults(defineProps<Props>(), {
  visible: false,
  formData: null,
  projectId: 0
})

// Emits
const emit = defineEmits<{
  'update:visible': [value: boolean]
  success: []
}>()

// 响应式数据
const formRef = ref<FormInstance>()
const submitLoading = ref(false)
const categoryOptions = ref<WorkflowCategoryOption[]>([])
const cronHelpVisible = ref(false)

// Cron表达式示例
const cronExamples = [
  { expression: '0 0 2 * * ?', description: '每天凌晨2点执行' },
  { expression: '0 0 */2 * * ?', description: '每2小时执行一次' },
  { expression: '0 */30 * * * ?', description: '每30分钟执行一次' },
  { expression: '0 */5 * * * ?', description: '每5分钟执行一次' },
  { expression: '0 0 9 * * MON-FRI', description: '工作日上午9点执行' },
  { expression: '0 0 0 1 * ?', description: '每月1号凌晨执行' },
  { expression: '0 0 0 ? * SUN', description: '每周日凌晨执行' },
  { expression: '0 0 6,18 * * ?', description: '每天早上6点和晚上6点执行' }
]

// 计算属性
const dialogVisible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
})

const isEdit = computed(() => !!props.formData?.id)

// 表单数据
const form = reactive<WorkflowForm>({
  projectId: 0, // 初始值，会在watch中更新
  name: '',
  code: '',
  description: '',
  tags: '',
  category: '',
  globalParams: '',
  warningType: 'NONE'
})

// 重置表单函数
const resetForm = () => {
  Object.assign(form, {
    id: undefined,
    projectId: props.projectId,
    name: '',
    code: '',
    description: '',
    tags: '',
    category: '',
    globalParams: '',
    warningType: 'NONE'
  })
}

// 表单验证规则
const rules: FormRules = {
  name: [
    { required: true, message: '请输入工作流名称', trigger: 'blur' },
    { min: 1, max: 100, message: '长度在 1 到 100 个字符', trigger: 'blur' }
  ],
  code: [
    { required: true, message: '请输入工作流编码', trigger: 'blur' },
    { min: 1, max: 50, message: '长度在 1 到 50 个字符', trigger: 'blur' },
    { pattern: /^[A-Z][A-Z0-9_]*$/, message: '编码必须以大写字母开头，只能包含大写字母、数字和下划线', trigger: 'blur' }
  ],
  category: [
    { required: true, message: '请选择分类', trigger: 'change' }
  ]
}

// 监听表单数据变化
watch(() => props.formData, (newData) => {
  if (newData) {
    Object.assign(form, {
      id: newData.id,
      projectId: newData.projectId,
      name: newData.name,
      code: newData.code,
      description: newData.description || '',
      tags: newData.tags || '',
      category: newData.category || '',
      globalParams: newData.globalParams || '',
      warningType: newData.warningType || 'NONE'
    })
  } else {
    resetForm()
  }
}, { immediate: true })

// 监听弹窗显示状态
watch(() => props.visible, (visible) => {
  if (visible) {
    // 更新表单中的项目ID
    form.projectId = props.projectId
    loadCategoryOptions()
    nextTick(() => {
      formRef.value?.clearValidate()
    })
  }
})

// 监听项目ID变化
watch(() => props.projectId, (newProjectId) => {
  form.projectId = newProjectId
}, { immediate: true })

// 加载分类选项
const loadCategoryOptions = async () => {
  try {
    const response = await workflowApi.getCategoryList()
    if (response.code === 200) {
      categoryOptions.value = response.content
    }
  } catch (error) {
    console.error('加载分类选项失败:', error)
  }
}





// 提交表单
const handleSubmit = async () => {
  if (!formRef.value) return

  try {
    const valid = await formRef.value.validate()
    if (!valid) return

    submitLoading.value = true

    const api = isEdit.value ? workflowApi.update : workflowApi.add
    const response = await api(form)

    if (response.code === 200) {
      ElMessage.success(isEdit.value ? '更新成功' : '创建成功')
      emit('success')
    } else {
      ElMessage.error(response.msg || (isEdit.value ? '更新失败' : '创建失败'))
    }
  } catch (error) {
    console.error('提交失败:', error)
    ElMessage.error('提交失败')
  } finally {
    submitLoading.value = false
  }
}

// 显示Cron帮助
const showCronHelper = () => {
  cronHelpVisible.value = true
}

// 使用Cron示例
const useCronExample = (expression: string) => {
  form.cronExpression = expression
  cronHelpVisible.value = false
  ElMessage.success('已应用Cron表达式')
}

// 关闭弹窗
const handleClose = () => {
  emit('update:visible', false)
}
</script>

<style scoped>
.form-tip {
  font-size: 12px;
  color: #909399;
  margin-top: 4px;
  line-height: 1.4;
}

.cron-examples {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
  margin-top: 4px;
}

.example-item {
  background: #f5f7fa;
  padding: 2px 6px;
  border-radius: 4px;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 11px;
  color: #606266;
}

/* Cron帮助对话框样式 */
.cron-help {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.help-section h4 {
  margin: 0 0 12px 0;
  color: #303133;
  font-size: 14px;
  font-weight: 600;
  border-bottom: 1px solid #ebeef5;
  padding-bottom: 8px;
}

.cron-format {
  display: flex;
  gap: 8px;
  margin-bottom: 8px;
}

.format-item {
  background: #409eff;
  color: white;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
}

.format-desc {
  margin: 0;
  font-size: 13px;
  color: #606266;
}

.examples-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 12px;
}

.example-card {
  border: 1px solid #ebeef5;
  border-radius: 6px;
  padding: 12px;
  background: #fafafa;
}

.example-expression {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 12px;
  color: #409eff;
  font-weight: 500;
  margin-bottom: 4px;
}

.example-desc {
  font-size: 12px;
  color: #606266;
  margin-bottom: 8px;
}

.special-chars {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 8px;
}

.char-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.char {
  background: #f56c6c;
  color: white;
  padding: 2px 6px;
  border-radius: 4px;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 12px;
  font-weight: bold;
  min-width: 20px;
  text-align: center;
}

.char-desc {
  font-size: 12px;
  color: #606266;
}

.dialog-footer {
  text-align: right;
}

.workflow-form :deep(.el-textarea__inner) {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
}
</style>
