<template>
  <div class="workflow-designer">
    <!-- 顶部工具栏 -->
    <div class="toolbar">
      <div class="toolbar-left">
        <el-button @click="handleBack">
          <el-icon><ArrowLeft /></el-icon>
          返回
        </el-button>
        <el-divider direction="vertical" />
        <span class="workflow-title">
          {{ workflowInfo?.name || '工作流设计器' }}
          <span v-if="isReadonly" class="readonly-badge">只读</span>
        </span>
      </div>
      <div class="toolbar-right">
        <span class="workflow-stats">
          节点: {{ workflowNodes.length }} | 连线: {{ workflowRelations.length }}
          <span v-if="hasUnsavedChanges" class="unsaved-indicator">*</span>
        </span>
        <el-button @click="handlePreview">
          <el-icon><View /></el-icon>
          预览
        </el-button>
        <el-button
          v-if="!isReadonly"
          type="primary"
          @click="handleSave"
          :loading="saving"
          :disabled="!hasUnsavedChanges"
        >
          <el-icon><Document /></el-icon>
          {{ hasUnsavedChanges ? '保存' : '已保存' }}
        </el-button>
      </div>
    </div>

    <!-- 主体内容 -->
    <div class="designer-content">
      <!-- 左侧节点面板 -->
      <div v-if="!isReadonly" class="node-panel">
        <div class="panel-header">
          <h3>节点库</h3>
        </div>
        <div class="panel-content">
          <NodeLibrary
            :node-types="nodeTypes"
            @drag-start="handleNodeDragStart"
          />
        </div>
      </div>

      <!-- 右侧画布区域 -->
      <div class="canvas-container" :class="{ 'full-width': isReadonly }">
        <WorkflowCanvas
          ref="canvasRef"
          :workflow-id="workflowId"
          :nodes="workflowNodes"
          :relations="workflowRelations"
          @node-add="handleNodeAdd"
          @node-update="handleNodeUpdate"
          @node-edit="handleNodeEdit"
          @node-delete="handleNodeDelete"
          @relation-add="handleRelationAdd"
          @relation-delete="handleRelationDelete"
        />
      </div>
    </div>

    <!-- 节点配置弹窗 -->
    <NodeConfigDialog
      v-model:visible="showConfigDialog"
      :node="selectedNode"
      :node-types="nodeTypes"
      :readonly="isReadonly"
      @save="handleNodeConfigSave"
    />


  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed, onBeforeUnmount, nextTick } from 'vue'
import { useRoute, useRouter, onBeforeRouteLeave } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { ArrowLeft, View, Document, Edit, CopyDocument, Delete } from '@element-plus/icons-vue'
import { workflowApi } from '@/api/workflow'
import { nodeTypeApi } from '@/api/nodeType'
import { workflowDesignerApi } from '@/api/workflowDesigner'
import { WorkflowChangeTracker } from '@/utils/workflowChangeTracker'
import { ChangeType } from '@/types/workflowNode'
import NodeLibrary from './components/NodeLibrary.vue'
import WorkflowCanvas from './components/WorkflowCanvas.vue'
import NodeConfigDialog from './components/NodeConfigDialog.vue'
import type { Workflow } from '@/types/workflow'
import type { NodeType } from '@/types/nodeType'
import type { WorkflowNode, WorkflowRelation } from '@/types/workflowNode'

const route = useRoute()
const router = useRouter()

// 检查是否为只读模式
const isReadonly = computed(() => route.query.mode === 'view')

// 响应式数据
const saving = ref(false)
const loading = ref(false)
const workflowInfo = ref<Workflow | null>(null)
const nodeTypes = ref<NodeType[]>([])
const workflowNodes = ref<WorkflowNode[]>([])
const workflowRelations = ref<WorkflowRelation[]>([])
const canvasRef = ref()

// 变更跟踪器
const changeTracker = new WorkflowChangeTracker()

// 响应式的变更状态
const hasChangesFlag = ref(false)

// 节点配置弹窗
const showConfigDialog = ref(false)
const selectedNode = ref<WorkflowNode | null>(null)



// 是否有未保存的变更
const hasUnsavedChanges = computed(() => {
  return hasChangesFlag.value
})

// 更新变更状态
const updateChangesFlag = () => {
  hasChangesFlag.value = changeTracker.hasChanges()
}

// 自动保存定时器
let autoSaveTimer: NodeJS.Timeout | null = null

// 启用自动保存
const enableAutoSave = (intervalMs: number = 30000) => {
  if (autoSaveTimer) {
    clearInterval(autoSaveTimer)
  }

  autoSaveTimer = setInterval(() => {
    if (hasUnsavedChanges.value && !saving.value) {
      console.log('自动保存触发...')
      handleAutoSave()
    }
  }, intervalMs)
}

// 禁用自动保存
const disableAutoSave = () => {
  if (autoSaveTimer) {
    clearInterval(autoSaveTimer)
    autoSaveTimer = null
  }
}

// 自动保存处理
const handleAutoSave = async () => {
  if (!workflowId.value || !hasUnsavedChanges.value || saving.value) {
    return
  }

  try {
    console.log('执行自动保存...')
    const changes = changeTracker.getChanges(workflowId.value)
    const response = await workflowDesignerApi.saveWorkflowDesignIncremental(changes)

    if (response.code === 200) {
      changeTracker.commitChanges(workflowNodes.value, workflowRelations.value)
      updateChangesFlag()
      console.log('自动保存成功')
    } else {
      console.error('自动保存失败:', response.msg)
    }
  } catch (error) {
    console.error('自动保存异常:', error)
  }
}

// 计算属性
const projectId = computed(() => Number(route.params.projectId))
const workflowId = computed(() => Number(route.params.workflowId))

// 页面离开确认
const handleBeforeUnload = (event: BeforeUnloadEvent) => {
  // 只读模式下不需要保存确认
  if (isReadonly.value) return

  if (hasUnsavedChanges.value) {
    event.preventDefault()
    event.returnValue = '您有未保存的更改，确定要离开吗？'
    return '您有未保存的更改，确定要离开吗？'
  }
}

// 路由离开确认
onBeforeRouteLeave(async (to, from, next) => {
  // 只读模式下直接允许离开
  if (isReadonly.value) {
    next()
    return
  }

  if (hasUnsavedChanges.value) {
    try {
      await ElMessageBox.confirm(
        '您有未保存的更改，是否要保存后离开？',
        '确认离开',
        {
          type: 'warning',
          confirmButtonText: '保存并离开',
          cancelButtonText: '直接离开',
          distinguishCancelAndClose: true,
          showClose: false
        }
      )
      // 用户选择保存并离开
      try {
        await handleSave()
        ElMessage.success('保存成功，正在跳转...')
        next()
      } catch (error) {
        ElMessage.error('保存失败，请重试')
        next(false)
      }
    } catch (action) {
      if (action === 'cancel') {
        // 用户选择直接离开
        next()
      } else {
        // 用户选择取消离开
        next(false)
      }
    }
  } else {
    // 没有未保存的更改，直接离开
    next()
  }
})

// 生命周期
onMounted(() => {
  loadWorkflowInfo()
  loadNodeTypes()
  loadWorkflowDesignData()

  // 添加页面离开监听
  window.addEventListener('beforeunload', handleBeforeUnload)
})

// 页面卸载前检查未保存的变更
onBeforeUnmount(() => {
  // 禁用自动保存
  disableAutoSave()

  // 移除页面离开监听
  window.removeEventListener('beforeunload', handleBeforeUnload)

  // 只读模式下不需要检查未保存变更
  if (!isReadonly.value && hasUnsavedChanges.value) {
    console.warn('有未保存的变更')
  }
})

// 加载工作流信息
const loadWorkflowInfo = async () => {
  try {
    const response = await workflowApi.getById(workflowId.value)
    if (response.code === 200) {
      workflowInfo.value = response.content
    } else {
      ElMessage.error('加载工作流信息失败')
    }
  } catch (error) {
    console.error('加载工作流信息失败:', error)
    ElMessage.error('加载工作流信息失败')
  }
}

// 加载节点类型
const loadNodeTypes = async () => {
  try {
    const response = await nodeTypeApi.list()
    if (response.code === 200) {
      nodeTypes.value = response.content
    } else {
      ElMessage.error('加载节点类型失败')
    }
  } catch (error) {
    console.error('加载节点类型失败:', error)
    ElMessage.error('加载节点类型失败')
  }
}

// 加载工作流设计数据（节点和关系）
const loadWorkflowDesignData = async () => {
  if (!workflowId.value) {
    console.warn('工作流ID为空，跳过加载设计数据')
    return
  }

  loading.value = true
  try {
    console.log('开始加载工作流设计数据:', workflowId.value)
    const response = await workflowDesignerApi.loadWorkflowDesign(workflowId.value)

    if (response.code === 200) {
      const designData = response.content
      workflowNodes.value = designData.nodes || []
      workflowRelations.value = designData.relations || []

      // 初始化变更跟踪器
      changeTracker.initializeData(workflowNodes.value, workflowRelations.value)
      updateChangesFlag()

      console.log('工作流设计数据加载成功:', {
        nodes: workflowNodes.value.length,
        relations: workflowRelations.value.length
      })
    } else {
      console.error('加载工作流设计数据失败:', response.msg)
      ElMessage.error(response.msg || '加载工作流设计数据失败')
    }
  } catch (error) {
    console.error('加载工作流设计数据异常:', error)
    ElMessage.error('加载工作流设计数据失败')
  } finally {
    loading.value = false
  }
}

// 节点拖拽开始
const handleNodeDragStart = (nodeType: NodeType) => {
  console.log('开始拖拽节点:', nodeType)
}

// 添加节点
const handleNodeAdd = (node: WorkflowNode) => {
  workflowNodes.value.push(node)
  // 跟踪节点变更
  changeTracker.trackNodeChange(node, ChangeType.ADDED)
  updateChangesFlag()
}

// 更新节点
const handleNodeUpdate = (node: WorkflowNode) => {
  const index = workflowNodes.value.findIndex(n => n.id === node.id)
  if (index !== -1) {
    workflowNodes.value[index] = node

    // 跟踪节点变更
    changeTracker.trackNodeChange(node)
    updateChangesFlag()
  }
}

// 编辑节点
const handleNodeEdit = (node: WorkflowNode) => {
  selectedNode.value = node
  showConfigDialog.value = true
}

// 保存节点配置
const handleNodeConfigSave = (updatedNode: WorkflowNode) => {
  const index = workflowNodes.value.findIndex(n => n.id === updatedNode.id)
  if (index !== -1) {
    workflowNodes.value[index] = updatedNode
    // 跟踪节点变更
    changeTracker.trackNodeChange(updatedNode)
    updateChangesFlag()
    showConfigDialog.value = false
    selectedNode.value = null
  }
}

// 删除节点
const handleNodeDelete = (nodeId: number) => {
  console.log('删除节点:', nodeId)

  // 找到要删除的节点
  const nodeToDelete = workflowNodes.value.find(n => n.id === nodeId)
  if (!nodeToDelete) {
    console.error('未找到要删除的节点:', nodeId)
    return
  }

  console.log('删除节点详情:', nodeToDelete)

  // 跟踪节点删除
  changeTracker.trackNodeDeletion(nodeId)
  updateChangesFlag()

  // 同时删除相关的连线 - 使用nodeCode进行匹配
  const relatedRelations = workflowRelations.value.filter(
    r => r.preNodeCode === nodeToDelete.nodeCode || r.postNodeCode === nodeToDelete.nodeCode
  )

  console.log('找到相关连线:', relatedRelations)

  // 跟踪连线删除
  relatedRelations.forEach(relation => {
    if (relation.id) {
      changeTracker.trackRelationDeletion(relation.id)
    }
  })

  // 删除相关连线
  workflowRelations.value = workflowRelations.value.filter(
    r => r.preNodeCode !== nodeToDelete.nodeCode && r.postNodeCode !== nodeToDelete.nodeCode
  )

  // 删除节点
  workflowNodes.value = workflowNodes.value.filter(n => n.id !== nodeId)

  console.log('节点删除完成，剩余节点数量:', workflowNodes.value.length)
  console.log('剩余连线数量:', workflowRelations.value.length)
}

// 添加连线
const handleRelationAdd = (relation: WorkflowRelation) => {
  console.log('添加连线:', relation)
  workflowRelations.value.push(relation)

  // 跟踪连线变更
  changeTracker.trackRelationChange(relation, ChangeType.ADDED)
  updateChangesFlag()
}

// 删除连线
const handleRelationDelete = (relationId: number) => {
  console.log('删除连线:', relationId)

  // 跟踪连线删除
  changeTracker.trackRelationDeletion(relationId)
  updateChangesFlag()

  workflowRelations.value = workflowRelations.value.filter(r => r.id !== relationId)
  console.log('连线删除完成，剩余连线数量:', workflowRelations.value.length)
}

// 返回
const handleBack = async () => {
  // 只读模式下直接返回，不需要保存校验
  if (isReadonly.value) {
    router.push(`/project/${projectId.value}/workflow`)
    return
  }

  if (hasUnsavedChanges.value) {
    try {
      await ElMessageBox.confirm(
        '您有未保存的更改，是否要保存后返回？',
        '确认返回',
        {
          type: 'warning',
          confirmButtonText: '保存并返回',
          cancelButtonText: '直接返回',
          distinguishCancelAndClose: true,
          showClose: false
        }
      )
      // 用户选择保存并返回
      try {
        await handleSave()
        ElMessage.success('保存成功，正在返回...')
        router.push(`/project/${projectId.value}/workflow`)
      } catch (error) {
        ElMessage.error('保存失败，请重试')
      }
    } catch (action) {
      if (action === 'cancel') {
        // 用户选择直接返回
        router.push(`/project/${projectId.value}/workflow`)
      }
      // 用户选择取消返回，不做任何操作
    }
  } else {
    // 没有未保存的更改，直接返回
    router.push(`/project/${projectId.value}/workflow`)
  }
}

// 预览
const handlePreview = () => {
  // TODO: 实现预览功能
  ElMessage.info('预览功能开发中...')
}


// 保存工作流设计
const handleSave = async () => {
  if (!workflowId.value) {
    ElMessage.error('工作流ID不能为空')
    return
  }

  try {
    // 检查是否有变更
    if (!changeTracker.hasChanges()) {
      ElMessage.info('没有需要保存的变更')
      return
    }

    await ElMessageBox.confirm('确认保存当前工作流设计吗？', '提示', {
      type: 'warning'
    })

    saving.value = true
    console.log('开始保存工作流设计...')

    // 使用增量保存
    const changes = changeTracker.getChanges(workflowId.value)
    console.log('保存变更数据:', changes)

    const response = await workflowDesignerApi.saveWorkflowDesignIncremental(changes)

    if (response.code === 200) {
      // 保存成功，提交变更
      changeTracker.commitChanges(workflowNodes.value, workflowRelations.value)
      updateChangesFlag()
      ElMessage.success('保存成功')
      console.log('工作流设计保存成功')
    } else {
      console.error('保存失败:', response.msg)
      ElMessage.error(response.msg || '保存失败')
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('保存异常:', error)
      ElMessage.error('保存失败')
    }
  } finally {
    saving.value = false
  }
}


</script>

<style scoped>
.workflow-designer {
  min-height: 40vh;
  max-height: 100vh;
  height: 100%;
  display: flex;
  flex-direction: column;
  background: #f5f5f5;
  overflow: hidden;
  position: relative;
}

/* 支持容器内嵌套时的响应式 */
.workflow-designer.container-mode {
  min-height: 500px;
  max-height: none;
  height: auto;
}

/* 响应式设计 - 适配不同屏幕尺寸 */
@media (max-height: 600px) {
  .workflow-designer {
    min-height: 600px;
    max-height: none;
  }
}

@media (min-height: 1200px) {
  .workflow-designer {
    min-height: 100vh;
    max-height: none;
  }
}

/* 移动端适配 */
@media (max-width: 768px) {
  .workflow-designer {
    min-height: calc(100vh - 60px); /* 减去可能的导航栏高度 */
  }

  .node-panel {
    width: 200px; /* 在小屏幕上缩小侧边栏 */
  }
}

/* 平板适配 */
@media (min-width: 769px) and (max-width: 1024px) {
  .workflow-designer {
    min-height: 100vh;
  }

  .node-panel {
    width: 220px;
  }
}

/* 大屏幕优化 */
@media (min-width: 1920px) {
  .workflow-designer {
    min-height: 100vh;
    max-height: none;
  }

  .node-panel {
    width: 280px; /* 在大屏幕上增加侧边栏宽度 */
  }
}

.toolbar {
  height: 60px;
  background: white;
  border-bottom: 1px solid #e4e7ed;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  z-index: 100;
}

.toolbar-left {
  display: flex;
  align-items: center;
  gap: 12px;
}

.workflow-title {
  font-size: 16px;
  font-weight: 500;
  color: #303133;
  display: flex;
  align-items: center;
  gap: 8px;
}

.readonly-badge {
  background: #f0f2f5;
  color: #606266;
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: normal;
}

.toolbar-right {
  display: flex;
  align-items: center;
  gap: 12px;
}

.workflow-stats {
  font-size: 14px;
  color: #606266;
  padding: 0 12px;
  border-right: 1px solid #e4e7ed;
}

.unsaved-indicator {
  color: #f56c6c;
  font-weight: bold;
  margin-left: 4px;
}

.designer-content {
  flex: 1;
  display: flex;
  overflow: hidden;
}

.node-panel {
  width: 250px;
  background: white;
  border-right: 1px solid #e4e7ed;
  display: flex;
  flex-direction: column;
}

.panel-header {
  height: 50px;
  padding: 0 16px;
  border-bottom: 1px solid #e4e7ed;
  display: flex;
  align-items: center;
}

.panel-header h3 {
  margin: 0;
  font-size: 14px;
  font-weight: 500;
  color: #303133;
}

.panel-content {
  flex: 1;
  overflow-y: auto;
}

.canvas-container {
  flex: 1;
  position: relative;
  overflow: hidden;
}

/* 只读模式下画布占满全宽 */
.canvas-container.full-width {
  width: 100%;
  margin-left: 0;
}


</style>
