<template>
  <div
    class="workflow-node"
    :class="{ selected, dragging }"
    :style="nodeStyle"
    @mousedown="handleMouseDown"
    @click="handleClick"
    @dblclick="handleDoubleClick"
    @contextmenu="handleContextMenu"
  >
    <!-- 节点主体 -->
    <div class="node-body" :style="{ backgroundColor: nodeColor }">
      <div class="node-icon">
        <el-icon>
          <component :is="nodeIcon" />
        </el-icon>
      </div>
      <div class="node-content">
        <div class="node-name">{{ node.nodeName }}</div>
        <div class="node-type">{{ node.nodeType }}</div>
      </div>
      
      <!-- 状态指示器 -->
      <div class="node-status" :class="statusClass">
        <el-icon v-if="node.status">
          <component :is="statusIcon" />
        </el-icon>
      </div>
    </div>
    
    <!-- 连接点 -->
    <div class="connection-points">
      <!-- 上边连接点 -->
      <div
        class="connection-point top"
        @mousedown.stop="(e) => handleConnectionStart('top', e)"
        @mouseup.stop="(e) => handleConnectionEnd('top', e)"
      >
        <div class="point-dot"></div>
      </div>

      <!-- 右边连接点 -->
      <div
        class="connection-point right"
        @mousedown.stop="(e) => handleConnectionStart('right', e)"
        @mouseup.stop="(e) => handleConnectionEnd('right', e)"
      >
        <div class="point-dot"></div>
      </div>

      <!-- 下边连接点 -->
      <div
        class="connection-point bottom"
        @mousedown.stop="(e) => handleConnectionStart('bottom', e)"
        @mouseup.stop="(e) => handleConnectionEnd('bottom', e)"
      >
        <div class="point-dot"></div>
      </div>

      <!-- 左边连接点 -->
      <div
        class="connection-point left"
        @mousedown.stop="(e) => handleConnectionStart('left', e)"
        @mouseup.stop="(e) => handleConnectionEnd('left', e)"
      >
        <div class="point-dot"></div>
      </div>
    </div>
    
    <!-- 选中时的操作按钮 -->
    <div v-if="selected" class="node-actions">
      <el-button size="small" circle @click.stop="handleEdit">
        <el-icon><Edit /></el-icon>
      </el-button>
      <!-- 删除按钮已移除，只能通过右键菜单删除 -->
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'
import {
  Edit,
  // Delete 图标已移除，删除功能只能通过右键菜单触发
  Folder,
  Document,
  Monitor,
  Link,
  Setting,
  Share,
  CircleCheck,
  CircleClose,
  Loading
} from '@element-plus/icons-vue'
import type { WorkflowNode } from '@/types/workflowNode'

// Props
interface Props {
  node: WorkflowNode
  selected: boolean
  scale: number
}

const props = defineProps<Props>()

// Emits
const emit = defineEmits<{
  select: [nodeId: number]
  update: [node: WorkflowNode]
  edit: [node: WorkflowNode]
  // delete emit 已移除，删除功能只能通过右键菜单触发
  'connect-start': [nodeId: number, point: { x: number, y: number, type: 'top' | 'right' | 'bottom' | 'left' }]
  'connect-end': [nodeId: number, point: { x: number, y: number, type: 'top' | 'right' | 'bottom' | 'left' }]
}>()

// 响应式数据
const dragging = ref(false)
const dragStart = ref({ x: 0, y: 0 })
const nodeStart = ref({ x: 0, y: 0 })

// 计算属性
const nodeStyle = computed(() => ({
  left: `${props.node.positionX}px`,
  top: `${props.node.positionY}px`,
  transform: `scale(${props.scale})`,
  transformOrigin: '0 0'
}))

const nodeColor = computed(() => {
  // 优先使用节点数据中的颜色
  if (props.node.color) {
    return props.node.color
  }

  // 如果没有颜色信息，使用默认的颜色映射
  const colorMap: Record<string, string> = {
    'DATAX': '#52c41a',
    'SQL': '#1890ff',
    'SHELL': '#722ed1',
    'PYTHON': '#fa8c16',
    'SPARK': '#eb2f96',
    'HTTP': '#13c2c2',
    'CONDITION': '#faad14',
    'SUB_WORKFLOW': '#2f54eb'
  }
  return colorMap[props.node.nodeType] || '#1890ff'
})

const nodeIcon = computed(() => {
  const iconMap: Record<string, any> = {
    'DATAX': Link,
    'SQL': Folder,
    'SHELL': Document,
    'PYTHON': Edit,
    'SPARK': Monitor,
    'HTTP': Link,
    'CONDITION': Setting,
    'SUB_WORKFLOW': Share
  }
  return iconMap[props.node.nodeType] || Document
})

const statusClass = computed(() => {
  // TODO: 根据节点执行状态返回对应的样式类
  return ''
})

const statusIcon = computed(() => {
  // TODO: 根据节点执行状态返回对应的图标
  return CircleCheck
})

// 生命周期
onMounted(() => {
  document.addEventListener('mousemove', handleMouseMove)
  document.addEventListener('mouseup', handleMouseUp)
})

onUnmounted(() => {
  document.removeEventListener('mousemove', handleMouseMove)
  document.removeEventListener('mouseup', handleMouseUp)
})

// 鼠标事件处理
const handleMouseDown = (event: MouseEvent) => {
  event.preventDefault()
  dragging.value = true
  dragStart.value = { x: event.clientX, y: event.clientY }
  nodeStart.value = { x: props.node.positionX, y: props.node.positionY }
}

const handleMouseMove = (event: MouseEvent) => {
  if (!dragging.value) return
  
  const dx = (event.clientX - dragStart.value.x) / props.scale
  const dy = (event.clientY - dragStart.value.y) / props.scale
  
  const newX = Math.max(0, nodeStart.value.x + dx)
  const newY = Math.max(0, nodeStart.value.y + dy)
  
  const updatedNode = {
    ...props.node,
    positionX: Math.round(newX),
    positionY: Math.round(newY)
  }
  
  emit('update', updatedNode)
}

const handleMouseUp = () => {
  dragging.value = false
}

// 点击事件
const handleClick = (event: MouseEvent) => {
  event.stopPropagation()
  emit('select', props.node.id!)
}

// 双击编辑
const handleDoubleClick = (event: MouseEvent) => {
  console.log('WorkflowNode - 双击事件触发', props.node)
  console.log('双击事件对象:', event)
  event.stopPropagation()
  event.preventDefault()
  handleEdit()
}

// 右键菜单
const handleContextMenu = (event: MouseEvent) => {
  event.preventDefault()
  event.stopPropagation()

  // 右键时自动选中节点并触发右键菜单
  emit('select', props.node.id!)
  emit('context-menu', {
    event,
    nodeId: props.node.id!
  })
}

// 编辑节点
const handleEdit = () => {
  emit('edit', props.node)
}

// 删除节点方法已移除，删除功能只能通过右键菜单触发

// 连接点事件
const handleConnectionStart = (type: 'top' | 'right' | 'bottom' | 'left', event: MouseEvent) => {
  event.stopPropagation()
  const point = getConnectionPoint(type)
  emit('connect-start', props.node.id!, { ...point, type })
}

const handleConnectionEnd = (type: 'top' | 'right' | 'bottom' | 'left', event: MouseEvent) => {
  event.stopPropagation()
  const point = getConnectionPoint(type)
  emit('connect-end', props.node.id!, { ...point, type })
}

// 获取连接点坐标
const getConnectionPoint = (type: 'top' | 'right' | 'bottom' | 'left') => {
  const nodeWidth = 120
  const nodeHeight = 50
  const centerX = props.node.positionX + nodeWidth / 2
  const centerY = props.node.positionY + nodeHeight / 2

  switch (type) {
    case 'top':
      return { x: centerX, y: props.node.positionY }
    case 'right':
      return { x: props.node.positionX + nodeWidth, y: centerY }
    case 'bottom':
      return { x: centerX, y: props.node.positionY + nodeHeight }
    case 'left':
      return { x: props.node.positionX, y: centerY }
    default:
      return { x: centerX, y: centerY }
  }
}
</script>

<style scoped>
.workflow-node {
  position: absolute;
  width: 120px;
  height: 50px;
  cursor: move;
  pointer-events: auto;
  user-select: none;
  z-index: 10;
}

.workflow-node.selected {
  z-index: 20;
}

.workflow-node.dragging {
  z-index: 30;
}

.node-body {
  width: 100%;
  height: 100%;
  border-radius: 6px;
  display: flex;
  align-items: center;
  padding: 0 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.2s;
  position: relative;
  overflow: hidden;
}

.workflow-node.selected .node-body {
  box-shadow: 0 0 0 2px #409eff;
}

.workflow-node:hover .node-body {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  transform: translateY(-1px);
}

.node-icon {
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 14px;
  margin-right: 6px;
}

.node-content {
  flex: 1;
  min-width: 0;
}

.node-name {
  font-size: 11px;
  font-weight: 500;
  color: white;
  line-height: 1.2;
  margin-bottom: 1px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.node-type {
  font-size: 9px;
  color: rgba(255, 255, 255, 0.8);
  line-height: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.node-status {
  width: 16px;
  height: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  color: white;
}

.connection-points {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
}

.connection-point {
  position: absolute;
  width: 12px;
  height: 12px;
  pointer-events: auto;
  cursor: crosshair;
  display: flex;
  align-items: center;
  justify-content: center;
}

.connection-point.top {
  top: -6px;
  left: 50%;
  transform: translateX(-50%);
}

.connection-point.right {
  right: -6px;
  top: 50%;
  transform: translateY(-50%);
}

.connection-point.bottom {
  bottom: -6px;
  left: 50%;
  transform: translateX(-50%);
}

.connection-point.left {
  left: -6px;
  top: 50%;
  transform: translateY(-50%);
}

.point-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: #409eff;
  border: 2px solid white;
  opacity: 0.3; /* 改为半透明，让用户能看到连接点 */
  transition: all 0.2s;
}

.workflow-node:hover .point-dot,
.workflow-node.selected .point-dot {
  opacity: 1;
}

.connection-point:hover .point-dot {
  opacity: 1;
  transform: scale(1.2);
  background: #67c23a;
}

.node-actions {
  position: absolute;
  top: -35px;
  right: 0;
  display: flex;
  gap: 4px;
  background: white;
  padding: 4px;
  border-radius: 4px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.node-actions .el-button {
  width: 24px;
  height: 24px;
  padding: 0;
}
</style>
