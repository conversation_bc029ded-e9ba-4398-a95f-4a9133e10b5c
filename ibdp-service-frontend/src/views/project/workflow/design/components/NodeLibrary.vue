<template>
  <div class="node-library">
    <!-- 节点分类 -->
    <div v-for="category in categories" :key="category.code" class="category-section">
      <div class="category-header">
        <el-icon class="category-icon">
          <component :is="getCategoryIcon(category.code)" />
        </el-icon>
        <span class="category-name">{{ category.name }}</span>
        <el-icon 
          class="expand-icon" 
          :class="{ expanded: expandedCategories.includes(category.code) }"
          @click="toggleCategory(category.code)"
        >
          <ArrowRight />
        </el-icon>
      </div>
      
      <!-- 节点列表 -->
      <div 
        v-show="expandedCategories.includes(category.code)" 
        class="node-list"
      >
        <div
          v-for="nodeType in getNodesByCategory(category.code)"
          :key="nodeType.id"
          class="node-item"
          :draggable="true"
          @dragstart="handleDragStart($event, nodeType)"
          @dragend="handleDragEnd"
        >
          <div class="node-icon" :style="{ backgroundColor: nodeType.color }">
            <el-icon>
              <component :is="getNodeIcon(nodeType.icon)" />
            </el-icon>
          </div>
          <div class="node-info">
            <div class="node-name">{{ nodeType.typeName }}</div>
            <div class="node-desc">{{ nodeType.description }}</div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import {
  ArrowRight,
  Link,
  Monitor,
  Setting,
  Folder,
  Document,
  Share,
  Edit
} from '@element-plus/icons-vue'
import type { NodeType } from '@/types/nodeType'

// Props
interface Props {
  nodeTypes: NodeType[]
}

const props = defineProps<Props>()

// Emits
const emit = defineEmits<{
  'drag-start': [nodeType: NodeType]
}>()

// 响应式数据
const expandedCategories = ref<string[]>(['DATA_SYNC', 'COMPUTE', 'CONTROL'])

// 节点分类定义
const categories = [
  { code: 'DATA_SYNC', name: '数据同步' },
  { code: 'COMPUTE', name: '计算处理' },
  { code: 'CONTROL', name: '流程控制' }
]

// 计算属性
const getNodesByCategory = (category: string) => {
  return props.nodeTypes.filter(node => node.category === category)
}

// 获取分类图标
const getCategoryIcon = (category: string) => {
  const iconMap: Record<string, any> = {
    'DATA_SYNC': Link,
    'COMPUTE': Monitor,
    'CONTROL': Setting
  }
  return iconMap[category] || Document
}

// 获取节点图标
const getNodeIcon = (icon?: string) => {
  const iconMap: Record<string, any> = {
    'sync': Link,
    'database': Folder,
    'code': Document,
    'python': Edit,
    'spark': Monitor,
    'api': Link,
    'condition': Setting,
    'workflow': Share
  }
  return iconMap[icon || 'document'] || Document
}

// 切换分类展开状态
const toggleCategory = (category: string) => {
  const index = expandedCategories.value.indexOf(category)
  if (index > -1) {
    expandedCategories.value.splice(index, 1)
  } else {
    expandedCategories.value.push(category)
  }
}

// 拖拽开始
const handleDragStart = (event: DragEvent, nodeType: NodeType) => {
  if (event.dataTransfer) {
    // 设置拖拽数据
    event.dataTransfer.setData('application/json', JSON.stringify(nodeType))
    event.dataTransfer.effectAllowed = 'copy'
    
    // 创建拖拽时的视觉效果
    const dragImage = createDragImage(nodeType)
    event.dataTransfer.setDragImage(dragImage, 50, 25)
  }
  
  emit('drag-start', nodeType)
}

// 拖拽结束
const handleDragEnd = (event: DragEvent) => {
  // 清理拖拽状态
  console.log('拖拽结束')
}

// 创建拖拽时的视觉效果
const createDragImage = (nodeType: NodeType) => {
  const dragElement = document.createElement('div')
  dragElement.style.cssText = `
    position: absolute;
    top: -1000px;
    left: -1000px;
    width: 100px;
    height: 50px;
    background: ${nodeType.color || '#1890ff'};
    color: white;
    border-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
    z-index: 9999;
  `
  dragElement.textContent = nodeType.typeName
  document.body.appendChild(dragElement)
  
  // 延迟删除元素
  setTimeout(() => {
    document.body.removeChild(dragElement)
  }, 0)
  
  return dragElement
}
</script>

<style scoped>
.node-library {
  padding: 8px 0;
}

.category-section {
  margin-bottom: 8px;
}

.category-header {
  height: 36px;
  padding: 0 12px;
  display: flex;
  align-items: center;
  cursor: pointer;
  border-radius: 4px;
  transition: background-color 0.2s;
}

.category-header:hover {
  background-color: #f5f7fa;
}

.category-icon {
  font-size: 16px;
  color: #606266;
  margin-right: 8px;
}

.category-name {
  flex: 1;
  font-size: 13px;
  font-weight: 500;
  color: #303133;
}

.expand-icon {
  font-size: 12px;
  color: #909399;
  transition: transform 0.2s;
}

.expand-icon.expanded {
  transform: rotate(90deg);
}

.node-list {
  padding-left: 8px;
}

.node-item {
  display: flex;
  align-items: center;
  padding: 8px 12px;
  margin: 2px 0;
  border-radius: 4px;
  cursor: grab;
  transition: all 0.2s;
  user-select: none;
}

.node-item:hover {
  background-color: #f0f9ff;
  transform: translateX(2px);
}

.node-item:active {
  cursor: grabbing;
}

.node-icon {
  width: 24px;
  height: 24px;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 8px;
  color: white;
  font-size: 12px;
}

.node-info {
  flex: 1;
  min-width: 0;
}

.node-name {
  font-size: 12px;
  font-weight: 500;
  color: #303133;
  line-height: 1.2;
  margin-bottom: 2px;
}

.node-desc {
  font-size: 11px;
  color: #909399;
  line-height: 1.2;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* 拖拽时的样式 */
.node-item[draggable="true"]:hover {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}
</style>
