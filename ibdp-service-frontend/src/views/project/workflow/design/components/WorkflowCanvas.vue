<template>
  <div class="workflow-canvas" ref="canvasContainer">
    <!-- 画布背景 -->
    <div class="canvas-background" :style="backgroundStyle"></div>
    
    <!-- SVG画布 -->
    <svg
      ref="svgCanvas"
      class="canvas-svg"
      :width="canvasSize.width"
      :height="canvasSize.height"
      @drop="handleDrop"
      @dragover="handleDragOver"
      @click="handleCanvasClick"
      @contextmenu="handleCanvasContextMenu"
    >
      <!-- 网格背景 -->
      <defs>
        <pattern id="grid" width="20" height="20" patternUnits="userSpaceOnUse">
          <path d="M 20 0 L 0 0 0 20" fill="none" stroke="#e4e7ed" stroke-width="0.5"/>
        </pattern>
      </defs>
      <rect width="100%" height="100%" fill="url(#grid)" />
      
      <!-- 连线 -->
      <g class="connections">
        <!-- 连线（包含不可见的宽点击区域） -->
        <g v-for="connection in connections" :key="connection?.id" class="connection-group">
          <!-- 不可见的宽点击区域 -->
          <path
            :d="connection?.path"
            class="connection-click-area"
            @click="selectConnection(connection?.id || 0)"
            @contextmenu="handleConnectionContextMenu($event, connection?.id || 0)"
            @mouseenter="hoverConnection(connection?.id || 0)"
            @mouseleave="unhoverConnection()"
          />
          <!-- 可见的连线 -->
          <path
            :d="connection?.path"
            class="connection-path"
            :class="{
              selected: selectedConnection === connection?.id,
              hovered: hoveredConnection === connection?.id
            }"
          />
        </g>
      </g>
      
      <!-- 临时连线（拖拽时） -->
      <path
        v-if="tempConnection"
        :d="tempConnection.path"
        class="temp-connection"
      />
    </svg>
    
    <!-- 节点容器 -->
    <div class="nodes-container">
      <!-- @delete 事件监听已移除，删除功能只能通过右键菜单触发 -->
      <WorkflowNode
        v-for="node in nodes"
        :key="node.id"
        :node="node"
        :selected="selectedNode === node.id"
        :scale="scale"
        @select="selectNode"
        @update="updateNode"
        @edit="editNode"
        @connect-start="startConnection"
        @connect-end="endConnection"
        @context-menu="handleNodeContextMenu"
      />
    </div>
    
    <!-- 右键菜单 -->
    <div
      v-if="contextMenu.visible"
      class="context-menu"
      :style="{ left: contextMenu.x + 'px', top: contextMenu.y + 'px' }"
    >
      <!-- 节点右键菜单 -->
      <template v-if="selectedNode">
        <div class="menu-item" @click="editSelectedNode">
          <el-icon><Edit /></el-icon>
          编辑节点
        </div>
        <div class="menu-item" @click="deleteSelectedItem">
          <el-icon><Delete /></el-icon>
          删除节点
        </div>
      </template>

      <!-- 连线右键菜单 -->
      <template v-if="selectedConnection">
        <div class="menu-item" @click="deleteSelectedItem">
          <el-icon><Delete /></el-icon>
          删除连线
        </div>
      </template>
    </div>
    
    <!-- 缩放控制 -->
    <div class="zoom-controls">
      <el-button-group>
        <el-button size="small" @click="zoomIn">
          <el-icon><Plus /></el-icon>
        </el-button>
        <el-button size="small" @click="resetZoom">
          {{ Math.round(scale * 100) }}%
        </el-button>
        <el-button size="small" @click="zoomOut">
          <el-icon><Minus /></el-icon>
        </el-button>
      </el-button-group>
    </div>

    <!-- 连线状态提示 -->
    <div v-if="connecting" class="connection-hint">
      正在连线中...请点击目标节点的任意连接点完成连线
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted, onUnmounted, nextTick } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Delete, Plus, Minus, Edit } from '@element-plus/icons-vue'
import WorkflowNode from './WorkflowNode.vue'
import type { WorkflowNode as WorkflowNodeType, WorkflowRelation } from '@/types/workflowNode'

// Props
interface Props {
  workflowId: number
  nodes: WorkflowNodeType[]
  relations: WorkflowRelation[]
}

const props = defineProps<Props>()

// Emits
const emit = defineEmits<{
  'node-add': [node: WorkflowNodeType]
  'node-update': [node: WorkflowNodeType]
  'node-edit': [node: WorkflowNodeType]
  'node-delete': [nodeId: number]
  'relation-add': [relation: WorkflowRelation]
  'relation-delete': [relationId: number]
}>()

// 响应式数据
const canvasContainer = ref<HTMLElement>()
const svgCanvas = ref<SVGElement>()
const scale = ref(1)
const selectedNode = ref<number | null>(null)
const selectedConnection = ref<number | null>(null)
const hoveredConnection = ref<number | null>(null)
const draggedNodeType = ref<any>(null)
const connecting = ref(false)
const tempConnection = ref<any>(null)
const connectionStart = ref<any>(null)

// 画布尺寸
const canvasSize = reactive({
  width: 2000,
  height: 1000
})

// 自适应画布尺寸
const updateCanvasSize = () => {
  if (canvasContainer.value) {
    const rect = canvasContainer.value.getBoundingClientRect()
    // 确保画布尺寸不小于容器尺寸，但提供足够的工作空间
    canvasSize.width = Math.max(rect.width, 2000)
    canvasSize.height = Math.max(rect.height, 1000)
  }
}

// 右键菜单
const contextMenu = reactive({
  visible: false,
  x: 0,
  y: 0
})

// 计算属性
const backgroundStyle = computed(() => ({
  transform: `scale(${scale.value})`,
  transformOrigin: '0 0'
}))

const connections = computed(() => {
  console.log('计算连线:', props.relations, props.nodes)
  const result = props.relations.map(relation => {
    // 使用nodeCode来查找节点，因为关系表中存储的是nodeCode
    const sourceNode = props.nodes.find(n => n.nodeCode === relation.preNodeCode)
    const targetNode = props.nodes.find(n => n.nodeCode === relation.postNodeCode)

    if (!sourceNode || !targetNode) {
      console.warn('找不到连线对应的节点:', relation, '可用节点:', props.nodes.map(n => ({ id: n.id, nodeCode: n.nodeCode })))
      return null
    }

    // 智能选择最佳连接点
    const { startPoint, endPoint } = getBestConnectionPoints(sourceNode, targetNode)
    const path = createConnectionPath(startPoint, endPoint)

    console.log('创建连线路径:', path)

    return {
      id: relation.id || `${relation.preNodeCode}-${relation.postNodeCode}`,
      path,
      relation
    }
  }).filter(connection => connection !== null)

  console.log('最终连线结果:', result)
  return result
})

// 生命周期
onMounted(() => {
  document.addEventListener('click', handleDocumentClick)
  window.addEventListener('resize', updateCanvasSize)
  updateCanvasSize()
})

onUnmounted(() => {
  document.removeEventListener('click', handleDocumentClick)
  window.removeEventListener('resize', updateCanvasSize)
  document.removeEventListener('mousemove', handleConnectionMove)
  document.removeEventListener('mouseup', handleConnectionCancel)
})

// 处理拖放
const handleDragOver = (event: DragEvent) => {
  event.preventDefault()
  event.dataTransfer!.dropEffect = 'copy'
}

const handleDrop = (event: DragEvent) => {
  event.preventDefault()
  
  try {
    const nodeTypeData = event.dataTransfer!.getData('application/json')
    const nodeType = JSON.parse(nodeTypeData)
    
    const rect = svgCanvas.value!.getBoundingClientRect()
    const x = (event.clientX - rect.left) / scale.value
    const y = (event.clientY - rect.top) / scale.value
    
    createNode(nodeType, x, y)
  } catch (error) {
    console.error('处理拖放失败:', error)
  }
}

// 创建节点
const createNode = (nodeType: any, x: number, y: number) => {
  const nodeId = Date.now() // 使用数字ID

  // 根据节点类型设置默认配置
  let defaultConfigParams = '{}'

  const newNode: WorkflowNodeType = {
    id: nodeId,
    workflowId: props.workflowId,
    nodeCode: `node_${nodeId}`,
    nodeName: nodeType.typeName,
    nodeType: nodeType.typeCode,
    description: nodeType.description,
    configParams: defaultConfigParams,
    positionX: Math.round(x - 60), // 居中
    positionY: Math.round(y - 25),
    timeout: 0,
    retryTimes: 0,
    retryInterval: 1,
    maxRetryTimes: 3,
    failureStrategy: 'FAIL',
    priority: 2,
    workerGroup: 'default',
    createUser: 'admin',
    updateUser: 'admin',
    // 添加颜色信息
    color: nodeType.color
  }
  
  emit('node-add', newNode)
}

// 画布点击
const handleCanvasClick = (event: MouseEvent) => {
  // 点击画布空白区域时取消所有选中状态
  const target = event.target as Element
  if (target === svgCanvas.value || target.tagName === 'rect' || target.tagName === 'svg') {
    selectedNode.value = null
    selectedConnection.value = null
  }
}

// 选择节点
const selectNode = (nodeId: number) => {
  selectedNode.value = nodeId
  selectedConnection.value = null
}

// 选择连线
const selectConnection = (connectionId: number) => {
  selectedConnection.value = connectionId
  selectedNode.value = null
}

// 更新节点
const updateNode = (node: WorkflowNodeType) => {
  emit('node-update', node)
}

// 编辑节点
const editNode = (node: WorkflowNodeType) => {
  emit('node-edit', node)
}

// 删除节点
const deleteNode = (nodeId: number) => {
  emit('node-delete', nodeId)
}

// 开始连接
const startConnection = (nodeId: number, point: { x: number, y: number, type: string }) => {
  console.log('开始连接:', { nodeId, point })

  // 现在所有四个方向的连接点都可以作为起始点
  connecting.value = true
  connectionStart.value = { nodeId, point }
  console.log('连接状态已设置:', { connecting: connecting.value, connectionStart: connectionStart.value })

  // 添加鼠标移动监听，显示临时连线
  document.addEventListener('mousemove', handleConnectionMove)
  document.addEventListener('mouseup', handleConnectionCancel)
}

// 结束连接
const endConnection = (nodeId: number, point: { x: number, y: number, type: string }) => {
  console.log('结束连接:', { nodeId, point, connecting: connecting.value, connectionStart: connectionStart.value })

  if (connecting.value && connectionStart.value && nodeId !== connectionStart.value.nodeId) {
    // 查找对应的节点以获取nodeCode
    const sourceNode = props.nodes.find(n => n.id === connectionStart.value!.nodeId)
    const targetNode = props.nodes.find(n => n.id === nodeId)

    if (!sourceNode || !targetNode) {
      console.error('无法找到连接的节点:', { sourceId: connectionStart.value.nodeId, targetId: nodeId })
      return
    }

    // 创建连线 - 使用nodeCode而不是id
    const newRelation: WorkflowRelation = {
      id: Date.now(), // 使用数字ID
      workflowId: props.workflowId,
      preNodeCode: sourceNode.nodeCode,
      postNodeCode: targetNode.nodeCode,
      conditionType: 'SUCCESS',
      createUser: 'admin',
      updateUser: 'admin'
    }

    emit('relation-add', newRelation)
  } else {
    console.log('连线创建条件不满足:', {
      connecting: connecting.value,
      hasConnectionStart: !!connectionStart.value,
      sameNode: nodeId === connectionStart.value?.nodeId
    })
  }

  // 清理连接状态
  connecting.value = false
  connectionStart.value = null
  tempConnection.value = null
  document.removeEventListener('mousemove', handleConnectionMove)
  document.removeEventListener('mouseup', handleConnectionCancel)
}

// 连接过程中的鼠标移动
const handleConnectionMove = (event: MouseEvent) => {
  if (connecting.value && connectionStart.value && svgCanvas.value) {
    const rect = svgCanvas.value.getBoundingClientRect()
    const endX = (event.clientX - rect.left) / scale.value
    const endY = (event.clientY - rect.top) / scale.value

    const path = createConnectionPath(
      connectionStart.value.point,
      { x: endX, y: endY }
    )

    tempConnection.value = { path }
  }
}

// 取消连接
const handleConnectionCancel = () => {
  connecting.value = false
  connectionStart.value = null
  tempConnection.value = null
  document.removeEventListener('mousemove', handleConnectionMove)
  document.removeEventListener('mouseup', handleConnectionCancel)
}

// 获取最佳连接点
const getBestConnectionPoints = (sourceNode: any, targetNode: any) => {
  const nodeWidth = 120
  const nodeHeight = 50

  // 计算两个节点的中心点
  const sourceCenterX = sourceNode.positionX + nodeWidth / 2
  const sourceCenterY = sourceNode.positionY + nodeHeight / 2
  const targetCenterX = targetNode.positionX + nodeWidth / 2
  const targetCenterY = targetNode.positionY + nodeHeight / 2

  // 计算相对位置
  const dx = targetCenterX - sourceCenterX
  const dy = targetCenterY - sourceCenterY

  let startPoint, endPoint

  // 根据相对位置选择最佳连接点
  if (Math.abs(dx) > Math.abs(dy)) {
    // 水平方向距离更大
    if (dx > 0) {
      // 目标在右侧
      startPoint = { x: sourceNode.positionX + nodeWidth, y: sourceCenterY }
      endPoint = { x: targetNode.positionX, y: targetCenterY }
    } else {
      // 目标在左侧
      startPoint = { x: sourceNode.positionX, y: sourceCenterY }
      endPoint = { x: targetNode.positionX + nodeWidth, y: targetCenterY }
    }
  } else {
    // 垂直方向距离更大
    if (dy > 0) {
      // 目标在下方
      startPoint = { x: sourceCenterX, y: sourceNode.positionY + nodeHeight }
      endPoint = { x: targetCenterX, y: targetNode.positionY }
    } else {
      // 目标在上方
      startPoint = { x: sourceCenterX, y: sourceNode.positionY }
      endPoint = { x: targetCenterX, y: targetNode.positionY + nodeHeight }
    }
  }

  return { startPoint, endPoint }
}

// 创建连接路径
const createConnectionPath = (start: { x: number, y: number }, end: { x: number, y: number }) => {
  const dx = end.x - start.x
  const dy = end.y - start.y

  // 根据连接方向创建不同的路径
  if (Math.abs(dx) > Math.abs(dy)) {
    // 水平连接
    const cp1x = start.x + dx * 0.5
    const cp1y = start.y
    const cp2x = end.x - dx * 0.5
    const cp2y = end.y
    return `M ${start.x} ${start.y} C ${cp1x} ${cp1y}, ${cp2x} ${cp2y}, ${end.x} ${end.y}`
  } else {
    // 垂直连接
    const cp1x = start.x
    const cp1y = start.y + dy * 0.5
    const cp2x = end.x
    const cp2y = end.y - dy * 0.5
    return `M ${start.x} ${start.y} C ${cp1x} ${cp1y}, ${cp2x} ${cp2y}, ${end.x} ${end.y}`
  }
}

// 缩放控制
const zoomIn = () => {
  scale.value = Math.min(scale.value * 1.2, 3)
}

const zoomOut = () => {
  scale.value = Math.max(scale.value / 1.2, 0.2)
}

const resetZoom = () => {
  scale.value = 1
}

// 节点右键菜单
const handleNodeContextMenu = ({ event, nodeId }: { event: MouseEvent, nodeId: number }) => {
  event.preventDefault()

  // 确保节点被选中
  selectedNode.value = nodeId
  selectedConnection.value = null

  // 显示右键菜单
  contextMenu.visible = true
  contextMenu.x = event.clientX
  contextMenu.y = event.clientY
}

// 连线右键菜单
const handleConnectionContextMenu = (event: MouseEvent, connectionId: number) => {
  event.preventDefault()
  event.stopPropagation()

  // 确保连线被选中
  selectedConnection.value = connectionId
  selectedNode.value = null

  // 显示右键菜单
  contextMenu.visible = true
  contextMenu.x = event.clientX
  contextMenu.y = event.clientY
}

// 画布右键菜单（空白区域）
const handleCanvasContextMenu = (event: MouseEvent) => {
  event.preventDefault()
  // 点击空白区域时隐藏右键菜单，取消选中状态
  contextMenu.visible = false
  selectedNode.value = null
  selectedConnection.value = null
}

// 连线悬停处理
const hoverConnection = (connectionId: number) => {
  hoveredConnection.value = connectionId
}

const unhoverConnection = () => {
  hoveredConnection.value = null
}

const handleDocumentClick = () => {
  contextMenu.visible = false
}

// 编辑选中节点
const editSelectedNode = () => {
  if (selectedNode.value) {
    const node = props.nodes.find(n => n.id === selectedNode.value)
    if (node) {
      emit('node-edit', node)
    }
  }
  contextMenu.visible = false
}

// 删除选中项
const deleteSelectedItem = () => {
  if (selectedNode.value) {
    ElMessageBox.confirm(
      '确定要删除这个节点吗？删除后相关连线也会被删除。',
      '确认删除',
      {
        type: 'warning',
        confirmButtonText: '删除',
        cancelButtonText: '取消'
      }
    ).then(() => {
      deleteNode(selectedNode.value!)
      ElMessage.success('节点删除成功')
    }).catch(() => {
      // 用户取消删除
    })
  } else if (selectedConnection.value) {
    ElMessageBox.confirm(
      '确定要删除这条连线吗？',
      '确认删除',
      {
        type: 'warning',
        confirmButtonText: '删除',
        cancelButtonText: '取消'
      }
    ).then(() => {
      emit('relation-delete', selectedConnection.value!)
      ElMessage.success('连线删除成功')
    }).catch(() => {
      // 用户取消删除
    })
  }
  contextMenu.visible = false
}
</script>

<style scoped>
.workflow-canvas {
  width: 100%;
  height: 100%;
  overflow: auto;
  background: #fafafa;
}

.canvas-background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: white;
}

.canvas-svg {
  position: absolute;
  top: 0;
  left: 0;
  cursor: default;
  z-index: 1;
}

.nodes-container {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 2;
}

/* 连线点击区域（不可见但可点击） */
.connection-click-area {
  fill: none;
  stroke: transparent;
  stroke-width: 12;
  cursor: pointer;
  pointer-events: stroke;
}

.connection-path {
  fill: none;
  stroke: #409eff;
  stroke-width: 2;
  pointer-events: none;
  transition: all 0.2s;
  opacity: 0.8;
}

.connection-path.hovered {
  stroke: #67c23a;
  stroke-width: 3;
  opacity: 1;
}

.connection-path.selected {
  stroke: #f56c6c;
  stroke-width: 3;
  opacity: 1;
}

.temp-connection {
  fill: none;
  stroke: #409eff;
  stroke-width: 2;
  stroke-dasharray: 5,5;
}

.context-menu {
  position: fixed;
  background: white;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  z-index: 1000;
  min-width: 80px;
}

.menu-item {
  padding: 8px 12px;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 12px;
  color: #606266;
}

.menu-item:hover {
  background: #f5f7fa;
  color: #409eff;
}

.zoom-controls {
  position: absolute;
  bottom: 20px;
  right: 20px;
  z-index: 100;
}

.connection-hint {
  position: absolute;
  top: 20px;
  left: 50%;
  transform: translateX(-50%);
  background: #409eff;
  color: white;
  padding: 8px 16px;
  border-radius: 4px;
  font-size: 14px;
  z-index: 100;
  animation: pulse 1.5s infinite;
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.7; }
}
</style>
