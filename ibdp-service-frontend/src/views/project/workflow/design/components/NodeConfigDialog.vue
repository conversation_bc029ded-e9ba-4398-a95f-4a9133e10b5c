<template>
  <el-dialog
    v-model="dialogVisible"
    :title="`${props.readonly ? '查看' : '配置'}节点 - ${props.node?.nodeName || ''}`"
    :width="dialogWidth"
    :close-on-click-modal="false"
    @close="handleClose"
    destroy-on-close
    class="node-config-dialog"
    top="5vh"
  >
    <div class="dialog-content">


      <el-form
        ref="formRef"
        :model="form"
        :rules="rules"
        label-width="120px"
        label-position="right"
        size="default"
      >
      <el-row :gutter="20">
        <el-col :span="12" :xs="24" :sm="12">
          <el-form-item label="节点名称" prop="nodeName">
            <el-input
              v-model="form.nodeName"
              placeholder="请输入节点名称"
              maxlength="100"
              show-word-limit
              :disabled="props.readonly"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12" :xs="24" :sm="12">
          <el-form-item label="节点类型" prop="nodeType">
            <el-select
              v-model="form.nodeType"
              placeholder="请选择节点类型"
              style="width: 100%"
              disabled
            >
              <el-option
                v-for="type in nodeTypes"
                :key="type.typeCode"
                :label="type.typeName"
                :value="type.typeCode"
              />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12" :xs="24" :sm="12">
          <el-form-item label="超时时间(秒)" prop="timeout">
            <el-input-number
              v-model="form.timeout"
              :min="0"
              :max="86400"
              style="width: 100%"
              placeholder="0表示不限制"
              :disabled="props.readonly"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12" :xs="24" :sm="12">
          <el-form-item label="重试次数" prop="retryTimes">
            <el-input-number
              v-model="form.retryTimes"
              :min="0"
              :max="10"
              style="width: 100%"
              :disabled="props.readonly"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12" :xs="24" :sm="12">
          <el-form-item label="重试间隔(秒)" prop="retryInterval">
            <el-input-number
              v-model="form.retryInterval"
              :min="1"
              :max="3600"
              style="width: 100%"
              :disabled="props.readonly"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12" :xs="24" :sm="12">
          <el-form-item label="失败策略" prop="failureStrategy">
            <el-select
              v-model="form.failureStrategy"
              placeholder="请选择失败策略"
              style="width: 100%"
              :disabled="props.readonly"
            >
              <el-option label="失败" value="FAIL" />
              <el-option label="继续" value="CONTINUE" />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12" :xs="24" :sm="12">
          <el-form-item label="优先级" prop="priority">
            <el-select
              v-model="form.priority"
              placeholder="请选择优先级"
              style="width: 100%"
              :disabled="props.readonly"
            >
              <el-option label="最高" :value="1" />
              <el-option label="高" :value="2" />
              <el-option label="中" :value="3" />
              <el-option label="低" :value="4" />
              <el-option label="最低" :value="5" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12" :xs="24" :sm="12">
          <el-form-item label="工作组" prop="workerGroup">
            <el-select
              v-model="form.workerGroup"
              placeholder="请选择工作组"
              style="width: 100%"
              filterable
              allow-create
              :disabled="props.readonly"
            >
              <el-option label="默认" value="default" />
              <el-option label="数据处理" value="data-process" />
              <el-option label="计算任务" value="compute" />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-form-item label="节点描述" prop="description">
        <el-input
          v-model="form.description"
          type="textarea"
          :rows="3"
          placeholder="请输入节点描述"
          maxlength="500"
          show-word-limit
          :disabled="props.readonly"
        />
      </el-form-item>

      <!-- 节点特定配置 -->
      <el-form-item label="节点配置" v-if="configComponent">
        <div class="node-specific-config">
          <component
            :is="configComponent"
            v-model="nodeConfig"
            :node="props.node"
            :readonly="props.readonly"
            @validate="handleConfigValidate"
          />
        </div>
      </el-form-item>

      <!-- 原始JSON配置（作为后备） -->
      <el-form-item label="配置参数" prop="configParams" v-if="!configComponent">
        <el-input
          v-model="form.configParams"
          type="textarea"
          :rows="6"
          placeholder="请输入JSON格式的配置参数"
          class="config-textarea"
          :disabled="props.readonly"
        />
      </el-form-item>
      </el-form>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">
          {{ props.readonly ? '关闭' : '取消' }}
        </el-button>
        <el-button
          v-if="!props.readonly"
          type="primary"
          :loading="submitLoading"
          @click="handleSubmit"
        >
          保存
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch, nextTick, markRaw, defineAsyncComponent, onMounted, onUnmounted } from 'vue'
import { ElMessage, type FormInstance, type FormRules } from 'element-plus'
import type { WorkflowNode } from '@/types/workflowNode'

// 导入配置组件
import SqlNodeConfig from './config/SqlNodeConfig.vue'
import DataxNodeConfig from './config/DataxNodeConfig.vue'
import type { NodeType } from '@/types/nodeType'

// Props
interface Props {
  visible: boolean
  node: WorkflowNode | null
  nodeTypes: NodeType[]
  readonly?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  visible: false,
  node: null,
  nodeTypes: () => [],
  readonly: false
})

// Emits
const emit = defineEmits<{
  'update:visible': [value: boolean]
  'save': [node: WorkflowNode]
}>()

// 响应式数据
const formRef = ref<FormInstance>()
const submitLoading = ref(false)
const nodeConfig = ref<any>({})
const configValid = ref(true)
const windowWidth = ref(window.innerWidth)



// 计算属性
const dialogVisible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
})

// 响应式对话框宽度
const dialogWidth = computed(() => {
  if (typeof window === 'undefined') return '800px'

  const screenWidth = windowWidth.value

  if (screenWidth < 768) {
    // 移动端
    return '95%'
  } else if (screenWidth < 1200) {
    // 平板
    return '85%'
  } else if (screenWidth < 1600) {
    // 小屏桌面
    return '70%'
  } else {
    // 大屏桌面
    return '1200px'
  }
})

// 根据节点类型动态选择配置组件
const configComponent = computed(() => {
  if (!props.node?.nodeType) {
    return null
  }

  const componentMap: Record<string, any> = {
    'SQL': SqlNodeConfig,
    'DATAX': DataxNodeConfig,
    // 可以继续添加其他节点类型的配置组件
  }

  return componentMap[props.node.nodeType] || null
})

// 表单数据
const form = reactive({
  nodeName: '',
  nodeType: '',
  description: '',
  configParams: '{}',
  timeout: 0,
  retryTimes: 0,
  retryInterval: 1,
  failureStrategy: 'FAIL',
  priority: 2,
  workerGroup: 'default'
})

// 表单验证规则
const rules: FormRules = {
  nodeName: [
    { required: true, message: '请输入节点名称', trigger: 'blur' },
    { min: 1, max: 100, message: '节点名称长度在 1 到 100 个字符', trigger: 'blur' }
  ],
  nodeType: [
    { required: true, message: '请选择节点类型', trigger: 'change' }
  ],
  timeout: [
    { type: 'number', min: 0, max: 86400, message: '超时时间范围 0-86400 秒', trigger: 'blur' }
  ],
  retryTimes: [
    { type: 'number', min: 0, max: 10, message: '重试次数范围 0-10 次', trigger: 'blur' }
  ],
  retryInterval: [
    { type: 'number', min: 1, max: 3600, message: '重试间隔范围 1-3600 秒', trigger: 'blur' }
  ],
  priority: [
    { required: true, message: '请选择优先级', trigger: 'change' }
  ],
  workerGroup: [
    { required: true, message: '请选择工作组', trigger: 'change' }
  ]
}

// 方法
const loadFormData = () => {
  if (!props.node) {
    return
  }

  // 先重置表单到默认状态
  resetForm()

  // 加载节点数据到表单
  form.nodeName = props.node.nodeName || ''
  form.nodeType = props.node.nodeType || ''
  form.description = props.node.description || ''
  form.configParams = props.node.configParams || '{}'
  form.timeout = props.node.timeout || 0
  form.retryTimes = props.node.retryTimes || 0
  form.retryInterval = props.node.retryInterval || 1
  form.failureStrategy = props.node.failureStrategy || 'FAIL'
  form.priority = props.node.priority || 2
  form.workerGroup = props.node.workerGroup || 'default'

  // 解析节点配置
  try {
    nodeConfig.value = props.node.configParams ? JSON.parse(props.node.configParams) : {}
  } catch (error) {
    console.warn('解析节点配置参数失败:', error)
    nodeConfig.value = {}
  }

  // 清除表单验证状态
  nextTick(() => {
    formRef.value?.clearValidate()
  })
}

// 重置表单到默认状态
const resetForm = () => {
  // 使用Object.assign确保响应式更新
  Object.assign(form, {
    nodeName: '',
    nodeType: '',
    description: '',
    configParams: '{}',
    timeout: 0,
    retryTimes: 0,
    retryInterval: 1,
    failureStrategy: 'FAIL',
    priority: 2,
    workerGroup: 'default'
  })

  nodeConfig.value = {}
}



// 处理配置验证
const handleConfigValidate = (valid: boolean) => {
  configValid.value = valid
}



const handleSubmit = async () => {
  if (!formRef.value || !props.node) return

  try {
    await formRef.value.validate()

    // 如果有动态配置组件，验证其配置
    if (configComponent.value && !configValid.value) {
      ElMessage.error('请检查节点配置信息')
      return
    }

    // 准备配置参数
    let configParams = form.configParams
    if (configComponent.value) {
      // 使用动态配置组件的数据
      configParams = JSON.stringify(nodeConfig.value)
    } else {
      // 验证原始JSON配置参数
      try {
        JSON.parse(form.configParams)
      } catch (error) {
        ElMessage.error('配置参数必须是有效的JSON格式')
        return
      }
    }

    submitLoading.value = true

    const updatedNode: WorkflowNode = {
      ...props.node,
      nodeName: form.nodeName,
      description: form.description,
      configParams: configParams,
      timeout: form.timeout,
      retryTimes: form.retryTimes,
      retryInterval: form.retryInterval,
      failureStrategy: form.failureStrategy,
      priority: form.priority,
      workerGroup: form.workerGroup,
      updateUser: 'admin' // TODO: 从用户状态获取
    }

    emit('save', updatedNode)
    ElMessage.success('节点配置已更新，请保存工作流')
    handleClose()
  } catch (error) {
    console.error('保存节点配置失败:', error)
    ElMessage.error('保存节点配置失败')
  } finally {
    submitLoading.value = false
  }
}

const handleClose = () => {
  // 重置表单字段
  formRef.value?.resetFields()
  // 重置表单数据
  resetForm()
  // 关闭对话框
  emit('update:visible', false)
}

// 窗口大小变化处理
const handleResize = () => {
  windowWidth.value = window.innerWidth
}

// 监听对话框显示状态
watch(
  () => props.visible,
  (visible) => {
    if (visible) {
      // 使用setTimeout确保DOM更新完成
      setTimeout(() => {
        loadFormData()
      }, 50)
    }
  },
  { immediate: true }
)

// 监听节点变化，确保节点数据更新时重新加载表单
watch(
  () => props.node,
  (newNode) => {
    if (newNode && props.visible) {
      // 使用setTimeout确保数据更新完成
      setTimeout(() => {
        loadFormData()
      }, 50)
    }
  },
  { immediate: true, deep: true }
)

// 监听表单数据变化已移除，避免潜在的递归问题



// 生命周期钩子
onMounted(() => {
  window.addEventListener('resize', handleResize)
})

onUnmounted(() => {
  window.removeEventListener('resize', handleResize)
})
</script>

<style scoped>
.node-config-dialog {
  /* 对话框内容区域 */
  .dialog-content {
    max-height: 75vh;
    overflow-y: auto;
    padding-right: 8px;

    /* 自定义滚动条样式 */
    &::-webkit-scrollbar {
      width: 6px;
    }

    &::-webkit-scrollbar-track {
      background: var(--el-fill-color-lighter);
      border-radius: 3px;
    }

    &::-webkit-scrollbar-thumb {
      background: var(--el-border-color-darker);
      border-radius: 3px;

      &:hover {
        background: var(--el-border-color-dark);
      }
    }
  }

  .dialog-footer {
    display: flex;
    justify-content: flex-end;
    gap: 12px;
    padding-top: 16px;
    border-top: 1px solid var(--el-border-color-lighter);
  }

  .config-textarea :deep(.el-textarea__inner) {
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    font-size: 12px;
  }

  .node-specific-config {
    border: 1px solid var(--el-border-color-light);
    border-radius: 6px;
    padding: 16px;
    background: var(--el-bg-color-page);
    max-height: 60vh;
    overflow-y: auto;

    /* 自定义滚动条样式 */
    &::-webkit-scrollbar {
      width: 6px;
    }

    &::-webkit-scrollbar-track {
      background: var(--el-fill-color-lighter);
      border-radius: 3px;
    }

    &::-webkit-scrollbar-thumb {
      background: var(--el-border-color-darker);
      border-radius: 3px;

      &:hover {
        background: var(--el-border-color-dark);
      }
    }
  }

  /* 修复下拉组件在对话框中的滚动条问题 */
  :deep(.el-select-dropdown) {
    max-height: 274px !important;
  }

  :deep(.el-dropdown-menu) {
    max-height: 300px !important;
  }

  /* 响应式设计 */
  @media (max-width: 768px) {
    .dialog-content {
      max-height: 70vh;
      padding-right: 4px;
    }

    .node-specific-config {
      max-height: 50vh;
      padding: 12px;
    }

    :deep(.el-form) {
      label-width: 100px !important;
    }

    :deep(.el-form-item) {
      margin-bottom: 16px;
    }

    :deep(.el-form-item__label) {
      font-size: 13px;
      line-height: 1.2;
    }

    :deep(.el-input__inner),
    :deep(.el-textarea__inner),
    :deep(.el-select__input) {
      font-size: 14px;
    }

    :deep(.el-row) {
      margin: 0 -8px !important;
    }

    :deep(.el-col) {
      padding: 0 8px !important;
      margin-bottom: 8px;
    }
  }

  @media (max-width: 480px) {
    .dialog-content {
      max-height: 75vh;
      padding-right: 2px;
    }

    :deep(.el-form) {
      label-width: 80px !important;
    }

    :deep(.el-form-item) {
      margin-bottom: 12px;
    }

    :deep(.el-form-item__label) {
      font-size: 12px;
      line-height: 1.1;
    }

    :deep(.el-input__inner),
    :deep(.el-textarea__inner),
    :deep(.el-select__input) {
      font-size: 13px;
      padding: 8px 12px;
    }

    :deep(.el-button) {
      font-size: 12px;
      padding: 6px 12px;
    }

    :deep(.el-row) {
      margin: 0 -4px !important;
    }

    :deep(.el-col) {
      padding: 0 4px !important;
      margin-bottom: 6px;
    }

    .dialog-footer {
      padding-top: 12px;

      .el-button {
        min-width: 60px;
      }
    }
  }
}

/* 全局对话框样式优化 */
:deep(.el-dialog) {
  margin-top: 5vh !important;
  margin-bottom: 5vh !important;
  max-height: 90vh;
  display: flex;
  flex-direction: column;
}

:deep(.el-dialog__body) {
  flex: 1;
  overflow: hidden;
  padding: 20px 20px 0 20px;
}

:deep(.el-dialog__footer) {
  padding: 0 20px 20px 20px;
  flex-shrink: 0;
}

/* 修复对话框内下拉框定位问题 */
.node-config-dialog {
  /* 确保对话框有正确的层级 */
  z-index: 2000;
}

.node-config-dialog :deep(.el-select-dropdown) {
  /* 确保下拉框在对话框之上 */
  z-index: 3000 !important;
}

.node-config-dialog :deep(.datax-select-dropdown) {
  /* DataX配置专用下拉框样式 */
  z-index: 3000 !important;
  max-height: 200px;
}

/* 确保下拉框不会被对话框遮挡 */
.node-config-dialog .dialog-content {
  position: relative;
  z-index: 1;
}
</style>
