<template>
  <div class="datax-node-config">

    <!-- 数据源配置 -->
    <el-card class="config-section" shadow="never">
      <template #header>
        <div class="section-header">
          <el-icon><Connection /></el-icon>
          <span>数据源配置</span>
        </div>
      </template>
      <el-row :gutter="20" class="datasource-row">
        <!-- 源数据源 -->
        <el-col :span="12" :xs="24" :sm="24" :md="12">
          <el-card class="datasource-card" shadow="never">
            <template #header>
              <span class="datasource-title">源数据源</span>
            </template>
            <el-form :model="config.source" label-width="80px" size="small">
              <el-form-item label="数据源" required>
                <el-select
                  v-model="config.source.datasourceId"
                  placeholder="请选择源数据源"
                  style="width: 100%"
                  @change="handleSourceDatasourceChange"
                  :teleported="false"
                  popper-class="datax-select-dropdown"
                >
                  <el-option
                    v-for="ds in datasources"
                    :key="ds.id"
                    :label="`${ds.name} (${ds.type})`"
                    :value="ds.id"
                  />
                </el-select>
              </el-form-item>
              <el-form-item label="数据库">
                <el-select
                  v-model="config.source.database"
                  placeholder="请选择数据库"
                  style="width: 100%"
                  @change="handleSourceDatabaseChange"
                  :loading="loadingSourceDatabases"
                  :teleported="false"
                  popper-class="datax-select-dropdown"
                >
                  <el-option
                    v-for="db in sourceDatabases"
                    :key="db"
                    :label="db"
                    :value="db"
                  />
                </el-select>
              </el-form-item>
              <el-form-item label="表名" required>
                <el-select
                  v-model="config.source.tableName"
                  placeholder="请选择表"
                  style="width: 100%"
                  filterable
                  :loading="loadingSourceTables"
                  @change="loadSourceColumns"
                  :teleported="false"
                  popper-class="datax-select-dropdown"
                >
                  <el-option
                    v-for="table in sourceTables"
                    :key="table"
                    :label="table"
                    :value="table"
                  />
                </el-select>
              </el-form-item>
              <el-form-item label="查询条件">
                <el-input
                  v-model="config.source.whereCondition"
                  type="textarea"
                  :rows="3"
                  placeholder="WHERE条件，如：id > 100"
                />
              </el-form-item>
              <el-form-item label="分片字段">
                <el-select
                  v-model="config.source.splitPk"
                  placeholder="选择分片字段"
                  style="width: 100%"
                  clearable
                  :loading="loadingSourceColumns"
                  :teleported="false"
                  popper-class="datax-select-dropdown"
                >
                  <el-option
                    v-for="col in sourceColumns"
                    :key="col.name"
                    :label="`${col.name} (${col.type})`"
                    :value="col.name"
                  />
                </el-select>
              </el-form-item>
            </el-form>
          </el-card>
        </el-col>

        <!-- 目标数据源 -->
        <el-col :span="12" :xs="24" :sm="24" :md="12">
          <el-card class="datasource-card" shadow="never">
            <template #header>
              <span class="datasource-title">目标数据源</span>
            </template>
            <el-form :model="config.target" label-width="80px" size="small">
              <el-form-item label="数据源" required>
                <el-select
                  v-model="config.target.datasourceId"
                  placeholder="请选择目标数据源"
                  style="width: 100%"
                  @change="handleTargetDatasourceChange"
                  :teleported="false"
                  popper-class="datax-select-dropdown"
                >
                  <el-option
                    v-for="ds in datasources"
                    :key="ds.id"
                    :label="`${ds.name} (${ds.type})`"
                    :value="ds.id"
                  />
                </el-select>
              </el-form-item>
              <el-form-item label="数据库">
                <el-select
                  v-model="config.target.database"
                  placeholder="请选择数据库"
                  style="width: 100%"
                  @change="handleTargetDatabaseChange"
                  :loading="loadingTargetDatabases"
                  :teleported="false"
                  popper-class="datax-select-dropdown"
                >
                  <el-option
                    v-for="db in targetDatabases"
                    :key="db"
                    :label="db"
                    :value="db"
                  />
                </el-select>
              </el-form-item>
              <el-form-item label="表名" required>
                <el-select
                  v-model="config.target.tableName"
                  placeholder="请选择表"
                  style="width: 100%"
                  filterable
                  :loading="loadingTargetTables"
                  @change="loadTargetColumns"
                  :teleported="false"
                  popper-class="datax-select-dropdown"
                >
                  <el-option
                    v-for="table in targetTables"
                    :key="table"
                    :label="table"
                    :value="table"
                  />
                </el-select>
              </el-form-item>
              <el-form-item label="写入模式">
                <el-select v-model="config.target.writeMode" style="width: 100%">
                  <el-option label="插入(INSERT)" value="insert" />
                  <el-option label="替换(REPLACE)" value="replace" />
                  <el-option label="更新(UPDATE)" value="update" />
                </el-select>
              </el-form-item>
              <el-form-item label="预处理SQL">
                <el-input
                  v-model="config.target.preSql"
                  type="textarea"
                  :rows="2"
                  placeholder="同步前执行的SQL"
                />
              </el-form-item>
              <el-form-item label="后处理SQL">
                <el-input
                  v-model="config.target.postSql"
                  type="textarea"
                  :rows="2"
                  placeholder="同步后执行的SQL"
                />
              </el-form-item>
            </el-form>
          </el-card>
        </el-col>
      </el-row>
    </el-card>

    <!-- 同步策略配置 -->
    <el-card class="config-section" shadow="never">
      <template #header>
        <div class="section-header">
          <el-icon><Timer /></el-icon>
          <span>同步策略配置</span>
          <div class="header-actions">
            <el-button
              link
              size="small"
              @click="showVariableHelper = true"
            >
              变量帮助
            </el-button>
          </div>
        </div>
      </template>

      <el-form :model="config.syncStrategy" label-width="120px" size="small">
        <!-- 同步类型 -->
        <el-form-item label="同步类型" required>
          <el-radio-group v-model="config.syncStrategy.type" @change="onSyncTypeChange">
            <el-radio value="FULL">全量同步</el-radio>
            <el-radio value="INCREMENTAL">增量同步</el-radio>
          </el-radio-group>
        </el-form-item>

        <!-- 增量同步配置 -->
        <div v-if="config.syncStrategy.type === 'INCREMENTAL'" class="incremental-config">
          <!-- 增量字段 -->
          <el-form-item label="增量字段" required>
            <el-select
              v-model="config.syncStrategy.incrementalColumn"
              placeholder="请选择增量字段"
              style="width: 100%"
              filterable
              @change="onIncrementalColumnChange"
              :teleported="false"
              popper-class="datax-select-dropdown"
            >
              <el-option
                v-for="column in sourceColumns"
                :key="column.name"
                :label="`${column.name} (${column.type})`"
                :value="column.name"
              />
            </el-select>
          </el-form-item>

          <!-- 增量类型 -->
          <el-form-item label="增量类型" required>
            <el-select
              v-model="config.syncStrategy.incrementalType"
              placeholder="请选择增量类型"
              style="width: 100%"
              :teleported="false"
              popper-class="datax-select-dropdown"
            >
              <el-option label="时间戳" value="TIMESTAMP" />
              <el-option label="主键ID" value="PRIMARY_KEY" />
              <el-option label="字符串" value="STRING" />
            </el-select>
          </el-form-item>

          <!-- 增量值表达式 -->
          <el-form-item label="增量值" required>
            <el-input
              v-model="config.syncStrategy.incrementalValue"
              placeholder="例如: ${lastSyncTime}"
              style="width: calc(100% - 100px)"
            />
            <el-dropdown @command="insertVariable" style="margin-left: 10px">
              <el-button type="primary" size="small">
                变量<el-icon class="el-icon--right"><ArrowDown /></el-icon>
              </el-button>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item command="${lastSyncTime}">上次同步时间</el-dropdown-item>
                  <el-dropdown-item command="${currentTime}">当前时间</el-dropdown-item>
                  <el-dropdown-item command="${yesterday}">昨天</el-dropdown-item>
                  <el-dropdown-item command="${today}">今天</el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
            <div class="form-tip">
              支持变量表达式，如 ${lastSyncTime} 表示上次同步时间
            </div>
          </el-form-item>

          <!-- 比较操作符 -->
          <el-form-item label="比较操作符">
            <el-select
              v-model="config.syncStrategy.operator"
              placeholder="请选择操作符"
              style="width: 200px"
              :teleported="false"
              popper-class="datax-select-dropdown"
            >
              <el-option label="大于 >" value=">" />
              <el-option label="大于等于 >=" value=">=" />
              <el-option label="小于 <" value="<" />
              <el-option label="小于等于 <=" value="<=" />
              <el-option label="等于 =" value="=" />
              <el-option label="不等于 !=" value="!=" />
            </el-select>
          </el-form-item>

          <!-- 自定义条件 -->
          <el-form-item label="自定义条件">
            <el-input
              v-model="config.syncStrategy.customCondition"
              type="textarea"
              :rows="2"
              placeholder="例如: status = 'active' AND deleted = 0"
            />
            <div class="form-tip">
              可以添加额外的WHERE条件，将与增量条件用AND连接
            </div>
          </el-form-item>

          <!-- WHERE条件预览 -->
          <el-form-item label="WHERE条件预览">
            <el-input
              :value="previewWhereCondition"
              type="textarea"
              :rows="3"
              readonly
              placeholder="WHERE条件将在这里显示"
            />
            <el-button
              link
              size="small"
              @click="generatePreview"
              style="margin-top: 5px"
            >
              <el-icon><View /></el-icon>
              生成预览
            </el-button>
          </el-form-item>
        </div>
      </el-form>
    </el-card>

    <!-- 字段映射配置 -->
    <el-card class="config-section" shadow="never">
      <template #header>
        <div class="section-header">
          <el-icon><Rank /></el-icon>
          <span>字段映射</span>
          <div class="header-actions">
            <el-button
              size="small"
              @click="autoMapping"
              :loading="loadingAutoMapping"
            >
              自动映射
            </el-button>
            <el-button size="small" @click="clearMapping">清空映射</el-button>
            <el-button size="small" type="primary" @click="addMapping">
              <el-icon><Plus /></el-icon>
              添加映射
            </el-button>
          </div>
        </div>
      </template>

      <!-- 字段映射提示 -->
      <div v-if="config.columnMappings.length === 0" class="mapping-tips">
        <el-alert
          title="字段映射说明"
          type="info"
          :closable="false"
          show-icon
        >
          <template #default>
            <p>1. 请先选择源表和目标表，然后点击"自动映射"按钮</p>
            <p>2. 自动映射会根据字段名称进行匹配</p>
            <p>3. 也可以手动点击"添加映射"按钮逐个配置字段映射</p>
          </template>
        </el-alert>
      </div>

      <div class="mapping-container">
        <el-table
          :data="config.columnMappings"
          style="width: 100%"
          size="small"
          empty-text="暂无字段映射，请点击上方按钮添加"
          :height="config.columnMappings.length === 0 ? '80px' : 'auto'"
        >
          <el-table-column label="源字段" width="200">
            <template #default="{ row, $index }">
              <el-select
                v-model="row.sourceColumn"
                placeholder="选择源字段"
                style="width: 100%"
                filterable
                :teleported="false"
                popper-class="datax-select-dropdown"
              >
                <el-option
                  v-for="col in sourceColumns"
                  :key="col.name"
                  :label="`${col.name} (${col.type})`"
                  :value="col.name"
                />
              </el-select>
            </template>
          </el-table-column>
          <el-table-column label="目标字段" width="200">
            <template #default="{ row, $index }">
              <el-select
                v-model="row.targetColumn"
                placeholder="选择目标字段"
                style="width: 100%"
                filterable
                :teleported="false"
                popper-class="datax-select-dropdown"
              >
                <el-option
                  v-for="col in targetColumns"
                  :key="col.name"
                  :label="`${col.name} (${col.type})`"
                  :value="col.name"
                />
              </el-select>
            </template>
          </el-table-column>
          <el-table-column label="数据转换" width="250">
            <template #default="{ row, $index }">
              <el-input
                v-model="row.transform"
                placeholder="转换表达式，如：UPPER(${sourceColumn})"
                size="small"
              />
            </template>
          </el-table-column>
          <el-table-column label="默认值" width="120">
            <template #default="{ row, $index }">
              <el-input
                v-model="row.defaultValue"
                placeholder="默认值"
                size="small"
              />
            </template>
          </el-table-column>
          <el-table-column label="说明">
            <template #default="{ row, $index }">
              <el-input
                v-model="row.description"
                placeholder="字段说明"
                size="small"
              />
            </template>
          </el-table-column>
          <el-table-column label="操作" width="80">
            <template #default="{ row, $index }">
              <el-button
                size="small"
                type="danger"
                link
                @click="removeMapping($index)"
              >
                删除
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </el-card>



    <!-- 性能参数 -->
    <el-card class="config-section" shadow="never">
      <template #header>
        <div class="section-header">
          <span>性能参数</span>
        </div>
      </template>
      <el-form :model="config.performance" label-width="120px">
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="并发数">
              <el-input-number
                v-model="config.performance.channel"
                :min="1"
                :max="32"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="批次大小">
              <el-input-number
                v-model="config.performance.batchSize"
                :min="100"
                :max="10000"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="字节限制(MB)">
              <el-input-number
                v-model="config.performance.byteLimit"
                :min="1"
                :max="1024"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="记录限制">
              <el-input-number
                v-model="config.performance.recordLimit"
                :min="1000"
                :max="1000000"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="速度限制(MB/s)">
              <el-input-number
                v-model="config.performance.speedLimit"
                :min="1"
                :max="100"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="错误限制">
              <el-input-number
                v-model="config.performance.errorLimit"
                :min="0"
                :max="1000"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </el-card>

    <!-- 变量帮助对话框 -->
    <el-dialog
      v-model="showVariableHelper"
      title="变量使用帮助"
      width="600px"
    >
      <div class="variable-help">
        <h4>支持的变量类型：</h4>
        <el-table :data="variableHelp" size="small">
          <el-table-column prop="variable" label="变量" width="200" />
          <el-table-column prop="description" label="说明" />
          <el-table-column prop="example" label="示例" width="150" />
        </el-table>

        <h4>时间格式说明：</h4>
        <ul>
          <li><code>${lastSyncTime}</code> - 默认格式：yyyy-MM-dd HH:mm:ss</li>
          <li><code>${lastSyncTime:yyyy-MM-dd}</code> - 指定格式</li>
          <li><code>${lastSyncTime:timestamp}</code> - 时间戳格式</li>
        </ul>

        <h4>使用示例：</h4>
        <div class="code-example">
          <pre><code>-- 时间戳增量
update_time > '${lastSyncTime}'

-- 主键增量
id > ${lastMaxId}

-- 自定义条件
update_time > '${lastSyncTime}' AND status = 'active'</code></pre>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted, onUnmounted, nextTick } from 'vue'
import { ElMessage } from 'element-plus'
import { datasourceApi } from '@/api/datasource'
import { deepClone, deepMerge, createNodeConfig } from '@/utils/objectUtils'
import {
  Connection,
  Rank,
  Refresh,
  Plus,
  Timer,
  ArrowDown,
  View
} from '@element-plus/icons-vue'
import type { WorkflowNode } from '@/types/workflowNode'

// Props
interface Props {
  modelValue: any
  node: WorkflowNode
}

const props = defineProps<Props>()

// Emits
const emit = defineEmits<{
  'update:modelValue': [value: any]
  'validate': [valid: boolean]
}>()

// 响应式数据 - 使用工具函数创建独立的配置实例
const config = ref(createNodeConfig.datax())

const datasources = ref<any[]>([])
const sourceDatabases = ref<string[]>([])
const targetDatabases = ref<string[]>([])
const sourceTables = ref<string[]>([])
const targetTables = ref<string[]>([])
const sourceColumns = ref<any[]>([])
const targetColumns = ref<any[]>([])

// 加载状态
const loadingSourceDatabases = ref(false)
const loadingTargetDatabases = ref(false)
const loadingSourceTables = ref(false)
const loadingTargetTables = ref(false)
const loadingSourceColumns = ref(false)
const loadingTargetColumns = ref(false)
const loadingAutoMapping = ref(false)

// 标记是否正在内部更新，避免循环
const isInternalUpdate = ref(false)

// 变量帮助数据
const variableHelp = ref([
  { variable: '${lastSyncTime}', description: '上次同步时间', example: '2023-12-01 10:00:00' },
  { variable: '${currentTime}', description: '当前时间', example: '2023-12-01 15:30:00' },
  { variable: '${yesterday}', description: '昨天时间', example: '2023-11-30 00:00:00' },
  { variable: '${today}', description: '今天开始时间', example: '2023-12-01 00:00:00' },
  { variable: '${nodeId}', description: '节点ID', example: '123' },
  { variable: '${executionId}', description: '执行ID', example: 'exec_456' }
])

// 监听配置变化
watch(config, (newConfig) => {
  if (!isInternalUpdate.value) {
    emit('update:modelValue', newConfig)
    validateConfig()
  }
}, { deep: true })

// 从configParams加载配置
const loadConfigFromParams = () => {
  const defaultConfig = createNodeConfig.datax()

  // 尝试从node.configParams加载现有配置
  let existingConfig = null
  if (props.node?.configParams) {
    try {
      existingConfig = JSON.parse(props.node.configParams)
    } catch (error) {
      // 解析失败时使用默认配置
    }
  }

  // 合并配置
  if (existingConfig && typeof existingConfig === 'object') {
    config.value = deepMerge(defaultConfig, existingConfig)
  } else {
    config.value = defaultConfig
  }
}

// 先声明所有需要的函数，避免初始化顺序问题
let loadSourceDatabases: () => Promise<void>
let loadTargetDatabases: () => Promise<void>
let loadSourceTables: () => Promise<void>
let loadTargetTables: () => Promise<void>
let loadSourceColumns: () => Promise<void>
let loadTargetColumns: () => Promise<void>
let autoMapping: () => Promise<void>

// 监听外部值变化
watch(() => props.modelValue, async (newValue) => {
  isInternalUpdate.value = true
  loadConfigFromParams()

  if (newValue && typeof newValue === 'object') {
    config.value = deepMerge(config.value, newValue)
  }

  // 重置内部更新标记
  nextTick(() => {
    isInternalUpdate.value = false
  })

  // 如果配置中已有数据源ID，则加载对应的数据库列表
  if (config.value.source.datasourceId && loadSourceDatabases) {
    await loadSourceDatabases()
    // 加载完数据库列表后，如果配置中有数据库，再加载表列表
    if (config.value.source.database && loadSourceTables) {
      await loadSourceTables()
      // 如果有表名，加载字段列表
      if (config.value.source.tableName && loadSourceColumns) {
        await loadSourceColumns()
      }
    }
  }

  if (config.value.target.datasourceId && loadTargetDatabases) {
    await loadTargetDatabases()
    // 加载完数据库列表后，如果配置中有数据库，再加载表列表
    if (config.value.target.database && loadTargetTables) {
      await loadTargetTables()
      // 如果有表名，加载字段列表
      if (config.value.target.tableName && loadTargetColumns) {
        await loadTargetColumns()
      }
    }
  }
}, { immediate: true })



// 重置配置到默认状态
const resetConfig = () => {
  config.value = createNodeConfig.datax()
  // 清空相关数据
  sourceDatabases.value = []
  targetDatabases.value = []
  sourceTables.value = []
  targetTables.value = []
  sourceColumns.value = []
  targetColumns.value = []
}

// 暴露重置方法给父组件
defineExpose({
  resetConfig
})

// 验证配置
const validateConfig = () => {
  const hasSourceDatasource = !!config.value.source.datasourceId
  const hasSourceTable = !!config.value.source.tableName
  const hasTargetDatasource = !!config.value.target.datasourceId
  const hasTargetTable = !!config.value.target.tableName
  const hasColumnMappings = config.value.columnMappings.length > 0

  const isValid = hasSourceDatasource && hasSourceTable &&
                  hasTargetDatasource && hasTargetTable && hasColumnMappings

  emit('validate', isValid)
}



const loadDatasources = async () => {
  try {
    const response = await datasourceApi.list()

    if (response.code === 200) {
      datasources.value = response.content || []
    } else {
      ElMessage.error(response.msg || '加载数据源失败')
    }
  } catch (error) {
    ElMessage.error('加载数据源失败')
  }
}

const handleSourceDatasourceChange = async () => {
  // 清空相关数据
  sourceDatabases.value = []
  sourceTables.value = []
  sourceColumns.value = []
  config.value.source.database = ''
  config.value.source.tableName = ''

  // 加载数据库列表
  if (config.value.source.datasourceId) {
    await loadSourceDatabases()
  }
}

const handleSourceDatabaseChange = async () => {
  // 清空表和列数据
  sourceTables.value = []
  sourceColumns.value = []
  config.value.source.tableName = ''

  // 加载表列表
  if (config.value.source.database) {
    await loadSourceTables()
  }
}

const handleTargetDatasourceChange = async () => {
  // 清空相关数据
  targetDatabases.value = []
  targetTables.value = []
  targetColumns.value = []
  config.value.target.database = ''
  config.value.target.tableName = ''

  // 加载数据库列表
  if (config.value.target.datasourceId) {
    await loadTargetDatabases()
  }
}

const handleTargetDatabaseChange = async () => {
  // 清空表和列数据
  targetTables.value = []
  targetColumns.value = []
  config.value.target.tableName = ''

  // 加载表列表
  if (config.value.target.database) {
    await loadTargetTables()
  }
}

loadSourceDatabases = async () => {
  if (!config.value.source.datasourceId) return

  loadingSourceDatabases.value = true
  try {
    const response = await datasourceApi.getDatabases(config.value.source.datasourceId)
    if (response.code === 200) {
      sourceDatabases.value = response.content || []
    } else {
      sourceDatabases.value = []
    }
  } catch (error) {
    sourceDatabases.value = []
    ElMessage.error('加载源数据库列表失败')
  } finally {
    loadingSourceDatabases.value = false
  }
}

loadTargetDatabases = async () => {
  if (!config.value.target.datasourceId) return

  loadingTargetDatabases.value = true
  try {
    const response = await datasourceApi.getDatabases(config.value.target.datasourceId)
    if (response.code === 200) {
      targetDatabases.value = response.content || []
    } else {
      targetDatabases.value = []
    }
  } catch (error) {
    targetDatabases.value = []
    ElMessage.error('加载目标数据库列表失败')
  } finally {
    loadingTargetDatabases.value = false
  }
}

loadSourceTables = async () => {
  if (!config.value.source.datasourceId) return

  loadingSourceTables.value = true
  try {
    if (config.value.source.database) {
      // 如果有数据库，调用API获取表列表
      const response = await datasourceApi.getTables(config.value.source.datasourceId, config.value.source.database)
      if (response.code === 200) {
        sourceTables.value = response.content || []
      } else {
        sourceTables.value = []
      }
    } else {
      // 如果没有数据库（某些数据源类型不需要选择数据库），使用模拟数据
      sourceTables.value = ['user_info', 'order_info', 'product_info']
    }
  } catch (error) {
    sourceTables.value = []
    ElMessage.error('加载源表列表失败')
  } finally {
    loadingSourceTables.value = false
  }
}

loadTargetTables = async () => {
  if (!config.value.target.datasourceId) return

  loadingTargetTables.value = true
  try {
    if (config.value.target.database) {
      // 如果有数据库，调用API获取表列表
      const response = await datasourceApi.getTables(config.value.target.datasourceId, config.value.target.database)
      if (response.code === 200) {
        targetTables.value = response.content || []
      } else {
        console.warn('获取目标表列表失败:', response.msg)
        targetTables.value = []
      }
    } else {
      // 如果没有数据库（某些数据源类型不需要选择数据库），使用模拟数据
      targetTables.value = ['user_info', 'order_info', 'product_info']
    }
  } catch (error) {
    console.error('加载目标表列表失败:', error)
    targetTables.value = []
    ElMessage.error('加载目标表列表失败')
  } finally {
    loadingTargetTables.value = false
  }
}

loadSourceColumns = async () => {
  if (!config.value.source.datasourceId || !config.value.source.database || !config.value.source.tableName) {
    return
  }

  loadingSourceColumns.value = true
  try {
    const response = await datasourceApi.getColumns(
      config.value.source.datasourceId,
      config.value.source.database,
      config.value.source.tableName
    )

    if (response.code === 200) {
      sourceColumns.value = (response.content || []).map(col => ({
        name: col.columnName,
        type: col.dataType,
        size: col.columnSize,
        nullable: col.nullable,
        remarks: col.remarks
      }))
    } else {
      ElMessage.error(response.msg || '获取源表字段失败')
      sourceColumns.value = []
    }
  } catch (error) {
    console.error('加载源字段列表失败:', error)
    ElMessage.error('加载源字段列表失败')
    sourceColumns.value = []
  } finally {
    loadingSourceColumns.value = false
  }
}

loadTargetColumns = async () => {
  if (!config.value.target.datasourceId || !config.value.target.database || !config.value.target.tableName) {
    return
  }

  loadingTargetColumns.value = true
  try {
    const response = await datasourceApi.getColumns(
      config.value.target.datasourceId,
      config.value.target.database,
      config.value.target.tableName
    )

    if (response.code === 200) {
      targetColumns.value = (response.content || []).map(col => ({
        name: col.columnName,
        type: col.dataType,
        size: col.columnSize,
        nullable: col.nullable,
        remarks: col.remarks
      }))
    } else {
      ElMessage.error(response.msg || '获取目标表字段失败')
      targetColumns.value = []
    }
  } catch (error) {
    console.error('加载目标字段列表失败:', error)
    ElMessage.error('加载目标字段列表失败')
    targetColumns.value = []
  } finally {
    loadingTargetColumns.value = false
  }
}

autoMapping = async () => {
  try {
    loadingAutoMapping.value = true

    // 检查源表和目标表是否已选择
    if (!config.value.source.tableName) {
      ElMessage.warning('请先选择源表')
      return
    }

    if (!config.value.target.tableName) {
      ElMessage.warning('请先选择目标表')
      return
    }

    // 确保字段已加载
    if (sourceColumns.value.length === 0) {
      ElMessage.info('正在加载源表字段...')
      await loadSourceColumns()
    }

    if (targetColumns.value.length === 0) {
      ElMessage.info('正在加载目标表字段...')
      await loadTargetColumns()
    }

    // 再次检查字段是否加载成功
    if (sourceColumns.value.length === 0 || targetColumns.value.length === 0) {
      ElMessage.error('无法获取表字段信息，请检查数据源连接和表名是否正确')
      return
    }

    // 清空现有映射
    config.value.columnMappings = []

    // 执行自动映射
    sourceColumns.value.forEach(sourceCol => {
      // 优先按名称完全匹配
      let targetCol = targetColumns.value.find(col => col.name === sourceCol.name)

      // 如果没有完全匹配，尝试忽略大小写匹配
      if (!targetCol) {
        targetCol = targetColumns.value.find(col =>
          col.name.toLowerCase() === sourceCol.name.toLowerCase()
        )
      }

      // 如果找到匹配的字段，添加映射
      if (targetCol) {
        config.value.columnMappings.push({
          sourceColumn: sourceCol.name,
          targetColumn: targetCol.name,
          transform: '',
          defaultValue: '',
          description: `${sourceCol.type} -> ${targetCol.type}`
        })
      }
    })

    if (config.value.columnMappings.length > 0) {
      ElMessage.success(`自动映射完成，共映射 ${config.value.columnMappings.length} 个字段`)
    } else {
      ElMessage.warning('未找到匹配的字段，请手动添加字段映射')
    }
  } catch (error) {
    console.error('自动映射失败:', error)
    ElMessage.error('自动映射失败，请手动添加字段映射')
  } finally {
    loadingAutoMapping.value = false
  }
}

const clearMapping = () => {
  config.value.columnMappings = []
  ElMessage.success('字段映射已清空')
}

const addMapping = () => {
  config.value.columnMappings.push({
    sourceColumn: '',
    targetColumn: '',
    transform: '',
    defaultValue: '',
    description: ''
  })

  ElMessage.success('已添加新的字段映射')
}

const removeMapping = (index: number) => {
  config.value.columnMappings.splice(index, 1)
}

// 增量同步相关方法
const showVariableHelper = ref(false)
const previewWhereCondition = ref('')

// 同步类型变化处理
const onSyncTypeChange = (type: string) => {
  if (type === 'INCREMENTAL') {
    // 设置默认增量值
    if (!config.value.syncStrategy.incrementalValue) {
      config.value.syncStrategy.incrementalValue = '${lastSyncTime}'
    }
    // 设置默认操作符
    if (!config.value.syncStrategy.operator) {
      config.value.syncStrategy.operator = '>'
    }
  }
  debounceGeneratePreview()
}

// 增量字段变化处理
const onIncrementalColumnChange = (column: string) => {
  // 根据字段类型自动设置增量类型
  const columnInfo = sourceColumns.value.find(c => c.name === column)
  if (columnInfo) {
    const columnType = columnInfo.type.toLowerCase()
    if (columnType.includes('timestamp') || columnType.includes('datetime')) {
      config.value.syncStrategy.incrementalType = 'TIMESTAMP'
    } else if (columnType.includes('int') || columnType.includes('bigint')) {
      config.value.syncStrategy.incrementalType = 'PRIMARY_KEY'
    } else {
      config.value.syncStrategy.incrementalType = 'STRING'
    }
  }
  // 延迟生成预览，避免频繁请求
  debounceGeneratePreview()
}

// 插入变量
const insertVariable = (variable: string) => {
  config.value.syncStrategy.incrementalValue = variable
  debounceGeneratePreview()
}

// 防抖生成预览
let previewTimer: NodeJS.Timeout | null = null
const debounceGeneratePreview = () => {
  if (previewTimer) {
    clearTimeout(previewTimer)
  }
  previewTimer = setTimeout(() => {
    generatePreview()
  }, 500) // 500ms防抖
}

// 生成WHERE条件预览
const generatePreview = async () => {
  if (config.value.syncStrategy.type !== 'INCREMENTAL') {
    previewWhereCondition.value = ''
    return
  }

  // 如果必要字段为空，直接生成本地预览
  if (!config.value.syncStrategy.incrementalColumn || !config.value.syncStrategy.incrementalValue) {
    generateLocalPreview()
    return
  }

  try {
    const response = await request.post('/api/datax/incremental-config/preview', {
      incrementalColumn: config.value.syncStrategy.incrementalColumn,
      incrementalValue: config.value.syncStrategy.incrementalValue,
      incrementalType: config.value.syncStrategy.incrementalType,
      operator: config.value.syncStrategy.operator,
      customCondition: config.value.syncStrategy.customCondition
    })

    if (response.code === 200) {
      previewWhereCondition.value = response.data
    }
  } catch (error) {
    console.error('生成预览失败:', error)
    // 简单的本地预览生成
    generateLocalPreview()
  }
}

// 本地预览生成（备用方案）
const generateLocalPreview = () => {
  const { incrementalColumn, incrementalValue, operator, customCondition } = config.value.syncStrategy

  if (!incrementalColumn || !incrementalValue) {
    previewWhereCondition.value = ''
    return
  }

  let condition = `${incrementalColumn} ${operator} '${incrementalValue}'`

  if (customCondition) {
    condition += ` AND (${customCondition})`
  }

  previewWhereCondition.value = condition
}

// 生命周期
onMounted(async () => {
  // 首先加载配置
  loadConfigFromParams()

  await loadDatasources()

  // 如果配置中已有数据源，加载对应的数据库和表
  if (config.value.source.datasourceId) {
    await loadSourceDatabases()
    if (config.value.source.database) {
      await loadSourceTables()
      if (config.value.source.tableName) {
        await loadSourceColumns()
      }
    }
  }

  if (config.value.target.datasourceId) {
    await loadTargetDatabases()
    if (config.value.target.database) {
      await loadTargetTables()
      if (config.value.target.tableName) {
        await loadTargetColumns()
      }
    }
  }

  validateConfig()
})

// 组件销毁时清理状态
onUnmounted(() => {
  resetConfig()
})
</script>

<style scoped>
.datax-node-config {
  /* 全局增加表单项间距 */
}

.datax-node-config :deep(.el-form-item) {
  margin-bottom: 20px;
}

.datax-node-config .config-section {
  margin-bottom: 16px;
}

.datax-node-config .config-section:last-child {
  margin-bottom: 0;
}

.datax-node-config .section-header {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 500;
  color: var(--el-color-primary);
}

.datax-node-config .section-header .header-actions {
  margin-left: auto;
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.datax-node-config .datasource-row .el-col {
  margin-bottom: 16px;
}

.datax-node-config .datasource-card {
  height: 100%;
}

.datax-node-config .datasource-card .datasource-title {
  font-weight: 500;
  color: var(--el-color-primary);
}

/* 增加表单项的上下间距 */
.datax-node-config .datasource-card :deep(.el-form-item) {
  margin-bottom: 20px;
}

.datax-node-config .mapping-container {
  max-height: 400px;
  overflow-y: auto;
}

/* 自定义滚动条样式 */
.datax-node-config .mapping-container::-webkit-scrollbar {
  width: 6px;
}

.datax-node-config .mapping-container::-webkit-scrollbar-track {
  background: var(--el-fill-color-lighter);
  border-radius: 3px;
}

.datax-node-config .mapping-container::-webkit-scrollbar-thumb {
  background: var(--el-border-color-darker);
  border-radius: 3px;
}

.datax-node-config .mapping-container::-webkit-scrollbar-thumb:hover {
  background: var(--el-border-color-dark);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .datax-node-config .section-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }

  .datax-node-config .section-header .header-actions {
    margin-left: 0;
    width: 100%;
    justify-content: flex-start;
  }

  .datax-node-config .datasource-row .el-col {
    margin-bottom: 20px;
  }

  .datax-node-config .mapping-container {
    max-height: 300px;
  }

  /* 表格在移动端的优化 */
  .datax-node-config :deep(.el-table) {
    font-size: 12px;
  }

  .datax-node-config :deep(.el-table .el-table__cell) {
    padding: 8px 4px;
  }
}

@media (max-width: 480px) {
  .datax-node-config .config-section {
    margin-bottom: 12px;
  }

  .datax-node-config .section-header .header-actions .el-button {
    font-size: 12px;
    padding: 4px 8px;
  }

  .datax-node-config .datasource-card :deep(.el-card__header) {
    padding: 12px;
  }

  .datax-node-config .datasource-card :deep(.el-card__body) {
    padding: 12px;
  }

  .datax-node-config .datasource-card :deep(.el-form-item) {
    margin-bottom: 16px;
  }

  .datax-node-config .mapping-container {
    max-height: 400px;
    overflow-y: auto;
  }

  .datax-node-config .mapping-container :deep(.el-table) {
    font-size: 11px;
  }

  .datax-node-config .mapping-container :deep(.el-table .el-table__cell) {
    padding: 6px 2px;
  }

  .datax-node-config .mapping-container :deep(.el-input),
  .datax-node-config .mapping-container :deep(.el-select) {
    font-size: 12px;
  }

  /* 字段映射提示样式 */
  .datax-node-config .mapping-tips {
    margin-bottom: 16px;
  }

  /* 修复下拉框定位问题 */
  .datax-select-dropdown {
    z-index: 3000 !important;
  }

  /* 确保对话框内的下拉框正确定位 */
  .node-config-dialog .datax-node-config .el-select {
    position: relative;
  }

  .node-config-dialog .datax-node-config .el-select .el-select__wrapper {
    position: relative;
    z-index: 1;
  }

  .datax-node-config .mapping-tips :deep(.el-alert__content) {
    font-size: 13px;
  }

  .datax-node-config .mapping-tips p {
    margin: 4px 0;
    color: #606266;
  }

  /* 空状态样式优化 */
  .datax-node-config .mapping-container :deep(.el-table__empty-block) {
    min-height: 60px !important;
    height: 60px !important;
  }

  .datax-node-config .mapping-container :deep(.el-table__empty-text) {
    color: #909399;
    font-size: 13px;
    line-height: 1.2;
    padding: 0 20px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  /* 增量同步配置样式 */
  .datax-node-config .incremental-config {
    background: #f9f9f9;
    padding: 15px;
    border-radius: 4px;
    margin: 10px 0;
    border-left: 3px solid #409eff;
  }

  .datax-node-config .form-tip {
    font-size: 12px;
    color: #999;
    margin-top: 5px;
    line-height: 1.4;
  }

  /* 变量帮助对话框样式 */
  .variable-help h4 {
    margin: 15px 0 10px 0;
    color: #333;
    font-size: 14px;
  }

  .variable-help ul {
    margin: 10px 0;
    padding-left: 20px;
  }

  .variable-help li {
    margin: 5px 0;
    font-size: 13px;
    color: #666;
  }

  .variable-help code {
    background: #f5f5f5;
    padding: 2px 4px;
    border-radius: 2px;
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    font-size: 12px;
    color: #e96900;
  }

  .code-example {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 4px;
    padding: 15px;
    margin: 10px 0;
  }

  .code-example pre {
    margin: 0;
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    font-size: 13px;
    line-height: 1.4;
    color: #495057;
  }

  .code-example code {
    background: none;
    padding: 0;
    color: inherit;
  }

  /* WHERE条件预览样式 */
  .datax-node-config .el-textarea.is-disabled .el-textarea__inner {
    background-color: #f8f9fa;
    border-color: #e9ecef;
    color: #495057;
  }
}
</style>
