<template>
  <div class="sql-node-config">
    <!-- 数据源配置 -->
    <el-card class="config-section" shadow="never">
      <template #header>
        <div class="section-header">
          <span>数据源配置</span>
        </div>
      </template>
      <el-form :model="config" :rules="rules" ref="formRef" label-width="120px" size="default">
        <el-form-item label="数据源" prop="datasourceId" required>
          <el-select
            v-model="config.datasourceId"
            placeholder="请选择数据源"
            style="width: 100%"
            filterable
            @change="handleDatasourceChange"
          >
            <el-option
              v-for="ds in datasources"
              :key="ds.id"
              :label="`${ds.name} (${ds.type})`"
              :value="ds.id"
            >
              <div class="datasource-option">
                <span class="ds-name">{{ ds.name }}</span>
                <span class="ds-type">{{ ds.type }}</span>
                <span class="ds-url">{{ ds.url }}</span>
              </div>
            </el-option>
          </el-select>
          <el-button
            type="primary"
            link
            @click="testConnection"
            :loading="testing"
            style="margin-left: 8px"
          >
            测试连接
          </el-button>
        </el-form-item>
        <el-form-item label="数据库" v-if="databases.length > 0">
          <el-select
            v-model="config.database"
            placeholder="请选择数据库"
            style="width: 100%"
            filterable
            @change="handleDatabaseChange"
          >
            <el-option
              v-for="db in databases"
              :key="db"
              :label="db"
              :value="db"
            />
          </el-select>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- SQL编辑器 -->
    <el-card class="config-section" shadow="never">
      <template #header>
        <div class="section-header">
          <span>SQL语句</span>
          <div class="header-actions">
            <el-button size="small" @click="formatSql">格式化</el-button>
            <el-button size="small" @click="validateSql" :loading="validating">验证</el-button>
            <el-button size="small" type="primary" @click="testSql" :loading="testing">测试执行</el-button>
          </div>
        </div>
      </template>
      <div class="sql-editor-container">
        <el-input
          v-model="config.sql"
          type="textarea"
          :rows="sqlEditorRows"
          placeholder="请输入SQL语句..."
          class="sql-editor"
          @input="handleSqlChange"
        />
        <div class="sql-info">
          <span class="sql-length">字符数: {{ config.sql?.length || 0 }}</span>
          <span class="sql-lines">行数: {{ sqlLines }}</span>
        </div>
      </div>

      <!-- SQL执行结果预览 -->
      <div v-if="showResult && sqlResult" class="sql-result-preview">
        <div class="result-header">
          <span>执行结果预览</span>
          <el-button size="small" link @click="showResult = false">
            <el-icon><Close /></el-icon>
            关闭
          </el-button>
        </div>
        <div class="result-content">
          <div class="result-info">
            <span>总行数: {{ sqlResult.total || 0 }}</span>
            <span>执行时间: {{ sqlResult.executeTime || 0 }}ms</span>
          </div>
          <el-table
            :data="sqlResult.data || []"
            style="width: 100%"
            size="small"
            max-height="200"
            border
          >
            <el-table-column
              v-for="(column, index) in sqlResult.columns || []"
              :key="index"
              :prop="column.name"
              :label="column.name"
              :width="120"
              show-overflow-tooltip
            />
          </el-table>
        </div>
      </div>
    </el-card>

    <!-- 参数配置 -->
    <el-card class="config-section" shadow="never">
      <template #header>
        <div class="section-header">
          <span>参数配置</span>
        </div>
      </template>
      <el-tabs v-model="activeTab" type="border-card">
        <!-- 输入参数 -->
        <el-tab-pane label="输入参数" name="input">
          <div class="param-section">
            <div class="param-header">
              <span>输入参数用于SQL语句中的占位符替换，格式：${paramName}</span>
              <el-button size="small" type="primary" @click="addInputParam">
                <el-icon><Plus /></el-icon>
                添加参数
              </el-button>
            </div>
            <el-table :data="config.inputParams" style="width: 100%" size="small">
              <el-table-column prop="name" label="参数名" width="150">
                <template #default="{ row, $index }">
                  <el-input v-model="row.name" placeholder="参数名" size="small" />
                </template>
              </el-table-column>
              <el-table-column prop="type" label="数据类型" width="120">
                <template #default="{ row, $index }">
                  <el-select v-model="row.type" size="small" style="width: 100%">
                    <el-option label="字符串" value="STRING" />
                    <el-option label="整数" value="INTEGER" />
                    <el-option label="小数" value="DECIMAL" />
                    <el-option label="日期" value="DATE" />
                    <el-option label="时间戳" value="TIMESTAMP" />
                  </el-select>
                </template>
              </el-table-column>
              <el-table-column prop="defaultValue" label="默认值" width="150">
                <template #default="{ row, $index }">
                  <el-input v-model="row.defaultValue" placeholder="默认值" size="small" />
                </template>
              </el-table-column>
              <el-table-column prop="required" label="必填" width="80">
                <template #default="{ row, $index }">
                  <el-checkbox v-model="row.required" />
                </template>
              </el-table-column>
              <el-table-column prop="description" label="描述">
                <template #default="{ row, $index }">
                  <el-input v-model="row.description" placeholder="参数描述" size="small" />
                </template>
              </el-table-column>
              <el-table-column label="操作" width="80">
                <template #default="{ row, $index }">
                  <el-button
                    size="small"
                    type="danger"
                    link
                    @click="removeInputParam($index)"
                  >
                    删除
                  </el-button>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </el-tab-pane>

        <!-- 输出参数 -->
        <el-tab-pane label="输出参数" name="output">
          <div class="param-section">
            <div class="param-header">
              <span>输出参数用于将SQL执行结果传递给下游节点</span>
              <el-button size="small" type="primary" @click="addOutputParam">
                <el-icon><Plus /></el-icon>
                添加参数
              </el-button>
            </div>
            <el-table :data="config.outputParams" style="width: 100%" size="small">
              <el-table-column prop="name" label="参数名" width="150">
                <template #default="{ row, $index }">
                  <el-input v-model="row.name" placeholder="参数名" size="small" />
                </template>
              </el-table-column>
              <el-table-column prop="type" label="数据类型" width="120">
                <template #default="{ row, $index }">
                  <el-select v-model="row.type" size="small" style="width: 100%">
                    <el-option label="字符串" value="STRING" />
                    <el-option label="整数" value="INTEGER" />
                    <el-option label="小数" value="DECIMAL" />
                    <el-option label="日期" value="DATE" />
                    <el-option label="时间戳" value="TIMESTAMP" />
                    <el-option label="JSON" value="JSON" />
                  </el-select>
                </template>
              </el-table-column>
              <el-table-column prop="source" label="数据来源" width="150">
                <template #default="{ row, $index }">
                  <el-select v-model="row.source" size="small" style="width: 100%">
                    <el-option label="查询结果" value="RESULT" />
                    <el-option label="影响行数" value="AFFECTED_ROWS" />
                    <el-option label="执行状态" value="STATUS" />
                  </el-select>
                </template>
              </el-table-column>
              <el-table-column prop="description" label="描述">
                <template #default="{ row, $index }">
                  <el-input v-model="row.description" placeholder="参数描述" size="small" />
                </template>
              </el-table-column>
              <el-table-column label="操作" width="80">
                <template #default="{ row, $index }">
                  <el-button
                    size="small"
                    type="danger"
                    link
                    @click="removeOutputParam($index)"
                  >
                    删除
                  </el-button>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </el-tab-pane>
      </el-tabs>
    </el-card>

    <!-- 执行选项 -->
    <el-card class="config-section" shadow="never">
      <template #header>
        <div class="section-header">
          <span>执行选项</span>
        </div>
      </template>
      <el-form :model="config" label-width="120px">
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="查询超时(秒)">
              <el-input-number
                v-model="config.queryTimeout"
                :min="1"
                :max="3600"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="最大结果行数">
              <el-input-number
                v-model="config.maxRows"
                :min="1"
                :max="100000"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="SQL类型">
              <el-select v-model="config.sqlType" style="width: 100%">
                <el-option label="查询(SELECT)" value="SELECT" />
                <el-option label="更新(UPDATE)" value="UPDATE" />
                <el-option label="插入(INSERT)" value="INSERT" />
                <el-option label="删除(DELETE)" value="DELETE" />
                <el-option label="DDL" value="DDL" />
                <el-option label="存储过程" value="PROCEDURE" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item>
              <template #label>
                <span>事务处理</span>
                <el-tooltip content="是否在事务中执行SQL" placement="top">
                  <el-icon><QuestionFilled /></el-icon>
                </el-tooltip>
              </template>
              <el-checkbox v-model="config.useTransaction">使用事务</el-checkbox>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item>
              <template #label>
                <span>预编译</span>
                <el-tooltip content="使用预编译语句，提高性能和安全性" placement="top">
                  <el-icon><QuestionFilled /></el-icon>
                </el-tooltip>
              </template>
              <el-checkbox v-model="config.usePreparedStatement">使用预编译语句</el-checkbox>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted, nextTick } from 'vue'
import { ElMessage } from 'element-plus'
import {
  Plus,
  QuestionFilled,
  Close
} from '@element-plus/icons-vue'
import type { WorkflowNode } from '@/types/workflowNode'
import { datasourceApi } from '@/api/datasource'
import { deepClone, createNodeConfig } from '@/utils/objectUtils'

// Props
interface Props {
  modelValue: any
  node: WorkflowNode
}

const props = defineProps<Props>()

// Emits
const emit = defineEmits<{
  'update:modelValue': [value: any]
  'validate': [valid: boolean]
}>()

// 响应式数据
const config = ref({
  datasourceId: null,
  database: '',
  sql: '',
  inputParams: [],
  outputParams: [],
  queryTimeout: 30,
  maxRows: 1000,
  sqlType: 'SELECT',
  useTransaction: false,
  usePreparedStatement: true
})

const datasources = ref<any[]>([])
const databases = ref<string[]>([])
const activeTab = ref('input')
const testing = ref(false)
const validating = ref(false)
const formRef = ref()
const sqlResult = ref<any>(null)
const showResult = ref(false)

// 计算属性
const sqlLines = computed(() => {
  return config.value.sql ? config.value.sql.split('\n').length : 0
})

// 响应式SQL编辑器行数
const sqlEditorRows = computed(() => {
  if (typeof window === 'undefined') return 8

  const screenHeight = window.innerHeight

  if (screenHeight < 600) {
    return 6
  } else if (screenHeight < 800) {
    return 8
  } else {
    return 10
  }
})

// 验证规则
const rules = {
  datasourceId: [
    { required: true, message: '请选择数据源', trigger: 'change' }
  ]
}

// 标记是否正在内部更新，避免循环
const isInternalUpdate = ref(false)

// 监听配置变化
watch(config, (newConfig) => {
  if (!isInternalUpdate.value) {
    emit('update:modelValue', newConfig)
    validateConfig()
  }
}, { deep: true })

// 监听外部值变化
watch(() => props.modelValue, (newValue) => {
  // 设置内部更新标记，避免触发上面的watch
  isInternalUpdate.value = true

  // 创建默认配置
  const defaultConfig = createNodeConfig.sql()

  if (newValue && typeof newValue === 'object' && Object.keys(newValue).length > 0) {
    // 如果有有效配置，则合并配置
    config.value = Object.assign({}, defaultConfig, deepClone(newValue))
  } else {
    // 如果没有有效配置（空对象或null），使用默认配置
    config.value = defaultConfig
  }

  // 重置内部更新标记
  nextTick(() => {
    isInternalUpdate.value = false
  })
}, { immediate: true })

// 方法
const validateConfig = async () => {
  try {
    await formRef.value?.validate()
    const isValid = config.value.datasourceId &&
                   config.value.sql.trim() &&
                   (databases.value.length === 0 || config.value.database)
    emit('validate', !!isValid)
  } catch {
    emit('validate', false)
  }
}

const loadDatasources = async () => {
  try {
    const response = await datasourceApi.list()
    if (response.code === 200) {
      datasources.value = response.content || []
    } else {
      ElMessage.error(response.msg || '加载数据源失败')
    }
  } catch (error) {
    console.error('加载数据源失败:', error)
    ElMessage.error('加载数据源失败')
  }
}

const handleDatasourceChange = async () => {
  // 清空数据库选择
  config.value.database = ''
  databases.value = []

  if (config.value.datasourceId) {
    await loadDatabases()
  }

  validateConfig()
}

const handleDatabaseChange = () => {
  validateConfig()
}

const loadDatabases = async () => {
  if (!config.value.datasourceId) return

  try {
    const response = await datasourceApi.getDatabases(config.value.datasourceId)
    if (response.code === 200) {
      databases.value = response.content || []
    } else {
      console.warn('获取数据库列表失败:', response.msg)
      databases.value = []
    }
  } catch (error) {
    console.error('加载数据库列表失败:', error)
    databases.value = []
  }
}

const handleSqlChange = () => {
  validateConfig()
}

const testConnection = async () => {
  if (!config.value.datasourceId) {
    ElMessage.warning('请先选择数据源')
    return
  }

  testing.value = true
  try {
    const response = await datasourceApi.testConnection(config.value.datasourceId)
    if (response.code === 200) {
      ElMessage.success('数据源连接测试成功')
    } else {
      ElMessage.error(response.msg || '数据源连接测试失败')
    }
  } catch (error) {
    console.error('测试数据源连接失败:', error)
    ElMessage.error('数据源连接测试失败')
  } finally {
    testing.value = false
  }
}

const formatSql = () => {
  // 改进的SQL格式化
  if (config.value.sql) {
    let sql = config.value.sql
      .replace(/\s+/g, ' ') // 合并多个空格
      .trim()

    // 格式化主要关键字
    sql = sql
      .replace(/\bSELECT\b/gi, '\nSELECT')
      .replace(/\bFROM\b/gi, '\nFROM')
      .replace(/\bWHERE\b/gi, '\nWHERE')
      .replace(/\bAND\b/gi, '\n  AND')
      .replace(/\bOR\b/gi, '\n  OR')
      .replace(/\bORDER BY\b/gi, '\nORDER BY')
      .replace(/\bGROUP BY\b/gi, '\nGROUP BY')
      .replace(/\bHAVING\b/gi, '\nHAVING')
      .replace(/\bLIMIT\b/gi, '\nLIMIT')
      .replace(/\bUNION\b/gi, '\nUNION')
      .replace(/\bINNER JOIN\b/gi, '\nINNER JOIN')
      .replace(/\bLEFT JOIN\b/gi, '\nLEFT JOIN')
      .replace(/\bRIGHT JOIN\b/gi, '\nRIGHT JOIN')
      .replace(/\bFULL JOIN\b/gi, '\nFULL JOIN')
      .replace(/,/g, ',\n  ') // 字段分隔

    // 清理多余的换行和空格
    config.value.sql = sql
      .split('\n')
      .map(line => line.trim())
      .filter(line => line.length > 0)
      .join('\n')
      .replace(/\n\s*\n/g, '\n') // 移除空行
  }
}

const validateSql = async () => {
  if (!config.value.sql.trim()) {
    ElMessage.warning('请输入SQL语句')
    return
  }

  if (!config.value.datasourceId) {
    ElMessage.warning('请先选择数据源')
    return
  }

  validating.value = true
  try {
    const response = await datasourceApi.executeQuery(config.value.datasourceId, {
      database: config.value.database,
      sql: `EXPLAIN ${config.value.sql}`,
      maxRows: 1
    })

    if (response.code === 200) {
      ElMessage.success('SQL语法验证通过')
    } else {
      ElMessage.error(response.msg || 'SQL语法验证失败')
    }
  } catch (error) {
    console.error('SQL语法验证失败:', error)
    ElMessage.error('SQL语法验证失败')
  } finally {
    validating.value = false
  }
}

const testSql = async () => {
  if (!config.value.datasourceId || !config.value.sql.trim()) {
    ElMessage.warning('请选择数据源并输入SQL语句')
    return
  }

  testing.value = true
  try {
    const response = await datasourceApi.executeQuery(config.value.datasourceId, {
      database: config.value.database,
      sql: config.value.sql,
      maxRows: config.value.maxRows || 10
    })

    if (response.code === 200) {
      const result = response.content
      sqlResult.value = result
      showResult.value = true
      ElMessage.success(`SQL测试执行成功，返回 ${result.total || 0} 行数据`)
    } else {
      ElMessage.error(response.msg || 'SQL测试执行失败')
    }
  } catch (error) {
    console.error('SQL测试执行失败:', error)
    ElMessage.error('SQL测试执行失败')
  } finally {
    testing.value = false
  }
}

const addInputParam = () => {
  config.value.inputParams.push({
    name: '',
    type: 'STRING',
    defaultValue: '',
    required: false,
    description: ''
  })
}

const removeInputParam = (index: number) => {
  config.value.inputParams.splice(index, 1)
}

const addOutputParam = () => {
  config.value.outputParams.push({
    name: '',
    type: 'STRING',
    source: 'RESULT',
    description: ''
  })
}

const removeOutputParam = (index: number) => {
  config.value.outputParams.splice(index, 1)
}

// 生命周期
onMounted(() => {
  loadDatasources()
  validateConfig()
})
</script>

<style scoped>
.sql-node-config {
  /* 全局增加表单项间距 */
}

.sql-node-config :deep(.el-form-item) {
  margin-bottom: 20px;
}

.sql-node-config .config-section {
  margin-bottom: 16px;
}

.sql-node-config .config-section:last-child {
  margin-bottom: 0;
}

.sql-node-config .section-header {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 500;
  color: var(--el-color-primary);
}

.sql-node-config .section-header .header-actions {
  margin-left: auto;
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.sql-node-config .datasource-option {
  display: flex;
  flex-direction: column;
}

.sql-node-config .datasource-option .ds-name {
  font-weight: 500;
}

.sql-node-config .datasource-option .ds-type {
  font-size: 12px;
  color: var(--el-color-info);
}

.sql-node-config .datasource-option .ds-url {
  font-size: 11px;
  color: var(--el-color-info-light-3);
}

.sql-node-config .sql-editor-container {
  position: relative;
}

.sql-node-config .sql-editor-container .sql-editor :deep(.el-textarea__inner) {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 13px;
  line-height: 1.4;
}

.sql-node-config .sql-editor-container .sql-info {
  display: flex;
  justify-content: space-between;
  margin-top: 8px;
  font-size: 12px;
  color: var(--el-color-info);
}

.sql-node-config .sql-editor-container .sql-result-preview {
  margin-top: 16px;
  border: 1px solid var(--el-border-color);
  border-radius: 6px;
  overflow: hidden;
}

.sql-node-config .sql-editor-container .sql-result-preview .result-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  background: var(--el-fill-color-light);
  border-bottom: 1px solid var(--el-border-color);
  font-size: 13px;
  font-weight: 500;
}

.sql-node-config .sql-editor-container .sql-result-preview .result-content {
  padding: 12px;
}

.sql-node-config .sql-editor-container .sql-result-preview .result-content .result-info {
  display: flex;
  gap: 16px;
  margin-bottom: 12px;
  font-size: 12px;
  color: var(--el-color-info);
}

.sql-node-config .param-section .param-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding: 12px;
  background: var(--el-color-info-light-9);
  border-radius: 4px;
  font-size: 13px;
  color: var(--el-color-info-dark-2);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .sql-node-config .section-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }

  .sql-node-config .section-header .header-actions {
    margin-left: 0;
    width: 100%;
    justify-content: flex-start;
  }

  .sql-node-config .param-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
    text-align: left;
  }

  .sql-node-config .sql-editor :deep(.el-textarea__inner) {
    font-size: 14px;
  }
}

@media (max-width: 480px) {
  .sql-node-config .config-section {
    margin-bottom: 12px;
  }

  /* 移动端表单项间距 */
  .sql-node-config :deep(.el-form-item) {
    margin-bottom: 16px;
  }

  .sql-node-config .section-header .header-actions .el-button {
    font-size: 12px;
    padding: 4px 8px;
  }

  .sql-node-config .param-section .param-header {
    padding: 8px;
    font-size: 12px;
  }
}
</style>
