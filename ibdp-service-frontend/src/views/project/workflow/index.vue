<template>
  <div class="workflow-container">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-left">
        <h2>流程定义</h2>
        <p class="header-desc">管理项目中的流程定义，包括创建、编辑、发布和上线等操作</p>
      </div>
      <div class="header-right">
        <el-button type="primary" @click="handleAdd">
          <el-icon><Plus /></el-icon>
          新建流程
        </el-button>
      </div>
    </div>

    <!-- 搜索筛选 -->
    <div class="search-container">
      <el-form :model="queryForm" inline>
        <el-form-item label="工作流名称">
          <el-input
            v-model="queryForm.name"
            placeholder="请输入工作流名称"
            clearable
            style="width: 200px"
          />
        </el-form-item>
        <el-form-item label="分类">
          <el-select
            v-model="queryForm.category"
            placeholder="请选择分类"
            clearable
            style="width: 150px"
          >
            <el-option
              v-for="item in categoryOptions"
              :key="item.code"
              :label="item.name"
              :value="item.code"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="状态">
          <el-select
            v-model="queryForm.status"
            placeholder="请选择状态"
            clearable
            style="width: 150px"
          >
            <el-option
              v-for="item in statusOptions"
              :key="item.code"
              :label="item.name"
              :value="item.code"
            />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">
            <el-icon><Search /></el-icon>
            搜索
          </el-button>
          <el-button @click="handleReset">
            <el-icon><Refresh /></el-icon>
            重置
          </el-button>
        </el-form-item>
      </el-form>
    </div>

    <!-- 数据表格 -->
    <div class="table-container">
      <el-table
        v-loading="loading"
        :data="tableData"
        stripe
        style="width: 100%"
      >
        <el-table-column prop="name" label="工作流名称" min-width="200">
          <template #default="{ row }">
            <div class="workflow-name">
              <span class="name">{{ row.name }}</span>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="category" label="分类" width="120">
          <template #default="{ row }">
            <el-tag :type="getCategoryTagType(row.category)">
              {{ getCategoryName(row.category) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="status" label="状态" width="120">
          <template #default="{ row }">
            <el-tag :type="getStatusTagType(row.status)">
              {{ getStatusName(row.status) }}
            </el-tag>
          </template>
        </el-table-column>

<!--        <el-table-column prop="tags" label="标签" width="200">
          <template #default="{ row }">
            <div v-if="row.tags" class="tags-container">
              <el-tag
                v-for="tag in row.tags.split(',')"
                :key="tag"
                size="small"
                class="tag-item"
              >
                {{ tag.trim() }}
              </el-tag>
            </div>
            <span v-else class="text-muted">-</span>
          </template>
        </el-table-column>-->

        <!-- 新增：执行统计列 -->
        <el-table-column label="执行统计" width="150">
          <template #default="{ row }">
            <div class="execution-stats">
              <div class="stat-item">
                <span class="stat-label">成功:</span>
                <span class="stat-value success">{{ row.successCount || 0 }}</span>
                <span class="stat-label">失败:</span>
                <span class="stat-value failed">{{ row.failureCount || 0 }}</span>
              </div>
            </div>
          </template>
        </el-table-column>

        <!-- 最后执行时间 -->
        <el-table-column label="最后执行" width="160">
          <template #default="{ row }">
            <span v-if="row.lastTriggerTime" class="execution-time">
              {{ formatDateTime(row.lastTriggerTime) }}
            </span>
            <span v-else class="text-muted">未执行</span>
          </template>
        </el-table-column>

        <el-table-column prop="description" label="描述" min-width="200" show-overflow-tooltip />
        <el-table-column prop="createTime" label="创建时间" width="180">
          <template #default="{ row }">
            {{ formatDateTime(row.createTime) }}
          </template>
        </el-table-column>
        <el-table-column prop="updateTime" label="更新时间" width="180">
          <template #default="{ row }">
            {{ formatDateTime(row.updateTime) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="{ row }">
            <div class="action-buttons">
              <el-button
                link
                type="primary"
                @click="handleView(row)"
                title="查看工作流详情"
              >
                <el-icon><View /></el-icon>
                查看
              </el-button>
              <el-button
                v-if="row.status === 2"
                link
                type="success"
                @click="handleManualExecute(row)"
                title="立即执行一次工作流"
              >
                <el-icon><VideoPlay /></el-icon>
                执行
              </el-button>
              <el-button
                v-if="isEditable(row)"
                link
                type="primary"
                @click="handleEdit(row)"
                title="编辑工作流"
              >
                <el-icon><Edit /></el-icon>
                编辑
              </el-button>

              <el-dropdown
                @command="(command) => handleAction(command, row)"
                class="action-dropdown"
              >
                <el-button link type="primary" class="dropdown-trigger">
                  更多<el-icon class="el-icon--right"><arrow-down /></el-icon>
                </el-button>
                <template #dropdown>
                  <el-dropdown-menu>
                    <el-dropdown-item
                      v-if="isPublishable(row)"
                      command="publish"
                    >
                      发布
                    </el-dropdown-item>
                    <el-dropdown-item
                      v-if="isOnlineable(row)"
                      command="online"
                    >
                      上线
                    </el-dropdown-item>
                    <el-dropdown-item
                      v-if="isOfflineable(row)"
                      command="offline"
                    >
                      下线
                    </el-dropdown-item>
                    <el-dropdown-item
                      v-if="row.status === 2"
                      command="schedule-config"
                      divided
                    >
                      调度配置
                    </el-dropdown-item>

                    <el-dropdown-item
                      v-if="isDeletable(row)"
                      command="delete"
                      divided
                    >
                      删除
                    </el-dropdown-item>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
            </div>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="queryForm.pageNum"
          v-model:page-size="queryForm.pageSize"
          :total="total"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>

    <!-- 工作流表单弹窗 -->
    <WorkflowForm
      v-model:visible="formVisible"
      :form-data="formData"
      :project-id="projectId"
      @success="handleFormSuccess"
    />

    <!-- 调度配置弹窗 -->
    <el-dialog
      v-model="scheduleConfigDialog.visible"
      :title="`调度配置 - ${scheduleConfigDialog.workflowName}`"
      width="600px"
      @close="closeScheduleConfigDialog"
    >
      <el-form
        :model="scheduleConfigForm"
        label-width="120px"
        v-loading="scheduleConfigDialog.loading"
      >
        <el-form-item label="Cron表达式" required>
          <el-input
            v-model="scheduleConfigForm.cronExpression"
            placeholder="请输入Cron表达式，如：0 0 2 * * ?"
          />
          <div class="form-tip">
            示例：0 0 2 * * ? (每天凌晨2点执行)
          </div>
        </el-form-item>

        <el-form-item label="路由策略">
          <el-select v-model="scheduleConfigForm.executorRouteStrategy" style="width: 100%">
            <el-option label="第一个" value="FIRST" />
            <el-option label="最后一个" value="LAST" />
            <el-option label="轮询" value="ROUND" />
            <el-option label="随机" value="RANDOM" />
            <el-option label="一致性HASH" value="CONSISTENT_HASH" />
            <el-option label="最不经常使用" value="LEAST_FREQUENTLY_USED" />
            <el-option label="最近最久未使用" value="LEAST_RECENTLY_USED" />
            <el-option label="故障转移" value="FAILOVER" />
            <el-option label="忙碌转移" value="BUSYOVER" />
            <el-option label="分片广播" value="SHARDING_BROADCAST" />
          </el-select>
        </el-form-item>

        <el-form-item label="阻塞策略">
          <el-select v-model="scheduleConfigForm.executorBlockStrategy" style="width: 100%">
            <el-option label="单机串行" value="SERIAL_EXECUTION" />
            <el-option label="丢弃后续调度" value="DISCARD_LATER" />
            <el-option label="覆盖之前调度" value="COVER_EARLY" />
          </el-select>
        </el-form-item>

        <el-form-item label="超时时间">
          <el-input-number
            v-model="scheduleConfigForm.executorTimeout"
            :min="0"
            :max="86400"
            style="width: 100%"
          />
          <div class="form-tip">单位：秒，0表示不限制</div>
        </el-form-item>

        <el-form-item label="重试次数">
          <el-input-number
            v-model="scheduleConfigForm.executorFailRetryCount"
            :min="0"
            :max="10"
            style="width: 100%"
          />
        </el-form-item>

        <el-form-item label="过期策略">
          <el-select v-model="scheduleConfigForm.misfireStrategy" style="width: 100%">
            <el-option label="忽略" value="DO_NOTHING" />
            <el-option label="立即执行一次" value="FIRE_ONCE_NOW" />
          </el-select>
        </el-form-item>

        <el-form-item label="任务描述">
          <el-input
            v-model="scheduleConfigForm.jobDesc"
            type="textarea"
            :rows="2"
            placeholder="请输入任务描述"
          />
        </el-form-item>

        <el-form-item label="告警邮箱">
          <el-input
            v-model="scheduleConfigForm.alarmEmail"
            placeholder="请输入告警邮箱，多个邮箱用逗号分隔"
          />
        </el-form-item>
      </el-form>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="closeScheduleConfigDialog">取消</el-button>
          <el-button
            type="primary"
            @click="saveScheduleConfig"
            :loading="scheduleConfigDialog.loading"
          >
            保存
          </el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 简化调度配置 -->
    <SimpleScheduleConfig
      v-model:visible="scheduleConfigVisible"
      :workflow="scheduleConfigWorkflow"
      @refresh="loadData"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus, Search, Refresh, ArrowDown, VideoPlay, View, Edit } from '@element-plus/icons-vue'
import { workflowApi } from '@/api/workflow'
import WorkflowForm from './components/WorkflowForm.vue'
import SimpleScheduleConfig from '@/components/workflow/SimpleScheduleConfig.vue'
import type { Workflow, WorkflowQuery, WorkflowCategoryOption, WorkflowStatusOption } from '@/types/workflow'
import { formatDateTime } from '@/utils/date'

const route = useRoute()
const router = useRouter()

// 响应式数据
const loading = ref(false)
const tableData = ref<Workflow[]>([])
const total = ref(0)

// 调度状态缓存
const scheduleStatusMap = ref<Map<number, any>>(new Map())

// 调度配置弹窗
const scheduleConfigDialog = ref({
  visible: false,
  loading: false,
  workflowId: null as number | null,
  workflowName: ''
})

// 调度配置表单
const scheduleConfigForm = ref({
  workflowId: null as number | null,
  cronExpression: '',
  executorRouteStrategy: 'FIRST',
  executorBlockStrategy: 'SERIAL_EXECUTION',
  executorTimeout: 3600,
  executorFailRetryCount: 3,
  misfireStrategy: 'DO_NOTHING',
  jobDesc: '',
  alarmEmail: ''
})
const formVisible = ref(false)
const formData = ref<Workflow | null>(null)
const categoryOptions = ref<WorkflowCategoryOption[]>([])
const statusOptions = ref<WorkflowStatusOption[]>([])

// 简化调度配置
const scheduleConfigVisible = ref(false)
const scheduleConfigWorkflow = ref<Workflow | null>(null)

// 项目ID
const projectId = computed(() => {
  const id = Number(route.params.projectId)
  return isNaN(id) ? 0 : id
})

// 格式化日期时间
const formatDateTime = (dateTime: string) => {
  if (!dateTime) return ''
  const date = new Date(dateTime)
  return date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  })
}

// 查询表单
const queryForm = ref<WorkflowQuery>({
  pageNum: 1,
  pageSize: 10,
  projectId: 0, // 初始值，会在onMounted中更新
  name: '',
  category: '',
  status: undefined
})

// 生命周期
onMounted(() => {
  console.log('工作流页面挂载，项目ID:', projectId.value)
  // 更新查询表单中的项目ID
  queryForm.value.projectId = projectId.value
  loadCategoryOptions()
  loadStatusOptions()
  loadTableData()
})

// 加载分类选项
const loadCategoryOptions = async () => {
  try {
    const response = await workflowApi.getCategoryList()
    if (response.code === 200) {
      categoryOptions.value = response.content
    }
  } catch (error) {
    console.error('加载分类选项失败:', error)
  }
}

// 加载状态选项
const loadStatusOptions = async () => {
  try {
    const response = await workflowApi.getStatusList()
    if (response.code === 200) {
      statusOptions.value = response.content
    }
  } catch (error) {
    console.error('加载状态选项失败:', error)
  }
}

// 加载表格数据
const loadTableData = async () => {
  if (!queryForm.value.projectId) {
    console.warn('项目ID为空，跳过加载数据')
    return
  }

  loading.value = true
  try {
    const response = await workflowApi.pageList(queryForm.value)
    console.log('API响应:', response)
    if (response.code === 200) {
      const pageData = response.content
      tableData.value = pageData.data || []
      total.value = pageData.recordsTotal || 0

      // 加载已上线工作流的调度状态
      await loadScheduleStatus()
    } else {
      ElMessage.error(response.msg || '加载数据失败')
      tableData.value = []
      total.value = 0
    }
  } catch (error) {
    console.error('加载数据失败:', error)
    ElMessage.error('加载数据失败')
    tableData.value = []
    total.value = 0
  } finally {
    loading.value = false
  }
}

// 加载调度状态
const loadScheduleStatus = async () => {
  // 只为已上线的工作流获取调度状态
  const onlineWorkflows = tableData.value.filter(item => item.status === 2)

  for (const workflow of onlineWorkflows) {
    try {
      const response = await workflowApi.getScheduleStatus(workflow.id!)
      if (response.code === 200) {
        scheduleStatusMap.value.set(workflow.id!, response.content)
      } else {
        // 设置异常状态
        scheduleStatusMap.value.set(workflow.id!, {
          scheduleStatus: -1,
          scheduleStatusName: '获取失败'
        })
      }
    } catch (error) {
      console.error(`获取工作流${workflow.id}调度状态失败:`, error)
      // 设置异常状态
      scheduleStatusMap.value.set(workflow.id!, {
        scheduleStatus: -1,
        scheduleStatusName: '网络错误'
      })
    }
  }
}

// 获取工作流的调度状态
const getScheduleStatus = (workflowId: number) => {
  return scheduleStatusMap.value.get(workflowId)
}

// 判断调度是否正在运行
const isScheduleRunning = (workflowId: number) => {
  const status = getScheduleStatus(workflowId)
  return status?.scheduleStatus === 1
}

// 获取调度状态显示名称
const getScheduleStatusName = (workflowId: number) => {
  const status = getScheduleStatus(workflowId)
  if (!status) return '未知'

  switch (status.scheduleStatus) {
    case 1:
      return '运行中'
    case 0:
      return '已停止'
    case -1:
      return '异常'
    default:
      return '未知'
  }
}

// 获取调度状态标签类型
const getScheduleStatusTagType = (workflowId: number) => {
  const status = getScheduleStatus(workflowId)
  if (!status) return 'info'

  switch (status.scheduleStatus) {
    case 1:
      return 'success'  // 运行中 - 绿色
    case 0:
      return 'info'     // 已停止 - 灰色
    case -1:
      return 'danger'   // 异常 - 红色
    default:
      return 'warning'  // 未知 - 橙色
  }
}

// 搜索
const handleSearch = () => {
  queryForm.value.pageNum = 1
  loadTableData()
}

// 重置
const handleReset = () => {
  queryForm.value.name = ''
  queryForm.value.category = ''
  queryForm.value.status = undefined
  queryForm.value.pageNum = 1
  loadTableData()
}

// 分页大小改变
const handleSizeChange = (size: number) => {
  queryForm.value.pageSize = size
  queryForm.value.pageNum = 1
  loadTableData()
}

// 当前页改变
const handleCurrentChange = (page: number) => {
  queryForm.value.pageNum = page
  loadTableData()
}

// 新增
const handleAdd = () => {
  formData.value = null
  formVisible.value = true
}

// 编辑
const handleEdit = (row: Workflow) => {
  // 检查是否可以编辑
  if (!isEditable(row)) {
    ElMessage.warning('已上线的工作流不允许编辑，请先下线后再编辑')
    return
  }

  // 跳转到工作流设计页面
  router.push(`/project/${projectId.value}/workflow/${row.id}/design`)
}

// 查看
const handleView = (row: Workflow) => {
  // 跳转到工作流设计页面（只读模式）
  router.push({
    path: `/project/${projectId.value}/workflow/${row.id}/design`,
    query: { mode: 'view' }
  })
}

// 操作处理
const handleAction = async (command: string, row: Workflow) => {
  switch (command) {
    case 'publish':
      await handlePublish(row)
      break
    case 'online':
      await handleOnline(row)
      break
    case 'offline':
      await handleOffline(row)
      break
    case 'delete':
      await handleDelete(row)
      break
    case 'schedule-config':
      await handleScheduleConfig(row)
      break
    case 'start-schedule':
      await handleStartSchedule(row)
      break
    case 'stop-schedule':
      await handleStopSchedule(row)
      break

  }
}

// 发布
const handlePublish = async (row: Workflow) => {
  try {
    await ElMessageBox.confirm('确认发布此工作流吗？', '提示', {
      type: 'warning'
    })
    
    const response = await workflowApi.publish(row.id!)
    if (response.code === 200) {
      ElMessage.success('发布成功')
      loadTableData()
    } else {
      ElMessage.error(response.msg || '发布失败')
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('发布失败:', error)
      ElMessage.error('发布失败')
    }
  }
}

// 上线
const handleOnline = async (row: Workflow) => {
  try {
    await ElMessageBox.confirm('确认上线此工作流吗？上线后可以被调度执行。', '提示', {
      type: 'warning'
    })
    
    const response = await workflowApi.online(row.id!)
    if (response.code === 200) {
      ElMessage.success('上线成功')
      loadTableData()
    } else {
      ElMessage.error(response.msg || '上线失败')
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('上线失败:', error)
      ElMessage.error('上线失败')
    }
  }
}

// 下线
const handleOffline = async (row: Workflow) => {
  try {
    // 1. 先检查下线安全性
    const safetyCheck = await workflowApi.checkOfflineSafety(row.id!)
    if (safetyCheck.code !== 200) {
      ElMessage.error(safetyCheck.msg || '检查下线安全性失败')
      return
    }

    const safetyInfo = safetyCheck.content
    let confirmMessage = '确认下线此工作流吗？下线后不能被调度执行。'
    let confirmType: 'warning' | 'error' = 'warning'

    // 2. 根据检查结果生成提示信息
    if (safetyInfo.hasWarnings && safetyInfo.warnings.length > 0) {
      confirmMessage = `下线工作流"${row.name}"将产生以下影响：\n\n`
      safetyInfo.warnings.forEach((warning: string, index: number) => {
        confirmMessage += `${index + 1}. ${warning}\n`
      })
      confirmMessage += '\n确定要继续下线吗？'
      confirmType = 'error'
    }

    // 3. 显示确认对话框
    await ElMessageBox.confirm(confirmMessage, '下线确认', {
      type: confirmType,
      confirmButtonText: '确定下线',
      cancelButtonText: '取消',
      dangerouslyUseHTMLString: false
    })

    // 4. 执行下线操作
    const response = await workflowApi.offline(row.id!)
    if (response.code === 200) {
      ElMessage.success('下线成功')
      await loadTableData()
      await loadScheduleStatus() // 刷新调度状态
    } else {
      ElMessage.error(response.msg || '下线失败')
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('下线失败:', error)
      ElMessage.error('下线失败')
    }
  }
}

// 删除
const handleDelete = async (row: Workflow) => {
  try {
    await ElMessageBox.confirm('确认删除此工作流吗？删除后无法恢复。', '提示', {
      type: 'warning'
    })
    
    const response = await workflowApi.delete(row.id!)
    if (response.code === 200) {
      ElMessage.success('删除成功')
      loadTableData()
    } else {
      ElMessage.error(response.msg || '删除失败')
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除失败:', error)
      ElMessage.error('删除失败')
    }
  }
}

// 表单成功回调
const handleFormSuccess = () => {
  formVisible.value = false
  loadTableData()
}

// 获取分类名称
const getCategoryName = (category: string) => {
  const option = categoryOptions.value.find(item => item.code === category)
  return option?.name || category
}

// 获取状态名称
const getStatusName = (status: number) => {
  const option = statusOptions.value.find(item => item.code === status)
  return option?.name || status
}

// 获取分类标签类型
const getCategoryTagType = (category: string) => {
  const typeMap: Record<string, string> = {
    'ETL': 'primary',
    'REPORT': 'success',
    'ANALYSIS': 'warning',
    'SYNC': 'info'
  }
  return typeMap[category] || ''
}

// 获取状态标签类型
const getStatusTagType = (status: number) => {
  const typeMap: Record<number, string> = {
    0: 'info',      // 草稿
    1: 'warning',   // 已发布
    2: 'success',   // 已上线
    3: 'danger'     // 已下线
  }
  return typeMap[status] || ''
}

// 权限判断方法
const isEditable = (row: Workflow) => {
  // 已上线的工作流不允许编辑
  return row.status !== 2
}

const isDeletable = (row: Workflow) => {
  // 只有草稿状态的工作流可以删除
  return row.status === 0
}

const isPublishable = (row: Workflow) => {
  // 只有草稿状态的工作流可以发布
  return row.status === 0
}

const isOnlineable = (row: Workflow) => {
  // 已发布状态和已下线状态的工作流可以上线
  return row.status === 1 || row.status === 3
}

const isOfflineable = (row: Workflow) => {
  // 只有已上线状态的工作流可以下线
  return row.status === 2
}

// 调度配置（简化版）
const handleScheduleConfig = async (row: Workflow) => {
  scheduleConfigWorkflow.value = row
  scheduleConfigVisible.value = true
}

// 手动执行工作流
const handleManualExecute = async (row: Workflow) => {
  try {
    await ElMessageBox.confirm(
      `确定要立即执行工作流"${row.name}"吗？\n\n此操作将立即触发一次工作流执行，不会影响定时调度。`,
      '确认执行',
      {
        type: 'warning',
        confirmButtonText: '立即执行',
        cancelButtonText: '取消',
        dangerouslyUseHTMLString: false
      }
    )

    // 显示执行中的加载提示
    const loadingMessage = ElMessage({
      message: '正在触发工作流执行...',
      type: 'info',
      duration: 0
    })

    const response = await workflowApi.execute(row.id!, 'admin')
    loadingMessage.close()

    if (response.code === 200) {
      const responseContent = response.content

      // 从响应消息中提取执行ID
      let executionId = responseContent
      const executionIdMatch = responseContent.match(/执行ID[：:]\s*([a-f0-9-]{36})/i)
      if (executionIdMatch) {
        executionId = executionIdMatch[1]
      }

      ElMessage.success(`工作流执行已启动！\n执行ID: ${executionId}`)

      // 询问是否跳转到监控页面
      try {
        await ElMessageBox.confirm(
          '是否跳转到监控页面查看执行进度？',
          '执行成功',
          {
            type: 'success',
            confirmButtonText: '查看监控',
            cancelButtonText: '留在当前页'
          }
        )

        // 跳转到监控页面
        router.push(`/project/${projectId.value}/workflow-instance/monitor/${executionId}`)
      } catch {
        // 用户选择留在当前页，刷新列表
        await loadData()
      }
    } else {
      ElMessage.error(response.msg || '执行失败')
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('手动执行工作流失败:', error)
      ElMessage.error('执行失败，请稍后重试')
    }
  }
}

// 保存调度配置
const saveScheduleConfig = async () => {
  try {
    scheduleConfigDialog.value.loading = true

    const response = await workflowApi.updateScheduleConfig(scheduleConfigForm.value)
    if (response.code === 200) {
      ElMessage.success('调度配置更新成功')
      scheduleConfigDialog.value.visible = false
    } else {
      ElMessage.error(response.msg || '更新调度配置失败')
    }
  } catch (error) {
    console.error('更新调度配置失败:', error)
    ElMessage.error('更新调度配置失败')
  } finally {
    scheduleConfigDialog.value.loading = false
  }
}

// 关闭调度配置弹窗
const closeScheduleConfigDialog = () => {
  scheduleConfigDialog.value.visible = false
  scheduleConfigDialog.value.workflowId = null
  scheduleConfigDialog.value.workflowName = ''
  // 重置表单
  Object.assign(scheduleConfigForm.value, {
    workflowId: null,
    cronExpression: '',
    executorRouteStrategy: 'FIRST',
    executorBlockStrategy: 'SERIAL_EXECUTION',
    executorTimeout: 3600,
    executorFailRetryCount: 3,
    misfireStrategy: 'DO_NOTHING',
    jobDesc: '',
    alarmEmail: ''
  })
}

// 启动调度
const handleStartSchedule = async (row: Workflow) => {
  try {
    await ElMessageBox.confirm(
      `确定要启动工作流"${row.name}"的调度吗？`,
      '确认启动',
      {
        type: 'warning',
        confirmButtonText: '确定',
        cancelButtonText: '取消'
      }
    )

    const response = await workflowApi.startSchedule(row.id!)
    if (response.code === 200) {
      ElMessage.success('调度启动成功')
      await loadScheduleStatus() // 刷新调度状态
    } else {
      ElMessage.error(response.msg || '启动调度失败')
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('启动调度失败:', error)
      ElMessage.error('启动调度失败')
    }
  }
}

// 停止调度
const handleStopSchedule = async (row: Workflow) => {
  try {
    await ElMessageBox.confirm(
      `确定要停止工作流"${row.name}"的调度吗？`,
      '确认停止',
      {
        type: 'warning',
        confirmButtonText: '确定',
        cancelButtonText: '取消'
      }
    )

    const response = await workflowApi.stopSchedule(row.id!)
    if (response.code === 200) {
      ElMessage.success('调度停止成功')
      await loadScheduleStatus() // 刷新调度状态
    } else {
      ElMessage.error(response.msg || '停止调度失败')
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('停止调度失败:', error)
      ElMessage.error('停止调度失败')
    }
  }
}


</script>

<style scoped>
.workflow-container {
  padding: 20px;
  background: #f5f5f5;
  min-height: calc(100vh - 60px);
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 20px;
  padding: 20px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.page-header .header-left h2 {
  margin: 0 0 8px 0;
  color: #303133;
  font-size: 20px;
  font-weight: 600;
}

.page-header .header-left .header-desc {
  margin: 0;
  color: #909399;
  font-size: 14px;
}

.search-container {
  margin-bottom: 20px;
  padding: 20px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.table-container {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  padding: 20px;
}

.workflow-name .name {
  display: block;
  font-weight: 500;
  color: #303133;
}

.workflow-name .code {
  display: block;
  font-size: 12px;
  color: #909399;
  margin-top: 2px;
}

/* 新增：执行统计样式 */
.execution-stats {
  display: flex;
  flex-direction: column;
  gap: 4px;
  font-size: 12px;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 4px;
}

.stat-label {
  color: #909399;
  font-size: 11px;
}

.stat-value {
  font-weight: 500;
  color: #303133;
}

.stat-value.success {
  color: #67c23a;
}

.stat-value.failed {
  color: #f56c6c;
}

/* 最后执行时间样式 */
.execution-time {
  color: #606266;
}

.tags-container .tag-item {
  margin-right: 4px;
  margin-bottom: 2px;
}

.text-muted {
  color: #c0c4cc;
}

/* 表格中的操作按钮容器 */
.table-container .action-buttons {
  display: flex;
  align-items: center;
  gap: 4px;
  flex-wrap: wrap;
}

.action-dropdown {
  display: inline-flex;
  align-items: center;
}

.dropdown-trigger {
  display: inline-flex;
  align-items: center;
  height: 24px;
  line-height: 24px;
}

/* 确保表格中的操作按钮有一致的样式 */
:deep(.table-container .action-buttons .el-button) {
  height: 24px;
  line-height: 24px;
  padding: 0 2px;
  margin: 0;
  font-size: 12px;
}

:deep(.table-container .action-dropdown .el-button) {
  height: 24px;
  line-height: 24px;
  padding: 0 2px;
  margin: 0;
  font-size: 12px;
}

/* 调度配置弹窗样式 */
.form-tip {
  font-size: 12px;
  color: #909399;
  margin-top: 4px;
}

.dialog-footer {
  text-align: right;
}

/* 文本静音样式 */
.text-muted {
  color: #c0c4cc;
  font-size: 14px;
}

.pagination-container {
  display: flex;
  justify-content: flex-end;
  margin-top: 20px;
}
</style>
