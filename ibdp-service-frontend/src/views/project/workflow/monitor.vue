<template>
  <div class="workflow-monitor">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-left">
        <h2>流程执行监控</h2>
        <p class="header-desc">实时监控流程执行状态和进度</p>
      </div>
      <div class="header-right">
        <el-button @click="goBack">
          <el-icon><ArrowLeft /></el-icon>
          返回列表
        </el-button>
      </div>
    </div>

    <!-- 执行概览 -->
    <div class="execution-overview" v-if="execution">
      <div class="overview-card">
        <div class="card-header">
          <h3>{{ execution.workflowName }}</h3>
          <div class="execution-actions">
            <el-button
              v-if="execution.status === WorkflowInstanceStatus.RUNNING"
              type="danger"
              size="small"
              @click="handleTerminate">
              终止执行
            </el-button>
            <el-button 
              type="primary" 
              size="small"
              @click="refreshData">
              <el-icon><Refresh /></el-icon>
              刷新
            </el-button>
          </div>
        </div>
        
        <div class="overview-content">
          <div class="overview-item">
            <span class="label">执行ID:</span>
            <span class="value execution-id">{{ execution.executionId }}</span>
          </div>
          <div class="overview-item">
            <span class="label">状态:</span>
            <el-tag :type="getExecutionStateType(execution.status)" size="large">
              {{ getExecutionStateName(execution.status) }}
            </el-tag>
          </div>
          <div class="overview-item">
            <span class="label">进度:</span>
            <div class="progress-container">
              <el-progress
                :percentage="execution.progress"
                :status="getProgressStatus(execution.status)"
                :stroke-width="8" />
              <span class="progress-text">
                {{ execution.completedNodes }}/{{ execution.totalNodes }} 节点完成
              </span>
            </div>
          </div>
          <div class="overview-item">
            <span class="label">执行时间:</span>
            <span class="value">{{ formatDateTime(execution.startTime) }}</span>
          </div>
          <div class="overview-item" v-if="execution.duration">
            <span class="label">耗时:</span>
            <span class="value">{{ formatDuration(execution.duration) }}</span>
          </div>
          <div class="overview-item">
            <span class="label">执行用户:</span>
            <span class="value">{{ execution.executeUser }}</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 节点执行状态 -->
    <div class="nodes-section">
      <div class="section-header">
        <h3>节点执行状态</h3>
        <div class="legend">
          <span class="legend-item">
            <el-icon class="waiting"><Clock /></el-icon>
            等待中
          </span>
          <span class="legend-item">
            <el-icon class="running"><Loading /></el-icon>
            运行中
          </span>
          <span class="legend-item">
            <el-icon class="success"><Check /></el-icon>
            成功
          </span>
          <span class="legend-item">
            <el-icon class="failed"><Close /></el-icon>
            失败
          </span>
        </div>
      </div>

      <div class="nodes-grid" v-if="nodes.length > 0">
        <div 
          v-for="node in nodes" 
          :key="node.nodeCode"
          class="node-card"
          :class="getNodeStateClass(node.status)"
          @click="showNodeDetail(node)">

          <div class="node-header">
            <div class="node-icon">
              <el-icon v-if="node.status === NodeExecutionState.WAITING"><Clock /></el-icon>
              <el-icon v-else-if="node.status === NodeExecutionState.RUNNING" class="rotating"><Loading /></el-icon>
              <el-icon v-else-if="node.status === NodeExecutionState.SUCCESS"><Check /></el-icon>
              <el-icon v-else-if="node.status === NodeExecutionState.FAILED"><Close /></el-icon>
              <el-icon v-else><QuestionFilled /></el-icon>
            </div>
            <div class="node-type">{{ node.nodeType }}</div>
          </div>
          
          <div class="node-content">
            <div class="node-name">{{ node.nodeName }}</div>
            <div class="node-code">{{ node.nodeCode }}</div>
            
            <div class="node-info" v-if="node.startTime">
              <div class="info-item">
                <span class="info-label">开始:</span>
                <span class="info-value">{{ formatTime(node.startTime) }}</span>
              </div>
              <div class="info-item" v-if="node.duration">
                <span class="info-label">耗时:</span>
                <span class="info-value">{{ formatDuration(node.duration) }}</span>
              </div>
            </div>
            
            <div class="node-actions" v-if="node.status === NodeExecutionState.FAILED">
              <el-button 
                type="primary" 
                size="small"
                @click.stop="retryNode(node)">
                重试
              </el-button>
            </div>
          </div>
        </div>
      </div>

      <div v-else class="empty-state">
        <el-empty description="暂无节点数据" />
      </div>
    </div>

    <!-- 节点详情对话框 -->
    <el-dialog 
      v-model="nodeDetailVisible" 
      :title="`节点详情 - ${selectedNode?.nodeName}`"
      width="60%">
      
      <div v-if="selectedNode" class="node-detail">
        <div class="detail-section">
          <h4>基本信息</h4>
          <div class="detail-grid">
            <div class="detail-item">
              <span class="detail-label">节点编码:</span>
              <span class="detail-value">{{ selectedNode.nodeCode }}</span>
            </div>
            <div class="detail-item">
              <span class="detail-label">节点类型:</span>
              <span class="detail-value">{{ selectedNode.nodeType }}</span>
            </div>
            <div class="detail-item">
              <span class="detail-label">执行状态:</span>
              <el-tag :type="getNodeStateType(selectedNode.status)">
                {{ getNodeStateName(selectedNode.status) }}
              </el-tag>
            </div>
            <div class="detail-item" v-if="selectedNode.executorAddress">
              <span class="detail-label">执行器地址:</span>
              <span class="detail-value">{{ selectedNode.executorAddress }}</span>
            </div>
          </div>
        </div>

        <div class="detail-section" v-if="selectedNode.startTime">
          <h4>执行信息</h4>
          <div class="detail-grid">
            <div class="detail-item">
              <span class="detail-label">开始时间:</span>
              <span class="detail-value">{{ formatDateTime(selectedNode.startTime) }}</span>
            </div>
            <div class="detail-item" v-if="selectedNode.endTime">
              <span class="detail-label">结束时间:</span>
              <span class="detail-value">{{ formatDateTime(selectedNode.endTime) }}</span>
            </div>
            <div class="detail-item" v-if="selectedNode.duration">
              <span class="detail-label">执行耗时:</span>
              <span class="detail-value">{{ formatDuration(selectedNode.duration) }}</span>
            </div>
          </div>
        </div>

        <div class="detail-section" v-if="selectedNode.errorMessage">
          <h4>错误信息</h4>
          <div class="error-message">
            {{ selectedNode.errorMessage }}
          </div>
        </div>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="nodeDetailVisible = false">关闭</el-button>
          <el-button 
            v-if="selectedNode?.state === NodeExecutionState.FAILED"
            type="primary"
            @click="retryNode(selectedNode)">
            重试节点
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { 
  ArrowLeft, 
  Refresh, 
  Clock, 
  Loading, 
  Check, 
  Close, 
  QuestionFilled 
} from '@element-plus/icons-vue'
import { workflowApi } from '@/api/workflow'
import type { 
  WorkflowExecution, 
  NodeExecution 
} from '@/types/workflow'
import {
  WorkflowExecutionState,
  WorkflowInstanceStatus,
  NodeExecutionState
} from '@/types/workflow'

const route = useRoute()
const router = useRouter()

// 响应式数据
const execution = ref<WorkflowExecution | null>(null)
const nodes = ref<NodeExecution[]>([])
const nodeDetailVisible = ref(false)
const selectedNode = ref<NodeExecution | null>(null)
const refreshTimer = ref<number | null>(null)

// 获取执行ID
const executionId = route.params.executionId as string

// 页面挂载时开始监控
onMounted(() => {
  loadExecutionData()
  startAutoRefresh()
})

// 页面卸载时清理定时器
onUnmounted(() => {
  stopAutoRefresh()
})

// 加载执行数据
const loadExecutionData = async () => {
  try {
    const response = await workflowApi.getExecution(executionId)
    if (response.code === 200) {
      // 兼容不同的响应格式
      const responseData = response.content || response.data

      if (responseData) {
        // 如果有execution字段，使用它；否则直接使用responseData作为execution
        execution.value = responseData.execution || responseData
        nodes.value = responseData.nodes || []

        console.log('加载执行数据成功:', {
          executionId: execution.value?.executionId,
          status: execution.value?.status,
          nodesCount: nodes.value.length
        })
      } else {
        console.error('响应数据为空:', response)
        ElMessage.error('响应数据格式错误')
      }
    } else {
      console.error('API响应错误:', response)
      ElMessage.error(response.msg || '加载执行数据失败')
    }
  } catch (error) {
    console.error('加载执行数据失败:', error)
    ElMessage.error('加载执行数据失败')
  }
}

// 开始自动刷新
const startAutoRefresh = () => {
  refreshTimer.value = window.setInterval(() => {
    loadExecutionData()
  }, 3000) // 每3秒刷新一次
}

// 停止自动刷新
const stopAutoRefresh = () => {
  if (refreshTimer.value) {
    clearInterval(refreshTimer.value)
    refreshTimer.value = null
  }
}

// 手动刷新
const refreshData = () => {
  loadExecutionData()
  ElMessage.success('数据已刷新')
}

// 返回列表
const goBack = () => {
  const projectId = route.params.projectId
  router.push(`/project/${projectId}/workflow-instance`)
}

// 终止执行
const handleTerminate = async () => {
  try {
    await ElMessageBox.confirm(
      '确定要终止当前工作流执行吗？此操作不可撤销。',
      '确认终止',
      {
        type: 'warning',
        confirmButtonText: '确定终止',
        cancelButtonText: '取消'
      }
    )

    const response = await workflowApi.terminateExecution(executionId, '用户手动终止')
    if (response.code === 200) {
      ElMessage.success('工作流执行已终止')
      await loadExecutionData()
    } else {
      ElMessage.error(response.msg || '终止失败')
    }
  } catch (error) {
    // 用户取消操作
  }
}

// 重试节点
const retryNode = async (node: NodeExecution) => {
  try {
    await ElMessageBox.confirm(
      `确定要重试节点 "${node.nodeName}" 吗？`,
      '确认重试',
      {
        type: 'warning',
        confirmButtonText: '确定重试',
        cancelButtonText: '取消'
      }
    )

    const response = await workflowApi.retryFailedNode(executionId, node.nodeCode)
    if (response.code === 200) {
      ElMessage.success('节点重试已启动')
      nodeDetailVisible.value = false
      await loadExecutionData()
    } else {
      ElMessage.error(response.msg || '重试失败')
    }
  } catch (error) {
    // 用户取消操作
  }
}

// 显示节点详情
const showNodeDetail = (node: NodeExecution) => {
  selectedNode.value = node
  nodeDetailVisible.value = true
}

// 获取执行状态类型
const getExecutionStateType = (state: number) => {
  switch (state) {
    case WorkflowInstanceStatus.RUNNING: return 'primary'
    case WorkflowInstanceStatus.SUCCESS: return 'success'
    case WorkflowInstanceStatus.FAILURE: return 'danger'
    case WorkflowInstanceStatus.PAUSE: return 'warning'
    case WorkflowInstanceStatus.STOP: return 'warning'
    default: return 'info'
  }
}

// 获取执行状态名称
const getExecutionStateName = (state: number) => {
  switch (state) {
    case WorkflowInstanceStatus.SUBMIT_SUCCESS: return '提交成功'
    case WorkflowInstanceStatus.RUNNING: return '运行中'
    case WorkflowInstanceStatus.READY_PAUSE: return '准备暂停'
    case WorkflowInstanceStatus.PAUSE: return '已暂停'
    case WorkflowInstanceStatus.READY_STOP: return '准备停止'
    case WorkflowInstanceStatus.STOP: return '已停止'
    case WorkflowInstanceStatus.FAILURE: return '执行失败'
    case WorkflowInstanceStatus.SUCCESS: return '执行成功'
    default: return '未知状态'
  }
}

// 获取进度条状态
const getProgressStatus = (state: number) => {
  switch (state) {
    case WorkflowInstanceStatus.SUCCESS: return 'success'
    case WorkflowInstanceStatus.FAILURE: return 'exception'
    case WorkflowInstanceStatus.STOP: return 'warning'
    default: return undefined
  }
}

// 获取节点状态类型
const getNodeStateType = (state: number) => {
  switch (state) {
    case NodeExecutionState.RUNNING: return 'primary'
    case NodeExecutionState.SUCCESS: return 'success'
    case NodeExecutionState.FAILED: return 'danger'
    case NodeExecutionState.TERMINATED: return 'warning'
    default: return 'info'
  }
}

// 获取节点状态名称
const getNodeStateName = (state: number) => {
  switch (state) {
    case NodeExecutionState.WAITING: return '等待中'
    case NodeExecutionState.RUNNING: return '运行中'
    case NodeExecutionState.SUCCESS: return '成功'
    case NodeExecutionState.FAILED: return '失败'
    case NodeExecutionState.SKIPPED: return '跳过'
    case NodeExecutionState.TERMINATED: return '已终止'
    default: return '未知'
  }
}

// 获取节点状态样式类
const getNodeStateClass = (state: number) => {
  switch (state) {
    case NodeExecutionState.WAITING: return 'node-waiting'
    case NodeExecutionState.RUNNING: return 'node-running'
    case NodeExecutionState.SUCCESS: return 'node-success'
    case NodeExecutionState.FAILED: return 'node-failed'
    case NodeExecutionState.TERMINATED: return 'node-terminated'
    default: return 'node-unknown'
  }
}

// 格式化日期时间
const formatDateTime = (dateTime: string) => {
  if (!dateTime) return ''
  const date = new Date(dateTime)
  return date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit'
  })
}

// 格式化时间（只显示时分秒）
const formatTime = (dateTime: string) => {
  if (!dateTime) return ''
  const date = new Date(dateTime)
  return date.toLocaleTimeString('zh-CN')
}

// 格式化持续时间
const formatDuration = (duration: number) => {
  if (!duration) return ''
  
  const seconds = Math.floor(duration / 1000)
  const minutes = Math.floor(seconds / 60)
  const hours = Math.floor(minutes / 60)
  
  if (hours > 0) {
    return `${hours}小时${minutes % 60}分${seconds % 60}秒`
  } else if (minutes > 0) {
    return `${minutes}分${seconds % 60}秒`
  } else {
    return `${seconds}秒`
  }
}
</script>

<style scoped>
.workflow-monitor {
  padding: 20px;
  background: #f5f5f5;
  min-height: calc(100vh - 60px);
}

/* 页面头部 */
.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding: 20px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.header-left h2 {
  margin: 0 0 8px 0;
  color: #303133;
  font-size: 20px;
  font-weight: 600;
}

.header-desc {
  margin: 0;
  color: #909399;
  font-size: 14px;
}

/* 执行概览 */
.execution-overview {
  margin-bottom: 20px;
}

.overview-card {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  border-bottom: 1px solid #ebeef5;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.card-header h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
}

.overview-content {
  padding: 20px;
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
}

.overview-item {
  display: flex;
  align-items: center;
  gap: 12px;
}

.overview-item .label {
  font-weight: 500;
  color: #606266;
  min-width: 80px;
}

.overview-item .value {
  color: #303133;
}

.execution-id {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  background: #f5f7fa;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
}

.progress-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.progress-text {
  font-size: 12px;
  color: #909399;
}

/* 节点部分 */
.nodes-section {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  border-bottom: 1px solid #ebeef5;
}

.section-header h3 {
  margin: 0;
  color: #303133;
  font-size: 16px;
  font-weight: 600;
}

.legend {
  display: flex;
  gap: 20px;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
  color: #606266;
}

.legend-item .el-icon {
  font-size: 14px;
}

.legend-item .waiting { color: #909399; }
.legend-item .running { color: #409eff; }
.legend-item .success { color: #67c23a; }
.legend-item .failed { color: #f56c6c; }

/* 节点网格 */
.nodes-grid {
  padding: 20px;
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 16px;
}

.node-card {
  border: 2px solid #ebeef5;
  border-radius: 8px;
  padding: 16px;
  cursor: pointer;
  transition: all 0.3s ease;
  background: white;
}

.node-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.node-card.node-waiting {
  border-color: #d3d4d6;
}

.node-card.node-running {
  border-color: #409eff;
  background: linear-gradient(135deg, #e3f2fd 0%, #f3e5f5 100%);
}

.node-card.node-success {
  border-color: #67c23a;
  background: linear-gradient(135deg, #f1f8e9 0%, #e8f5e8 100%);
}

.node-card.node-failed {
  border-color: #f56c6c;
  background: linear-gradient(135deg, #ffebee 0%, #fce4ec 100%);
}

.node-card.node-terminated {
  border-color: #e6a23c;
  background: linear-gradient(135deg, #fff8e1 0%, #fef7e0 100%);
}

.node-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.node-icon {
  font-size: 20px;
}

.node-icon .waiting { color: #909399; }
.node-icon .running { color: #409eff; }
.node-icon .success { color: #67c23a; }
.node-icon .failed { color: #f56c6c; }

.rotating {
  animation: rotate 1s linear infinite;
}

@keyframes rotate {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.node-type {
  background: #f5f7fa;
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 11px;
  color: #606266;
  font-weight: 500;
}

.node-content {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.node-name {
  font-weight: 500;
  color: #303133;
  font-size: 14px;
}

.node-code {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 11px;
  color: #909399;
}

.node-info {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.info-item {
  display: flex;
  justify-content: space-between;
  font-size: 11px;
}

.info-label {
  color: #909399;
}

.info-value {
  color: #606266;
  font-weight: 500;
}

.node-actions {
  margin-top: 8px;
}

/* 空状态 */
.empty-state {
  padding: 40px;
  text-align: center;
}

/* 节点详情对话框 */
.node-detail {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.detail-section h4 {
  margin: 0 0 12px 0;
  color: #303133;
  font-size: 14px;
  font-weight: 600;
  border-bottom: 1px solid #ebeef5;
  padding-bottom: 8px;
}

.detail-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 12px;
}

.detail-item {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.detail-label {
  font-size: 12px;
  color: #909399;
  font-weight: 500;
}

.detail-value {
  color: #303133;
  font-size: 14px;
}

.error-message {
  background: #fef0f0;
  border: 1px solid #fbc4c4;
  border-radius: 4px;
  padding: 12px;
  color: #f56c6c;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 12px;
  white-space: pre-wrap;
  word-break: break-all;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .workflow-monitor {
    padding: 10px;
  }

  .page-header {
    flex-direction: column;
    gap: 16px;
    align-items: flex-start;
  }

  .overview-content {
    grid-template-columns: 1fr;
  }

  .nodes-grid {
    grid-template-columns: 1fr;
    padding: 10px;
  }

  .legend {
    flex-wrap: wrap;
    gap: 12px;
  }
}
</style>
