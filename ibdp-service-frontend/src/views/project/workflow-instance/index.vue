<template>
  <div class="workflow-instance-container">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-left">
        <h2>流程实例</h2>
        <p class="page-description">查看和管理流程的执行实例</p>
      </div>
      <div class="header-right">
        <el-button @click="handleRefresh" :loading="loading">
          <el-icon><Refresh /></el-icon>
          刷新
        </el-button>
      </div>
    </div>

    <!-- 搜索条件 -->
    <div class="search-container">
      <el-form :model="searchForm" inline>
        <el-form-item label="实例名称">
          <el-input
            v-model="searchForm.workflowName"
            placeholder="请输入实例名称"
            clearable
            style="width: 200px"
          />
        </el-form-item>
        <el-form-item label="实例状态">
          <el-select
            v-model="searchForm.status"
            placeholder="请选择状态"
            clearable
            style="width: 150px"
          >
            <el-option label="提交成功" :value="0" />
            <el-option label="正在运行" :value="1" />
            <el-option label="准备暂停" :value="2" />
            <el-option label="暂停" :value="3" />
            <el-option label="准备停止" :value="4" />
            <el-option label="停止" :value="5" />
            <el-option label="失败" :value="6" />
            <el-option label="成功" :value="7" />
            <el-option label="需要容错" :value="8" />
            <el-option label="kill" :value="9" />
            <el-option label="等待线程" :value="10" />
            <el-option label="等待依赖完成" :value="11" />
          </el-select>
        </el-form-item>
        <el-form-item label="触发类型">
          <el-select
            v-model="searchForm.triggerType"
            placeholder="请选择触发类型"
            clearable
            style="width: 150px"
          >
            <el-option label="定时触发" value="CRON" />
            <el-option label="手动触发" value="MANUAL" />
            <el-option label="接口触发" value="API" />
            <el-option label="重试触发" value="RETRY" />
          </el-select>
        </el-form-item>
        <el-form-item label="执行时间">
          <el-date-picker
            v-model="searchForm.dateRange"
            type="datetimerange"
            range-separator="至"
            start-placeholder="开始时间"
            end-placeholder="结束时间"
            format="YYYY-MM-DD HH:mm:ss"
            value-format="YYYY-MM-DD HH:mm:ss"
            style="width: 350px"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">
            <el-icon><Search /></el-icon>
            搜索
          </el-button>
          <el-button @click="handleReset">
            <el-icon><RefreshLeft /></el-icon>
            重置
          </el-button>
        </el-form-item>
      </el-form>
    </div>

    <!-- 数据表格 -->
    <div class="table-container">
      <el-table
        :data="tableData"
        v-loading="loading"
        stripe
        border
        style="width: 100%"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55" />
        <el-table-column prop="executionId" label="执行实例ID" width="280" show-overflow-tooltip>
          <template #default="{ row }">
            <span class="execution-id">{{ row.executionId }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="workflowName" label="实例名称" width="200" show-overflow-tooltip />
        <el-table-column prop="status" label="状态" width="100">
          <template #default="{ row }">
            <el-tag :type="getStatusTagType(row.status)">
              {{ getStatusName(row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="triggerType" label="触发类型" width="120">
          <template #default="{ row }">
            <el-tag :type="getTriggerTypeTagType(row.triggerType)" size="small">
              {{ getTriggerTypeName(row.triggerType) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="triggerTime" label="触发时间" width="180">
          <template #default="{ row }">
            <span>{{ formatDateTime(row.triggerTime) }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="startTime" label="开始时间" width="180">
          <template #default="{ row }">
            <span>{{ formatDateTime(row.startTime) }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="endTime" label="结束时间" width="180">
          <template #default="{ row }">
            <span>{{ formatDateTime(row.endTime) }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="duration" label="执行耗时" width="120">
          <template #default="{ row }">
            <span v-if="row.duration">{{ formatDuration(row.duration) }}</span>
            <span v-else>-</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="160" fixed="right">
          <template #default="{ row }">
            <div class="action-buttons">
              <el-button
                link
                type="primary"
                @click="handleViewMonitor(row)"
                :disabled="!row.executionId"
              >
                <el-icon><Monitor /></el-icon>
                执行监控
              </el-button>
              <el-button
                v-if="row.status === 1"
                link
                type="warning"
                @click="handleStop(row)"
              >
                停止
              </el-button>
              <el-button
                v-if="row.status === 6 || row.status === 5 || row.status === 9"
                link
                type="success"
                @click="handleRetry(row)"
              >
                重试
              </el-button>
              <el-button
                v-if="row.status !== 1 && row.status !== 2 && row.status !== 4"
                link
                type="danger"
                @click="handleDelete(row)"
              >
                删除
              </el-button>
            </div>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.size"
          :total="total"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>

    <!-- 批量操作 -->
    <div v-if="selectedRows.length > 0" class="batch-actions">
      <el-alert
        :title="`已选择 ${selectedRows.length} 项`"
        type="info"
        show-icon
        :closable="false"
      >
        <template #default>
          <div class="batch-buttons">
            <el-button size="small" @click="handleBatchDelete">
              批量删除
            </el-button>
          </div>
        </template>
      </el-alert>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Refresh, Search, RefreshLeft, Monitor } from '@element-plus/icons-vue'
import workflowInstanceApi from '@/api/workflow-instance'
import type { WorkflowInstance, WorkflowInstanceQuery } from '@/types/workflow-instance'

// 路由信息
const route = useRoute()
const router = useRouter()
const projectId = ref(route.params.projectId as string)

// 响应式数据
const loading = ref(false)
const tableData = ref<WorkflowInstance[]>([])
const total = ref(0)
const selectedRows = ref<WorkflowInstance[]>([])

// 搜索表单
const searchForm = reactive<Omit<WorkflowInstanceQuery, 'page' | 'size'> & { dateRange: string[] | null }>({
  workflowName: '',
  status: undefined,
  triggerType: '',
  dateRange: null
})

// 分页信息
const pagination = reactive({
  page: 1,
  size: 10
})

// 页面初始化
onMounted(() => {
  loadTableData()
})

// 加载表格数据
const loadTableData = async () => {
  loading.value = true
  try {
    const params: WorkflowInstanceQuery = {
      page: pagination.page,
      size: pagination.size,
      projectId: Number(projectId.value),
      workflowName: searchForm.workflowName || undefined,
      status: searchForm.status,
      triggerType: searchForm.triggerType || undefined,
      startTime: searchForm.dateRange?.[0],
      endTime: searchForm.dateRange?.[1]
    }

    const response = await workflowInstanceApi.pageList(params)

    if (response.code === 200) {
      const pageData = response.content
      tableData.value = pageData.data || []
      total.value = pageData.recordsTotal || 0
    } else {
      ElMessage.error(response.msg || '加载数据失败')
      tableData.value = []
      total.value = 0
    }
  } catch (error) {
    console.error('加载数据失败:', error)
    ElMessage.error('加载数据失败')
    tableData.value = []
    total.value = 0
  } finally {
    loading.value = false
  }
}

// 搜索
const handleSearch = () => {
  pagination.page = 1
  loadTableData()
}

// 重置
const handleReset = () => {
  Object.assign(searchForm, {
    workflowName: '', // 实际对应实例名称
    status: undefined,
    triggerType: '',
    dateRange: null
  })
  pagination.page = 1
  loadTableData()
}

// 刷新
const handleRefresh = () => {
  loadTableData()
}

// 分页处理
const handleSizeChange = (size: number) => {
  pagination.size = size
  pagination.page = 1
  loadTableData()
}

const handleCurrentChange = (page: number) => {
  pagination.page = page
  loadTableData()
}

// 选择处理
const handleSelectionChange = (selection: any[]) => {
  selectedRows.value = selection
}

// 操作处理
const handleViewMonitor = (row: WorkflowInstance) => {
  if (!row.executionId) {
    ElMessage.warning('该实例没有执行ID，无法查看监控')
    return
  }

  // 跳转到工作流执行监控页面
  router.push(`/project/${projectId.value}/workflow-instance/monitor/${row.executionId}`)
}



const handleStop = async (row: WorkflowInstance) => {
  try {
    await ElMessageBox.confirm(
      `确定要停止工作流实例"${row.workflowName}"吗？`,
      '确认停止',
      {
        type: 'warning',
        confirmButtonText: '确定停止',
        cancelButtonText: '取消'
      }
    )

    const response = await workflowInstanceApi.stopInstance(row.id)
    if (response.code === 200) {
      ElMessage.success('实例停止成功')
      await loadTableData() // 刷新列表
    } else {
      ElMessage.error(response.msg || '停止实例失败')
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('停止实例失败:', error)
      ElMessage.error('停止实例失败')
    }
  }
}

const handleRetry = async (row: WorkflowInstance) => {
  try {
    await ElMessageBox.confirm(
      `确定要重试工作流实例"${row.workflowName}"吗？`,
      '确认重试',
      {
        type: 'warning',
        confirmButtonText: '确定重试',
        cancelButtonText: '取消'
      }
    )

    const response = await workflowInstanceApi.retryInstance(row.id)
    if (response.code === 200) {
      ElMessage.success('实例重试成功')
      await loadTableData() // 刷新列表
    } else {
      ElMessage.error(response.msg || '重试实例失败')
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('重试实例失败:', error)
      ElMessage.error('重试实例失败')
    }
  }
}

const handleDelete = async (row: WorkflowInstance) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除工作流实例"${row.workflowName}"吗？删除后无法恢复。`,
      '确认删除',
      {
        type: 'warning',
        confirmButtonText: '确定删除',
        cancelButtonText: '取消'
      }
    )

    const response = await workflowInstanceApi.deleteInstance(row.id)
    if (response.code === 200) {
      ElMessage.success('实例删除成功')
      await loadTableData() // 刷新列表
    } else {
      ElMessage.error(response.msg || '删除实例失败')
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除实例失败:', error)
      ElMessage.error('删除实例失败')
    }
  }
}

const handleBatchDelete = async () => {
  try {
    await ElMessageBox.confirm(
      `确定要删除选中的 ${selectedRows.value.length} 个实例吗？删除后无法恢复。`,
      '批量删除',
      {
        type: 'warning',
        confirmButtonText: '确定删除',
        cancelButtonText: '取消'
      }
    )

    const ids = selectedRows.value.map(row => row.id)
    const response = await workflowInstanceApi.batchDeleteInstances(ids)

    if (response.code === 200) {
      ElMessage.success('批量删除成功')
      selectedRows.value = [] // 清空选择
      await loadTableData() // 刷新列表
    } else {
      ElMessage.error(response.msg || '批量删除失败')
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('批量删除失败:', error)
      ElMessage.error('批量删除失败')
    }
  }
}

// 工具方法
const getStatusName = (status: number) => {
  const statusMap: Record<number, string> = {
    0: '提交成功',
    1: '正在运行',
    2: '准备暂停',
    3: '暂停',
    4: '准备停止',
    5: '停止',
    6: '失败',
    7: '成功',
    8: '需要容错',
    9: 'kill',
    10: '等待线程',
    11: '等待依赖完成'
  }
  return statusMap[status] || '未知'
}

const getStatusTagType = (status: number) => {
  const typeMap: Record<number, string> = {
    0: 'info',      // 提交成功
    1: 'warning',   // 正在运行
    2: 'warning',   // 准备暂停
    3: 'info',      // 暂停
    4: 'warning',   // 准备停止
    5: 'info',      // 停止
    6: 'danger',    // 失败
    7: 'success',   // 成功
    8: 'warning',   // 需要容错
    9: 'danger',    // kill
    10: 'info',     // 等待线程
    11: 'info'      // 等待依赖完成
  }
  return typeMap[status] || ''
}

const getTriggerTypeName = (triggerType: string) => {
  const typeMap: Record<string, string> = {
    'MANUAL': '手动触发',
    'SCHEDULE': '定时调度',
    'API': 'API触发',
    'EVENT': '事件触发',
    'DEPENDENCY': '依赖触发',
    // 向后兼容
    'CRON': '定时触发',
    'RETRY': '重试触发'
  }
  return typeMap[triggerType] || '未知'
}

const getTriggerTypeTagType = (triggerType: string) => {
  const typeMap: Record<string, string> = {
    'MANUAL': 'success',
    'SCHEDULE': 'primary',
    'API': 'warning',
    'EVENT': 'info',
    'DEPENDENCY': 'warning',
    // 向后兼容
    'CRON': 'primary',
    'RETRY': 'info'
  }
  return typeMap[triggerType] || ''
}

const formatDuration = (duration: number) => {
  if (duration < 1000) {
    return `${duration}ms`
  } else if (duration < 60000) {
    return `${(duration / 1000).toFixed(1)}s`
  } else {
    const minutes = Math.floor(duration / 60000)
    const seconds = Math.floor((duration % 60000) / 1000)
    return `${minutes}m${seconds}s`
  }
}

// 格式化日期时间
const formatDateTime = (dateTime: string | null | undefined) => {
  if (!dateTime) return '-'

  try {
    const date = new Date(dateTime)
    if (isNaN(date.getTime())) return '-'

    const year = date.getFullYear()
    const month = String(date.getMonth() + 1).padStart(2, '0')
    const day = String(date.getDate()).padStart(2, '0')
    const hours = String(date.getHours()).padStart(2, '0')
    const minutes = String(date.getMinutes()).padStart(2, '0')
    const seconds = String(date.getSeconds()).padStart(2, '0')

    return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`
  } catch (error) {
    return '-'
  }
}
</script>

<style scoped>
.workflow-instance-container {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.header-left h2 {
  margin: 0 0 5px 0;
  color: #303133;
  font-size: 20px;
}

.page-description {
  margin: 0;
  color: #909399;
  font-size: 14px;
}

.search-container {
  background: #f8f9fa;
  padding: 20px;
  border-radius: 8px;
  margin-bottom: 20px;
}

.table-container {
  background: white;
  border-radius: 8px;
  overflow: hidden;
}

.pagination-container {
  padding: 20px;
  text-align: right;
  border-top: 1px solid #ebeef5;
}

/* 表格中的操作按钮容器 */
.table-container .action-buttons {
  display: flex;
  align-items: center;
  gap: 4px;
  white-space: nowrap;
}

/* 确保表格中的操作按钮有一致的样式 */
:deep(.table-container .action-buttons .el-button) {
  height: 28px;
  line-height: 28px;
  padding: 0 2px;
  margin: 0;
  font-size: 12px;
  min-width: auto;
}

:deep(.table-container .action-buttons .el-button .el-icon) {
  margin-right: 4px;
}

:deep(.table-container .action-buttons .el-button:disabled) {
  opacity: 0.5;
}

.batch-actions {
  margin-top: 20px;
}

.batch-buttons {
  margin-top: 10px;
}

.execution-id {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  background: #f5f7fa;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  color: #606266;
  border: 1px solid #e4e7ed;
}
</style>
