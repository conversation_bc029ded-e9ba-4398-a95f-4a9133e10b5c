<template>
  <div class="project-container">
    <!-- 页面标题 -->
    <div class="page-header">
      <h1 class="page-title">项目管理</h1>
      <p>管理和维护数据开发项目，支持项目权限控制和成员管理</p>
    </div>

    <!-- 搜索和操作区域 -->
    <div class="search-section">
      <el-form :model="searchForm" inline class="search-form">
        <el-form-item label="项目名称">
          <el-input
            v-model="searchForm.name"
            placeholder="请输入项目名称"
            clearable
            style="width: 200px"
          />
        </el-form-item>
        <el-form-item label="项目类型">
          <el-select
            v-model="searchForm.type"
            placeholder="请选择项目类型"
            clearable
            style="width: 150px"
          >
            <el-option
              v-for="type in projectTypes"
              :key="type.code"
              :label="type.name"
              :value="type.code"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="状态">
          <el-select
            v-model="searchForm.status"
            placeholder="请选择状态"
            clearable
            style="width: 120px"
          >
            <el-option label="启用" :value="1" />
            <el-option label="禁用" :value="0" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch" :loading="loading">
            <el-icon><Search /></el-icon>
            搜索
          </el-button>
          <el-button @click="handleReset">
            <el-icon><Refresh /></el-icon>
            重置
          </el-button>
        </el-form-item>
      </el-form>

      <div class="action-buttons">
        <el-button type="primary" @click="handleAdd">
          <el-icon><Plus /></el-icon>
          新增项目
        </el-button>
      </div>
    </div>

    <!-- 项目列表表格 -->
    <div class="table-section">
      <el-table
        :data="tableData"
        v-loading="loading"
        stripe
        border
        style="width: 100%"
        @sort-change="handleSortChange"
      >
        <el-table-column prop="name" label="项目名称" min-width="150" show-overflow-tooltip>
          <template #default="{ row }">
            <div class="project-name">
              <el-icon class="project-icon" :class="getProjectTypeClass(row.type)">
                <component :is="getProjectTypeIcon(row.type)" />
              </el-icon>
              <el-link
                type="primary"
                underline="never"
                @click="handleGoToProject(row)"
                class="project-name-link"
              >
                {{ row.name }}
              </el-link>
            </div>
          </template>
        </el-table-column>
        
        <el-table-column prop="code" label="项目编码" width="120" show-overflow-tooltip />
        
        <el-table-column prop="type" label="项目类型" width="100" align="center">
          <template #default="{ row }">
            <el-tag :type="row.type === 'PUBLIC' ? 'success' : 'info'" size="small">
              {{ getProjectTypeName(row.type) }}
            </el-tag>
          </template>
        </el-table-column>
        
        <el-table-column prop="description" label="项目描述" min-width="200" show-overflow-tooltip />
        
        <el-table-column prop="memberCount" label="成员数量" width="100" align="center">
          <template #default="{ row }">
            <el-link type="primary" @click="handleViewMembers(row)">
              {{ row.memberCount || 0 }}人
            </el-link>
          </template>
        </el-table-column>
        
        <el-table-column prop="currentUserRole" label="我的角色" width="100" align="center">
          <template #default="{ row }">
            <el-tag v-if="row.currentUserRole" :type="getRoleTagType(row.currentUserRole)" size="small">
              {{ getRoleName(row.currentUserRole) }}
            </el-tag>
            <span v-else class="text-muted">-</span>
          </template>
        </el-table-column>
        
        <el-table-column prop="status" label="状态" width="80" align="center">
          <template #default="{ row }">
            <el-tag :type="row.status === 1 ? 'success' : 'danger'" size="small">
              {{ row.status === 1 ? '启用' : '禁用' }}
            </el-tag>
          </template>
        </el-table-column>
        
        <el-table-column prop="createTime" label="创建时间" width="160" sortable="custom">
          <template #default="{ row }">
            {{ formatDateTime(row.createTime) }}
          </template>
        </el-table-column>
        
        <el-table-column label="操作" width="150" fixed="right">
          <template #default="{ row }">
            <div class="action-buttons">
              <el-button type="primary" link size="small" @click="handleView(row)">
                查看
              </el-button>
              <el-button
                v-if="canEdit(row)"
                type="primary"
                link
                size="small"
                @click="handleEdit(row)"
              >
                编辑
              </el-button>
              <el-dropdown
                @command="(command) => handleDropdownCommand(command, row)"
                class="action-dropdown"
              >
                <el-button type="primary" link size="small" class="dropdown-trigger">
                  更多<el-icon class="el-icon--right"><ArrowDown /></el-icon>
                </el-button>
                <template #dropdown>
                  <el-dropdown-menu>
                    <el-dropdown-item
                      v-if="canManageMembers(row)"
                      :command="`members-${row.id}`"
                    >
                      成员管理
                    </el-dropdown-item>
                    <el-dropdown-item
                      v-if="canEdit(row)"
                      :command="`status-${row.id}`"
                      :divided="canManageMembers(row)"
                    >
                      {{ row.status === 1 ? '禁用' : '启用' }}项目
                    </el-dropdown-item>
                    <el-dropdown-item
                      v-if="canEdit(row)"
                      :command="`type-${row.id}`"
                    >
                      切换为{{ row.type === 'PUBLIC' ? '私有' : '公共' }}项目
                    </el-dropdown-item>
                    <el-dropdown-item
                      v-if="canEdit(row)"
                      :command="`delete-${row.id}`"
                      divided
                    >
                      删除项目
                    </el-dropdown-item>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
            </div>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-section">
        <el-pagination
          v-model:current-page="pagination.current"
          v-model:page-size="pagination.size"
          :page-sizes="[10, 20, 50, 100]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>

    <!-- 项目表单对话框 -->
    <ProjectForm
      v-model:visible="formVisible"
      :project="currentProject"
      :mode="formMode"
      @success="handleFormSuccess"
    />

    <!-- 项目详情对话框 -->
    <ProjectDetail
      v-model:visible="detailVisible"
      :project="currentProject"
      @edit="handleEditFromDetail"
      @manageMembers="handleMembersFromDetail"
    />

    <!-- 项目成员管理对话框 -->
    <ProjectMembers
      v-model:visible="membersVisible"
      :project="currentProject"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Search, Refresh, Plus, ArrowDown, Unlock, Lock } from '@element-plus/icons-vue'
import { useProjectStore } from '@/stores/project'
import type { Project, ProjectTypeOption } from '@/types'
import { formatDateTime } from '@/utils/date'
import ProjectForm from './components/ProjectForm.vue'
import ProjectDetail from './components/ProjectDetail.vue'
import ProjectMembers from './components/ProjectMembers.vue'

// Router
const router = useRouter()

// Store
const projectStore = useProjectStore()

// 响应式数据
const loading = ref(false)
const tableData = ref<Project[]>([])
const projectTypes = ref<ProjectTypeOption[]>([])

// 搜索表单
const searchForm = reactive({
  name: '',
  type: '',
  status: undefined as number | undefined
})

// 分页
const pagination = reactive({
  current: 1,
  size: 10,
  total: 0
})

// 对话框状态
const formVisible = ref(false)
const detailVisible = ref(false)
const membersVisible = ref(false)
const currentProject = ref<Project | null>(null)
const formMode = ref<'add' | 'edit'>('add')

// 计算属性
const canEdit = computed(() => (project: Project) => {
  return project.currentUserRole === 'ADMIN'
})

const canManageMembers = computed(() => (project: Project) => {
  return project.currentUserRole === 'ADMIN'
})

// 方法
const loadData = async () => {
  try {
    loading.value = true
    const params = {
      pageNum: pagination.current,
      pageSize: pagination.size,
      name: searchForm.name || undefined,
      type: searchForm.type || undefined,
      status: searchForm.status
    }
    
    const result = await projectStore.fetchProjects(params)
    tableData.value = result.data
    pagination.total = result.recordsTotal
  } catch (error) {
    console.error('加载项目列表失败:', error)
  } finally {
    loading.value = false
  }
}

const loadProjectTypes = async () => {
  try {
    projectTypes.value = await projectStore.fetchProjectTypes()
  } catch (error) {
    console.error('加载项目类型失败:', error)
  }
}

const handleSearch = () => {
  pagination.current = 1
  loadData()
}

const handleReset = () => {
  searchForm.name = ''
  searchForm.type = ''
  searchForm.status = undefined
  pagination.current = 1
  loadData()
}

const handleAdd = () => {
  currentProject.value = null
  formMode.value = 'add'
  formVisible.value = true
}

const handleEdit = (project: Project) => {
  currentProject.value = { ...project }
  formMode.value = 'edit'
  formVisible.value = true
}

const handleView = (project: Project) => {
  currentProject.value = project
  detailVisible.value = true
}

const handleViewMembers = (project: Project) => {
  currentProject.value = project
  membersVisible.value = true
}

const handleMembers = (project: Project) => {
  currentProject.value = project
  membersVisible.value = true
}

const handleDropdownCommand = async (command: string, project: Project) => {
  const [action, id] = command.split('-')

  switch (action) {
    case 'members':
      handleMembers(project)
      break
    case 'status':
      await handleToggleStatus(project)
      break
    case 'type':
      await handleToggleType(project)
      break
    case 'delete':
      await handleDelete(project)
      break
  }
}

const handleToggleStatus = async (project: Project) => {
  const newStatus = project.status === 1 ? 0 : 1
  const action = newStatus === 1 ? '启用' : '禁用'
  
  try {
    await ElMessageBox.confirm(
      `确定要${action}项目"${project.name}"吗？`,
      '确认操作',
      { type: 'warning' }
    )
    
    const success = await projectStore.updateProjectStatus(project.id!, newStatus)
    if (success) {
      loadData()
    }
  } catch (error) {
    // 用户取消操作
  }
}

const handleToggleType = async (project: Project) => {
  const newType = project.type === 'PUBLIC' ? 'PRIVATE' : 'PUBLIC'
  const action = newType === 'PUBLIC' ? '公共' : '私有'
  
  try {
    await ElMessageBox.confirm(
      `确定要将项目"${project.name}"切换为${action}项目吗？`,
      '确认操作',
      { type: 'warning' }
    )
    
    const success = await projectStore.updateProjectType(project.id!, newType)
    if (success) {
      loadData()
    }
  } catch (error) {
    // 用户取消操作
  }
}

const handleDelete = async (project: Project) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除项目"${project.name}"吗？删除后将无法恢复！`,
      '确认删除',
      { type: 'error' }
    )
    
    const success = await projectStore.deleteProject(project.id!)
    if (success) {
      loadData()
    }
  } catch (error) {
    // 用户取消操作
  }
}

const handleFormSuccess = () => {
  formVisible.value = false
  loadData()
}

const handleGoToProject = (project: Project) => {
  router.push(`/project/${project.id}/overview`)
}

const handleEditFromDetail = (project: Project) => {
  detailVisible.value = false
  currentProject.value = { ...project }
  formMode.value = 'edit'
  formVisible.value = true
}

const handleMembersFromDetail = (project: Project) => {
  detailVisible.value = false
  currentProject.value = project
  membersVisible.value = true
}

const handleSizeChange = (size: number) => {
  pagination.size = size
  pagination.current = 1
  loadData()
}

const handleCurrentChange = (current: number) => {
  pagination.current = current
  loadData()
}

const handleSortChange = ({ prop, order }: any) => {
  // TODO: 实现排序逻辑
  console.log('排序:', prop, order)
}

// 辅助方法
const getProjectTypeIcon = (type: string) => {
  return type === 'PUBLIC' ? Unlock : Lock
}

const getProjectTypeClass = (type: string) => {
  return type === 'PUBLIC' ? 'public-project' : 'private-project'
}

const getProjectTypeName = (type: string) => {
  return type === 'PUBLIC' ? '公共' : '私有'
}

const getRoleName = (role: string) => {
  const roleMap: Record<string, string> = {
    'ADMIN': '管理员',
    'DEVELOPER': '开发者',
    'VIEWER': '查看者'
  }
  return roleMap[role] || role
}

const getRoleTagType = (role: string) => {
  const typeMap: Record<string, string> = {
    'ADMIN': 'danger',
    'DEVELOPER': 'warning',
    'VIEWER': 'info'
  }
  return typeMap[role] || 'info'
}

// 生命周期
onMounted(() => {
  loadData()
  loadProjectTypes()
})
</script>

<style scoped>
.project-container {
  padding: 20px;
}

.page-header {
  margin-bottom: 20px;
}

.page-title {
  font-size: 24px;
  font-weight: 600;
  color: #303133;
  margin-bottom: 8px;
}

.page-header h2 {
  margin: 0 0 8px 0;
  color: #303133;
  font-size: 20px;
  font-weight: 600;
}

.page-header p {
  margin: 0;
  color: #606266;
  font-size: 14px;
}

.search-section {
  background: #fff;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  margin-bottom: 20px;
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
}

.search-form {
  flex: 1;
}

.table-section {
  background: #fff;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.project-name {
  display: flex;
  align-items: center;
  gap: 8px;
}

.project-icon {
  font-size: 16px;
}

.project-icon.public-project {
  color: #67c23a;
}

.project-icon.private-project {
  color: #909399;
}

.project-name-link {
  font-weight: 500;
}

.project-name-link:hover {
  font-weight: 600;
}

.pagination-section {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
}

.text-muted {
  color: #c0c4cc;
}

/* 表格中的操作按钮容器 */
.table-section .action-buttons {
  display: flex;
  align-items: center;
  gap: 4px;
  flex-wrap: wrap;
}

.action-dropdown {
  display: inline-flex;
  align-items: center;
}

.dropdown-trigger {
  display: inline-flex;
  align-items: center;
  height: 24px;
  line-height: 24px;
}

/* 确保表格中的操作按钮有一致的样式 */
:deep(.table-section .action-buttons .el-button) {
  height: 24px;
  line-height: 24px;
  padding: 0 2px;
  margin: 0;
  font-size: 12px;
}

:deep(.table-section .action-dropdown .el-button) {
  height: 24px;
  line-height: 24px;
  padding: 0 2px;
  margin: 0;
  font-size: 12px;
}
</style>
