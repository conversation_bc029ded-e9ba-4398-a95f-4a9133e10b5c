<template>
  <el-dialog
    v-model="dialogVisible"
    :title="mode === 'add' ? '新增项目' : '编辑项目'"
    width="600px"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="100px"
      label-position="right"
    >
      <el-form-item label="项目名称" prop="name">
        <el-input
          v-model="formData.name"
          placeholder="请输入项目名称"
          maxlength="100"
          show-word-limit
        />
      </el-form-item>

      <el-form-item label="项目编码" prop="code">
        <el-input
          v-model="formData.code"
          placeholder="请输入项目编码，用于系统内部标识"
          maxlength="50"
          show-word-limit
        />
      </el-form-item>

      <el-form-item label="项目类型" prop="type">
        <el-radio-group v-model="formData.type">
          <el-radio value="PUBLIC">
            <div class="radio-option">
              <div class="radio-title">
                <el-icon><Unlock /></el-icon>
                公共项目
              </div>
              <div class="radio-desc">所有用户都可以查看和访问</div>
            </div>
          </el-radio>
          <el-radio value="PRIVATE">
            <div class="radio-option">
              <div class="radio-title">
                <el-icon><Lock /></el-icon>
                私有项目
              </div>
              <div class="radio-desc">仅项目成员可以查看和访问</div>
            </div>
          </el-radio>
        </el-radio-group>
      </el-form-item>

      <el-form-item label="项目描述" prop="description">
        <el-input
          v-model="formData.description"
          type="textarea"
          :rows="4"
          placeholder="请输入项目描述"
          maxlength="500"
          show-word-limit
        />
      </el-form-item>

      <el-form-item label="项目状态" prop="status">
        <el-radio-group v-model="formData.status">
          <el-radio :value="1">启用</el-radio>
          <el-radio :value="0">禁用</el-radio>
        </el-radio-group>
      </el-form-item>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="handleSubmit" :loading="submitting">
          {{ mode === 'add' ? '创建' : '更新' }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch, nextTick } from 'vue'
import { ElMessage } from 'element-plus'
import type { FormInstance, FormRules } from 'element-plus'
import { Lock, Unlock } from '@element-plus/icons-vue'
import { useProjectStore } from '@/stores/project'
import type { Project, ProjectType, ProjectStatus } from '@/types'

// Props
interface Props {
  visible: boolean
  project?: Project | null
  mode: 'add' | 'edit'
}

const props = withDefaults(defineProps<Props>(), {
  project: null,
  mode: 'add'
})

// Emits
const emit = defineEmits<{
  'update:visible': [value: boolean]
  success: []
}>()

// Store
const projectStore = useProjectStore()

// 响应式数据
const formRef = ref<FormInstance>()
const submitting = ref(false)

// 表单数据
const formData = reactive<Project>({
  name: '',
  code: '',
  type: 'PRIVATE' as ProjectType,
  description: '',
  status: 1 as ProjectStatus
})

// 表单验证规则
const formRules: FormRules = {
  name: [
    { required: true, message: '请输入项目名称', trigger: 'blur' },
    { min: 2, max: 100, message: '项目名称长度为2-100个字符', trigger: 'blur' }
  ],
  code: [
    { required: true, message: '请输入项目编码', trigger: 'blur' },
    { min: 2, max: 50, message: '项目编码长度为2-50个字符', trigger: 'blur' },
    { pattern: /^[a-zA-Z][a-zA-Z0-9_-]*$/, message: '项目编码必须以字母开头，只能包含字母、数字、下划线和横线', trigger: 'blur' }
  ],
  type: [
    { required: true, message: '请选择项目类型', trigger: 'change' }
  ],
  status: [
    { required: true, message: '请选择项目状态', trigger: 'change' }
  ]
}

// 计算属性
const dialogVisible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
})

// 重置表单
const resetForm = () => {
  Object.assign(formData, {
    name: '',
    code: '',
    type: 'PRIVATE' as ProjectType,
    description: '',
    status: 1 as ProjectStatus
  })
}

const loadFormData = () => {
  if (props.project) {
    Object.assign(formData, props.project)
  } else {
    resetForm()
  }
}

// 提交表单
const handleSubmit = async () => {
  try {
    // 先验证表单
    const valid = await formRef.value?.validate()
    if (!valid) return

    submitting.value = true
    
    let success = false
    if (props.mode === 'add') {
      success = await projectStore.addProject(formData)
    } else {
      success = await projectStore.updateProject(formData)
    }
    
    if (success) {
      emit('success')
    }
  } catch (error) {
    console.error('提交项目失败:', error)
  } finally {
    submitting.value = false
  }
}

// 关闭对话框
const handleClose = () => {
  formRef.value?.resetFields()
  emit('update:visible', false)
}

// 监听对话框显示状态
watch(
  () => props.visible,
  (visible) => {
    if (visible) {
      nextTick(() => {
        loadFormData()
      })
    }
  }
)
</script>

<style scoped>
.radio-option {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.radio-title {
  display: flex;
  align-items: center;
  gap: 6px;
  font-weight: 500;
  color: #303133;
}

.radio-desc {
  font-size: 12px;
  color: #909399;
  margin-left: 20px;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

:deep(.el-radio) {
  display: flex;
  align-items: flex-start;
  margin-bottom: 16px;
  margin-right: 0;
}

:deep(.el-radio__input) {
  margin-top: 2px;
}

:deep(.el-radio__label) {
  padding-left: 8px;
}
</style>
