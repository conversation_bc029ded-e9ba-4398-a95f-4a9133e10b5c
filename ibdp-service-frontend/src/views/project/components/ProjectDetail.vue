<template>
  <el-dialog
    v-model="dialogVisible"
    title="项目详情"
    width="800px"
    :close-on-click-modal="false"
  >
    <div v-if="project" class="project-detail">
      <!-- 基本信息 -->
      <div class="detail-section">
        <h3 class="section-title">基本信息</h3>
        <div class="detail-grid">
          <div class="detail-item">
            <label>项目名称：</label>
            <span class="project-name">
              <el-icon class="project-icon" :class="getProjectTypeClass(project.type)">
                <component :is="getProjectTypeIcon(project.type)" />
              </el-icon>
              {{ project.name }}
            </span>
          </div>
          <div class="detail-item">
            <label>项目编码：</label>
            <span>{{ project.code }}</span>
          </div>
          <div class="detail-item">
            <label>项目类型：</label>
            <el-tag :type="project.type === 'PUBLIC' ? 'success' : 'info'" size="small">
              {{ getProjectTypeName(project.type) }}
            </el-tag>
          </div>
          <div class="detail-item">
            <label>项目状态：</label>
            <el-tag :type="project.status === 1 ? 'success' : 'danger'" size="small">
              {{ project.status === 1 ? '启用' : '禁用' }}
            </el-tag>
          </div>
          <div class="detail-item">
            <label>我的角色：</label>
            <el-tag v-if="project.currentUserRole" :type="getRoleTagType(project.currentUserRole)" size="small">
              {{ getRoleName(project.currentUserRole) }}
            </el-tag>
            <span v-else class="text-muted">-</span>
          </div>
          <div class="detail-item">
            <label>成员数量：</label>
            <span>{{ project.memberCount || 0 }}人</span>
          </div>
        </div>
      </div>

      <!-- 项目描述 -->
      <div class="detail-section">
        <h3 class="section-title">项目描述</h3>
        <div class="description-content">
          <p v-if="project.description">{{ project.description }}</p>
          <p v-else class="text-muted">暂无描述</p>
        </div>
      </div>

      <!-- 项目成员 -->
      <div class="detail-section">
        <h3 class="section-title">
          项目成员
          <el-button 
            v-if="canManageMembers" 
            type="primary" 
            size="small" 
            @click="handleManageMembers"
          >
            管理成员
          </el-button>
        </h3>
        <div class="members-content">
          <el-table :data="members" style="width: 100%" v-loading="membersLoading">
            <el-table-column prop="username" label="用户名" width="150" />
            <el-table-column prop="role" label="角色" width="120">
              <template #default="{ row }">
                <el-tag :type="getRoleTagType(row.role)" size="small">
                  {{ getRoleName(row.role) }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="joinTime" label="加入时间" width="160">
              <template #default="{ row }">
                {{ formatDateTime(row.joinTime) }}
              </template>
            </el-table-column>
            <el-table-column prop="status" label="状态" width="80">
              <template #default="{ row }">
                <el-tag :type="row.status === 1 ? 'success' : 'danger'" size="small">
                  {{ row.status === 1 ? '启用' : '禁用' }}
                </el-tag>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>

      <!-- 创建信息 -->
      <div class="detail-section">
        <h3 class="section-title">创建信息</h3>
        <div class="detail-grid">
          <div class="detail-item">
            <label>创建人：</label>
            <span>{{ project.createUser }}</span>
          </div>
          <div class="detail-item">
            <label>创建时间：</label>
            <span>{{ formatDateTime(project.createTime) }}</span>
          </div>
          <div class="detail-item">
            <label>更新人：</label>
            <span>{{ project.updateUser }}</span>
          </div>
          <div class="detail-item">
            <label>更新时间：</label>
            <span>{{ formatDateTime(project.updateTime) }}</span>
          </div>
        </div>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="dialogVisible = false">关闭</el-button>
        <el-button v-if="canEdit" type="primary" @click="handleEdit">
          编辑项目
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { Lock, Unlock } from '@element-plus/icons-vue'
import { useProjectMemberStore } from '@/stores/projectMember'
import type { Project, ProjectMember } from '@/types'
import { formatDateTime } from '@/utils/date'

// Props
interface Props {
  visible: boolean
  project?: Project | null
}

const props = withDefaults(defineProps<Props>(), {
  project: null
})

// Emits
const emit = defineEmits<{
  'update:visible': [value: boolean]
  edit: [project: Project]
  manageMembers: [project: Project]
}>()

// Store
const projectMemberStore = useProjectMemberStore()

// 响应式数据
const members = ref<ProjectMember[]>([])
const membersLoading = ref(false)

// 计算属性
const dialogVisible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
})

const canEdit = computed(() => {
  return props.project?.currentUserRole === 'ADMIN'
})

const canManageMembers = computed(() => {
  return props.project?.currentUserRole === 'ADMIN'
})

// 方法
const loadMembers = async () => {
  if (!props.project?.id) return
  
  try {
    membersLoading.value = true
    members.value = await projectMemberStore.fetchMembersByProjectId(props.project.id)
  } catch (error) {
    console.error('加载项目成员失败:', error)
  } finally {
    membersLoading.value = false
  }
}

const handleEdit = () => {
  if (props.project) {
    emit('edit', props.project)
  }
}

const handleManageMembers = () => {
  if (props.project) {
    emit('manageMembers', props.project)
  }
}

// 辅助方法
const getProjectTypeIcon = (type: string) => {
  return type === 'PUBLIC' ? Unlock : Lock
}

const getProjectTypeClass = (type: string) => {
  return type === 'PUBLIC' ? 'public-project' : 'private-project'
}

const getProjectTypeName = (type: string) => {
  return type === 'PUBLIC' ? '公共' : '私有'
}

const getRoleName = (role: string) => {
  const roleMap: Record<string, string> = {
    'ADMIN': '管理员',
    'DEVELOPER': '开发者',
    'VIEWER': '查看者'
  }
  return roleMap[role] || role
}

const getRoleTagType = (role: string) => {
  const typeMap: Record<string, string> = {
    'ADMIN': 'danger',
    'DEVELOPER': 'warning',
    'VIEWER': 'info'
  }
  return typeMap[role] || 'info'
}

// 监听对话框显示状态
watch(
  () => props.visible,
  (visible) => {
    if (visible && props.project) {
      loadMembers()
    }
  }
)
</script>

<style scoped>
.project-detail {
  max-height: 600px;
  overflow-y: auto;
}

.detail-section {
  margin-bottom: 24px;
}

.detail-section:last-child {
  margin-bottom: 0;
}

.section-title {
  margin: 0 0 16px 0;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.detail-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16px;
}

.detail-item {
  display: flex;
  align-items: center;
}

.detail-item label {
  min-width: 80px;
  color: #606266;
  font-weight: 500;
}

.project-name {
  display: flex;
  align-items: center;
  gap: 8px;
}

.project-icon {
  font-size: 16px;
}

.project-icon.public-project {
  color: #67c23a;
}

.project-icon.private-project {
  color: #909399;
}

.description-content {
  padding: 12px;
  background: #f5f7fa;
  border-radius: 4px;
  border: 1px solid #e4e7ed;
}

.description-content p {
  margin: 0;
  line-height: 1.6;
  color: #303133;
}

.members-content {
  border: 1px solid #e4e7ed;
  border-radius: 4px;
}

.text-muted {
  color: #c0c4cc;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}
</style>
