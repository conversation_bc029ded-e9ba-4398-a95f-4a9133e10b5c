<template>
  <el-dialog
    v-model="dialogVisible"
    title="项目成员管理"
    width="900px"
    :close-on-click-modal="false"
  >
    <div v-if="project" class="members-container">
      <!-- 操作区域 -->
      <div class="action-section">
        <div class="section-title">
          <h3>{{ project.name }} - 成员管理</h3>
          <div class="action-buttons">
            <el-button type="primary" @click="handleAddMember">
              <el-icon><Plus /></el-icon>
              添加成员
            </el-button>
          </div>
        </div>
        
        <!-- 搜索区域 -->
        <el-form :model="searchForm" inline class="search-form">
          <el-form-item label="用户名">
            <el-input
              v-model="searchForm.username"
              placeholder="请输入用户名"
              clearable
              style="width: 200px"
            />
          </el-form-item>
          <el-form-item label="角色">
            <el-select
              v-model="searchForm.role"
              placeholder="请选择角色"
              clearable
              style="width: 150px"
            >
              <el-option
                v-for="role in projectRoles"
                :key="role.code"
                :label="role.name"
                :value="role.code"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="状态">
            <el-select
              v-model="searchForm.status"
              placeholder="请选择状态"
              clearable
              style="width: 120px"
            >
              <el-option label="启用" :value="1" />
              <el-option label="禁用" :value="0" />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="handleSearch" :loading="loading">
              <el-icon><Search /></el-icon>
              搜索
            </el-button>
            <el-button @click="handleReset">
              <el-icon><Refresh /></el-icon>
              重置
            </el-button>
          </el-form-item>
        </el-form>
      </div>

      <!-- 成员列表 -->
      <div class="table-section">
        <el-table
          :data="tableData"
          v-loading="loading"
          stripe
          border
          style="width: 100%"
        >
          <el-table-column prop="username" label="用户名" width="150" show-overflow-tooltip />
          
          <el-table-column prop="userId" label="用户ID" width="120" show-overflow-tooltip />
          
          <el-table-column prop="role" label="角色" width="120" align="center">
            <template #default="{ row }">
              <el-tag :type="getRoleTagType(row.role)" size="small">
                {{ getRoleName(row.role) }}
              </el-tag>
            </template>
          </el-table-column>
          
          <el-table-column prop="joinTime" label="加入时间" width="160">
            <template #default="{ row }">
              {{ formatDateTime(row.joinTime) }}
            </template>
          </el-table-column>
          
          <el-table-column prop="status" label="状态" width="80" align="center">
            <template #default="{ row }">
              <el-tag :type="row.status === 1 ? 'success' : 'danger'" size="small">
                {{ row.status === 1 ? '启用' : '禁用' }}
              </el-tag>
            </template>
          </el-table-column>
          
          <el-table-column label="操作" width="200" fixed="right">
            <template #default="{ row }">
              <el-button type="primary" link size="small" @click="handleEditRole(row)">
                修改角色
              </el-button>
              <el-button 
                type="primary" 
                link 
                size="small" 
                @click="handleToggleStatus(row)"
              >
                {{ row.status === 1 ? '禁用' : '启用' }}
              </el-button>
              <el-button 
                type="danger" 
                link 
                size="small" 
                @click="handleRemove(row)"
                :disabled="!canRemoveMember(row)"
              >
                移除
              </el-button>
            </template>
          </el-table-column>
        </el-table>

        <!-- 分页 -->
        <div class="pagination-section">
          <el-pagination
            v-model:current-page="pagination.current"
            v-model:page-size="pagination.size"
            :page-sizes="[10, 20, 50]"
            :total="pagination.total"
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
        </div>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="dialogVisible = false">关闭</el-button>
      </div>
    </template>

    <!-- 添加成员对话框 -->
    <!-- <MemberForm
      v-model:visible="memberFormVisible"
      :project="project"
      @success="handleMemberFormSuccess"
    /> -->

    <!-- 修改角色对话框 -->
    <!-- <RoleForm
      v-model:visible="roleFormVisible"
      :member="currentMember"
      @success="handleRoleFormSuccess"
    /> -->
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus, Search, Refresh } from '@element-plus/icons-vue'
import { useProjectMemberStore } from '@/stores/projectMember'
import type { Project, ProjectMember, ProjectRoleOption } from '@/types'
import { formatDateTime } from '@/utils/date'
// import MemberForm from './MemberForm.vue'
// import RoleForm from './RoleForm.vue'

// Props
interface Props {
  visible: boolean
  project?: Project | null
}

const props = withDefaults(defineProps<Props>(), {
  project: null
})

// Emits
const emit = defineEmits<{
  'update:visible': [value: boolean]
}>()

// Store
const projectMemberStore = useProjectMemberStore()

// 响应式数据
const loading = ref(false)
const tableData = ref<ProjectMember[]>([])
const projectRoles = ref<ProjectRoleOption[]>([])
const memberFormVisible = ref(false)
const roleFormVisible = ref(false)
const currentMember = ref<ProjectMember | null>(null)

// 搜索表单
const searchForm = reactive({
  username: '',
  role: '',
  status: undefined as number | undefined
})

// 分页
const pagination = reactive({
  current: 1,
  size: 10,
  total: 0
})

// 计算属性
const dialogVisible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
})

const canRemoveMember = computed(() => (member: ProjectMember) => {
  // 不能移除最后一个管理员
  if (member.role === 'ADMIN') {
    const adminCount = tableData.value.filter(m => m.role === 'ADMIN').length
    return adminCount > 1
  }
  return true
})

// 方法
const loadData = async () => {
  if (!props.project?.id) return
  
  try {
    loading.value = true
    const params = {
      start: (pagination.current - 1) * pagination.size,
      length: pagination.size,
      projectId: props.project.id,
      username: searchForm.username || undefined,
      role: searchForm.role || undefined,
      status: searchForm.status
    }
    
    const result = await projectMemberStore.fetchMembers(params)
    tableData.value = result.data
    pagination.total = result.recordsTotal
  } catch (error) {
    console.error('加载项目成员列表失败:', error)
  } finally {
    loading.value = false
  }
}

const loadProjectRoles = async () => {
  try {
    projectRoles.value = await projectMemberStore.fetchProjectRoles()
  } catch (error) {
    console.error('加载项目角色失败:', error)
  }
}

const handleSearch = () => {
  pagination.current = 1
  loadData()
}

const handleReset = () => {
  searchForm.username = ''
  searchForm.role = ''
  searchForm.status = undefined
  pagination.current = 1
  loadData()
}

const handleAddMember = () => {
  memberFormVisible.value = true
}

const handleEditRole = (member: ProjectMember) => {
  currentMember.value = { ...member }
  roleFormVisible.value = true
}

const handleToggleStatus = async (member: ProjectMember) => {
  const newStatus = member.status === 1 ? 0 : 1
  const action = newStatus === 1 ? '启用' : '禁用'
  
  try {
    await ElMessageBox.confirm(
      `确定要${action}成员"${member.username}"吗？`,
      '确认操作',
      { type: 'warning' }
    )
    
    const success = await projectMemberStore.updateMemberStatus(member.id!, newStatus)
    if (success) {
      loadData()
    }
  } catch (error) {
    // 用户取消操作
  }
}

const handleRemove = async (member: ProjectMember) => {
  try {
    await ElMessageBox.confirm(
      `确定要移除成员"${member.username}"吗？`,
      '确认移除',
      { type: 'warning' }
    )
    
    const success = await projectMemberStore.removeMember(member.id!)
    if (success) {
      loadData()
    }
  } catch (error) {
    // 用户取消操作
  }
}

const handleMemberFormSuccess = () => {
  memberFormVisible.value = false
  loadData()
}

const handleRoleFormSuccess = () => {
  roleFormVisible.value = false
  loadData()
}

const handleSizeChange = (size: number) => {
  pagination.size = size
  pagination.current = 1
  loadData()
}

const handleCurrentChange = (current: number) => {
  pagination.current = current
  loadData()
}

// 辅助方法
const getRoleName = (role: string) => {
  const roleMap: Record<string, string> = {
    'ADMIN': '管理员',
    'DEVELOPER': '开发者',
    'VIEWER': '查看者'
  }
  return roleMap[role] || role
}

const getRoleTagType = (role: string) => {
  const typeMap: Record<string, string> = {
    'ADMIN': 'danger',
    'DEVELOPER': 'warning',
    'VIEWER': 'info'
  }
  return typeMap[role] || 'info'
}

// 监听对话框显示状态
watch(
  () => props.visible,
  (visible) => {
    if (visible && props.project) {
      loadData()
    }
  }
)

// 生命周期
onMounted(() => {
  loadProjectRoles()
})
</script>

<style scoped>
.members-container {
  max-height: 600px;
  overflow-y: auto;
}

.action-section {
  margin-bottom: 20px;
}

.section-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.section-title h3 {
  margin: 0;
  color: #303133;
  font-size: 16px;
  font-weight: 600;
}

.search-form {
  background: #f5f7fa;
  padding: 16px;
  border-radius: 4px;
  border: 1px solid #e4e7ed;
}

.table-section {
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  padding: 16px;
}

.pagination-section {
  margin-top: 16px;
  display: flex;
  justify-content: flex-end;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}
</style>
