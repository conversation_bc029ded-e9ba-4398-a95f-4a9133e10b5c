<template>
  <div class="login-container">
    <!-- 动态背景 -->
    <div class="background-animation">
      <div class="floating-shapes">
        <div class="shape shape-1"></div>
        <div class="shape shape-2"></div>
        <div class="shape shape-3"></div>
        <div class="shape shape-4"></div>
        <div class="shape shape-5"></div>
        <div class="shape shape-6"></div>
      </div>
    </div>



    <div class="login-box">
      <!-- 登录表单 -->
      <div class="login-form">
        <div class="login-header">
          <div class="logo-container">
            <div class="logo-icon">
              <el-icon size="48"><DataBoard /></el-icon>
            </div>
          </div>
          <h1 class="login-title">
            <span class="title-text">SEVB</span>
            <span class="title-gradient">数据开发平台</span>
          </h1>
          <p class="login-subtitle">
            <span class="subtitle-icon">🚀</span>
            欢迎回来，开启您的数据之旅
          </p>
        </div>

        <el-form
          ref="loginFormRef"
          :model="loginForm"
          :rules="loginRules"
          class="login-form-content"
          @keyup.enter="handleLogin"
        >
          <el-form-item prop="userName" class="form-item-enhanced">
            <div class="input-wrapper">
              <el-input
                v-model="loginForm.userName"
                placeholder="请输入用户名"
                size="large"
                prefix-icon="User"
                class="enhanced-input"
              />
              <div class="input-border-effect"></div>
            </div>
          </el-form-item>

          <el-form-item prop="password" class="form-item-enhanced">
            <div class="input-wrapper">
              <el-input
                v-model="loginForm.password"
                type="password"
                placeholder="请输入密码"
                size="large"
                prefix-icon="Lock"
                class="enhanced-input"
              />
              <div class="input-border-effect"></div>
            </div>
          </el-form-item>

          <el-form-item>
            <div class="login-options">
              <el-checkbox v-model="loginForm.ifRemember">
                记住我
              </el-checkbox>
            </div>
          </el-form-item>

          <el-form-item>
            <div class="button-wrapper">
              <el-button
                type="primary"
                size="large"
                class="login-button enhanced-button"
                :loading="loading"
                @click="handleLogin"
              >
                <span v-if="!loading" class="button-content">
                  <el-icon class="button-icon"><Right /></el-icon>
                  <span class="button-text">立即登录</span>
                </span>
                <span v-else class="button-loading">
                  <el-icon class="loading-icon"><Loading /></el-icon>
                  <span>登录中...</span>
                </span>
              </el-button>
              <div class="button-glow"></div>
            </div>
          </el-form-item>
        </el-form>

        <div class="login-footer">
          <p class="copyright">© 2025 SEVB数据开发平台. All rights reserved.</p>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import type { FormInstance, FormRules } from 'element-plus'
import { User, Lock, DataBoard, Right, Loading } from '@element-plus/icons-vue'
import { useUserStore } from '@/stores/user'
import type { LoginParams } from '@/api/auth'

// Router
const router = useRouter()

// Store
const userStore = useUserStore()

// 响应式数据
const loginFormRef = ref<FormInstance>()
const loading = ref(false)

// 登录表单数据
const loginForm = reactive<LoginParams>({
  userName: '',
  password: '',
  ifRemember: false
})

// 表单验证规则
const loginRules: FormRules = {
  userName: [
    { required: true, message: '请输入用户名', trigger: 'blur' },
    { min: 2, max: 50, message: '用户名长度为2-50个字符', trigger: 'blur' }
  ],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' },
    { min: 6, max: 50, message: '密码长度为6-50个字符', trigger: 'blur' }
  ]
}



// 处理登录
const handleLogin = async () => {
  try {
    // 验证表单
    const valid = await loginFormRef.value?.validate()
    if (!valid) return

    loading.value = true

    // 调用登录接口
    const success = await userStore.login(loginForm)
    
    if (success) {
      // 登录成功，跳转到首页
      router.push('/home')
    }
  } catch (error) {
    console.error('登录失败:', error)
    ElMessage.error('登录失败，请重试')
  } finally {
    loading.value = false
  }
}

// 生命周期
onMounted(() => {
  // 如果已经登录，直接跳转到首页
  if (userStore.checkLoginStatus()) {
    router.push('/home')
  }
  
  // 设置默认用户名（开发环境）
  if (process.env.NODE_ENV === 'development') {
    loginForm.userName = 'admin'
    loginForm.password = '123456'
  }
})
</script>

<style scoped>
.login-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
  position: relative;
  overflow: hidden;
}

/* 动态背景 */
.background-animation {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
}

.floating-shapes {
  position: absolute;
  width: 100%;
  height: 100%;
}

.shape {
  position: absolute;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.1);
  animation: float 20s infinite linear;
}

.shape-1 {
  width: 80px;
  height: 80px;
  top: 10%;
  left: 10%;
  animation-delay: 0s;
}

.shape-2 {
  width: 120px;
  height: 120px;
  top: 20%;
  right: 10%;
  animation-delay: -5s;
}

.shape-3 {
  width: 60px;
  height: 60px;
  bottom: 30%;
  left: 20%;
  animation-delay: -10s;
}

.shape-4 {
  width: 100px;
  height: 100px;
  bottom: 20%;
  right: 20%;
  animation-delay: -15s;
}

.shape-5 {
  width: 40px;
  height: 40px;
  top: 50%;
  left: 50%;
  animation-delay: -8s;
}

.shape-6 {
  width: 90px;
  height: 90px;
  top: 70%;
  right: 30%;
  animation-delay: -12s;
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px) rotate(0deg);
    opacity: 0.7;
  }
  50% {
    transform: translateY(-20px) rotate(180deg);
    opacity: 1;
  }
}



.login-box {
  width: 100%;
  max-width: 420px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 20px;
  box-shadow:
    0 8px 32px rgba(0, 0, 0, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  overflow: hidden;
  position: relative;
  z-index: 10;
  animation: slideInUp 0.8s ease-out;
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(50px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.login-form {
  padding: 40px 32px;
}

.login-header {
  text-align: center;
  margin-bottom: 40px;
}

.logo-container {
  margin-bottom: 20px;
}

.logo-icon {
  display: inline-block;
  width: 80px;
  height: 80px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 20px;
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
  animation: logoFloat 3s ease-in-out infinite;
}

.logo-icon .el-icon {
  color: white;
  font-size: 48px;
}

@keyframes logoFloat {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

.login-title {
  font-size: 32px;
  font-weight: 700;
  margin: 0 0 12px 0;
  line-height: 1.2;
}

.title-text {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  display: inline-block;
}

.title-gradient {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  display: inline-block;
  margin-left: 8px;
}

.login-subtitle {
  font-size: 16px;
  color: rgba(255, 255, 255, 0.9);
  margin: 0;
  font-weight: 500;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

.subtitle-icon {
  font-size: 18px;
  animation: bounce 2s infinite;
}

@keyframes bounce {
  0%, 20%, 50%, 80%, 100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-5px);
  }
  60% {
    transform: translateY(-3px);
  }
}

.login-form-content {
  margin-bottom: 32px;
  width: 100%;
}

/* 确保表单项完全对齐 */
:deep(.el-form) {
  width: 100%;
}

:deep(.el-form-item) {
  margin-bottom: 24px;
  width: 100%;
}

:deep(.el-form-item__content) {
  width: 100%;
  display: flex;
  flex-direction: column;
}

/* 增强表单项样式 */
.form-item-enhanced {
  margin-bottom: 24px;
  width: 100%;
}

.input-wrapper {
  position: relative;
  width: 100%;
}

.enhanced-input {
  transition: all 0.3s ease;
  width: 100%;
}

.input-border-effect {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 0;
  height: 2px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  transition: width 0.3s ease;
}

.input-wrapper:focus-within .input-border-effect {
  width: 100%;
}

:deep(.enhanced-input .el-input__wrapper) {
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 12px;
  backdrop-filter: blur(10px);
  transition: all 0.3s ease;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  height: 52px; /* 与登录按钮高度一致 */
  width: 100%; /* 确保宽度100% */
  box-sizing: border-box; /* 确保边框包含在宽度内 */
  display: flex;
  align-items: center;
}



:deep(.enhanced-input .el-input__wrapper.is-focus) {
  border-color: #667eea;
  box-shadow: 0 4px 20px rgba(102, 126, 234, 0.3);
  background: rgba(255, 255, 255, 0.15);
}

:deep(.enhanced-input .el-input__inner) {
  color: rgba(255, 255, 255, 0.9);
  font-weight: 500;
  font-size: 16px; /* 与按钮字体大小一致 */
  height: 50px; /* 内部高度调整 */
  line-height: 50px; /* 垂直居中 */
  border: none;
  background: transparent;
  flex: 1;
  padding: 0 12px;
}

:deep(.enhanced-input .el-input__inner::placeholder) {
  color: rgba(255, 255, 255, 0.6);
  font-size: 16px; /* 占位符字体大小 */
}

:deep(.enhanced-input .el-input__prefix) {
  color: rgba(255, 255, 255, 0.7);
  display: flex;
  align-items: center;
  height: 52px; /* 与输入框高度一致 */
  padding-left: 16px;
  flex-shrink: 0;
}

:deep(.enhanced-input .el-input__suffix) {
  color: rgba(255, 255, 255, 0.7);
  display: flex;
  align-items: center;
  height: 52px; /* 与输入框高度一致 */
  padding-right: 16px;
  flex-shrink: 0;
}

.login-options {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  width: 100%;
  margin-bottom: 32px;
}

:deep(.login-options .el-checkbox) {
  color: rgba(255, 255, 255, 0.9);
}

:deep(.login-options .el-checkbox__label) {
  color: rgba(255, 255, 255, 0.9);
}

/* 按钮样式 */
.button-wrapper {
  position: relative;
  width: 100%;
}

.enhanced-button {
  width: 100%;
  height: 52px;
  font-size: 16px;
  font-weight: 600;
  border: none;
  border-radius: 16px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  position: relative;
  overflow: hidden;
  transition: all 0.3s ease;
  z-index: 1;
  box-sizing: border-box; /* 确保边框包含在宽度内 */
}

.enhanced-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
}

.enhanced-button:active {
  transform: translateY(0);
}

.button-content {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

.button-icon {
  font-size: 18px;
  transition: transform 0.3s ease;
}

.enhanced-button:hover .button-icon {
  transform: translateX(4px);
}

.button-text {
  font-weight: 600;
  letter-spacing: 0.5px;
}

.button-loading {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

.loading-icon {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.button-glow {
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  transition: left 0.5s ease;
}

.enhanced-button:hover .button-glow {
  left: 100%;
}

.login-footer {
  text-align: center;
  padding-top: 24px;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.copyright {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.6);
  margin: 0;
  font-weight: 400;
}

/* 响应式设计 */
@media (max-width: 480px) {
  .login-container {
    padding: 16px;
  }

  .login-box {
    max-width: 100%;
    margin: 0 8px;
  }

  .login-form {
    padding: 32px 24px;
  }

  .login-title {
    font-size: 26px;
  }

  .logo-icon {
    width: 60px;
    height: 60px;
  }

  .logo-icon .el-icon {
    font-size: 36px;
  }

  .enhanced-button {
    height: 48px;
    font-size: 15px;
  }

  :deep(.enhanced-input .el-input__wrapper) {
    height: 48px; /* 移动端与按钮高度一致 */
  }

  :deep(.enhanced-input .el-input__inner) {
    height: 46px;
    line-height: 46px;
    font-size: 15px;
  }

  :deep(.enhanced-input .el-input__prefix),
  :deep(.enhanced-input .el-input__suffix) {
    height: 48px;
  }

  .floating-shapes {
    display: none; /* 移动端隐藏浮动形状以提升性能 */
  }
}

/* 表单项样式优化 */
:deep(.el-form-item) {
  margin-bottom: 24px;
}

:deep(.el-input__wrapper) {
  border-radius: 12px;
}

:deep(.el-button) {
  border-radius: 16px;
}
</style>
