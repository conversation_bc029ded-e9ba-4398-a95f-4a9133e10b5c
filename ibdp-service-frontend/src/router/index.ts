import { createRouter, createWebHistory } from 'vue-router'
import type { RouteRecordRaw } from 'vue-router'

const routes: RouteRecordRaw[] = [
  {
    path: '/login',
    name: 'Login',
    component: () => import('@/views/Login.vue'),
    meta: {
      title: '登录',
      hideInMenu: true,
      requiresAuth: false
    }
  },
  {
    path: '/',
    name: 'Layout',
    component: () => import('@/components/Layout.vue'),
    redirect: '/home',
    children: [
      {
        path: '/home',
        name: 'Home',
        component: () => import('@/views/Home.vue'),
        meta: {
          title: '首页',
          icon: 'House'
        }
      },
      {
        path: '/debug',
        name: 'Debug',
        component: () => import('@/views/Debug.vue'),
        meta: {
          title: '调试页面',
          hideInMenu: true
        }
      },
      {
        path: '/project',
        name: 'Project',
        component: () => import('@/views/project/index.vue'),
        meta: {
          title: '项目管理',
          icon: 'Folder'
        }
      },
      {
        path: '/project/:projectId',
        name: 'ProjectDetail',
        component: () => import('@/views/project/detail/index.vue'),
        redirect: (to) => `/project/${to.params.projectId}/overview`,
        meta: {
          title: '项目详情',
          hideInMenu: true
        },
        children: [
          {
            path: 'overview',
            name: 'ProjectOverview',
            component: () => import('@/views/project/detail/overview.vue'),
            meta: {
              title: '项目概览'
            }
          },
          {
            path: 'workflow',
            name: 'ProjectWorkflow',
            component: () => import('@/views/project/workflow/index.vue'),
            meta: {
              title: '流程定义'
            }
          },
          {
            path: 'workflow/:workflowId/design',
            name: 'WorkflowDesign',
            component: () => import('@/views/project/workflow/design/index.vue'),
            meta: {
              title: '工作流设计器'
            }
          },
          {
            path: 'workflow-instance',
            name: 'ProjectWorkflowInstance',
            component: () => import('@/views/project/workflow-instance/index.vue'),
            meta: {
              title: '流程实例'
            }
          },
          {
            path: 'workflow-instance/monitor/:executionId',
            name: 'WorkflowMonitor',
            component: () => import('@/views/project/workflow/monitor.vue'),
            meta: {
              title: '流程监控',
              activeMenu: '/project/:projectId/workflow-instance'
            }
          },
          {
            path: 'node-instance',
            name: 'ProjectNodeInstance',
            component: () => import('@/views/project/node-instance/index.vue'),
            meta: {
              title: '节点实例'
            }
          }
        ]
      },

      {
        path: '/datasource',
        name: 'Datasource',
        component: () => import('@/views/datasource/index.vue'),
        meta: {
          title: '数据源管理',
          icon: 'Database'
        }
      },
      {
        path: '/executor',
        name: 'Executor',
        component: () => import('@/views/executor/index.vue'),
        meta: {
          title: '执行器管理',
          icon: 'Monitor'
        }
      },

      {
        path: '/datax',
        name: 'DataX',
        redirect: '/datax/sync-monitor',
        meta: {
          title: 'DataX管理',
          icon: 'Connection'
        },
        children: [
          {
            path: 'sync-monitor',
            name: 'DataXSyncMonitor',
            component: () => import('@/views/datax/sync-monitor/index.vue'),
            meta: {
              title: '增量同步监控'
            }
          },
          {
            path: 'test',
            name: 'DataXTest',
            component: () => import('@/views/datax/test/index.vue'),
            meta: {
              title: '功能测试'
            }
          }
        ]
      },
      {
        path: '/alert',
        name: 'Alert',
        component: () => import('@/views/alert/index.vue'),
        redirect: '/alert/template',
        meta: {
          title: '告警中心',
          icon: 'Bell'
        },
        children: [
          {
            path: 'template',
            name: 'AlertTemplate',
            component: () => import('@/views/alert/template/index.vue'),
            meta: {
              title: '告警模板'
            }
          },
          {
            path: 'channel',
            name: 'AlertChannel',
            component: () => import('@/views/alert/channel/index.vue'),
            meta: {
              title: '通道配置'
            }
          },
          {
            path: 'record',
            name: 'AlertRecord',
            component: () => import('@/views/alert/record/index.vue'),
            meta: {
              title: '发送记录'
            }
          }
        ]
      }
    ]
  }
]

const router = createRouter({
  history: createWebHistory(),
  routes
})

// 路由守卫
router.beforeEach(async (to, from, next) => {
  // 设置页面标题
  if (to.meta?.title) {
    document.title = `${to.meta.title} - IBDP数据开发平台`
  }

  // 导入用户Store（动态导入避免循环依赖）
  const { useUserStore } = await import('@/stores/user')
  const userStore = useUserStore()

  // 初始化用户状态
  userStore.initUserState()

  // 如果访问登录页面且已经登录，跳转到首页
  if (to.path === '/login' && userStore.checkLoginStatus()) {
    next('/home')
    return
  }

  // 如果访问需要登录的页面但未登录，跳转到登录页
  if (to.meta?.requiresAuth !== false && !userStore.checkLoginStatus()) {
    next('/login')
    return
  }

  next()
})

export default router
