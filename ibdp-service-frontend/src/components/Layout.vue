<template>
  <div class="layout-container">
    <!-- 顶部导航 -->
    <el-header class="layout-header" height="60px">
      <div class="header-content">
        <!-- Logo和标题 -->
        <div class="header-left">
          <div class="logo">
            <el-icon size="28" color="#409eff">
              <DataBoard />
            </el-icon>
            <span class="title">SEVB数据开发平台</span>
          </div>
        </div>

        <!-- 导航菜单 -->
        <div class="header-center">
          <el-menu
            :default-active="activeMenu"
            mode="horizontal"
            :ellipsis="false"
            @select="handleMenuSelect"
            class="header-menu"
          >
            <el-menu-item index="/home">
              <el-icon><House /></el-icon>
              <span>首页</span>
            </el-menu-item>
            <el-menu-item index="/project">
              <el-icon><Folder /></el-icon>
              <span>项目管理</span>
            </el-menu-item>
            <el-menu-item index="/datasource">
              <el-icon><Coin /></el-icon>
              <span>数据源管理</span>
            </el-menu-item>
            <el-menu-item index="/executor">
              <el-icon><Monitor /></el-icon>
              <span>执行器管理</span>
            </el-menu-item>

            <el-menu-item index="/alert">
              <el-icon><Bell /></el-icon>
              <span>告警中心</span>
            </el-menu-item>
          </el-menu>
        </div>

        <!-- 右侧用户信息 -->
        <div class="header-right">
          <el-dropdown @command="handleUserCommand">
            <span class="user-info">
              <el-icon><User /></el-icon>
              <span>{{ userStore.userInfo?.username || '管理员' }}</span>
              <el-icon class="el-icon--right"><ArrowDown /></el-icon>
            </span>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item command="profile">个人设置</el-dropdown-item>
                <el-dropdown-item command="logout" divided>退出登录</el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </div>
      </div>
    </el-header>

    <!-- 主内容区域 -->
    <el-main class="layout-main">
      <router-view />
    </el-main>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { ElMessageBox } from 'element-plus'
import {
  DataBoard,
  House,
  Folder,
  Coin,
  Monitor,
  Bell,
  User,
  ArrowDown
} from '@element-plus/icons-vue'
import { useUserStore } from '@/stores/user'

const router = useRouter()
const route = useRoute()
const userStore = useUserStore()

// 当前激活的菜单
const activeMenu = computed(() => {
  const path = route.path
  // 处理告警中心的子路由
  if (path.startsWith('/alert')) {
    return '/alert'
  }
  // 处理项目管理的子路由
  if (path.startsWith('/project')) {
    return '/project'
  }
  // 处理数据源的子路由
  if (path.startsWith('/datasource')) {
    return '/datasource'
  }
  // 其他路由直接返回
  return path
})

// 处理菜单选择
const handleMenuSelect = (index: string) => {
  router.push(index)
}

// 处理用户下拉菜单命令
const handleUserCommand = async (command: string) => {
  switch (command) {
    case 'profile':
      // TODO: 跳转到个人设置页面
      console.log('个人设置')
      break
    case 'logout':
      await handleLogout()
      break
  }
}

// 处理退出登录
const handleLogout = async () => {
  try {
    await ElMessageBox.confirm(
      '确定要退出登录吗？',
      '确认退出',
      {
        type: 'warning',
        confirmButtonText: '确定',
        cancelButtonText: '取消'
      }
    )

    // 调用退出登录
    const success = await userStore.logout()
    if (success) {
      // 跳转到登录页
      router.push('/login')
    }
  } catch (error) {
    // 用户取消操作
  }
}
</script>

<style scoped>
.layout-container {
  height: 100vh;
  display: flex;
  flex-direction: column;
}

.layout-header {
  background: #fff;
  border-bottom: 1px solid #e4e7ed;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.12), 0 0 6px rgba(0, 0, 0, 0.04);
  padding: 0;
}

.header-content {
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 20px;
}

.header-left {
  flex-shrink: 0;
}

.logo {
  display: flex;
  align-items: center;
  gap: 12px;
}

.title {
  font-size: 20px;
  font-weight: 600;
  color: #303133;
}

.header-center {
  flex: 1;
  display: flex;
  justify-content: center;
}

.header-menu {
  border-bottom: none;
}

.header-menu .el-menu-item {
  height: 60px;
  line-height: 60px;
  border-bottom: 2px solid transparent;
}

.header-menu .el-menu-item:hover,
.header-menu .el-menu-item.is-active {
  border-bottom-color: var(--el-color-primary);
  background-color: transparent;
}

.header-right {
  flex-shrink: 0;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.3s;
}

.user-info:hover {
  background-color: #f5f7fa;
}

.layout-main {
  flex: 1;
  background: #f5f7fa;
  padding: 0;
  overflow: auto;
  min-height: 0;
  position: relative;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .header-content {
    padding: 0 10px;
  }
  
  .title {
    display: none;
  }
  
  .header-menu .el-menu-item span {
    display: none;
  }
}
</style>
