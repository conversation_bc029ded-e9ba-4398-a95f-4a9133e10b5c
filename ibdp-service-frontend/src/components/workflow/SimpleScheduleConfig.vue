<template>
  <el-dialog
    :model-value="visible"
    @update:model-value="$emit('update:visible', $event)"
    title="调度配置"
    width="700px"
    :close-on-click-modal="false"
    class="schedule-config-dialog"
    @close="handleClose">


    
    <!-- 配置表单卡片 -->
    <div class="config-form-card">
      <div class="card-header">
        <el-icon class="header-icon"><Clock /></el-icon>
        <span class="header-title">调度配置</span>
      </div>
      <div class="card-content">
        <el-form
          ref="formRef"
          :model="form"
          :rules="rules"
          label-width="120px"
          class="config-form">
          <el-form-item label="Cron表达式" prop="cronExpression">
            <div class="cron-input-container">
              <div class="cron-input-wrapper">
                <el-input
                  v-model="form.cronExpression"
                  placeholder="请输入Cron表达式，如：0 0 2 * * ?"
                  clearable
                  size="large"
                  class="cron-input">
                  <template #prepend>
                    <el-icon><Timer /></el-icon>
                  </template>
                </el-input>
              </div>
              <el-button
                @click="showCronHelper"
                type="primary"
                class="help-button">
                <el-icon><QuestionFilled /></el-icon>
                帮助
              </el-button>
            </div>
            <div class="cron-examples">
              <div class="examples-title">
                <el-icon><Star /></el-icon>
                常用示例
              </div>
              <div class="examples-list">
                <el-tag
                  v-for="example in cronExamples"
                  :key="example.cron"
                  size="small"
                  class="example-tag"
                  @click="form.cronExpression = example.cron"
                  :title="`点击使用：${example.cron}`">
                  {{ example.desc }}
                </el-tag>
              </div>
            </div>
          </el-form-item>
        </el-form>

        <!-- 操作说明 -->
        <div class="operation-tips">
          <div class="tips-header">
            <el-icon class="tips-icon"><InfoFilled /></el-icon>
            <span>操作说明</span>
          </div>
          <ul class="tips-list">
            <li>修改Cron表达式后，调度任务将自动更新</li>
            <li>工作流状态为"运行中"时，调度任务正在执行</li>
            <li>如需启动/停止调度，请在工作流列表中上线/下线工作流</li>
          </ul>
        </div>
      </div>
    </div>
    
    <template #footer>
      <el-button @click="handleClose">取消</el-button>
      <el-button type="primary" @click="handleSave" :loading="loading">
        保存
      </el-button>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, watch, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import type { FormInstance, FormRules } from 'element-plus'
import {
  Clock,
  Timer,
  QuestionFilled,
  Star,
  InfoFilled
} from '@element-plus/icons-vue'
import { workflowApi } from '@/api/workflow'
import type { Workflow } from '@/types/workflow'

interface Props {
  visible: boolean
  workflow?: Workflow
}

const props = defineProps<Props>()
const emit = defineEmits<{
  'update:visible': [value: boolean]
  refresh: []
}>()

const loading = ref(false)
const formRef = ref<FormInstance>()
const form = ref({
  cronExpression: ''
})

const rules: FormRules = {
  cronExpression: [
    { required: true, message: '请输入Cron表达式', trigger: 'blur' },
    {
      pattern: /^[0-9\s\*\/\-\,\?A-Z]+$/,
      message: 'Cron表达式格式不正确',
      trigger: 'blur'
    }
  ]
}

// Cron表达式示例
const cronExamples = [
  { cron: '0 0 2 * * ?', desc: '每天凌晨2点' },
  { cron: '0 */5 * * * ?', desc: '每5分钟' },
  { cron: '0 0 * * * ?', desc: '每小时' },
  { cron: '0 0 9 * * MON-FRI', desc: '工作日9点' },
  { cron: '0 0 0 1 * ?', desc: '每月1号' },
  { cron: '0 0 0 ? * SUN', desc: '每周日' }
]

// 监听对话框显示状态
watch(() => props.visible, (newVal) => {
  if (newVal && props.workflow) {
    form.value.cronExpression = props.workflow.cronExpression || ''
  }
})

// 内部可见状态
const internalVisible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
})

// 获取状态名称
const getStatusName = (status?: number) => {
  const statusMap: Record<number, string> = {
    0: '草稿',
    1: '已发布',
    2: '运行中',  // 已上线 = 正在调度运行
    3: '已停止'   // 已下线 = 已停止调度
  }
  return status !== undefined ? statusMap[status] || '未知' : '未知'
}

// 获取状态标签类型
const getStatusTagType = (status?: number) => {
  const typeMap: Record<number, string> = {
    0: 'info',
    1: 'warning', 
    2: 'success',
    3: 'danger'
  }
  return status !== undefined ? typeMap[status] || '' : ''
}

// 关闭对话框
const handleClose = () => {
  internalVisible.value = false
  // 重置表单
  if (formRef.value) {
    formRef.value.resetFields()
  }
}

// 保存配置
const handleSave = async () => {
  if (!formRef.value || !props.workflow) return
  
  try {
    await formRef.value.validate()
    
    loading.value = true
    
    // 调用更新调度配置接口
    await workflowApi.updateScheduleConfig({
      workflowId: props.workflow.id,
      cronExpression: form.value.cronExpression
    })
    
    ElMessage.success('调度配置更新成功')
    handleClose()
    emit('refresh')
  } catch (error: any) {
    ElMessage.error(error.message || '保存失败')
  } finally {
    loading.value = false
  }
}

// 显示Cron帮助
const showCronHelper = () => {
  ElMessageBox.alert(`
<div class="cron-help-content">
  <div class="help-section">
    <div class="section-header">
      <span class="section-icon">📝</span>
      <h4>Cron表达式格式</h4>
    </div>
    <div class="format-display">
      <div class="format-item">秒</div>
      <div class="format-item">分</div>
      <div class="format-item">时</div>
      <div class="format-item">日</div>
      <div class="format-item">月</div>
      <div class="format-item">周</div>
    </div>
  </div>



  <div class="help-section">
    <div class="section-header">
      <span class="section-icon">🔧</span>
      <h4>特殊字符说明</h4>
    </div>
    <div class="chars-grid">
      <div class="char-item">
        <code>*</code>
        <span>表示所有值</span>
      </div>
      <div class="char-item">
        <code>?</code>
        <span>表示不指定值（用于日和周）</span>
      </div>
      <div class="char-item">
        <code>-</code>
        <span>表示范围（如：1-5）</span>
      </div>
      <div class="char-item">
        <code>,</code>
        <span>表示列举（如：1,3,5）</span>
      </div>
      <div class="char-item">
        <code>/</code>
        <span>表示步长（如：*/5）</span>
      </div>
    </div>
  </div>
</div>

<style>
.cron-help-content {
  text-align: left;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.help-section {
  margin-bottom: 24px;
}

.section-header {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
}

.section-icon {
  font-size: 18px;
  margin-right: 8px;
}

.section-header h4 {
  margin: 0;
  color: #303133;
  font-size: 16px;
  font-weight: 600;
}

.format-display {
  display: flex;
  gap: 8px;
  margin-bottom: 8px;
}

.format-item {
  background: #f0f9ff;
  border: 1px solid #0ea5e9;
  color: #0369a1;
  padding: 4px 12px;
  border-radius: 6px;
  font-weight: 500;
  font-size: 14px;
}

.chars-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 12px;
}

.char-item {
  background: #f8fafc;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  padding: 12px;
  transition: all 0.2s;
}

.char-item:hover {
  border-color: #3b82f6;
  box-shadow: 0 2px 8px rgba(59, 130, 246, 0.1);
}

.char-item code {
  background: #1e293b;
  color: #f1f5f9;
  padding: 2px 6px;
  border-radius: 4px;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 13px;
  display: block;
  margin-bottom: 4px;
}

.char-item span {
  color: #64748b;
  font-size: 13px;
}
</style>
  `, 'Cron表达式帮助', {
    confirmButtonText: '知道了',
    dangerouslyUseHTMLString: true,
    customStyle: {
      width: '600px'
    }
  })
}
</script>

<style scoped>
/* 对话框样式 */
:deep(.schedule-config-dialog) {
  .el-dialog__header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 20px 24px;
    margin: 0;
  }

  .el-dialog__title {
    color: white;
    font-weight: 600;
    font-size: 18px;
  }

  .el-dialog__headerbtn .el-dialog__close {
    color: white;
    font-size: 18px;
  }

  .el-dialog__body {
    padding: 24px;
    background: #f8fafc;
  }

  .el-dialog__footer {
    background: white;
    border-top: 1px solid #e2e8f0;
    padding: 16px 24px;
  }
}

/* 卡片样式 */
.config-form-card {
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
  margin-bottom: 20px;
  overflow: hidden;
  border: 1px solid #e2e8f0;
}

.card-header {
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  padding: 16px 20px;
  border-bottom: 1px solid #e2e8f0;
  display: flex;
  align-items: center;
}

.header-icon {
  color: #3b82f6;
  font-size: 18px;
  margin-right: 8px;
}

.header-title {
  font-weight: 600;
  color: #1e293b;
  font-size: 16px;
}

.card-content {
  padding: 20px;
}



/* 表单样式 */
.config-form {
  margin-bottom: 20px;
}

/* Cron输入容器 - 固定宽度布局 */
.cron-input-container {
  display: flex;
  gap: 12px;
  align-items: flex-start;
  margin-bottom: 16px;
}

.cron-input-wrapper {
  flex: 1;
  min-width: 0; /* 防止flex项目溢出 */
}

.help-button {
  flex-shrink: 0; /* 防止按钮被压缩 */
  height: 40px; /* 与large size input保持一致 */
}

/* 固定宽度的输入框样式 */
:deep(.cron-input) {
  width: 100%;

  .el-input__wrapper {
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
    border: 1px solid #d1d5db;
    transition: border-color 0.2s, box-shadow 0.2s;
    /* 固定宽度，防止clear按钮导致宽度变化 */
    min-width: 0;
    width: 100%;
  }

  .el-input__wrapper:hover {
    border-color: #3b82f6;
    box-shadow: 0 2px 12px rgba(59, 130, 246, 0.1);
  }

  .el-input__wrapper.is-focus {
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
  }

  /* 确保内部元素不会导致宽度变化 */
  .el-input__inner {
    width: 100%;
  }

  /* 清除按钮样式优化 */
  .el-input__suffix {
    position: absolute;
    right: 8px;
  }

  .el-input__clear {
    font-size: 14px;
  }
}

/* Cron示例样式 */
.cron-examples {
  background: #f8fafc;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  padding: 16px;
}

.examples-title {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
  color: #374151;
  font-weight: 500;
  font-size: 14px;
}

.examples-title .el-icon {
  color: #eab308;
  margin-right: 6px;
}

.examples-list {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.example-tag {
  cursor: pointer;
  transition: all 0.2s;
  border: 1px solid #d1d5db;
  background: white;
}

.example-tag:hover {
  background: #3b82f6;
  color: white;
  border-color: #3b82f6;
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(59, 130, 246, 0.2);
}

/* 操作说明样式 */
.operation-tips {
  background: linear-gradient(135deg, #eff6ff 0%, #dbeafe 100%);
  border: 1px solid #bfdbfe;
  border-radius: 8px;
  padding: 16px;
}

.tips-header {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
  color: #1e40af;
  font-weight: 600;
  font-size: 14px;
}

.tips-icon {
  color: #3b82f6;
  margin-right: 6px;
}

.tips-list {
  margin: 0;
  padding-left: 20px;
  color: #1e40af;
}

.tips-list li {
  margin-bottom: 6px;
  font-size: 13px;
  line-height: 1.5;
}

.tips-list li:last-child {
  margin-bottom: 0;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .info-row {
    flex-direction: column;
    gap: 16px;
  }

  .examples-list {
    flex-direction: column;
  }

  .example-tag {
    text-align: center;
  }
}
</style>
