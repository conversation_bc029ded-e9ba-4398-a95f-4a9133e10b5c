<template>
  <div class="workflow-schedule-status">
    <!-- 调度中心编排模式（统一模式） -->
    <div class="central-mode">
      <el-descriptions title="工作流调度" :column="2" border>
        <el-descriptions-item label="工作流状态">
          <el-tag :type="getScheduleStatusTagType(workflow.status)">
            {{ getScheduleStatusName(workflow.status) }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="下次执行时间">
          <span v-if="nextExecutionTime">{{ nextExecutionTime }}</span>
          <span v-else class="text-gray-400">-</span>
        </el-descriptions-item>
        <el-descriptions-item label="Cron表达式" :span="2">
          <code v-if="workflow.cronExpression">{{ workflow.cronExpression }}</code>
          <span v-else class="text-gray-400">未设置</span>
        </el-descriptions-item>
      </el-descriptions>

      <div class="mt-4">
        <el-button
          v-if="workflow.status !== 2"
          type="primary"
          @click="handleOnline"
          :loading="loading">
          上线
        </el-button>
        <el-button
          v-if="workflow.status === 2"
          type="danger"
          @click="handleOffline"
          :loading="loading">
          下线
        </el-button>
        <el-button
          v-if="workflow.status === 2"
          @click="handleEditSchedule">
          修改调度
        </el-button>
      </div>
    </div>
    
    <!-- 调度配置对话框 -->
    <el-dialog 
      v-model="scheduleDialogVisible" 
      title="调度配置" 
      width="600px">
      <el-form 
        ref="scheduleFormRef" 
        :model="scheduleForm" 
        :rules="scheduleRules" 
        label-width="120px">
        <el-form-item label="Cron表达式" prop="cronExpression">
          <el-input 
            v-model="scheduleForm.cronExpression" 
            placeholder="请输入Cron表达式，如：0 0 2 * * ?"
            clearable>
            <template #append>
              <el-button @click="showCronHelper">帮助</el-button>
            </template>
          </el-input>
          <div class="text-sm text-gray-500 mt-1">
            常用示例：每天凌晨2点执行 (0 0 2 * * ?)，每5分钟执行 (0 */5 * * * ?)
          </div>
        </el-form-item>
      </el-form>
      
      <template #footer>
        <el-button @click="scheduleDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleSaveSchedule" :loading="loading">
          保存
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import type { FormInstance, FormRules } from 'element-plus'
import { workflowApi } from '@/api/workflow'
import type { Workflow } from '@/types/workflow'

interface Props {
  workflow: Workflow
}

const props = defineProps<Props>()
const emit = defineEmits<{
  refresh: []
}>()

const loading = ref(false)
const scheduleDialogVisible = ref(false)
const scheduleFormRef = ref<FormInstance>()
const scheduleForm = ref({
  cronExpression: ''
})

const scheduleRules: FormRules = {
  cronExpression: [
    { required: true, message: '请输入Cron表达式', trigger: 'blur' },
    { 
      pattern: /^[0-9\s\*\/\-\,\?]+$/, 
      message: 'Cron表达式格式不正确', 
      trigger: 'blur' 
    }
  ]
}

// 计算下次执行时间
const nextExecutionTime = computed(() => {
  if (!props.workflow.cronExpression || props.workflow.status !== 2) {
    return null
  }
  
  // 这里可以集成一个Cron解析库来计算下次执行时间
  // 暂时返回占位符
  return '计算中...'
})

// 获取工作流状态名称
const getScheduleStatusName = (status: number) => {
  const statusMap: Record<number, string> = {
    0: '草稿',
    1: '已发布',
    2: '运行中',  // 已上线 = 正在调度运行
    3: '已停止'   // 已下线 = 已停止调度
  }
  return statusMap[status] || '未知'
}

// 获取调度状态标签类型
const getScheduleStatusTagType = (status: number) => {
  const typeMap: Record<number, string> = {
    0: 'info',
    1: 'warning', 
    2: 'success',
    3: 'danger'
  }
  return typeMap[status] || ''
}

// 处理上线
const handleOnline = async () => {
  if (!props.workflow.cronExpression) {
    ElMessage.warning('请先设置Cron表达式')
    handleEditSchedule()
    return
  }
  
  try {
    await ElMessageBox.confirm('确定要上线此工作流吗？', '确认上线', {
      type: 'warning'
    })
    
    loading.value = true
    await workflowApi.onlineWorkflow(props.workflow.id, props.workflow.cronExpression)
    ElMessage.success('工作流上线成功')
    emit('refresh')
  } catch (error: any) {
    if (error !== 'cancel') {
      ElMessage.error(error.message || '上线失败')
    }
  } finally {
    loading.value = false
  }
}

// 处理下线
const handleOffline = async () => {
  try {
    await ElMessageBox.confirm('确定要下线此工作流吗？', '确认下线', {
      type: 'warning'
    })
    
    loading.value = true
    await workflowApi.offlineWorkflow(props.workflow.id)
    ElMessage.success('工作流下线成功')
    emit('refresh')
  } catch (error: any) {
    if (error !== 'cancel') {
      ElMessage.error(error.message || '下线失败')
    }
  } finally {
    loading.value = false
  }
}

// 处理编辑调度
const handleEditSchedule = () => {
  scheduleForm.value.cronExpression = props.workflow.cronExpression || ''
  scheduleDialogVisible.value = true
}



// 保存调度配置
const handleSaveSchedule = async () => {
  if (!scheduleFormRef.value) return
  
  try {
    await scheduleFormRef.value.validate()
    
    loading.value = true
    
    if (props.workflow.status === 2) {
      // 已上线：更新调度配置
      await workflowApi.updateScheduleConfig(props.workflow.id, {
        cronExpression: scheduleForm.value.cronExpression
      })
      ElMessage.success('调度配置更新成功')
    } else {
      // 未上线：直接上线
      await workflowApi.onlineWorkflow(props.workflow.id, scheduleForm.value.cronExpression)
      ElMessage.success('工作流上线成功')
    }
    
    scheduleDialogVisible.value = false
    emit('refresh')
  } catch (error: any) {
    ElMessage.error(error.message || '保存失败')
  } finally {
    loading.value = false
  }
}

// 显示Cron帮助
const showCronHelper = () => {
  ElMessageBox.alert(`
Cron表达式格式：秒 分 时 日 月 周

常用示例：
• 每天凌晨2点：0 0 2 * * ?
• 每5分钟：0 */5 * * * ?
• 每小时：0 0 * * * ?
• 工作日上午9点：0 0 9 * * MON-FRI
• 每月1号凌晨：0 0 0 1 * ?
• 每周日凌晨：0 0 0 ? * SUN

特殊字符：
• * 表示所有值
• ? 表示不指定值
• - 表示范围
• , 表示列举
• / 表示步长
  `, 'Cron表达式帮助', {
    confirmButtonText: '知道了'
  })
}

onMounted(() => {
  // 组件挂载时的初始化逻辑
})
</script>

<style scoped>
.workflow-schedule-status {
  padding: 16px;
}

.central-mode,
.xxl-job-mode {
  background: #f9f9f9;
  padding: 16px;
  border-radius: 8px;
}

.text-gray-400 {
  color: #9ca3af;
}

.text-gray-500 {
  color: #6b7280;
}

.text-sm {
  font-size: 0.875rem;
}

.mt-1 {
  margin-top: 0.25rem;
}

.mt-4 {
  margin-top: 1rem;
}

code {
  background: #f1f5f9;
  padding: 2px 6px;
  border-radius: 4px;
  font-family: 'Courier New', monospace;
  font-size: 0.875rem;
}
</style>
