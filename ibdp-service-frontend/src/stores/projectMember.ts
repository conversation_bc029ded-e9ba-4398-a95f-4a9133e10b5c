import { defineStore } from 'pinia'
import { ref } from 'vue'
import { ElMessage } from 'element-plus'
import { projectMemberApi } from '@/api/projectMember'
import type { ProjectMember, ProjectRoleOption, PageParams, PageResponse } from '@/types'

export const useProjectMemberStore = defineStore('projectMember', () => {
  // 状态
  const members = ref<ProjectMember[]>([])
  const currentMember = ref<ProjectMember | null>(null)
  const projectRoles = ref<ProjectRoleOption[]>([])
  const loading = ref(false)

  // 分页查询项目成员列表
  const fetchMembers = async (params: PageParams & {
    projectId?: number
    username?: string
    role?: string
    status?: number
  }): Promise<PageResponse<ProjectMember>> => {
    try {
      loading.value = true
      const response = await projectMemberApi.pageList(params)
      if (response.code === 200) {
        members.value = response.content.data
        return response.content
      } else {
        ElMessage.error(response.msg || '查询项目成员列表失败')
        return { recordsTotal: 0, recordsFiltered: 0, data: [] }
      }
    } catch (error) {
      console.error('查询项目成员列表失败:', error)
      ElMessage.error('查询项目成员列表失败')
      return { recordsTotal: 0, recordsFiltered: 0, data: [] }
    } finally {
      loading.value = false
    }
  }

  // 添加项目成员
  const addMember = async (member: ProjectMember): Promise<boolean> => {
    try {
      loading.value = true
      const response = await projectMemberApi.add(member)
      if (response.code === 200) {
        ElMessage.success(response.msg || '添加项目成员成功')
        return true
      } else {
        ElMessage.error(response.msg || '添加项目成员失败')
        return false
      }
    } catch (error) {
      console.error('添加项目成员失败:', error)
      ElMessage.error('添加项目成员失败')
      return false
    } finally {
      loading.value = false
    }
  }

  // 批量添加项目成员
  const batchAddMembers = async (members: ProjectMember[]): Promise<boolean> => {
    try {
      loading.value = true
      const response = await projectMemberApi.batchAdd(members)
      if (response.code === 200) {
        ElMessage.success(response.msg || '批量添加项目成员成功')
        return true
      } else {
        ElMessage.error(response.msg || '批量添加项目成员失败')
        return false
      }
    } catch (error) {
      console.error('批量添加项目成员失败:', error)
      ElMessage.error('批量添加项目成员失败')
      return false
    } finally {
      loading.value = false
    }
  }

  // 移除项目成员
  const removeMember = async (id: number): Promise<boolean> => {
    try {
      loading.value = true
      const response = await projectMemberApi.remove(id)
      if (response.code === 200) {
        ElMessage.success(response.msg || '移除项目成员成功')
        return true
      } else {
        ElMessage.error(response.msg || '移除项目成员失败')
        return false
      }
    } catch (error) {
      console.error('移除项目成员失败:', error)
      ElMessage.error('移除项目成员失败')
      return false
    } finally {
      loading.value = false
    }
  }

  // 根据项目和用户移除成员
  const removeMemberByUser = async (projectId: number, userId: string): Promise<boolean> => {
    try {
      loading.value = true
      const response = await projectMemberApi.removeByUser(projectId, userId)
      if (response.code === 200) {
        ElMessage.success(response.msg || '移除项目成员成功')
        return true
      } else {
        ElMessage.error(response.msg || '移除项目成员失败')
        return false
      }
    } catch (error) {
      console.error('移除项目成员失败:', error)
      ElMessage.error('移除项目成员失败')
      return false
    } finally {
      loading.value = false
    }
  }

  // 更新成员角色
  const updateMemberRole = async (id: number, role: string): Promise<boolean> => {
    try {
      loading.value = true
      const response = await projectMemberApi.updateRole(id, role)
      if (response.code === 200) {
        ElMessage.success(response.msg || '更新成员角色成功')
        return true
      } else {
        ElMessage.error(response.msg || '更新成员角色失败')
        return false
      }
    } catch (error) {
      console.error('更新成员角色失败:', error)
      ElMessage.error('更新成员角色失败')
      return false
    } finally {
      loading.value = false
    }
  }

  // 更新成员状态
  const updateMemberStatus = async (id: number, status: number): Promise<boolean> => {
    try {
      loading.value = true
      const response = await projectMemberApi.updateStatus(id, status)
      if (response.code === 200) {
        ElMessage.success(response.msg || '更新成员状态成功')
        return true
      } else {
        ElMessage.error(response.msg || '更新成员状态失败')
        return false
      }
    } catch (error) {
      console.error('更新成员状态失败:', error)
      ElMessage.error('更新成员状态失败')
      return false
    } finally {
      loading.value = false
    }
  }

  // 查询项目成员详情
  const loadMember = async (id: number): Promise<ProjectMember | null> => {
    try {
      loading.value = true
      const response = await projectMemberApi.load(id)
      if (response.code === 200) {
        currentMember.value = response.content
        return response.content
      } else {
        ElMessage.error(response.msg || '查询项目成员详情失败')
        return null
      }
    } catch (error) {
      console.error('查询项目成员详情失败:', error)
      ElMessage.error('查询项目成员详情失败')
      return null
    } finally {
      loading.value = false
    }
  }

  // 根据项目和用户查询成员
  const loadMemberByProjectAndUser = async (projectId: number, userId: string): Promise<ProjectMember | null> => {
    try {
      const response = await projectMemberApi.loadByProjectAndUser(projectId, userId)
      if (response.code === 200) {
        return response.content
      } else {
        return null
      }
    } catch (error) {
      console.error('查询项目成员失败:', error)
      return null
    }
  }

  // 查询项目成员列表
  const fetchMembersByProjectId = async (projectId: number): Promise<ProjectMember[]> => {
    try {
      const response = await projectMemberApi.findByProjectId(projectId)
      if (response.code === 200) {
        return response.content
      } else {
        ElMessage.error(response.msg || '查询项目成员列表失败')
        return []
      }
    } catch (error) {
      console.error('查询项目成员列表失败:', error)
      ElMessage.error('查询项目成员列表失败')
      return []
    }
  }

  // 查询用户参与的项目
  const fetchMembersByUserId = async (userId: string): Promise<ProjectMember[]> => {
    try {
      const response = await projectMemberApi.findByUserId(userId)
      if (response.code === 200) {
        return response.content
      } else {
        ElMessage.error(response.msg || '查询用户项目失败')
        return []
      }
    } catch (error) {
      console.error('查询用户项目失败:', error)
      ElMessage.error('查询用户项目失败')
      return []
    }
  }

  // 查询项目管理员
  const fetchAdminsByProjectId = async (projectId: number): Promise<ProjectMember[]> => {
    try {
      const response = await projectMemberApi.findAdmins(projectId)
      if (response.code === 200) {
        return response.content
      } else {
        ElMessage.error(response.msg || '查询项目管理员失败')
        return []
      }
    } catch (error) {
      console.error('查询项目管理员失败:', error)
      ElMessage.error('查询项目管理员失败')
      return []
    }
  }

  // 检查是否为项目成员
  const checkIsMember = async (projectId: number, userId: string): Promise<boolean> => {
    try {
      const response = await projectMemberApi.isMember(projectId, userId)
      if (response.code === 200) {
        return response.content
      } else {
        return false
      }
    } catch (error) {
      console.error('检查项目成员失败:', error)
      return false
    }
  }

  // 统计项目成员数量
  const countMembersByProjectId = async (projectId: number): Promise<number> => {
    try {
      const response = await projectMemberApi.count(projectId)
      if (response.code === 200) {
        return response.content
      } else {
        return 0
      }
    } catch (error) {
      console.error('统计项目成员数量失败:', error)
      return 0
    }
  }

  // 获取项目角色列表
  const fetchProjectRoles = async (): Promise<ProjectRoleOption[]> => {
    try {
      if (projectRoles.value.length > 0) {
        return projectRoles.value
      }
      
      const response = await projectMemberApi.getProjectRoles()
      if (response.code === 200) {
        projectRoles.value = response.content
        return response.content
      } else {
        ElMessage.error(response.msg || '获取项目角色失败')
        return []
      }
    } catch (error) {
      console.error('获取项目角色失败:', error)
      ElMessage.error('获取项目角色失败')
      return []
    }
  }

  return {
    // 状态
    members,
    currentMember,
    projectRoles,
    loading,
    
    // 方法
    fetchMembers,
    addMember,
    batchAddMembers,
    removeMember,
    removeMemberByUser,
    updateMemberRole,
    updateMemberStatus,
    loadMember,
    loadMemberByProjectAndUser,
    fetchMembersByProjectId,
    fetchMembersByUserId,
    fetchAdminsByProjectId,
    checkIsMember,
    countMembersByProjectId,
    fetchProjectRoles
  }
})
