import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { datasourceApi } from '@/api/datasource'
import type { Datasource, DatasourceTypeOption, PageResponse } from '@/types'
import { ElMessage } from 'element-plus'

export const useDatasourceStore = defineStore('datasource', () => {
  // 状态
  const datasources = ref<Datasource[]>([])
  const datasourceTypes = ref<DatasourceTypeOption[]>([])
  const loading = ref(false)
  const total = ref(0)

  // 计算属性
  const enabledDatasources = computed(() => 
    datasources.value.filter(ds => ds.status === 1)
  )

  // 获取数据源类型列表
  const fetchDatasourceTypes = async () => {
    try {
      const response = await datasourceApi.getTypes()
      datasourceTypes.value = response.content || []
    } catch (error) {
      console.error('获取数据源类型失败:', error)
    }
  }

  // 分页查询数据源列表
  const fetchDatasources = async (params: {
    pageNum: number
    pageSize: number
    name?: string
    type?: string
    status?: number
  }) => {
    try {
      loading.value = true
      const response = await datasourceApi.pageList(params)
      const pageData: PageResponse<Datasource> = response.content
      datasources.value = pageData.data || []
      total.value = pageData.recordsTotal || 0
      return pageData
    } catch (error) {
      console.error('获取数据源列表失败:', error)
      datasources.value = []
      total.value = 0
      throw error
    } finally {
      loading.value = false
    }
  }

  // 获取所有启用的数据源
  const fetchEnabledDatasources = async () => {
    try {
      const response = await datasourceApi.list()
      return response.content || []
    } catch (error) {
      console.error('获取启用数据源失败:', error)
      return []
    }
  }

  // 根据ID获取数据源详情
  const getDatasourceById = async (id: number) => {
    try {
      const response = await datasourceApi.load(id)
      return response.content
    } catch (error) {
      console.error('获取数据源详情失败:', error)
      throw error
    }
  }

  // 添加数据源
  const addDatasource = async (datasource: Datasource) => {
    try {
      await datasourceApi.add(datasource)
      ElMessage.success('数据源添加成功')
      return true
    } catch (error) {
      console.error('添加数据源失败:', error)
      throw error
    }
  }

  // 更新数据源
  const updateDatasource = async (datasource: Datasource) => {
    try {
      await datasourceApi.update(datasource)
      ElMessage.success('数据源更新成功')
      return true
    } catch (error) {
      console.error('更新数据源失败:', error)
      throw error
    }
  }

  // 删除数据源
  const deleteDatasource = async (id: number) => {
    try {
      await datasourceApi.delete(id)
      ElMessage.success('数据源删除成功')
      return true
    } catch (error) {
      console.error('删除数据源失败:', error)
      throw error
    }
  }

  // 测试数据源连接
  const testConnection = async (id: number) => {
    try {
      const response = await datasourceApi.testConnection(id)
      ElMessage.success(response.content || '连接测试成功')
      return true
    } catch (error) {
      console.error('连接测试失败:', error)
      throw error
    }
  }

  // 仅测试连接（不保存结果）
  const testConnectionOnly = async (datasource: Datasource) => {
    try {
      const response = await datasourceApi.testConnectionOnly(datasource)
      ElMessage.success(response.content || '连接测试成功')
      return true
    } catch (error) {
      console.error('连接测试失败:', error)
      throw error
    }
  }

  // 获取默认端口
  const getDefaultPort = async (type: string) => {
    try {
      const response = await datasourceApi.getDefaultPort(type)
      return response.content || 0
    } catch (error) {
      console.error('获取默认端口失败:', error)
      return 0
    }
  }

  // 获取默认连接参数
  const getDefaultParams = async (type: string) => {
    try {
      const response = await datasourceApi.getDefaultParams(type)
      return response.content || ''
    } catch (error) {
      console.error('获取默认连接参数失败:', error)
      return ''
    }
  }

  return {
    // 状态
    datasources,
    datasourceTypes,
    loading,
    total,
    // 计算属性
    enabledDatasources,
    // 方法
    fetchDatasourceTypes,
    fetchDatasources,
    fetchEnabledDatasources,
    getDatasourceById,
    addDatasource,
    updateDatasource,
    deleteDatasource,
    testConnection,
    testConnectionOnly,
    getDefaultPort,
    getDefaultParams
  }
})
