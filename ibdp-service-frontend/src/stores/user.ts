import { defineStore } from 'pinia'
import { ref } from 'vue'
import { ElMessage } from 'element-plus'
import { authApi, type LoginParams, type UserInfo } from '@/api/auth'

export const useUserStore = defineStore('user', () => {
  // 状态
  const userInfo = ref<UserInfo | null>(null)
  const token = ref<string>('')
  const isLoggedIn = ref<boolean>(false)

  // 登录
  const login = async (params: LoginParams): Promise<boolean> => {
    try {
      const response = await authApi.login(params)
      if (response.code === 200) {
        // 登录成功
        isLoggedIn.value = true

        // 如果后端返回token，保存token
        if (response.content) {
          token.value = response.content
          localStorage.setItem('token', response.content)
        } else {
          // 即使没有token，也设置一个标识
          token.value = 'logged_in'
          localStorage.setItem('token', 'logged_in')
        }

        // 保存登录状态
        localStorage.setItem('isLoggedIn', 'true')

        // 获取用户信息（不阻塞登录流程）
        getCurrentUser().catch(error => {
          console.warn('获取用户信息失败:', error)
        })

        ElMessage.success('登录成功')
        return true
      } else {
        ElMessage.error(response.msg || '登录失败')
        return false
      }
    } catch (error) {
      console.error('登录失败:', error)
      ElMessage.error('登录失败，请检查网络连接')
      return false
    }
  }

  // 登出
  const logout = async (): Promise<boolean> => {
    try {
      const response = await authApi.logout()
      if (response.code === 200) {
        // 清除本地状态
        clearUserData()
        ElMessage.success('退出登录成功')
        return true
      } else {
        ElMessage.error(response.msg || '退出登录失败')
        return false
      }
    } catch (error) {
      console.error('退出登录失败:', error)
      // 即使接口调用失败，也清除本地状态
      clearUserData()
      return true
    }
  }

  // 清除用户数据
  const clearUserData = () => {
    userInfo.value = null
    token.value = ''
    isLoggedIn.value = false
    localStorage.removeItem('token')
    localStorage.removeItem('isLoggedIn')
    localStorage.removeItem('userInfo')
  }

  // 获取当前用户信息
  const getCurrentUser = async (): Promise<UserInfo | null> => {
    try {
      const response = await authApi.getCurrentUser()
      if (response.code === 200) {
        userInfo.value = response.content
        localStorage.setItem('userInfo', JSON.stringify(response.content))
        return response.content
      } else {
        console.error('获取用户信息失败:', response.msg)
        return null
      }
    } catch (error) {
      console.error('获取用户信息失败:', error)
      return null
    }
  }

  // 初始化用户状态（从localStorage恢复）
  const initUserState = () => {
    const savedToken = localStorage.getItem('token')
    const savedIsLoggedIn = localStorage.getItem('isLoggedIn')
    const savedUserInfo = localStorage.getItem('userInfo')

    if (savedToken) {
      token.value = savedToken
    }

    if (savedIsLoggedIn === 'true') {
      isLoggedIn.value = true
    }

    if (savedUserInfo) {
      try {
        userInfo.value = JSON.parse(savedUserInfo)
      } catch (error) {
        console.error('解析用户信息失败:', error)
      }
    }
  }

  // 检查登录状态
  const checkLoginStatus = (): boolean => {
    return isLoggedIn.value || localStorage.getItem('isLoggedIn') === 'true'
  }

  return {
    // 状态
    userInfo,
    token,
    isLoggedIn,
    // 方法
    login,
    logout,
    getCurrentUser,
    initUserState,
    checkLoginStatus,
    clearUserData
  }
})
