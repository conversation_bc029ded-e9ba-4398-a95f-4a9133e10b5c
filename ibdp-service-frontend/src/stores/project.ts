import { defineStore } from 'pinia'
import { ref } from 'vue'
import { ElMessage } from 'element-plus'
import { projectApi } from '@/api/project'
import type { Project, ProjectTypeOption, PageParams, PageResponse } from '@/types'

export const useProjectStore = defineStore('project', () => {
  // 状态
  const projects = ref<Project[]>([])
  const currentProject = ref<Project | null>(null)
  const projectTypes = ref<ProjectTypeOption[]>([])
  const loading = ref(false)

  // 分页查询项目列表
  const fetchProjects = async (params: PageParams & {
    name?: string
    type?: string
    status?: number
  }): Promise<PageResponse<Project>> => {
    try {
      loading.value = true
      const response = await projectApi.pageList(params)
      if (response.code === 200) {
        projects.value = response.content.data
        return response.content
      } else {
        ElMessage.error(response.msg || '查询项目列表失败')
        return { recordsTotal: 0, recordsFiltered: 0, data: [] }
      }
    } catch (error) {
      console.error('查询项目列表失败:', error)
      ElMessage.error('查询项目列表失败')
      return { recordsTotal: 0, recordsFiltered: 0, data: [] }
    } finally {
      loading.value = false
    }
  }

  // 新增项目
  const addProject = async (project: Project): Promise<boolean> => {
    try {
      loading.value = true
      const response = await projectApi.add(project)
      if (response.code === 200) {
        ElMessage.success(response.msg || '新增项目成功')
        return true
      } else {
        ElMessage.error(response.msg || '新增项目失败')
        return false
      }
    } catch (error) {
      console.error('新增项目失败:', error)
      ElMessage.error('新增项目失败')
      return false
    } finally {
      loading.value = false
    }
  }

  // 更新项目
  const updateProject = async (project: Project): Promise<boolean> => {
    try {
      loading.value = true
      const response = await projectApi.update(project)
      if (response.code === 200) {
        ElMessage.success(response.msg || '更新项目成功')
        return true
      } else {
        ElMessage.error(response.msg || '更新项目失败')
        return false
      }
    } catch (error) {
      console.error('更新项目失败:', error)
      ElMessage.error('更新项目失败')
      return false
    } finally {
      loading.value = false
    }
  }

  // 删除项目
  const deleteProject = async (id: number): Promise<boolean> => {
    try {
      loading.value = true
      const response = await projectApi.delete(id)
      if (response.code === 200) {
        ElMessage.success(response.msg || '删除项目成功')
        return true
      } else {
        ElMessage.error(response.msg || '删除项目失败')
        return false
      }
    } catch (error) {
      console.error('删除项目失败:', error)
      ElMessage.error('删除项目失败')
      return false
    } finally {
      loading.value = false
    }
  }

  // 查询项目详情
  const loadProject = async (id: number): Promise<Project | null> => {
    try {
      loading.value = true
      const response = await projectApi.load(id)
      if (response.code === 200) {
        currentProject.value = response.content
        return response.content
      } else {
        ElMessage.error(response.msg || '查询项目详情失败')
        return null
      }
    } catch (error) {
      console.error('查询项目详情失败:', error)
      ElMessage.error('查询项目详情失败')
      return null
    } finally {
      loading.value = false
    }
  }

  // 根据编码查询项目
  const loadProjectByCode = async (code: string): Promise<Project | null> => {
    try {
      const response = await projectApi.loadByCode(code)
      if (response.code === 200) {
        return response.content
      } else {
        ElMessage.error(response.msg || '查询项目失败')
        return null
      }
    } catch (error) {
      console.error('查询项目失败:', error)
      ElMessage.error('查询项目失败')
      return null
    }
  }

  // 查询可访问项目列表
  const fetchAccessibleProjects = async (): Promise<Project[]> => {
    try {
      const response = await projectApi.findAccessibleProjects()
      if (response.code === 200) {
        return response.content
      } else {
        ElMessage.error(response.msg || '查询可访问项目失败')
        return []
      }
    } catch (error) {
      console.error('查询可访问项目失败:', error)
      ElMessage.error('查询可访问项目失败')
      return []
    }
  }

  // 查询公共项目列表
  const fetchPublicProjects = async (): Promise<Project[]> => {
    try {
      const response = await projectApi.findPublicProjects()
      if (response.code === 200) {
        return response.content
      } else {
        ElMessage.error(response.msg || '查询公共项目失败')
        return []
      }
    } catch (error) {
      console.error('查询公共项目失败:', error)
      ElMessage.error('查询公共项目失败')
      return []
    }
  }

  // 查询用户参与的项目
  const fetchUserProjects = async (): Promise<Project[]> => {
    try {
      const response = await projectApi.findUserProjects()
      if (response.code === 200) {
        return response.content
      } else {
        ElMessage.error(response.msg || '查询用户项目失败')
        return []
      }
    } catch (error) {
      console.error('查询用户项目失败:', error)
      ElMessage.error('查询用户项目失败')
      return []
    }
  }

  // 更新项目状态
  const updateProjectStatus = async (id: number, status: number): Promise<boolean> => {
    try {
      const response = await projectApi.updateStatus(id, status)
      if (response.code === 200) {
        ElMessage.success(response.msg || '更新项目状态成功')
        return true
      } else {
        ElMessage.error(response.msg || '更新项目状态失败')
        return false
      }
    } catch (error) {
      console.error('更新项目状态失败:', error)
      ElMessage.error('更新项目状态失败')
      return false
    }
  }

  // 更新项目类型
  const updateProjectType = async (id: number, type: string): Promise<boolean> => {
    try {
      const response = await projectApi.updateType(id, type)
      if (response.code === 200) {
        ElMessage.success(response.msg || '更新项目类型成功')
        return true
      } else {
        ElMessage.error(response.msg || '更新项目类型失败')
        return false
      }
    } catch (error) {
      console.error('更新项目类型失败:', error)
      ElMessage.error('更新项目类型失败')
      return false
    }
  }

  // 检查项目访问权限
  const checkProjectAccess = async (id: number): Promise<boolean> => {
    try {
      const response = await projectApi.hasAccess(id)
      if (response.code === 200) {
        return response.content
      } else {
        return false
      }
    } catch (error) {
      console.error('检查项目访问权限失败:', error)
      return false
    }
  }

  // 检查是否为项目管理员
  const checkProjectAdmin = async (id: number): Promise<boolean> => {
    try {
      const response = await projectApi.isAdmin(id)
      if (response.code === 200) {
        return response.content
      } else {
        return false
      }
    } catch (error) {
      console.error('检查项目管理员权限失败:', error)
      return false
    }
  }

  // 获取用户项目角色
  const getUserProjectRole = async (id: number): Promise<string | null> => {
    try {
      const response = await projectApi.getUserRole(id)
      if (response.code === 200) {
        return response.content
      } else {
        return null
      }
    } catch (error) {
      console.error('获取用户项目角色失败:', error)
      return null
    }
  }

  // 获取项目类型列表
  const fetchProjectTypes = async (): Promise<ProjectTypeOption[]> => {
    try {
      if (projectTypes.value.length > 0) {
        return projectTypes.value
      }
      
      const response = await projectApi.getProjectTypes()
      if (response.code === 200) {
        projectTypes.value = response.content
        return response.content
      } else {
        ElMessage.error(response.msg || '获取项目类型失败')
        return []
      }
    } catch (error) {
      console.error('获取项目类型失败:', error)
      ElMessage.error('获取项目类型失败')
      return []
    }
  }

  // 查询启用的项目列表
  const fetchEnabledProjects = async (): Promise<Project[]> => {
    try {
      const response = await projectApi.findAllEnabled()
      if (response.code === 200) {
        return response.content
      } else {
        ElMessage.error(response.msg || '查询启用项目失败')
        return []
      }
    } catch (error) {
      console.error('查询启用项目失败:', error)
      ElMessage.error('查询启用项目失败')
      return []
    }
  }

  // 根据类型查询项目
  const fetchProjectsByType = async (type: string): Promise<Project[]> => {
    try {
      const response = await projectApi.findByType(type)
      if (response.code === 200) {
        return response.content
      } else {
        ElMessage.error(response.msg || '查询项目失败')
        return []
      }
    } catch (error) {
      console.error('查询项目失败:', error)
      ElMessage.error('查询项目失败')
      return []
    }
  }

  return {
    // 状态
    projects,
    currentProject,
    projectTypes,
    loading,
    
    // 方法
    fetchProjects,
    addProject,
    updateProject,
    deleteProject,
    loadProject,
    loadProjectByCode,
    fetchAccessibleProjects,
    fetchPublicProjects,
    fetchUserProjects,
    updateProjectStatus,
    updateProjectType,
    checkProjectAccess,
    checkProjectAdmin,
    getUserProjectRole,
    fetchProjectTypes,
    fetchEnabledProjects,
    fetchProjectsByType
  }
})
