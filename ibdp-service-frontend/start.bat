@echo off
title IBDP数据开发平台前端
echo ========================================
echo    IBDP数据开发平台前端启动脚本
echo ========================================
echo.

echo 检查Node.js环境...
node --version >nul 2>&1
if errorlevel 1 (
    echo 错误：未找到Node.js，请先安装Node.js
    pause
    exit /b 1
)

echo 检查npm环境...
npm --version >nul 2>&1
if errorlevel 1 (
    echo 错误：未找到npm，请检查Node.js安装
    pause
    exit /b 1
)

echo.
echo 检查依赖是否已安装...
if not exist "node_modules" (
    echo 正在安装依赖...
    call npm install
    if errorlevel 1 (
        echo 错误：依赖安装失败
        pause
        exit /b 1
    )
) else (
    echo 依赖已安装
)

echo.
echo 启动开发服务器...
echo 前端地址：http://localhost:3000
echo 后端地址：http://localhost:18080/ibdp
echo.
echo 按 Ctrl+C 停止服务器
echo ========================================

call npm run dev
