<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" 
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.sevb.admin.dao.WorkflowNodeDao">

    <resultMap id="WorkflowNode" type="com.sevb.admin.core.model.WorkflowNode">
        <result column="id" property="id"/>
        <result column="workflow_id" property="workflowId"/>
        <result column="node_code" property="nodeCode"/>
        <result column="node_name" property="nodeName"/>
        <result column="node_type" property="nodeType"/>
        <result column="color" property="color"/>
        <result column="description" property="description"/>
        <result column="config_params" property="configParams"/>
        <result column="position_x" property="positionX"/>
        <result column="position_y" property="positionY"/>
        <result column="timeout" property="timeout"/>
        <result column="retry_times" property="retryTimes"/>
        <result column="retry_interval" property="retryInterval"/>
        <result column="max_retry_times" property="maxRetryTimes"/>
        <result column="failure_strategy" property="failureStrategy"/>
        <result column="priority" property="priority"/>
        <result column="worker_group" property="workerGroup"/>
        <result column="environment_code" property="environmentCode"/>
        <result column="datasource_id" property="datasourceId"/>
        <result column="job_id" property="jobId"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="create_user" property="createUser"/>
        <result column="update_user" property="updateUser"/>
        <result column="deleted" property="deleted"/>
    </resultMap>

    <sql id="Base_Column_List">
        t.id,
        t.workflow_id,
        t.node_code,
        t.node_name,
        t.node_type,
        t.color,
        t.description,
        t.config_params,
        t.position_x,
        t.position_y,
        t.timeout,
        t.retry_times,
        t.retry_interval,
        t.max_retry_times,
        t.failure_strategy,
        t.priority,
        t.worker_group,
        t.environment_code,
        t.datasource_id,
        t.job_id,
        t.create_time,
        t.update_time,
        t.create_user,
        t.update_user,
        t.deleted
    </sql>

    <select id="listByWorkflow" resultMap="WorkflowNode">
        SELECT <include refid="Base_Column_List"/>
        FROM workflow_node AS t
        WHERE t.workflow_id = #{workflowId}
          AND t.deleted = 0
        ORDER BY t.create_time ASC
    </select>

    <select id="pageList" resultMap="WorkflowNode">
        SELECT <include refid="Base_Column_List"/>
        FROM workflow_node AS t
        WHERE t.workflow_id = #{workflowId}
          AND t.deleted = 0
        <if test="nodeName != null and nodeName != ''">
            AND t.node_name LIKE CONCAT('%', #{nodeName}, '%')
        </if>
        <if test="nodeType != null and nodeType != ''">
            AND t.node_type = #{nodeType}
        </if>
        ORDER BY t.create_time DESC
        LIMIT #{offset}, #{pageSize}
    </select>

    <select id="count" resultType="int">
        SELECT COUNT(1)
        FROM workflow_node AS t
        WHERE t.workflow_id = #{workflowId}
          AND t.deleted = 0
        <if test="nodeName != null and nodeName != ''">
            AND t.node_name LIKE CONCAT('%', #{nodeName}, '%')
        </if>
        <if test="nodeType != null and nodeType != ''">
            AND t.node_type = #{nodeType}
        </if>
    </select>

    <select id="load" resultMap="WorkflowNode">
        SELECT <include refid="Base_Column_List"/>
        FROM workflow_node AS t
        WHERE t.id = #{id}
          AND t.deleted = 0
    </select>

    <select id="loadByCode" resultMap="WorkflowNode">
        SELECT <include refid="Base_Column_List"/>
        FROM workflow_node AS t
        WHERE t.workflow_id = #{workflowId}
          AND t.node_code = #{nodeCode}
          AND t.deleted = 0
    </select>

    <select id="findByNodeCode" resultMap="WorkflowNode">
        SELECT <include refid="Base_Column_List"/>
        FROM workflow_node AS t
        WHERE t.node_code = #{nodeCode}
          AND t.deleted = 0
        LIMIT 1
    </select>



    <insert id="save" parameterType="com.sevb.admin.core.model.WorkflowNode" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO workflow_node (
            workflow_id, node_code, node_name, node_type, color, description, config_params,
            position_x, position_y, timeout, retry_times, retry_interval, max_retry_times,
            failure_strategy, priority, worker_group, environment_code, datasource_id, job_id,
            create_user, update_user
        ) VALUES (
            #{workflowId}, #{nodeCode}, #{nodeName}, #{nodeType}, #{color}, #{description}, #{configParams},
            #{positionX}, #{positionY}, #{timeout}, #{retryTimes}, #{retryInterval}, #{maxRetryTimes},
            #{failureStrategy}, #{priority}, #{workerGroup}, #{environmentCode}, #{datasourceId}, #{jobId},
            #{createUser}, #{updateUser}
        )
    </insert>

    <insert id="batchSave" parameterType="java.util.List" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO workflow_node (
            workflow_id, node_code, node_name, node_type, color, description, config_params,
            position_x, position_y, timeout, retry_times, retry_interval, max_retry_times,
            failure_strategy, priority, worker_group, environment_code, datasource_id, job_id,
            create_user, update_user
        ) VALUES
        <foreach collection="nodes" item="item" separator=",">
            (#{item.workflowId}, #{item.nodeCode}, #{item.nodeName}, #{item.nodeType}, #{item.color}, #{item.description}, #{item.configParams},
             #{item.positionX}, #{item.positionY}, #{item.timeout}, #{item.retryTimes}, #{item.retryInterval}, #{item.maxRetryTimes},
             #{item.failureStrategy}, #{item.priority}, #{item.workerGroup}, #{item.environmentCode}, #{item.datasourceId}, #{item.jobId},
             #{item.createUser}, #{item.updateUser})
        </foreach>
    </insert>

    <update id="update" parameterType="com.sevb.admin.core.model.WorkflowNode">
        UPDATE workflow_node
        SET workflow_id = #{workflowId},
            node_code = #{nodeCode},
            node_name = #{nodeName},
            node_type = #{nodeType},
            color = #{color},
            description = #{description},
            config_params = #{configParams},
            position_x = #{positionX},
            position_y = #{positionY},
            timeout = #{timeout},
            retry_times = #{retryTimes},
            retry_interval = #{retryInterval},
            max_retry_times = #{maxRetryTimes},
            failure_strategy = #{failureStrategy},
            priority = #{priority},
            worker_group = #{workerGroup},
            environment_code = #{environmentCode},
            datasource_id = #{datasourceId},
            job_id = #{jobId},
            update_time = NOW(),
            update_user = #{updateUser}
        WHERE id = #{id}
          AND deleted = 0
    </update>

    <update id="batchUpdate" parameterType="java.util.List">
        UPDATE workflow_node
        SET workflow_id = CASE id
            <foreach collection="nodes" item="item">
                WHEN #{item.id} THEN #{item.workflowId}
            </foreach>
            END,
            node_code = CASE id
            <foreach collection="nodes" item="item">
                WHEN #{item.id} THEN #{item.nodeCode}
            </foreach>
            END,
            node_name = CASE id
            <foreach collection="nodes" item="item">
                WHEN #{item.id} THEN #{item.nodeName}
            </foreach>
            END,
            node_type = CASE id
            <foreach collection="nodes" item="item">
                WHEN #{item.id} THEN #{item.nodeType}
            </foreach>
            END,
            color = CASE id
            <foreach collection="nodes" item="item">
                WHEN #{item.id} THEN #{item.color}
            </foreach>
            END,
            description = CASE id
            <foreach collection="nodes" item="item">
                WHEN #{item.id} THEN #{item.description}
            </foreach>
            END,
            config_params = CASE id
            <foreach collection="nodes" item="item">
                WHEN #{item.id} THEN #{item.configParams}
            </foreach>
            END,
            position_x = CASE id
            <foreach collection="nodes" item="item">
                WHEN #{item.id} THEN #{item.positionX}
            </foreach>
            END,
            position_y = CASE id
            <foreach collection="nodes" item="item">
                WHEN #{item.id} THEN #{item.positionY}
            </foreach>
            END,
            timeout = CASE id
            <foreach collection="nodes" item="item">
                WHEN #{item.id} THEN #{item.timeout}
            </foreach>
            END,
            retry_times = CASE id
            <foreach collection="nodes" item="item">
                WHEN #{item.id} THEN #{item.retryTimes}
            </foreach>
            END,
            retry_interval = CASE id
            <foreach collection="nodes" item="item">
                WHEN #{item.id} THEN #{item.retryInterval}
            </foreach>
            END,
            max_retry_times = CASE id
            <foreach collection="nodes" item="item">
                WHEN #{item.id} THEN #{item.maxRetryTimes}
            </foreach>
            END,
            failure_strategy = CASE id
            <foreach collection="nodes" item="item">
                WHEN #{item.id} THEN #{item.failureStrategy}
            </foreach>
            END,
            priority = CASE id
            <foreach collection="nodes" item="item">
                WHEN #{item.id} THEN #{item.priority}
            </foreach>
            END,
            worker_group = CASE id
            <foreach collection="nodes" item="item">
                WHEN #{item.id} THEN #{item.workerGroup}
            </foreach>
            END,
            environment_code = CASE id
            <foreach collection="nodes" item="item">
                WHEN #{item.id} THEN #{item.environmentCode}
            </foreach>
            END,
            datasource_id = CASE id
            <foreach collection="nodes" item="item">
                WHEN #{item.id} THEN #{item.datasourceId}
            </foreach>
            END,
            update_time = NOW(),
            update_user = CASE id
            <foreach collection="nodes" item="item">
                WHEN #{item.id} THEN #{item.updateUser}
            </foreach>
            END
        WHERE id IN
        <foreach collection="nodes" item="item" open="(" separator="," close=")">
            #{item.id}
        </foreach>
        AND deleted = 0
    </update>

    <update id="delete">
        UPDATE workflow_node
        SET deleted = 1,
            update_time = NOW(),
            update_user = #{updateUser}
        WHERE id = #{id}
          AND deleted = 0
    </update>

    <update id="batchDelete">
        UPDATE workflow_node
        SET deleted = 1,
            update_time = NOW(),
            update_user = #{updateUser}
        WHERE id IN
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
        AND deleted = 0
    </update>

    <update id="deleteByWorkflow">
        UPDATE workflow_node
        SET deleted = 1,
            update_time = NOW(),
            update_user = #{updateUser}
        WHERE workflow_id = #{workflowId}
          AND deleted = 0
    </update>

    <delete id="removeByWorkflow">
        DELETE FROM workflow_node
        WHERE workflow_id = #{workflowId}
    </delete>

    <update id="updatePosition">
        UPDATE workflow_node
        SET position_x = #{positionX},
            position_y = #{positionY},
            update_time = NOW(),
            update_user = #{updateUser}
        WHERE id = #{id}
          AND deleted = 0
    </update>

    <update id="batchUpdatePosition">
        UPDATE workflow_node
        SET position_x = CASE id
            <foreach collection="updates" item="item">
                WHEN #{item.id} THEN #{item.positionX}
            </foreach>
            END,
            position_y = CASE id
            <foreach collection="updates" item="item">
                WHEN #{item.id} THEN #{item.positionY}
            </foreach>
            END,
            update_time = NOW(),
            update_user = #{updateUser}
        WHERE id IN
        <foreach collection="updates" item="item" open="(" separator="," close=")">
            #{item.id}
        </foreach>
        AND deleted = 0
    </update>

    <select id="checkNodeCodeUnique" resultType="int">
        SELECT COUNT(1)
        FROM workflow_node
        WHERE workflow_id = #{workflowId}
          AND node_code = #{nodeCode}
          AND deleted = 0
        <if test="excludeId != null">
            AND id != #{excludeId}
        </if>
    </select>

    <select id="getMaxSortOrder" resultType="int">
        SELECT COALESCE(MAX(position_x + position_y), 0)
        FROM workflow_node
        WHERE workflow_id = #{workflowId}
          AND deleted = 0
    </select>

    <!-- 更新节点的JobId -->
    <update id="updateJobId">
        UPDATE workflow_node
        SET job_id = #{jobId},
            update_time = NOW()
        WHERE id = #{id}
    </update>

</mapper>
