<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" 
	"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sevb.admin.dao.AlertTemplateDao">

    <resultMap id="AlertTemplate" type="com.sevb.admin.core.model.AlertTemplate">
        <result column="id" property="id"/>
        <result column="template_code" property="templateCode"/>
        <result column="template_name" property="templateName"/>
        <result column="channel_type" property="channelType"/>
        <result column="template_content" property="templateContent"/>
        <result column="variables" property="variables"/>
        <result column="enabled" property="enabled"/>
        <result column="description" property="description"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="create_user" property="createUser"/>
        <result column="update_user" property="updateUser"/>
        <result column="deleted" property="deleted"/>
    </resultMap>

    <sql id="Base_Column_List">
        t.id,
        t.template_code,
        t.template_name,
        t.channel_type,
        t.template_content,
        t.variables,
        t.enabled,
        t.description,
        t.create_time,
        t.update_time,
        t.create_user,
        t.update_user,
        t.deleted
    </sql>

    <select id="pageList" resultMap="AlertTemplate">
        SELECT <include refid="Base_Column_List"/>
        FROM alert_template AS t
        WHERE t.deleted = 0
        <if test="templateCode != null and templateCode.trim() != ''">
            AND t.template_code LIKE CONCAT('%', #{templateCode}, '%')
        </if>
        <if test="templateName != null and templateName.trim() != ''">
            AND t.template_name LIKE CONCAT('%', #{templateName}, '%')
        </if>
        <if test="channelType != null and channelType.trim() != ''">
            AND t.channel_type = #{channelType}
        </if>
        <if test="enabled != null">
            AND t.enabled = #{enabled}
        </if>
        ORDER BY t.create_time DESC
        LIMIT #{offset}, #{pagesize}
    </select>

    <select id="pageListCount" resultType="int">
        SELECT COUNT(1)
        FROM alert_template AS t
        WHERE t.deleted = 0
        <if test="templateCode != null and templateCode.trim() != ''">
            AND t.template_code LIKE CONCAT('%', #{templateCode}, '%')
        </if>
        <if test="templateName != null and templateName.trim() != ''">
            AND t.template_name LIKE CONCAT('%', #{templateName}, '%')
        </if>
        <if test="channelType != null and channelType.trim() != ''">
            AND t.channel_type = #{channelType}
        </if>
        <if test="enabled != null">
            AND t.enabled = #{enabled}
        </if>
    </select>

    <select id="findAllEnabled" resultMap="AlertTemplate">
        SELECT <include refid="Base_Column_List"/>
        FROM alert_template AS t
        WHERE t.deleted = 0 AND t.enabled = 1
        ORDER BY t.template_code, t.channel_type
    </select>

    <select id="load" parameterType="java.lang.Long" resultMap="AlertTemplate">
        SELECT <include refid="Base_Column_List"/>
        FROM alert_template AS t
        WHERE t.id = #{id} AND t.deleted = 0
    </select>

    <select id="findByCodeAndChannel" resultMap="AlertTemplate">
        SELECT <include refid="Base_Column_List"/>
        FROM alert_template AS t
        WHERE t.template_code = #{templateCode} 
        AND t.channel_type = #{channelType}
        AND t.deleted = 0
    </select>

    <select id="findByTemplateCode" resultMap="AlertTemplate">
        SELECT <include refid="Base_Column_List"/>
        FROM alert_template AS t
        WHERE t.template_code = #{templateCode} AND t.deleted = 0
        ORDER BY t.channel_type
    </select>

    <select id="findByChannelType" resultMap="AlertTemplate">
        SELECT <include refid="Base_Column_List"/>
        FROM alert_template AS t
        WHERE t.channel_type = #{channelType} AND t.deleted = 0
        ORDER BY t.template_code
    </select>

    <insert id="save" parameterType="com.sevb.admin.core.model.AlertTemplate" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO alert_template (
            template_code, template_name, channel_type, template_content, variables,
            enabled, description, create_user
        ) VALUES (
            #{templateCode}, #{templateName}, #{channelType}, #{templateContent}, #{variables},
            #{enabled}, #{description}, #{createUser}
        )
    </insert>

    <update id="update" parameterType="com.sevb.admin.core.model.AlertTemplate">
        UPDATE alert_template
        SET template_name = #{templateName},
            template_content = #{templateContent},
            variables = #{variables},
            enabled = #{enabled},
            description = #{description},
            update_user = #{updateUser}
        WHERE id = #{id} AND deleted = 0
    </update>

    <update id="delete">
        UPDATE alert_template
        SET deleted = 1, update_user = #{updateUser}
        WHERE id = #{id} AND deleted = 0
    </update>

    <update id="updateEnabled">
        UPDATE alert_template
        SET enabled = #{enabled}, update_user = #{updateUser}
        WHERE id = #{id} AND deleted = 0
    </update>

    <update id="batchDelete">
        UPDATE alert_template
        SET deleted = 1, update_user = #{updateUser}
        WHERE deleted = 0 AND id IN
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <select id="checkCodeAndChannelExists" resultType="int">
        SELECT COUNT(1)
        FROM alert_template
        WHERE template_code = #{templateCode}
        AND channel_type = #{channelType}
        AND deleted = 0
        <if test="excludeId != null">
            AND id != #{excludeId}
        </if>
    </select>

</mapper>
