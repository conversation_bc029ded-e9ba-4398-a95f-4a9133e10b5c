<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" 
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.sevb.admin.dao.WorkflowInstanceDao">

    <!-- 结果映射 -->
    <resultMap id="WorkflowInstance" type="com.sevb.admin.core.model.WorkflowInstance">
        <id column="id" property="id"/>
        <result column="workflow_id" property="workflowId"/>
        <result column="instance_name" property="workflowName"/>
        <result column="workflow_version" property="workflowVersion"/>
        <result column="job_id" property="jobId"/>
        <result column="job_log_id" property="jobLogId"/>
        <result column="execution_id" property="executionId"/>
        <result column="orchestration_mode" property="orchestrationMode"/>
        <result column="progress" property="progress"/>
        <result column="completed_nodes" property="completedNodes"/>
        <result column="total_nodes" property="totalNodes"/>
        <result column="running_nodes" property="runningNodes"/>
        <result column="failed_nodes" property="failedNodes"/>
        <result column="state" property="status"/>
        <result column="trigger_type" property="triggerType"/>
        <result column="schedule_time" property="triggerTime"/>
        <result column="start_time" property="startTime"/>
        <result column="end_time" property="endTime"/>
        <result column="duration" property="duration"/>
        <result column="command_param" property="executeParams"/>
        <result column="execute_result" property="executeResult"/>
        <result column="error_message" property="errorMessage"/>
        <result column="host" property="executorAddress"/>
        <result column="executor_sharding_param" property="executorShardingParam"/>
        <result column="run_times" property="retryCount"/>
        <result column="execute_user" property="executeUser"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
    </resultMap>

    <!-- 保存工作流实例 -->
    <insert id="save" parameterType="com.sevb.admin.core.model.WorkflowInstance" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO task_instance (
            workflow_id, instance_name, workflow_version, job_id, job_log_id,
            execution_id, orchestration_mode, progress, completed_nodes, total_nodes,
            running_nodes, failed_nodes, state, trigger_type, schedule_time,
            start_time, end_time, duration, command_param, execute_result,
            error_message, host, executor_sharding_param, run_times, execute_user,
            create_time, update_time
        ) VALUES (
            #{workflowId}, #{workflowName}, #{workflowVersion}, #{jobId}, #{jobLogId},
            #{executionId}, #{orchestrationMode}, #{progress}, #{completedNodes}, #{totalNodes},
            #{runningNodes}, #{failedNodes}, #{status}, #{triggerType}, #{triggerTime},
            #{startTime}, #{endTime}, #{duration}, #{executeParams}, #{executeResult},
            #{errorMessage}, #{executorAddress}, #{executorShardingParam}, #{retryCount}, #{executeUser},
            #{createTime}, #{updateTime}
        )
    </insert>

    <!-- 根据ID查询工作流实例 -->
    <select id="load" parameterType="long" resultMap="WorkflowInstance">
        SELECT * FROM task_instance WHERE id = #{id}
    </select>

    <!-- 更新工作流实例 -->
    <update id="update" parameterType="com.sevb.admin.core.model.WorkflowInstance">
        UPDATE task_instance
        <set>
            <if test="workflowId != null">workflow_id = #{workflowId},</if>
            <if test="workflowName != null">instance_name = #{workflowName},</if>
            <if test="workflowVersion != null">workflow_version = #{workflowVersion},</if>
            <if test="jobId != null">job_id = #{jobId},</if>
            <if test="jobLogId != null">job_log_id = #{jobLogId},</if>
            <if test="executionId != null">execution_id = #{executionId},</if>
            <if test="orchestrationMode != null">orchestration_mode = #{orchestrationMode},</if>
            <if test="progress != null">progress = #{progress},</if>
            <if test="completedNodes != null">completed_nodes = #{completedNodes},</if>
            <if test="totalNodes != null">total_nodes = #{totalNodes},</if>
            <if test="runningNodes != null">running_nodes = #{runningNodes},</if>
            <if test="failedNodes != null">failed_nodes = #{failedNodes},</if>
            <if test="status != null">state = #{status},</if>
            <if test="triggerType != null">trigger_type = #{triggerType},</if>
            <if test="triggerTime != null">schedule_time = #{triggerTime},</if>
            <if test="startTime != null">start_time = #{startTime},</if>
            <if test="endTime != null">end_time = #{endTime},</if>
            <if test="duration != null">duration = #{duration},</if>
            <if test="executeParams != null">command_param = #{executeParams},</if>
            <if test="executeResult != null">execute_result = #{executeResult},</if>
            <if test="errorMessage != null">error_message = #{errorMessage},</if>
            <if test="executorAddress != null">host = #{executorAddress},</if>
            <if test="executorShardingParam != null">executor_sharding_param = #{executorShardingParam},</if>
            <if test="retryCount != null">run_times = #{retryCount},</if>
            update_time = NOW()
        </set>
        WHERE id = #{id}
    </update>

    <!-- 删除工作流实例 -->
    <delete id="delete" parameterType="long">
        DELETE FROM task_instance WHERE id = #{id}
    </delete>

    <!-- 分页查询工作流实例 -->
    <select id="pageList" resultMap="WorkflowInstance">
        SELECT ti.* FROM task_instance ti
        LEFT JOIN workflow w ON ti.workflow_id = w.id
        <where>
            <if test="projectId != null">
                AND w.project_id = #{projectId}
            </if>
            <if test="workflowId != null">
                AND ti.workflow_id = #{workflowId}
            </if>
            <if test="workflowName != null and workflowName != ''">
                AND ti.instance_name LIKE CONCAT('%', #{workflowName}, '%')
            </if>
            <if test="status != null">
                AND ti.state = #{status}
            </if>
            <if test="triggerType != null and triggerType != ''">
                AND ti.trigger_type = #{triggerType}
            </if>
            <if test="startTime != null">
                AND ti.schedule_time >= #{startTime}
            </if>
            <if test="endTime != null">
                AND ti.schedule_time &lt;= #{endTime}
            </if>
        </where>
        ORDER BY ti.create_time DESC
        LIMIT #{offset}, #{pagesize}
    </select>

    <!-- 查询工作流实例总数 -->
    <select id="pageListCount" resultType="int">
        SELECT COUNT(1) FROM task_instance ti
        LEFT JOIN workflow w ON ti.workflow_id = w.id
        <where>
            <if test="projectId != null">
                AND w.project_id = #{projectId}
            </if>
            <if test="workflowId != null">
                AND ti.workflow_id = #{workflowId}
            </if>
            <if test="workflowName != null and workflowName != ''">
                AND ti.instance_name LIKE CONCAT('%', #{workflowName}, '%')
            </if>
            <if test="status != null">
                AND ti.state = #{status}
            </if>
            <if test="triggerType != null and triggerType != ''">
                AND ti.trigger_type = #{triggerType}
            </if>
            <if test="startTime != null">
                AND ti.schedule_time >= #{startTime}
            </if>
            <if test="endTime != null">
                AND ti.schedule_time &lt;= #{endTime}
            </if>
        </where>
    </select>

    <!-- 根据工作流ID查询实例列表 -->
    <select id="findByWorkflowId" resultMap="WorkflowInstance">
        SELECT * FROM task_instance
        WHERE workflow_id = #{workflowId}
        ORDER BY create_time DESC
        <if test="limit != null">
            LIMIT #{limit}
        </if>
    </select>

    <!-- 根据XXL-JOB日志ID查询实例 -->
    <select id="findByJobLogId" parameterType="long" resultMap="WorkflowInstance">
        SELECT * FROM task_instance WHERE job_log_id = #{jobLogId}
    </select>

    <!-- 查询正在运行的实例 -->
    <select id="findRunningInstances" resultMap="WorkflowInstance">
        SELECT * FROM task_instance
        WHERE state = 1
        <if test="workflowId != null">
            AND workflow_id = #{workflowId}
        </if>
        ORDER BY create_time DESC
    </select>

    <!-- 统计工作流实例状态 -->
    <select id="getInstanceStats" resultMap="WorkflowInstance">
        SELECT * FROM task_instance
        WHERE workflow_id = #{workflowId}
        <if test="days != null and days > 0">
            AND create_time >= DATE_SUB(NOW(), INTERVAL #{days} DAY)
        </if>
        ORDER BY create_time DESC
    </select>

    <!-- 清理历史实例数据 -->
    <delete id="cleanHistoryInstances">
        DELETE FROM task_instance
        WHERE create_time &lt; #{beforeDate}
        <if test="keepCount != null and keepCount > 0">
            AND id NOT IN (
                SELECT id FROM (
                    SELECT id FROM workflow_instance 
                    ORDER BY create_time DESC 
                    LIMIT #{keepCount}
                ) tmp
            )
        </if>
    </delete>

    <!-- 根据执行ID查询工作流实例 -->
    <select id="loadByExecutionId" parameterType="string" resultMap="WorkflowInstance">
        SELECT * FROM task_instance WHERE execution_id = #{executionId}
    </select>

</mapper>
