<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" 
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.sevb.admin.dao.NodeInstanceDao">

    <!-- 结果映射 -->
    <resultMap id="NodeInstance" type="com.sevb.admin.core.model.NodeInstance">
        <id column="id" property="id"/>
        <result column="task_instance_id" property="workflowInstanceId"/>
        <result column="workflow_id" property="workflowId"/>
        <result column="execution_id" property="executionId"/>
        <result column="node_id" property="nodeId"/>
        <result column="node_code" property="nodeCode"/>
        <result column="node_name" property="nodeName"/>
        <result column="node_type" property="nodeType"/>
        <result column="job_id" property="jobId"/>
        <result column="job_log_id" property="jobLogId"/>
        <result column="executor_address" property="executorAddress"/>
        <result column="input_params" property="inputParams"/>
        <result column="output_params" property="outputParams"/>
        <result column="callback_url" property="callbackUrl"/>
        <result column="state" property="status"/>
        <result column="start_time" property="startTime"/>
        <result column="end_time" property="endTime"/>
        <result column="duration" property="duration"/>
        <result column="execute_params" property="executeParams"/>
        <result column="execute_result" property="executeResult"/>
        <result column="error_message" property="errorMessage"/>
        <result column="execute_log" property="executeLog"/>
        <result column="host" property="executorAddress"/>
        <result column="retry_count" property="retryCount"/>
        <result column="max_retry_count" property="maxRetryCount"/>
        <result column="execute_order" property="executeOrder"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
    </resultMap>

    <!-- 保存节点实例 -->
    <insert id="save" parameterType="com.sevb.admin.core.model.NodeInstance" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO node_instance (
            workflow_id, execution_id, node_id, node_code, node_name, node_type,
            job_id, state, start_time, end_time, duration,
            error_message, create_time, update_time
        ) VALUES (
            #{workflowId}, #{executionId}, #{nodeId}, #{nodeCode}, #{nodeName}, #{nodeType},
            #{jobId}, #{status}, #{startTime}, #{endTime}, #{duration},
            #{errorMessage}, #{createTime}, #{updateTime}
        )
    </insert>

    <!-- 根据ID查询节点实例 -->
    <select id="load" parameterType="long" resultMap="NodeInstance">
        SELECT * FROM node_instance WHERE id = #{id}
    </select>

    <!-- 更新节点实例 -->
    <update id="update" parameterType="com.sevb.admin.core.model.NodeInstance">
        UPDATE node_instance
        <set>
            <if test="workflowId != null">workflow_id = #{workflowId},</if>
            <if test="nodeId != null">node_id = #{nodeId},</if>
            <if test="nodeCode != null">node_code = #{nodeCode},</if>
            <if test="nodeName != null">node_name = #{nodeName},</if>
            <if test="nodeType != null">node_type = #{nodeType},</if>
            <if test="status != null">state = #{status},</if>
            <if test="startTime != null">start_time = #{startTime},</if>
            <if test="endTime != null">end_time = #{endTime},</if>
            <if test="duration != null">duration = #{duration},</if>
            <if test="errorMessage != null">error_message = #{errorMessage},</if>
            update_time = NOW()
        </set>
        WHERE id = #{id}
    </update>

    <!-- 删除节点实例 -->
    <delete id="delete" parameterType="long">
        DELETE FROM node_instance WHERE id = #{id}
    </delete>

    <!-- 分页查询节点实例 -->
    <select id="pageList" resultMap="NodeInstance">
        SELECT ni.* FROM node_instance ni
        LEFT JOIN task_instance ti ON ni.execution_id = ti.execution_id
        LEFT JOIN workflow w ON ti.workflow_id = w.id
        <where>
            <if test="projectId != null">
                AND w.project_id = #{projectId}
            </if>
            <if test="workflowInstanceId != null">
                AND ni.task_instance_id = #{workflowInstanceId}
            </if>
            <if test="workflowId != null">
                AND ni.workflow_id = #{workflowId}
            </if>
            <if test="nodeId != null">
                AND ni.node_code = #{nodeId}
            </if>
            <if test="nodeName != null and nodeName != ''">
                AND ni.node_name LIKE CONCAT('%', #{nodeName}, '%')
            </if>
            <if test="nodeType != null and nodeType != ''">
                AND ni.node_type = #{nodeType}
            </if>
            <if test="status != null">
                AND ni.state = #{status}
            </if>
            <if test="startTime != null">
                AND ni.start_time >= #{startTime}
            </if>
            <if test="endTime != null">
                AND ni.end_time &lt;= #{endTime}
            </if>
        </where>
        ORDER BY ni.create_time DESC
        LIMIT #{offset}, #{pagesize}
    </select>

    <!-- 查询节点实例总数 -->
    <select id="pageListCount" resultType="int">
        SELECT COUNT(1) FROM node_instance ni
        LEFT JOIN task_instance ti ON ni.execution_id = ti.execution_id
        LEFT JOIN workflow w ON ti.workflow_id = w.id
        <where>
            <if test="projectId != null">
                AND w.project_id = #{projectId}
            </if>
            <if test="workflowInstanceId != null">
                AND ni.task_instance_id = #{workflowInstanceId}
            </if>
            <if test="workflowId != null">
                AND ni.workflow_id = #{workflowId}
            </if>
            <if test="nodeId != null">
                AND ni.node_code = #{nodeId}
            </if>
            <if test="nodeName != null and nodeName != ''">
                AND ni.node_name LIKE CONCAT('%', #{nodeName}, '%')
            </if>
            <if test="nodeType != null and nodeType != ''">
                AND ni.node_type = #{nodeType}
            </if>
            <if test="status != null">
                AND ni.state = #{status}
            </if>
            <if test="startTime != null">
                AND ni.start_time >= #{startTime}
            </if>
            <if test="endTime != null">
                AND ni.end_time &lt;= #{endTime}
            </if>
        </where>
    </select>

    <!-- 根据工作流实例ID查询节点实例列表 -->
    <select id="findByWorkflowInstanceId" parameterType="long" resultMap="NodeInstance">
        SELECT * FROM node_instance
        WHERE task_instance_id = #{workflowInstanceId}
        ORDER BY create_time ASC
    </select>

    <!-- 根据工作流ID查询节点实例列表 -->
    <select id="findByWorkflowId" resultMap="NodeInstance">
        SELECT * FROM node_instance
        WHERE workflow_id = #{workflowId}
        ORDER BY create_time DESC
        <if test="limit != null">
            LIMIT #{limit}
        </if>
    </select>

    <!-- 根据节点ID查询实例列表 -->
    <select id="findByNodeId" resultMap="NodeInstance">
        SELECT * FROM node_instance
        WHERE node_code = #{nodeId}
        ORDER BY create_time DESC
        <if test="limit != null">
            LIMIT #{limit}
        </if>
    </select>

    <!-- 查询正在运行的节点实例 -->
    <select id="findRunningInstances" parameterType="long" resultMap="NodeInstance">
        SELECT * FROM node_instance
        WHERE task_instance_id = #{workflowInstanceId}
        AND state = 1
        ORDER BY create_time ASC
    </select>

    <!-- 批量保存节点实例 -->
    <insert id="batchSave" parameterType="list" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO node_instance (
            task_instance_id, workflow_id, node_code, node_name, node_type,
            state, start_time, end_time, duration, execute_params,
            execute_result, error_message, execute_log, host,
            retry_count, max_retry_count, create_time, update_time
        ) VALUES
        <foreach collection="nodeInstances" item="item" separator=",">
            (
                #{item.workflowInstanceId}, #{item.workflowId}, #{item.nodeId},
                #{item.nodeName}, #{item.nodeType}, #{item.status}, #{item.startTime},
                #{item.endTime}, #{item.duration}, #{item.executeParams}, #{item.executeResult},
                #{item.errorMessage}, #{item.executeLog}, #{item.executorAddress},
                #{item.retryCount}, #{item.maxRetryCount},
                #{item.createTime}, #{item.updateTime}
            )
        </foreach>
    </insert>

    <!-- 批量更新节点实例状态 -->
    <update id="batchUpdateStatus">
        UPDATE node_instance SET
            state = #{status},
            update_time = #{updateTime}
        WHERE id IN
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <!-- 统计节点实例状态 -->
    <select id="getNodeStats" resultMap="NodeInstance">
        SELECT * FROM node_instance
        WHERE 1=1
        <if test="workflowId != null">
            AND workflow_id = #{workflowId}
        </if>
        <if test="nodeId != null">
            AND node_id = #{nodeId}
        </if>
        <if test="days != null and days > 0">
            AND create_time >= DATE_SUB(NOW(), INTERVAL #{days} DAY)
        </if>
        ORDER BY create_time DESC
    </select>

    <!-- 清理历史节点实例数据 -->
    <delete id="cleanHistoryInstances">
        DELETE FROM node_instance
        WHERE create_time &lt; #{beforeDate}
    </delete>

    <!-- 根据执行ID和节点编码查询节点实例 -->
    <select id="loadByExecutionAndNode" resultMap="NodeInstance">
        SELECT * FROM node_instance
        WHERE execution_id = #{executionId} AND node_code = #{nodeCode}
    </select>

    <!-- 根据执行ID查询节点实例列表 -->
    <select id="findByExecutionId" resultMap="NodeInstance">
        SELECT * FROM node_instance
        WHERE execution_id = #{executionId}
        ORDER BY create_time ASC
    </select>

</mapper>
