<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" 
	"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sevb.admin.dao.AlertSendRecordDao">

    <resultMap id="AlertSendRecord" type="com.sevb.admin.core.model.AlertSendRecord">
        <result column="id" property="id"/>
        <result column="request_id" property="requestId"/>
        <result column="template_code" property="templateCode"/>
        <result column="channel_type" property="channelType"/>
        <result column="config_name" property="configName"/>
        <result column="recipients" property="recipients"/>
        <result column="title" property="title"/>
        <result column="content" property="content"/>
        <result column="variables" property="variables"/>
        <result column="send_time" property="sendTime"/>
        <result column="send_status" property="sendStatus"/>
        <result column="response_code" property="responseCode"/>
        <result column="response_message" property="responseMessage"/>
        <result column="retry_count" property="retryCount"/>
        <result column="max_retry_count" property="maxRetryCount"/>
        <result column="next_retry_time" property="nextRetryTime"/>
        <result column="source_system" property="sourceSystem"/>
        <result column="business_id" property="businessId"/>
        <result column="create_time" property="createTime"/>
    </resultMap>

    <sql id="Base_Column_List">
        t.id,
        t.request_id,
        t.template_code,
        t.channel_type,
        t.config_name,
        t.recipients,
        t.title,
        t.content,
        t.variables,
        t.send_time,
        t.send_status,
        t.response_code,
        t.response_message,
        t.retry_count,
        t.max_retry_count,
        t.next_retry_time,
        t.source_system,
        t.business_id,
        t.create_time
    </sql>

    <select id="pageList" resultMap="AlertSendRecord">
        SELECT <include refid="Base_Column_List"/>
        FROM alert_send_record AS t
        WHERE 1=1
        <if test="requestId != null and requestId.trim() != ''">
            AND t.request_id = #{requestId}
        </if>
        <if test="templateCode != null and templateCode.trim() != ''">
            AND t.template_code = #{templateCode}
        </if>
        <if test="channelType != null and channelType.trim() != ''">
            AND t.channel_type = #{channelType}
        </if>
        <if test="sendStatus != null and sendStatus.trim() != ''">
            AND t.send_status = #{sendStatus}
        </if>
        <if test="sourceSystem != null and sourceSystem.trim() != ''">
            AND t.source_system = #{sourceSystem}
        </if>
        <if test="businessId != null and businessId.trim() != ''">
            AND t.business_id = #{businessId}
        </if>
        <if test="startTime != null">
            AND t.send_time >= #{startTime}
        </if>
        <if test="endTime != null">
            AND t.send_time &lt;= #{endTime}
        </if>
        ORDER BY t.create_time DESC
        LIMIT #{offset}, #{pagesize}
    </select>

    <select id="pageListCount" resultType="int">
        SELECT COUNT(1)
        FROM alert_send_record AS t
        WHERE 1=1
        <if test="requestId != null and requestId.trim() != ''">
            AND t.request_id = #{requestId}
        </if>
        <if test="templateCode != null and templateCode.trim() != ''">
            AND t.template_code = #{templateCode}
        </if>
        <if test="channelType != null and channelType.trim() != ''">
            AND t.channel_type = #{channelType}
        </if>
        <if test="sendStatus != null and sendStatus.trim() != ''">
            AND t.send_status = #{sendStatus}
        </if>
        <if test="sourceSystem != null and sourceSystem.trim() != ''">
            AND t.source_system = #{sourceSystem}
        </if>
        <if test="businessId != null and businessId.trim() != ''">
            AND t.business_id = #{businessId}
        </if>
        <if test="startTime != null">
            AND t.send_time >= #{startTime}
        </if>
        <if test="endTime != null">
            AND t.send_time &lt;= #{endTime}
        </if>
    </select>

    <select id="load" parameterType="java.lang.Long" resultMap="AlertSendRecord">
        SELECT <include refid="Base_Column_List"/>
        FROM alert_send_record AS t
        WHERE t.id = #{id}
    </select>

    <select id="findByRequestId" resultMap="AlertSendRecord">
        SELECT <include refid="Base_Column_List"/>
        FROM alert_send_record AS t
        WHERE t.request_id = #{requestId}
        ORDER BY t.create_time DESC
    </select>

    <select id="findByBusinessId" resultMap="AlertSendRecord">
        SELECT <include refid="Base_Column_List"/>
        FROM alert_send_record AS t
        WHERE t.business_id = #{businessId}
        ORDER BY t.create_time DESC
    </select>

    <select id="findRetryRecords" resultMap="AlertSendRecord">
        SELECT <include refid="Base_Column_List"/>
        FROM alert_send_record AS t
        WHERE t.send_status = 'RETRY'
        AND t.next_retry_time &lt;= #{currentTime}
        AND t.retry_count &lt; t.max_retry_count
        ORDER BY t.next_retry_time ASC
        LIMIT #{limit}
    </select>

    <select id="findFailedRecords" resultMap="AlertSendRecord">
        SELECT <include refid="Base_Column_List"/>
        FROM alert_send_record AS t
        WHERE t.send_status = 'FAILED'
        <if test="startTime != null">
            AND t.send_time >= #{startTime}
        </if>
        <if test="endTime != null">
            AND t.send_time &lt;= #{endTime}
        </if>
        ORDER BY t.send_time DESC
    </select>

    <insert id="save" parameterType="com.sevb.admin.core.model.AlertSendRecord" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO alert_send_record (
            request_id, template_code, channel_type, config_name, recipients,
            title, content, variables, send_time, send_status, response_code,
            response_message, retry_count, max_retry_count, next_retry_time,
            source_system, business_id
        ) VALUES (
            #{requestId}, #{templateCode}, #{channelType}, #{configName}, #{recipients},
            #{title}, #{content}, #{variables}, #{sendTime}, #{sendStatus}, #{responseCode},
            #{responseMessage}, #{retryCount}, #{maxRetryCount}, #{nextRetryTime},
            #{sourceSystem}, #{businessId}
        )
    </insert>

    <update id="update" parameterType="com.sevb.admin.core.model.AlertSendRecord">
        UPDATE alert_send_record
        SET send_status = #{sendStatus},
            response_code = #{responseCode},
            response_message = #{responseMessage},
            retry_count = #{retryCount},
            next_retry_time = #{nextRetryTime}
        WHERE id = #{id}
    </update>

    <update id="updateSendStatus">
        UPDATE alert_send_record
        SET send_status = #{sendStatus},
            response_code = #{responseCode},
            response_message = #{responseMessage}
        WHERE id = #{id}
    </update>

    <update id="updateRetryInfo">
        UPDATE alert_send_record
        SET retry_count = #{retryCount},
            next_retry_time = #{nextRetryTime},
            send_status = #{sendStatus}
        WHERE id = #{id}
    </update>

    <delete id="batchDelete">
        DELETE FROM alert_send_record
        WHERE id IN
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <delete id="deleteByTimeRange">
        DELETE FROM alert_send_record
        WHERE send_time >= #{startTime} AND send_time &lt;= #{endTime}
    </delete>

    <select id="countByStatus" resultType="int">
        SELECT COUNT(1)
        FROM alert_send_record
        WHERE send_status = #{sendStatus}
        <if test="startTime != null">
            AND send_time >= #{startTime}
        </if>
        <if test="endTime != null">
            AND send_time &lt;= #{endTime}
        </if>
    </select>

    <select id="countGroupByStatus" resultMap="AlertSendRecord">
        SELECT send_status, COUNT(1) as retry_count
        FROM alert_send_record
        WHERE 1=1
        <if test="startTime != null">
            AND send_time >= #{startTime}
        </if>
        <if test="endTime != null">
            AND send_time &lt;= #{endTime}
        </if>
        GROUP BY send_status
    </select>

    <select id="countGroupByChannelType" resultMap="AlertSendRecord">
        SELECT channel_type, COUNT(1) as retry_count
        FROM alert_send_record
        WHERE 1=1
        <if test="startTime != null">
            AND send_time >= #{startTime}
        </if>
        <if test="endTime != null">
            AND send_time &lt;= #{endTime}
        </if>
        GROUP BY channel_type
    </select>

</mapper>
