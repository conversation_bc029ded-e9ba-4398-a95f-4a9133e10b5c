<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" 
	"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sevb.admin.dao.DataxSyncStateDao">
	
	<resultMap id="DataxSyncState" type="com.sevb.admin.core.model.DataxSyncState">
		<result column="id" property="id" />
		<result column="node_id" property="nodeId" />
		<result column="workflow_id" property="workflowId" />
		<result column="source_datasource_id" property="sourceDatasourceId" />
		<result column="source_table" property="sourceTable" />
		<result column="target_datasource_id" property="targetDatasourceId" />
		<result column="target_table" property="targetTable" />
		<result column="incremental_column" property="incrementalColumn" />
		<result column="incremental_type" property="incrementalType" />
		<result column="last_sync_value" property="lastSyncValue" />
		<result column="last_sync_time" property="lastSyncTime" />
		<result column="sync_count" property="syncCount" />
		<result column="total_records" property="totalRecords" />
		<result column="status" property="status" />
		<result column="error_message" property="errorMessage" />
		<result column="config_snapshot" property="configSnapshot" />
		<result column="create_time" property="createTime" />
		<result column="update_time" property="updateTime" />
		<result column="create_user" property="createUser" />
		<result column="update_user" property="updateUser" />
	</resultMap>

	<sql id="Base_Column_List">
		t.id,
		t.node_id,
		t.workflow_id,
		t.source_datasource_id,
		t.source_table,
		t.target_datasource_id,
		t.target_table,
		t.incremental_column,
		t.incremental_type,
		t.last_sync_value,
		t.last_sync_time,
		t.sync_count,
		t.total_records,
		t.status,
		t.error_message,
		t.config_snapshot,
		t.create_time,
		t.update_time,
		t.create_user,
		t.update_user
	</sql>

	<select id="load" parameterType="java.lang.Long" resultMap="DataxSyncState">
		SELECT <include refid="Base_Column_List" />
		FROM datax_sync_state AS t
		WHERE t.id = #{id}
	</select>

	<select id="findByNodeAndTables" resultMap="DataxSyncState">
		SELECT <include refid="Base_Column_List" />
		FROM datax_sync_state AS t
		WHERE t.node_id = #{nodeId}
		  AND t.source_table = #{sourceTable}
		  AND t.target_table = #{targetTable}
		LIMIT 1
	</select>

	<select id="findByNodeId" resultMap="DataxSyncState">
		SELECT <include refid="Base_Column_List" />
		FROM datax_sync_state AS t
		WHERE t.node_id = #{nodeId}
		LIMIT 1
	</select>

	<select id="findByWorkflowId" resultMap="DataxSyncState">
		SELECT <include refid="Base_Column_List" />
		FROM datax_sync_state AS t
		WHERE t.workflow_id = #{workflowId}
		ORDER BY t.create_time DESC
	</select>

	<select id="pageList" resultMap="DataxSyncState">
		SELECT <include refid="Base_Column_List" />
		FROM datax_sync_state AS t
		<where>
			<if test="workflowId != null">
				AND t.workflow_id = #{workflowId}
			</if>
			<if test="nodeId != null">
				AND t.node_id = #{nodeId}
			</if>
			<if test="sourceTable != null and sourceTable.trim() != ''">
				AND t.source_table LIKE CONCAT('%', #{sourceTable}, '%')
			</if>
			<if test="targetTable != null and targetTable.trim() != ''">
				AND t.target_table LIKE CONCAT('%', #{targetTable}, '%')
			</if>
			<if test="status != null and status.trim() != ''">
				AND t.status = #{status}
			</if>
			<if test="incrementalType != null and incrementalType.trim() != ''">
				AND t.incremental_type = #{incrementalType}
			</if>
		</where>
		ORDER BY t.update_time DESC
		LIMIT #{offset}, #{pagesize}
	</select>

	<select id="pageListCount" resultType="int">
		SELECT COUNT(1)
		FROM datax_sync_state AS t
		<where>
			<if test="workflowId != null">
				AND t.workflow_id = #{workflowId}
			</if>
			<if test="nodeId != null">
				AND t.node_id = #{nodeId}
			</if>
			<if test="sourceTable != null and sourceTable.trim() != ''">
				AND t.source_table LIKE CONCAT('%', #{sourceTable}, '%')
			</if>
			<if test="targetTable != null and targetTable.trim() != ''">
				AND t.target_table LIKE CONCAT('%', #{targetTable}, '%')
			</if>
			<if test="status != null and status.trim() != ''">
				AND t.status = #{status}
			</if>
			<if test="incrementalType != null and incrementalType.trim() != ''">
				AND t.incremental_type = #{incrementalType}
			</if>
		</where>
	</select>

	<select id="findAllActive" resultMap="DataxSyncState">
		SELECT <include refid="Base_Column_List" />
		FROM datax_sync_state AS t
		WHERE t.status = 'ACTIVE'
		ORDER BY t.update_time DESC
	</select>

	<select id="findNeedSync" resultMap="DataxSyncState">
		SELECT <include refid="Base_Column_List" />
		FROM datax_sync_state AS t
		WHERE t.status = 'ACTIVE'
		  AND (t.last_sync_time IS NULL OR t.last_sync_time &lt; DATE_SUB(NOW(), INTERVAL #{hours} HOUR))
		ORDER BY t.last_sync_time ASC
	</select>

	<select id="findErrorStates" resultMap="DataxSyncState">
		SELECT <include refid="Base_Column_List" />
		FROM datax_sync_state AS t
		WHERE t.status = 'ERROR'
		ORDER BY t.update_time DESC
	</select>

	<select id="findByDatasourceId" resultMap="DataxSyncState">
		SELECT <include refid="Base_Column_List" />
		FROM datax_sync_state AS t
		WHERE t.source_datasource_id = #{datasourceId} OR t.target_datasource_id = #{datasourceId}
		ORDER BY t.create_time DESC
	</select>

	<select id="countByStatus" resultType="int">
		SELECT COUNT(1)
		FROM datax_sync_state AS t
		WHERE t.status = #{status}
	</select>

	<select id="countByWorkflowId" resultType="int">
		SELECT COUNT(1)
		FROM datax_sync_state AS t
		WHERE t.workflow_id = #{workflowId}
	</select>

	<select id="findRecentSynced" resultMap="DataxSyncState">
		SELECT <include refid="Base_Column_List" />
		FROM datax_sync_state AS t
		WHERE t.last_sync_time IS NOT NULL
		ORDER BY t.last_sync_time DESC
		LIMIT #{limit}
	</select>

	<select id="checkDuplicate" resultType="int">
		SELECT COUNT(1)
		FROM datax_sync_state AS t
		WHERE t.node_id = #{nodeId}
		  AND t.source_table = #{sourceTable}
		  AND t.target_table = #{targetTable}
		  <if test="excludeId != null">
		  	AND t.id != #{excludeId}
		  </if>
	</select>

	<select id="findByIncrementalType" resultMap="DataxSyncState">
		SELECT <include refid="Base_Column_List" />
		FROM datax_sync_state AS t
		WHERE t.incremental_type = #{incrementalType}
		ORDER BY t.create_time DESC
	</select>

	<insert id="save" parameterType="com.sevb.admin.core.model.DataxSyncState" useGeneratedKeys="true" keyProperty="id">
		INSERT INTO datax_sync_state (
			node_id,
			workflow_id,
			source_datasource_id,
			source_table,
			target_datasource_id,
			target_table,
			incremental_column,
			incremental_type,
			last_sync_value,
			last_sync_time,
			sync_count,
			total_records,
			status,
			error_message,
			config_snapshot,
			create_time,
			update_time,
			create_user,
			update_user
		) VALUES (
			#{nodeId},
			#{workflowId},
			#{sourceDatasourceId},
			#{sourceTable},
			#{targetDatasourceId},
			#{targetTable},
			#{incrementalColumn},
			#{incrementalType},
			#{lastSyncValue},
			#{lastSyncTime},
			#{syncCount},
			#{totalRecords},
			#{status},
			#{errorMessage},
			#{configSnapshot},
			#{createTime},
			#{updateTime},
			#{createUser},
			#{updateUser}
		)
	</insert>

	<update id="update" parameterType="com.sevb.admin.core.model.DataxSyncState">
		UPDATE datax_sync_state
		SET node_id = #{nodeId},
			workflow_id = #{workflowId},
			source_datasource_id = #{sourceDatasourceId},
			source_table = #{sourceTable},
			target_datasource_id = #{targetDatasourceId},
			target_table = #{targetTable},
			incremental_column = #{incrementalColumn},
			incremental_type = #{incrementalType},
			last_sync_value = #{lastSyncValue},
			last_sync_time = #{lastSyncTime},
			sync_count = #{syncCount},
			total_records = #{totalRecords},
			status = #{status},
			error_message = #{errorMessage},
			config_snapshot = #{configSnapshot},
			update_time = #{updateTime},
			update_user = #{updateUser}
		WHERE id = #{id}
	</update>

	<update id="batchUpdateStatus">
		UPDATE datax_sync_state
		SET status = #{status},
			update_time = NOW(),
			update_user = #{updateUser}
		WHERE id IN
		<foreach collection="ids" item="id" open="(" separator="," close=")">
			#{id}
		</foreach>
	</update>

	<update id="updateLastSyncValue">
		UPDATE datax_sync_state
		SET last_sync_value = #{lastSyncValue},
			last_sync_time = #{lastSyncTime},
			sync_count = #{syncCount},
			total_records = #{totalRecords},
			status = 'ACTIVE',
			error_message = NULL,
			update_time = NOW(),
			update_user = #{updateUser}
		WHERE id = #{id}
	</update>

	<update id="setErrorStatus">
		UPDATE datax_sync_state
		SET status = 'ERROR',
			error_message = #{errorMessage},
			update_time = NOW(),
			update_user = #{updateUser}
		WHERE id = #{id}
	</update>

	<update id="resetToActive">
		UPDATE datax_sync_state
		SET status = 'ACTIVE',
			error_message = NULL,
			update_time = NOW(),
			update_user = #{updateUser}
		WHERE id = #{id}
	</update>

	<update id="updateConfigSnapshot">
		UPDATE datax_sync_state
		SET config_snapshot = #{configSnapshot},
			update_time = NOW(),
			update_user = #{updateUser}
		WHERE id = #{id}
	</update>

	<delete id="delete">
		DELETE FROM datax_sync_state
		WHERE id = #{id}
	</delete>

	<delete id="cleanupInactiveStates">
		DELETE FROM datax_sync_state
		WHERE status = 'INACTIVE'
		  AND update_time &lt; DATE_SUB(NOW(), INTERVAL #{days} DAY)
	</delete>

</mapper>
