<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sevb.admin.dao.WorkflowDao">

    <resultMap id="Workflow" type="com.sevb.admin.core.model.Workflow">
        <result column="id" property="id"/>
        <result column="project_id" property="projectId"/>
        <result column="name" property="name"/>
        <result column="code" property="code"/>
        <result column="description" property="description"/>
        <result column="tags" property="tags"/>
        <result column="category" property="category"/>
        <result column="version" property="version"/>

        <result column="global_params" property="globalParams"/>
        <result column="warning_type" property="warningType"/>
        <result column="warning_group_id" property="warningGroupId"/>
        <result column="job_id" property="jobId"/>
        <result column="orchestration_mode" property="orchestrationMode"/>
        <result column="last_execution_id" property="lastExecutionId"/>
        <result column="execution_count" property="executionCount"/>
        <result column="success_count" property="successCount"/>
        <result column="failure_count" property="failureCount"/>
        <result column="cron_expression" property="cronExpression"/>
        <result column="last_trigger_time" property="lastTriggerTime"/>
        <result column="status" property="status"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="create_user" property="createUser"/>
        <result column="update_user" property="updateUser"/>
        <result column="deleted" property="deleted"/>
    </resultMap>

    <sql id="Base_Column_List">
        t.id,
        t.project_id,
        t.name,
        t.code,
        t.description,
        t.tags,
        t.category,
        t.version,

        t.global_params,
        t.warning_type,
        t.warning_group_id,
        t.job_id,
        t.orchestration_mode,
        t.last_execution_id,
        t.execution_count,
        t.success_count,
        t.failure_count,
        t.cron_expression,
        t.last_trigger_time,
        t.status,
        t.create_time,
        t.update_time,
        t.create_user,
        t.update_user,
        t.deleted
    </sql>

    <select id="pageList" resultMap="Workflow">
        SELECT <include refid="Base_Column_List"/>
        FROM workflow AS t
        WHERE t.deleted = 0
        <if test="projectId != null">
            AND t.project_id = #{projectId}
        </if>
        <if test="name != null and name != ''">
            AND t.name LIKE CONCAT('%', #{name}, '%')
        </if>
        <if test="category != null and category != ''">
            AND t.category = #{category}
        </if>
        <if test="status != null">
            AND t.status = #{status}
        </if>
        ORDER BY t.create_time DESC
        LIMIT #{offset}, #{pagesize}
    </select>

    <select id="pageListCount" resultType="int">
        SELECT COUNT(1)
        FROM workflow AS t
        WHERE t.deleted = 0
        <if test="projectId != null">
            AND t.project_id = #{projectId}
        </if>
        <if test="name != null and name != ''">
            AND t.name LIKE CONCAT('%', #{name}, '%')
        </if>
        <if test="category != null and category != ''">
            AND t.category = #{category}
        </if>
        <if test="status != null">
            AND t.status = #{status}
        </if>
    </select>

    <select id="findByProjectId" resultMap="Workflow">
        SELECT <include refid="Base_Column_List"/>
        FROM workflow AS t
        WHERE t.project_id = #{projectId}
          AND t.deleted = 0
        ORDER BY t.create_time DESC
    </select>

    <select id="findByProjectIdAndStatus" resultMap="Workflow">
        SELECT <include refid="Base_Column_List"/>
        FROM workflow AS t
        WHERE t.project_id = #{projectId}
          AND t.status = #{status}
          AND t.deleted = 0
        ORDER BY t.create_time DESC
    </select>

    <select id="load" parameterType="java.lang.Long" resultMap="Workflow">
        SELECT <include refid="Base_Column_List"/>
        FROM workflow AS t
        WHERE t.id = #{id}
          AND t.deleted = 0
    </select>

    <select id="findByProjectIdAndCode" resultMap="Workflow">
        SELECT <include refid="Base_Column_List"/>
        FROM workflow AS t
        WHERE t.project_id = #{projectId}
          AND t.code = #{code}
          AND t.deleted = 0
    </select>

    <insert id="save" parameterType="com.sevb.admin.core.model.Workflow" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO workflow (
            project_id, name, code, description, tags, category, version,
            global_params, warning_type, warning_group_id, job_id, cron_expression, status,
            create_user, update_user
        ) VALUES (
            #{projectId}, #{name}, #{code}, #{description}, #{tags}, #{category}, #{version},
            #{globalParams}, #{warningType}, #{warningGroupId}, #{jobId}, #{cronExpression}, #{status},
            #{createUser}, #{updateUser}
        )
    </insert>

    <update id="update" parameterType="com.sevb.admin.core.model.Workflow">
        UPDATE workflow
        SET project_id = #{projectId},
            name = #{name},
            code = #{code},
            description = #{description},
            tags = #{tags},
            category = #{category},
            version = #{version},

            global_params = #{globalParams},
            warning_type = #{warningType},
            warning_group_id = #{warningGroupId},
            job_id = #{jobId},
            cron_expression = #{cronExpression},
            status = #{status},
            update_time = NOW(),
            update_user = #{updateUser}
        WHERE id = #{id}
    </update>

    <update id="delete">
        UPDATE workflow
        SET deleted = 1,
            update_time = NOW(),
            update_user = #{updateUser}
        WHERE id = #{id}
    </update>

    <update id="updateStatus">
        UPDATE workflow
        SET status = #{status},
            update_time = NOW(),
            update_user = #{updateUser}
        WHERE id = #{id}
    </update>

    <update id="updateJobId">
        UPDATE workflow
        SET job_id = #{jobId},
            status = #{status},
            update_time = NOW(),
            update_user = #{updateUser}
        WHERE id = #{id}
    </update>

    <select id="checkCodeExists" resultType="int">
        SELECT COUNT(1)
        FROM workflow
        WHERE project_id = #{projectId}
          AND code = #{code}
          AND deleted = 0
        <if test="excludeId != null">
            AND id != #{excludeId}
        </if>
    </select>

    <select id="checkNameExists" resultType="int">
        SELECT COUNT(1)
        FROM workflow
        WHERE project_id = #{projectId}
          AND name = #{name}
          AND deleted = 0
        <if test="excludeId != null">
            AND id != #{excludeId}
        </if>
    </select>

    <select id="countByProjectId" resultType="int">
        SELECT COUNT(1)
        FROM workflow
        WHERE project_id = #{projectId}
          AND deleted = 0
    </select>

    <select id="countByCategory" resultType="int">
        SELECT COUNT(1)
        FROM workflow
        WHERE project_id = #{projectId}
          AND category = #{category}
          AND deleted = 0
    </select>

    <!-- 查询所有在线且使用调度中心编排的工作流 -->
    <select id="findOnlineCentralWorkflows" resultMap="Workflow">
        SELECT <include refid="Base_Column_List"/>
        FROM workflow t
        WHERE t.status = 2
          AND t.orchestration_mode = 'CENTRAL'
          AND t.deleted = 0
        ORDER BY t.update_time DESC
    </select>

    <!-- 更新工作流最后执行ID -->
    <update id="updateLastExecutionId">
        UPDATE workflow
        SET last_execution_id = #{lastExecutionId},
            last_trigger_time = NOW(),
            update_time = NOW()
        WHERE id = #{id}
    </update>

    <!-- 更新工作流执行统计信息 -->
    <update id="updateExecutionStats">
        UPDATE workflow
        SET execution_count = #{executionCount},
            success_count = #{successCount},
            failure_count = #{failureCount},
            update_time = NOW()
        WHERE id = #{id}
    </update>

</mapper>
