<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sevb.admin.dao.NodeTypeDao">

    <resultMap id="NodeType" type="com.sevb.admin.core.model.NodeType">
        <result column="id" property="id"/>
        <result column="type_code" property="typeCode"/>
        <result column="type_name" property="typeName"/>
        <result column="category" property="category"/>
        <result column="icon" property="icon"/>
        <result column="color" property="color"/>
        <result column="description" property="description"/>
        <result column="config_schema" property="configSchema"/>
        <result column="executor_class" property="executorClass"/>
        <result column="sort_order" property="sortOrder"/>
        <result column="status" property="status"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
    </resultMap>

    <sql id="Base_Column_List">
        t.id,
        t.type_code,
        t.type_name,
        t.category,
        t.icon,
        t.color,
        t.description,
        t.config_schema,
        t.executor_class,
        t.sort_order,
        t.status,
        t.create_time,
        t.update_time
    </sql>

    <select id="pageList" resultMap="NodeType">
        SELECT <include refid="Base_Column_List"/>
        FROM node_type AS t
        WHERE 1=1
        <if test="typeName != null and typeName != ''">
            AND t.type_name LIKE CONCAT('%', #{typeName}, '%')
        </if>
        <if test="category != null and category != ''">
            AND t.category = #{category}
        </if>
        <if test="status != null">
            AND t.status = #{status}
        </if>
        ORDER BY t.sort_order ASC, t.create_time DESC
        LIMIT #{offset}, #{pagesize}
    </select>

    <select id="pageListCount" resultType="int">
        SELECT COUNT(1)
        FROM node_type AS t
        WHERE 1=1
        <if test="typeName != null and typeName != ''">
            AND t.type_name LIKE CONCAT('%', #{typeName}, '%')
        </if>
        <if test="category != null and category != ''">
            AND t.category = #{category}
        </if>
        <if test="status != null">
            AND t.status = #{status}
        </if>
    </select>

    <select id="findAllEnabled" resultMap="NodeType">
        SELECT <include refid="Base_Column_List"/>
        FROM node_type AS t
        WHERE t.status = 1
        ORDER BY t.sort_order ASC, t.create_time ASC
    </select>

    <select id="findByCategory" resultMap="NodeType">
        SELECT <include refid="Base_Column_List"/>
        FROM node_type AS t
        WHERE t.category = #{category}
          AND t.status = 1
        ORDER BY t.sort_order ASC, t.create_time ASC
    </select>

    <select id="load" parameterType="java.lang.Long" resultMap="NodeType">
        SELECT <include refid="Base_Column_List"/>
        FROM node_type AS t
        WHERE t.id = #{id}
    </select>

    <select id="findByTypeCode" parameterType="java.lang.String" resultMap="NodeType">
        SELECT <include refid="Base_Column_List"/>
        FROM node_type AS t
        WHERE t.type_code = #{typeCode}
    </select>

    <insert id="save" parameterType="com.sevb.admin.core.model.NodeType" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO node_type (
            type_code, type_name, category, icon, color, description,
            config_schema, executor_class, sort_order, status
        ) VALUES (
            #{typeCode}, #{typeName}, #{category}, #{icon}, #{color}, #{description},
            #{configSchema}, #{executorClass}, #{sortOrder}, #{status}
        )
    </insert>

    <update id="update" parameterType="com.sevb.admin.core.model.NodeType">
        UPDATE node_type
        SET type_code = #{typeCode},
            type_name = #{typeName},
            category = #{category},
            icon = #{icon},
            color = #{color},
            description = #{description},
            config_schema = #{configSchema},
            executor_class = #{executorClass},
            sort_order = #{sortOrder},
            status = #{status},
            update_time = NOW()
        WHERE id = #{id}
    </update>

    <delete id="delete" parameterType="java.lang.Long">
        DELETE FROM node_type WHERE id = #{id}
    </delete>

    <select id="checkTypeCodeExists" resultType="int">
        SELECT COUNT(1)
        FROM node_type
        WHERE type_code = #{typeCode}
        <if test="excludeId != null">
            AND id != #{excludeId}
        </if>
    </select>

    <select id="checkTypeNameExists" resultType="int">
        SELECT COUNT(1)
        FROM node_type
        WHERE type_name = #{typeName}
        <if test="excludeId != null">
            AND id != #{excludeId}
        </if>
    </select>

</mapper>
