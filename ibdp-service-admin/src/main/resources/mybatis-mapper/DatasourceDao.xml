<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sevb.admin.dao.DatasourceDao">

    <resultMap id="Datasource" type="com.sevb.admin.core.model.Datasource">
        <result column="id" property="id"/>
        <result column="name" property="name"/>
        <result column="type" property="type"/>
        <result column="host" property="host"/>
        <result column="port" property="port"/>
        <result column="database_name" property="databaseName"/>
        <result column="username" property="username"/>
        <result column="password" property="password"/>
        <result column="connection_params" property="connectionParams"/>
        <result column="description" property="description"/>
        <result column="status" property="status"/>
        <result column="test_status" property="testStatus"/>
        <result column="test_message" property="testMessage"/>
        <result column="test_time" property="testTime"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="create_user" property="createUser"/>
        <result column="update_user" property="updateUser"/>
        <result column="deleted" property="deleted"/>
    </resultMap>

    <sql id="Base_Column_List">
        t.id,
        t.name,
        t.type,
        t.host,
        t.port,
        t.database_name,
        t.username,
        t.password,
        t.connection_params,
        t.description,
        t.status,
        t.test_status,
        t.test_message,
        t.test_time,
        t.create_time,
        t.update_time,
        t.create_user,
        t.update_user,
        t.deleted
    </sql>

    <select id="pageList" resultMap="Datasource">
        SELECT <include refid="Base_Column_List"/>
        FROM datasource AS t
        WHERE t.deleted = 0
        <if test="name != null and name.trim() != ''">
            AND t.name LIKE CONCAT('%', #{name}, '%')
        </if>
        <if test="type != null and type.trim() != ''">
            AND t.type = #{type}
        </if>
        <if test="status != null">
            AND t.status = #{status}
        </if>
        ORDER BY t.create_time DESC
        LIMIT #{offset}, #{pagesize}
    </select>

    <select id="pageListCount" resultType="int">
        SELECT COUNT(1)
        FROM datasource AS t
        WHERE t.deleted = 0
        <if test="name != null and name.trim() != ''">
            AND t.name LIKE CONCAT('%', #{name}, '%')
        </if>
        <if test="type != null and type.trim() != ''">
            AND t.type = #{type}
        </if>
        <if test="status != null">
            AND t.status = #{status}
        </if>
    </select>

    <select id="findAllEnabled" resultMap="Datasource">
        SELECT <include refid="Base_Column_List"/>
        FROM datasource AS t
        WHERE t.deleted = 0 AND t.status = 1
        ORDER BY t.name ASC
    </select>

    <select id="load" parameterType="java.lang.Long" resultMap="Datasource">
        SELECT <include refid="Base_Column_List"/>
        FROM datasource AS t
        WHERE t.id = #{id} AND t.deleted = 0
    </select>

    <select id="findByName" parameterType="java.lang.String" resultMap="Datasource">
        SELECT <include refid="Base_Column_List"/>
        FROM datasource AS t
        WHERE t.name = #{name} AND t.deleted = 0
    </select>

    <insert id="save" parameterType="com.sevb.admin.core.model.Datasource" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO datasource (
            name, type, host, port, database_name, username, password,
            connection_params, description, status, create_user
        ) VALUES (
            #{name}, #{type}, #{host}, #{port}, #{databaseName}, #{username}, #{password},
            #{connectionParams}, #{description}, #{status}, #{createUser}
        )
    </insert>

    <update id="update" parameterType="com.sevb.admin.core.model.Datasource">
        UPDATE datasource
        SET name = #{name},
            type = #{type},
            host = #{host},
            port = #{port},
            database_name = #{databaseName},
            username = #{username},
            password = #{password},
            connection_params = #{connectionParams},
            description = #{description},
            status = #{status},
            update_user = #{updateUser}
        WHERE id = #{id} AND deleted = 0
    </update>

    <update id="delete">
        UPDATE datasource
        SET deleted = 1,
            update_user = #{updateUser}
        WHERE id = #{id} AND deleted = 0
    </update>

    <update id="updateTestStatus">
        UPDATE datasource
        SET test_status = #{testStatus},
            test_message = #{testMessage},
            test_time = NOW(),
            update_user = #{updateUser}
        WHERE id = #{id} AND deleted = 0
    </update>

    <select id="findByType" parameterType="java.lang.String" resultMap="Datasource">
        SELECT <include refid="Base_Column_List"/>
        FROM datasource AS t
        WHERE t.type = #{type} AND t.deleted = 0 AND t.status = 1
        ORDER BY t.name ASC
    </select>

    <select id="checkNameExists" resultType="int">
        SELECT COUNT(1)
        FROM datasource
        WHERE name = #{name} AND deleted = 0
        <if test="excludeId != null">
            AND id != #{excludeId}
        </if>
    </select>

</mapper>
