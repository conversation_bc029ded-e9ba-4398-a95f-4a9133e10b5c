<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" 
	"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sevb.admin.dao.DataxIncrementalConfigDao">
	
	<resultMap id="DataxIncrementalConfig" type="com.sevb.admin.core.model.DataxIncrementalConfig">
		<result column="id" property="id" />
		<result column="node_id" property="nodeId" />
		<result column="incremental_type" property="incrementalType" />
		<result column="incremental_column" property="incrementalColumn" />
		<result column="incremental_value" property="incrementalValue" />
		<result column="batch_size" property="batchSize" />
		<result column="max_retry_times" property="maxRetryTimes" />
		<result column="custom_condition" property="customCondition" />
		<result column="enable_checkpoint" property="enableCheckpoint" />
		<result column="checkpoint_interval" property="checkpointInterval" />
		<result column="enabled" property="enabled" />
		<result column="create_time" property="createTime" />
		<result column="update_time" property="updateTime" />
		<result column="create_user" property="createUser" />
		<result column="update_user" property="updateUser" />
	</resultMap>

	<sql id="Base_Column_List">
		t.id,
		t.node_id,
		t.incremental_type,
		t.incremental_column,
		t.incremental_value,
		t.batch_size,
		t.max_retry_times,
		t.custom_condition,
		t.enable_checkpoint,
		t.checkpoint_interval,
		t.enabled,
		t.create_time,
		t.update_time,
		t.create_user,
		t.update_user
	</sql>

	<select id="load" parameterType="java.lang.Long" resultMap="DataxIncrementalConfig">
		SELECT <include refid="Base_Column_List" />
		FROM datax_incremental_config AS t
		WHERE t.id = #{id}
	</select>

	<select id="findByNodeId" resultMap="DataxIncrementalConfig">
		SELECT <include refid="Base_Column_List" />
		FROM datax_incremental_config AS t
		WHERE t.node_id = #{nodeId}
		LIMIT 1
	</select>

	<select id="findAllEnabled" resultMap="DataxIncrementalConfig">
		SELECT <include refid="Base_Column_List" />
		FROM datax_incremental_config AS t
		WHERE t.enabled = 1
		ORDER BY t.create_time DESC
	</select>

	<select id="pageList" resultMap="DataxIncrementalConfig">
		SELECT <include refid="Base_Column_List" />
		FROM datax_incremental_config AS t
		<where>
			<if test="nodeId != null">
				AND t.node_id = #{nodeId}
			</if>
			<if test="incrementalType != null and incrementalType.trim() != ''">
				AND t.incremental_type = #{incrementalType}
			</if>
			<if test="enabled != null">
				AND t.enabled = #{enabled}
			</if>
		</where>
		ORDER BY t.update_time DESC
		LIMIT #{offset}, #{pagesize}
	</select>

	<select id="pageListCount" resultType="int">
		SELECT COUNT(1)
		FROM datax_incremental_config AS t
		<where>
			<if test="nodeId != null">
				AND t.node_id = #{nodeId}
			</if>
			<if test="incrementalType != null and incrementalType.trim() != ''">
				AND t.incremental_type = #{incrementalType}
			</if>
			<if test="enabled != null">
				AND t.enabled = #{enabled}
			</if>
		</where>
	</select>

	<select id="findByIncrementalType" resultMap="DataxIncrementalConfig">
		SELECT <include refid="Base_Column_List" />
		FROM datax_incremental_config AS t
		WHERE t.incremental_type = #{incrementalType}
		ORDER BY t.create_time DESC
	</select>

	<select id="existsByNodeId" resultType="boolean">
		SELECT COUNT(1) > 0
		FROM datax_incremental_config AS t
		WHERE t.node_id = #{nodeId}
	</select>

	<select id="findByNodeIds" resultMap="DataxIncrementalConfig">
		SELECT <include refid="Base_Column_List" />
		FROM datax_incremental_config AS t
		WHERE t.node_id IN
		<foreach collection="nodeIds" item="nodeId" open="(" separator="," close=")">
			#{nodeId}
		</foreach>
		ORDER BY t.create_time DESC
	</select>

	<select id="countEnabled" resultType="int">
		SELECT COUNT(1)
		FROM datax_incremental_config AS t
		WHERE t.enabled = 1
	</select>

	<select id="countTotal" resultType="int">
		SELECT COUNT(1)
		FROM datax_incremental_config AS t
	</select>

	<select id="findByIncrementalColumn" resultMap="DataxIncrementalConfig">
		SELECT <include refid="Base_Column_List" />
		FROM datax_incremental_config AS t
		WHERE t.incremental_column = #{incrementalColumn}
		ORDER BY t.create_time DESC
	</select>

	<select id="getDefaultTemplate" resultMap="DataxIncrementalConfig">
		SELECT <include refid="Base_Column_List" />
		FROM datax_incremental_config AS t
		WHERE t.node_id = 0
		LIMIT 1
	</select>

	<select id="validateConfig" resultType="boolean">
		SELECT COUNT(1) = 0
		FROM datax_incremental_config AS t
		WHERE t.node_id = #{nodeId}
		  AND t.incremental_column = #{incrementalColumn}
		  AND t.incremental_type = #{incrementalType}
	</select>

	<insert id="save" parameterType="com.sevb.admin.core.model.DataxIncrementalConfig" useGeneratedKeys="true" keyProperty="id">
		INSERT INTO datax_incremental_config (
			node_id,
			incremental_type,
			incremental_column,
			incremental_value,
			batch_size,
			max_retry_times,
			custom_condition,
			enable_checkpoint,
			checkpoint_interval,
			enabled,
			create_time,
			update_time,
			create_user,
			update_user
		) VALUES (
			#{nodeId},
			#{incrementalType},
			#{incrementalColumn},
			#{incrementalValue},
			#{batchSize},
			#{maxRetryTimes},
			#{customCondition},
			#{enableCheckpoint},
			#{checkpointInterval},
			#{enabled},
			#{createTime},
			#{updateTime},
			#{createUser},
			#{updateUser}
		)
	</insert>

	<insert id="saveOrUpdate" parameterType="com.sevb.admin.core.model.DataxIncrementalConfig">
		INSERT INTO datax_incremental_config (
			node_id,
			incremental_type,
			incremental_column,
			incremental_value,
			batch_size,
			max_retry_times,
			custom_condition,
			enable_checkpoint,
			checkpoint_interval,
			enabled,
			create_time,
			update_time,
			create_user,
			update_user
		) VALUES (
			#{nodeId},
			#{incrementalType},
			#{incrementalColumn},
			#{incrementalValue},
			#{batchSize},
			#{maxRetryTimes},
			#{customCondition},
			#{enableCheckpoint},
			#{checkpointInterval},
			#{enabled},
			#{createTime},
			#{updateTime},
			#{createUser},
			#{updateUser}
		) ON DUPLICATE KEY UPDATE
			incremental_type = VALUES(incremental_type),
			incremental_column = VALUES(incremental_column),
			incremental_value = VALUES(incremental_value),
			batch_size = VALUES(batch_size),
			max_retry_times = VALUES(max_retry_times),
			custom_condition = VALUES(custom_condition),
			enable_checkpoint = VALUES(enable_checkpoint),
			checkpoint_interval = VALUES(checkpoint_interval),
			enabled = VALUES(enabled),
			update_time = VALUES(update_time),
			update_user = VALUES(update_user)
	</insert>

	<insert id="copyConfig">
		INSERT INTO datax_incremental_config (
			node_id,
			incremental_type,
			incremental_column,
			incremental_value,
			batch_size,
			max_retry_times,
			custom_condition,
			enable_checkpoint,
			checkpoint_interval,
			enabled,
			create_time,
			update_time,
			create_user,
			update_user
		)
		SELECT 
			#{targetNodeId},
			incremental_type,
			incremental_column,
			incremental_value,
			batch_size,
			max_retry_times,
			custom_condition,
			enable_checkpoint,
			checkpoint_interval,
			enabled,
			NOW(),
			NOW(),
			#{createUser},
			#{createUser}
		FROM datax_incremental_config
		WHERE node_id = #{sourceNodeId}
	</insert>

	<update id="update" parameterType="com.sevb.admin.core.model.DataxIncrementalConfig">
		UPDATE datax_incremental_config
		SET node_id = #{nodeId},
			incremental_type = #{incrementalType},
			incremental_column = #{incrementalColumn},
			incremental_value = #{incrementalValue},
			batch_size = #{batchSize},
			max_retry_times = #{maxRetryTimes},
			custom_condition = #{customCondition},
			enable_checkpoint = #{enableCheckpoint},
			checkpoint_interval = #{checkpointInterval},
			enabled = #{enabled},
			update_time = #{updateTime},
			update_user = #{updateUser}
		WHERE id = #{id}
	</update>

	<update id="batchUpdateEnabled">
		UPDATE datax_incremental_config
		SET enabled = #{enabled},
			update_time = NOW(),
			update_user = #{updateUser}
		WHERE id IN
		<foreach collection="ids" item="id" open="(" separator="," close=")">
			#{id}
		</foreach>
	</update>

	<update id="updateEnabled">
		UPDATE datax_incremental_config
		SET enabled = #{enabled},
			update_time = NOW(),
			update_user = #{updateUser}
		WHERE id = #{id}
	</update>

	<delete id="delete">
		DELETE FROM datax_incremental_config
		WHERE id = #{id}
	</delete>

	<delete id="batchDelete">
		DELETE FROM datax_incremental_config
		WHERE id IN
		<foreach collection="ids" item="id" open="(" separator="," close=")">
			#{id}
		</foreach>
	</delete>

</mapper>
