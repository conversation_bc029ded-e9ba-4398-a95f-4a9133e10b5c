<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" 
	"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sevb.admin.dao.ProjectDao">
	
	<resultMap id="Project" type="com.sevb.admin.core.model.Project">
		<result column="id" property="id" />
		<result column="name" property="name" />
		<result column="code" property="code" />
		<result column="type" property="type" />
		<result column="description" property="description" />
		<result column="status" property="status" />
		<result column="create_time" property="createTime" />
		<result column="update_time" property="updateTime" />
		<result column="create_user" property="createUser" />
		<result column="update_user" property="updateUser" />
		<result column="deleted" property="deleted" />
		<result column="member_count" property="memberCount" />
		<result column="current_user_role" property="currentUserRole" />
	</resultMap>

	<sql id="Base_Column_List">
		t.id,
		t.name,
		t.code,
		t.type,
		t.description,
		t.status,
		t.create_time,
		t.update_time,
		t.create_user,
		t.update_user,
		t.deleted
	</sql>

	<select id="pageList" resultMap="Project">
		SELECT 
			<include refid="Base_Column_List" />,
			(SELECT COUNT(1) FROM project_member pm WHERE pm.project_id = t.id AND pm.deleted = 0) as member_count,
			<if test="userId != null and userId != ''">
				(SELECT pm.role FROM project_member pm WHERE pm.project_id = t.id AND pm.user_id = #{userId} AND pm.deleted = 0) as current_user_role
			</if>
			<if test="userId == null or userId == ''">
				NULL as current_user_role
			</if>
		FROM project t
		<where>
			t.deleted = 0
			<if test="name != null and name != ''">
				AND t.name LIKE CONCAT('%', #{name}, '%')
			</if>
			<if test="type != null and type != ''">
				AND t.type = #{type}
			</if>
			<if test="status != null">
				AND t.status = #{status}
			</if>
			<!-- 临时注释权限控制，用于调试 -->
			<!--
			<if test="userId != null and userId != ''">
				AND (
					t.type = 'PUBLIC'
					OR EXISTS (
						SELECT 1 FROM project_member pm
						WHERE pm.project_id = t.id
						AND pm.user_id = #{userId}
						AND pm.deleted = 0
					)
				)
			</if>
			-->
		</where>
		ORDER BY t.create_time DESC
		LIMIT #{offset}, #{pagesize}
	</select>

	<select id="pageListCount" resultType="int">
		SELECT COUNT(1)
		FROM project t
		<where>
			t.deleted = 0
			<if test="name != null and name != ''">
				AND t.name LIKE CONCAT('%', #{name}, '%')
			</if>
			<if test="type != null and type != ''">
				AND t.type = #{type}
			</if>
			<if test="status != null">
				AND t.status = #{status}
			</if>
			<!-- 临时注释权限控制，用于调试 -->
			<!--
			<if test="userId != null and userId != ''">
				AND (
					t.type = 'PUBLIC'
					OR EXISTS (
						SELECT 1 FROM project_member pm
						WHERE pm.project_id = t.id
						AND pm.user_id = #{userId}
						AND pm.deleted = 0
					)
				)
			</if>
			-->
		</where>
	</select>

	<select id="load" resultMap="Project">
		SELECT <include refid="Base_Column_List" />
		FROM project t
		WHERE t.id = #{id} AND t.deleted = 0
	</select>

	<select id="loadByCode" resultMap="Project">
		SELECT <include refid="Base_Column_List" />
		FROM project t
		WHERE t.code = #{code} AND t.deleted = 0
	</select>

	<insert id="save" parameterType="com.sevb.admin.core.model.Project" useGeneratedKeys="true" keyProperty="id">
		INSERT INTO project (
			name,
			code,
			type,
			description,
			status,
			create_user,
			update_user
		) VALUES (
			#{name},
			#{code},
			#{type},
			#{description},
			#{status},
			#{createUser},
			#{updateUser}
		)
	</insert>

	<update id="update" parameterType="com.sevb.admin.core.model.Project">
		UPDATE project SET
			name = #{name},
			code = #{code},
			type = #{type},
			description = #{description},
			status = #{status},
			update_user = #{updateUser}
		WHERE id = #{id} AND deleted = 0
	</update>

	<update id="delete">
		UPDATE project SET
			deleted = 1,
			update_user = #{updateUser}
		WHERE id = #{id} AND deleted = 0
	</update>

	<select id="findAccessibleProjects" resultMap="Project">
		SELECT DISTINCT <include refid="Base_Column_List" />
		FROM project t
		WHERE t.deleted = 0 AND t.status = 1
		AND (
			t.type = 'PUBLIC' 
			OR EXISTS (
				SELECT 1 FROM project_member pm 
				WHERE pm.project_id = t.id 
				AND pm.user_id = #{userId} 
				AND pm.deleted = 0
			)
		)
		ORDER BY t.create_time DESC
	</select>

	<select id="findPublicProjects" resultMap="Project">
		SELECT <include refid="Base_Column_List" />
		FROM project t
		WHERE t.deleted = 0 AND t.status = 1 AND t.type = 'PUBLIC'
		ORDER BY t.create_time DESC
	</select>

	<select id="findUserProjects" resultMap="Project">
		SELECT <include refid="Base_Column_List" />
		FROM project t
		INNER JOIN project_member pm ON t.id = pm.project_id
		WHERE t.deleted = 0 AND t.status = 1 
		AND pm.user_id = #{userId} AND pm.deleted = 0
		ORDER BY t.create_time DESC
	</select>

	<select id="checkCodeExists" resultType="int">
		SELECT COUNT(1)
		FROM project
		WHERE code = #{code} AND deleted = 0
		<if test="excludeId != null">
			AND id != #{excludeId}
		</if>
	</select>

	<update id="updateStatus">
		UPDATE project SET
			status = #{status},
			update_user = #{updateUser}
		WHERE id = #{id} AND deleted = 0
	</update>

	<update id="updateType">
		UPDATE project SET
			type = #{type},
			update_user = #{updateUser}
		WHERE id = #{id} AND deleted = 0
	</update>

	<select id="findAllEnabled" resultMap="Project">
		SELECT <include refid="Base_Column_List" />
		FROM project t
		WHERE t.deleted = 0 AND t.status = 1
		ORDER BY t.create_time DESC
	</select>

	<select id="findByType" resultMap="Project">
		SELECT <include refid="Base_Column_List" />
		FROM project t
		WHERE t.deleted = 0 AND t.type = #{type}
		ORDER BY t.create_time DESC
	</select>

</mapper>
