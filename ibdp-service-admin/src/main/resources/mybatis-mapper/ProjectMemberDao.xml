<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" 
	"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sevb.admin.dao.ProjectMemberDao">
	
	<resultMap id="ProjectMember" type="com.sevb.admin.core.model.ProjectMember">
		<result column="id" property="id" />
		<result column="project_id" property="projectId" />
		<result column="user_id" property="userId" />
		<result column="username" property="username" />
		<result column="role" property="role" />
		<result column="join_time" property="joinTime" />
		<result column="status" property="status" />
		<result column="create_time" property="createTime" />
		<result column="update_time" property="updateTime" />
		<result column="create_user" property="createUser" />
		<result column="update_user" property="updateUser" />
		<result column="deleted" property="deleted" />
		<result column="project_name" property="projectName" />
		<result column="project_code" property="projectCode" />
		<result column="role_name" property="roleName" />
	</resultMap>

	<sql id="Base_Column_List">
		t.id,
		t.project_id,
		t.user_id,
		t.username,
		t.role,
		t.join_time,
		t.status,
		t.create_time,
		t.update_time,
		t.create_user,
		t.update_user,
		t.deleted
	</sql>

	<select id="pageList" resultMap="ProjectMember">
		SELECT 
			<include refid="Base_Column_List" />,
			p.name as project_name,
			p.code as project_code,
			CASE 
				WHEN t.role = 'ADMIN' THEN '项目管理员'
				WHEN t.role = 'DEVELOPER' THEN '开发者'
				WHEN t.role = 'VIEWER' THEN '查看者'
				ELSE t.role
			END as role_name
		FROM project_member t
		LEFT JOIN project p ON t.project_id = p.id
		<where>
			t.deleted = 0
			<if test="projectId != null">
				AND t.project_id = #{projectId}
			</if>
			<if test="username != null and username != ''">
				AND t.username LIKE CONCAT('%', #{username}, '%')
			</if>
			<if test="role != null and role != ''">
				AND t.role = #{role}
			</if>
			<if test="status != null">
				AND t.status = #{status}
			</if>
		</where>
		ORDER BY t.join_time DESC
		LIMIT #{offset}, #{pagesize}
	</select>

	<select id="pageListCount" resultType="int">
		SELECT COUNT(1)
		FROM project_member t
		<where>
			t.deleted = 0
			<if test="projectId != null">
				AND t.project_id = #{projectId}
			</if>
			<if test="username != null and username != ''">
				AND t.username LIKE CONCAT('%', #{username}, '%')
			</if>
			<if test="role != null and role != ''">
				AND t.role = #{role}
			</if>
			<if test="status != null">
				AND t.status = #{status}
			</if>
		</where>
	</select>

	<select id="load" resultMap="ProjectMember">
		SELECT <include refid="Base_Column_List" />
		FROM project_member t
		WHERE t.id = #{id} AND t.deleted = 0
	</select>

	<select id="loadByProjectAndUser" resultMap="ProjectMember">
		SELECT <include refid="Base_Column_List" />
		FROM project_member t
		WHERE t.project_id = #{projectId} AND t.user_id = #{userId} AND t.deleted = 0
	</select>

	<select id="findByProjectId" resultMap="ProjectMember">
		SELECT 
			<include refid="Base_Column_List" />,
			CASE 
				WHEN t.role = 'ADMIN' THEN '项目管理员'
				WHEN t.role = 'DEVELOPER' THEN '开发者'
				WHEN t.role = 'VIEWER' THEN '查看者'
				ELSE t.role
			END as role_name
		FROM project_member t
		WHERE t.project_id = #{projectId} AND t.deleted = 0
		ORDER BY t.join_time ASC
	</select>

	<select id="findByUserId" resultMap="ProjectMember">
		SELECT 
			<include refid="Base_Column_List" />,
			p.name as project_name,
			p.code as project_code,
			CASE 
				WHEN t.role = 'ADMIN' THEN '项目管理员'
				WHEN t.role = 'DEVELOPER' THEN '开发者'
				WHEN t.role = 'VIEWER' THEN '查看者'
				ELSE t.role
			END as role_name
		FROM project_member t
		LEFT JOIN project p ON t.project_id = p.id
		WHERE t.user_id = #{userId} AND t.deleted = 0
		ORDER BY t.join_time DESC
	</select>

	<insert id="save" parameterType="com.sevb.admin.core.model.ProjectMember" useGeneratedKeys="true" keyProperty="id">
		INSERT INTO project_member (
			project_id,
			user_id,
			username,
			role,
			join_time,
			status,
			create_user,
			update_user
		) VALUES (
			#{projectId},
			#{userId},
			#{username},
			#{role},
			#{joinTime},
			#{status},
			#{createUser},
			#{updateUser}
		)
	</insert>

	<update id="update" parameterType="com.sevb.admin.core.model.ProjectMember">
		UPDATE project_member SET
			role = #{role},
			username = #{username},
			status = #{status},
			update_user = #{updateUser}
		WHERE id = #{id} AND deleted = 0
	</update>

	<update id="delete">
		UPDATE project_member SET
			deleted = 1,
			update_user = #{updateUser}
		WHERE id = #{id} AND deleted = 0
	</update>

	<update id="deleteByProjectAndUser">
		UPDATE project_member SET
			deleted = 1,
			update_user = #{updateUser}
		WHERE project_id = #{projectId} AND user_id = #{userId} AND deleted = 0
	</update>

	<select id="checkMemberExists" resultType="int">
		SELECT COUNT(1)
		FROM project_member
		WHERE project_id = #{projectId} AND user_id = #{userId} AND deleted = 0
	</select>

	<update id="updateRole">
		UPDATE project_member SET
			role = #{role},
			update_user = #{updateUser}
		WHERE id = #{id} AND deleted = 0
	</update>

	<update id="updateStatus">
		UPDATE project_member SET
			status = #{status},
			update_user = #{updateUser}
		WHERE id = #{id} AND deleted = 0
	</update>

	<select id="findAdminsByProjectId" resultMap="ProjectMember">
		SELECT <include refid="Base_Column_List" />
		FROM project_member t
		WHERE t.project_id = #{projectId} AND t.role = 'ADMIN' AND t.deleted = 0
		ORDER BY t.join_time ASC
	</select>

	<select id="countByProjectId" resultType="int">
		SELECT COUNT(1)
		FROM project_member
		WHERE project_id = #{projectId} AND deleted = 0
	</select>

	<update id="deleteByProjectId">
		UPDATE project_member SET
			deleted = 1,
			update_user = #{updateUser}
		WHERE project_id = #{projectId} AND deleted = 0
	</update>

	<insert id="batchSave" parameterType="java.util.List">
		INSERT INTO project_member (
			project_id,
			user_id,
			username,
			role,
			join_time,
			status,
			create_user,
			update_user
		) VALUES
		<foreach collection="members" item="member" separator=",">
			(
				#{member.projectId},
				#{member.userId},
				#{member.username},
				#{member.role},
				#{member.joinTime},
				#{member.status},
				#{member.createUser},
				#{member.updateUser}
			)
		</foreach>
	</insert>

</mapper>
