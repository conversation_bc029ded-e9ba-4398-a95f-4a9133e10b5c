<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" 
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.sevb.admin.dao.WorkflowNodeRelationDao">

    <resultMap id="WorkflowNodeRelation" type="com.sevb.admin.core.model.WorkflowNodeRelation">
        <result column="id" property="id"/>
        <result column="workflow_id" property="workflowId"/>
        <result column="pre_node_code" property="preNodeCode"/>
        <result column="post_node_code" property="postNodeCode"/>
        <result column="condition_type" property="conditionType"/>
        <result column="condition_params" property="conditionParams"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
    </resultMap>

    <sql id="Base_Column_List">
        t.id,
        t.workflow_id,
        t.pre_node_code,
        t.post_node_code,
        t.condition_type,
        t.condition_params,
        t.create_time,
        t.update_time
    </sql>

    <select id="listByWorkflow" resultMap="WorkflowNodeRelation">
        SELECT <include refid="Base_Column_List"/>
        FROM workflow_node_relation AS t
        WHERE t.workflow_id = #{workflowId}
        ORDER BY t.create_time ASC
    </select>

    <select id="load" resultMap="WorkflowNodeRelation">
        SELECT <include refid="Base_Column_List"/>
        FROM workflow_node_relation AS t
        WHERE t.id = #{id}
    </select>

    <select id="listByPreNode" resultMap="WorkflowNodeRelation">
        SELECT <include refid="Base_Column_List"/>
        FROM workflow_node_relation AS t
        WHERE t.workflow_id = #{workflowId}
          AND t.pre_node_code = #{preNodeCode}
        ORDER BY t.create_time ASC
    </select>

    <select id="listByPostNode" resultMap="WorkflowNodeRelation">
        SELECT <include refid="Base_Column_List"/>
        FROM workflow_node_relation AS t
        WHERE t.workflow_id = #{workflowId}
          AND t.post_node_code = #{postNodeCode}
        ORDER BY t.create_time ASC
    </select>

    <insert id="save" parameterType="com.sevb.admin.core.model.WorkflowNodeRelation" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO workflow_node_relation (
            workflow_id, pre_node_code, post_node_code, condition_type, condition_params
        ) VALUES (
            #{workflowId}, #{preNodeCode}, #{postNodeCode}, #{conditionType}, #{conditionParams}
        )
    </insert>

    <insert id="batchSave" parameterType="java.util.List" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO workflow_node_relation (
            workflow_id, pre_node_code, post_node_code, condition_type, condition_params
        ) VALUES
        <foreach collection="relations" item="item" separator=",">
            (#{item.workflowId}, #{item.preNodeCode}, #{item.postNodeCode}, #{item.conditionType}, #{item.conditionParams})
        </foreach>
    </insert>

    <update id="update" parameterType="com.sevb.admin.core.model.WorkflowNodeRelation">
        UPDATE workflow_node_relation
        SET workflow_id = #{workflowId},
            pre_node_code = #{preNodeCode},
            post_node_code = #{postNodeCode},
            condition_type = #{conditionType},
            condition_params = #{conditionParams},
            update_time = NOW()
        WHERE id = #{id}
    </update>

    <update id="batchUpdate" parameterType="java.util.List">
        UPDATE workflow_node_relation
        SET workflow_id = CASE id
            <foreach collection="relations" item="item">
                WHEN #{item.id} THEN #{item.workflowId}
            </foreach>
            END,
            pre_node_code = CASE id
            <foreach collection="relations" item="item">
                WHEN #{item.id} THEN #{item.preNodeCode}
            </foreach>
            END,
            post_node_code = CASE id
            <foreach collection="relations" item="item">
                WHEN #{item.id} THEN #{item.postNodeCode}
            </foreach>
            END,
            condition_type = CASE id
            <foreach collection="relations" item="item">
                WHEN #{item.id} THEN #{item.conditionType}
            </foreach>
            END,
            condition_params = CASE id
            <foreach collection="relations" item="item">
                WHEN #{item.id} THEN #{item.conditionParams}
            </foreach>
            END,
            update_time = NOW()
        WHERE id IN
        <foreach collection="relations" item="item" open="(" separator="," close=")">
            #{item.id}
        </foreach>
    </update>

    <delete id="delete">
        DELETE FROM workflow_node_relation
        WHERE id = #{id}
    </delete>

    <delete id="batchDelete">
        DELETE FROM workflow_node_relation
        WHERE id IN
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <delete id="deleteByWorkflow">
        DELETE FROM workflow_node_relation
        WHERE workflow_id = #{workflowId}
    </delete>

    <delete id="deleteByNode">
        DELETE FROM workflow_node_relation
        WHERE workflow_id = #{workflowId}
          AND (pre_node_code = #{nodeCode} OR post_node_code = #{nodeCode})
    </delete>

    <delete id="removeByWorkflow">
        DELETE FROM workflow_node_relation
        WHERE workflow_id = #{workflowId}
    </delete>

    <select id="checkRelationExists" resultType="int">
        SELECT COUNT(1)
        FROM workflow_node_relation
        WHERE workflow_id = #{workflowId}
          AND pre_node_code = #{preNodeCode}
          AND post_node_code = #{postNodeCode}
    </select>

    <select id="getUpstreamNodes" resultType="string">
        WITH RECURSIVE upstream_nodes AS (
            SELECT pre_node_code
            FROM workflow_node_relation
            WHERE workflow_id = #{workflowId}
              AND post_node_code = #{nodeCode}

            UNION ALL

            SELECT r.pre_node_code
            FROM workflow_node_relation r
            INNER JOIN upstream_nodes u ON r.post_node_code = u.pre_node_code
            WHERE r.workflow_id = #{workflowId}
        )
        SELECT DISTINCT pre_node_code
        FROM upstream_nodes
        WHERE pre_node_code IS NOT NULL
    </select>

    <select id="getDownstreamNodes" resultType="string">
        WITH RECURSIVE downstream_nodes AS (
            SELECT post_node_code
            FROM workflow_node_relation
            WHERE workflow_id = #{workflowId}
              AND pre_node_code = #{nodeCode}

            UNION ALL

            SELECT r.post_node_code
            FROM workflow_node_relation r
            INNER JOIN downstream_nodes d ON r.pre_node_code = d.post_node_code
            WHERE r.workflow_id = #{workflowId}
        )
        SELECT DISTINCT post_node_code
        FROM downstream_nodes
        WHERE post_node_code IS NOT NULL
    </select>

    <select id="checkCircularDependency" resultType="int">
        WITH RECURSIVE path_check AS (
            SELECT post_node_code as current_node, 1 as level
            FROM workflow_node_relation
            WHERE workflow_id = #{workflowId}
              AND pre_node_code = #{postNodeCode}

            UNION ALL

            SELECT r.post_node_code, p.level + 1
            FROM workflow_node_relation r
            INNER JOIN path_check p ON r.pre_node_code = p.current_node
            WHERE r.workflow_id = #{workflowId}
              AND p.level &lt; 100
        )
        SELECT COUNT(1)
        FROM path_check
        WHERE current_node = #{preNodeCode}
    </select>

</mapper>
