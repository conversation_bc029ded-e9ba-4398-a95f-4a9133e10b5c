<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" 
	"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sevb.admin.dao.AlertChannelConfigDao">

    <resultMap id="AlertChannelConfig" type="com.sevb.admin.core.model.AlertChannelConfig">
        <result column="id" property="id"/>
        <result column="config_name" property="configName"/>
        <result column="channel_type" property="channelType"/>
        <result column="auth_type" property="authType"/>
        <result column="auth_config" property="authConfig"/>
        <result column="channel_config" property="channelConfig"/>
        <result column="enabled" property="enabled"/>
        <result column="description" property="description"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="create_user" property="createUser"/>
        <result column="update_user" property="updateUser"/>
        <result column="deleted" property="deleted"/>
    </resultMap>

    <sql id="Base_Column_List">
        t.id,
        t.config_name,
        t.channel_type,
        t.auth_type,
        t.auth_config,
        t.channel_config,
        t.enabled,
        t.description,
        t.create_time,
        t.update_time,
        t.create_user,
        t.update_user,
        t.deleted
    </sql>

    <select id="pageList" resultMap="AlertChannelConfig">
        SELECT <include refid="Base_Column_List"/>
        FROM alert_channel_config AS t
        WHERE t.deleted = 0
        <if test="configName != null and configName.trim() != ''">
            AND t.config_name LIKE CONCAT('%', #{configName}, '%')
        </if>
        <if test="channelType != null and channelType.trim() != ''">
            AND t.channel_type = #{channelType}
        </if>
        <if test="enabled != null">
            AND t.enabled = #{enabled}
        </if>
        ORDER BY t.create_time DESC
        LIMIT #{offset}, #{pagesize}
    </select>

    <select id="pageListCount" resultType="int">
        SELECT COUNT(1)
        FROM alert_channel_config AS t
        WHERE t.deleted = 0
        <if test="configName != null and configName.trim() != ''">
            AND t.config_name LIKE CONCAT('%', #{configName}, '%')
        </if>
        <if test="channelType != null and channelType.trim() != ''">
            AND t.channel_type = #{channelType}
        </if>
        <if test="enabled != null">
            AND t.enabled = #{enabled}
        </if>
    </select>

    <select id="findAllEnabled" resultMap="AlertChannelConfig">
        SELECT <include refid="Base_Column_List"/>
        FROM alert_channel_config AS t
        WHERE t.deleted = 0 AND t.enabled = 1
        ORDER BY t.channel_type, t.config_name
    </select>

    <select id="load" parameterType="java.lang.Long" resultMap="AlertChannelConfig">
        SELECT <include refid="Base_Column_List"/>
        FROM alert_channel_config AS t
        WHERE t.id = #{id} AND t.deleted = 0
    </select>

    <select id="findByConfigName" resultMap="AlertChannelConfig">
        SELECT <include refid="Base_Column_List"/>
        FROM alert_channel_config AS t
        WHERE t.config_name = #{configName} AND t.deleted = 0
    </select>

    <select id="findByChannelType" resultMap="AlertChannelConfig">
        SELECT <include refid="Base_Column_List"/>
        FROM alert_channel_config AS t
        WHERE t.channel_type = #{channelType} AND t.deleted = 0
        ORDER BY t.config_name
    </select>

    <select id="findEnabledByChannelType" resultMap="AlertChannelConfig">
        SELECT <include refid="Base_Column_List"/>
        FROM alert_channel_config AS t
        WHERE t.channel_type = #{channelType} AND t.enabled = 1 AND t.deleted = 0
        ORDER BY t.config_name
    </select>

    <select id="findDefaultByChannelType" resultMap="AlertChannelConfig">
        SELECT <include refid="Base_Column_List"/>
        FROM alert_channel_config AS t
        WHERE t.channel_type = #{channelType} AND t.enabled = 1 AND t.deleted = 0
        ORDER BY t.create_time ASC
        LIMIT 1
    </select>

    <insert id="save" parameterType="com.sevb.admin.core.model.AlertChannelConfig" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO alert_channel_config (
            config_name, channel_type, auth_type, auth_config, channel_config,
            enabled, description, create_user
        ) VALUES (
            #{configName}, #{channelType}, #{authType}, #{authConfig}, #{channelConfig},
            #{enabled}, #{description}, #{createUser}
        )
    </insert>

    <update id="update" parameterType="com.sevb.admin.core.model.AlertChannelConfig">
        UPDATE alert_channel_config
        SET config_name = #{configName},
            auth_type = #{authType},
            auth_config = #{authConfig},
            channel_config = #{channelConfig},
            enabled = #{enabled},
            description = #{description},
            update_user = #{updateUser}
        WHERE id = #{id} AND deleted = 0
    </update>

    <update id="delete">
        UPDATE alert_channel_config
        SET deleted = 1, update_user = #{updateUser}
        WHERE id = #{id} AND deleted = 0
    </update>

    <update id="updateEnabled">
        UPDATE alert_channel_config
        SET enabled = #{enabled}, update_user = #{updateUser}
        WHERE id = #{id} AND deleted = 0
    </update>

    <update id="batchDelete">
        UPDATE alert_channel_config
        SET deleted = 1, update_user = #{updateUser}
        WHERE deleted = 0 AND id IN
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <select id="checkConfigNameExists" resultType="int">
        SELECT COUNT(1)
        FROM alert_channel_config
        WHERE config_name = #{configName}
        AND deleted = 0
        <if test="excludeId != null">
            AND id != #{excludeId}
        </if>
    </select>

</mapper>
