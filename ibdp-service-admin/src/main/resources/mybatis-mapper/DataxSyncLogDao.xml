<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" 
	"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sevb.admin.dao.DataxSyncLogDao">
	
	<resultMap id="DataxSyncLog" type="com.sevb.admin.core.model.DataxSyncLog">
		<result column="id" property="id" />
		<result column="sync_state_id" property="syncStateId" />
		<result column="execution_id" property="executionId" />
		<result column="start_time" property="startTime" />
		<result column="status" property="status" />
		<result column="error_message" property="errorMessage" />
		<result column="incremental_value_start" property="incrementalValueStart" />
		<result column="incremental_value_end" property="incrementalValueEnd" />
		<result column="create_time" property="createTime" />
	</resultMap>

	<sql id="Base_Column_List">
		t.id,
		t.sync_state_id,
		t.execution_id,
		t.start_time,
		t.status,
		t.error_message,
		t.incremental_value_start,
		t.incremental_value_end,
		t.create_time
	</sql>

	<select id="load" parameterType="java.lang.Long" resultMap="DataxSyncLog">
		SELECT <include refid="Base_Column_List" />
		FROM datax_sync_log AS t
		WHERE t.id = #{id}
	</select>

	<select id="findBySyncStateId" resultMap="DataxSyncLog">
		SELECT <include refid="Base_Column_List" />
		FROM datax_sync_log AS t
		WHERE t.sync_state_id = #{syncStateId}
		ORDER BY t.create_time DESC
	</select>

	<select id="findByExecutionId" resultMap="DataxSyncLog">
		SELECT <include refid="Base_Column_List" />
		FROM datax_sync_log AS t
		WHERE t.execution_id = #{executionId}
		LIMIT 1
	</select>

	<select id="findByJobLogId" resultMap="DataxSyncLog">
		SELECT <include refid="Base_Column_List" />
		FROM datax_sync_log AS t
		WHERE t.job_log_id = #{jobLogId}
		LIMIT 1
	</select>

	<select id="pageList" resultMap="DataxSyncLog">
		SELECT <include refid="Base_Column_List" />
		FROM datax_sync_log AS t
		<where>
			<if test="syncStateId != null">
				AND t.sync_state_id = #{syncStateId}
			</if>
			<if test="executionId != null and executionId.trim() != ''">
				AND t.execution_id LIKE CONCAT('%', #{executionId}, '%')
			</if>
			<if test="status != null and status.trim() != ''">
				AND t.status = #{status}
			</if>
			<if test="startTime != null">
				AND t.start_time &gt;= #{startTime}
			</if>
			<if test="endTime != null">
				AND t.start_time &lt;= #{endTime}
			</if>
		</where>
		ORDER BY t.create_time DESC
		LIMIT #{offset}, #{pagesize}
	</select>

	<select id="pageListCount" resultType="int">
		SELECT COUNT(1)
		FROM datax_sync_log AS t
		<where>
			<if test="syncStateId != null">
				AND t.sync_state_id = #{syncStateId}
			</if>
			<if test="executionId != null and executionId.trim() != ''">
				AND t.execution_id LIKE CONCAT('%', #{executionId}, '%')
			</if>
			<if test="status != null and status.trim() != ''">
				AND t.status = #{status}
			</if>
			<if test="startTime != null">
				AND t.start_time &gt;= #{startTime}
			</if>
			<if test="endTime != null">
				AND t.start_time &lt;= #{endTime}
			</if>
		</where>
	</select>

	<select id="findRunningLogs" resultMap="DataxSyncLog">
		SELECT <include refid="Base_Column_List" />
		FROM datax_sync_log AS t
		WHERE t.status = 'RUNNING'
		ORDER BY t.start_time ASC
	</select>

	<select id="findFailedLogs" resultMap="DataxSyncLog">
		SELECT <include refid="Base_Column_List" />
		FROM datax_sync_log AS t
		WHERE t.status = 'FAILED'
		ORDER BY t.create_time DESC
		LIMIT #{limit}
	</select>

	<select id="findRetryableLogs" resultMap="DataxSyncLog">
		SELECT <include refid="Base_Column_List" />
		FROM datax_sync_log AS t
		WHERE t.status = 'FAILED'
		ORDER BY t.create_time DESC
	</select>

	<select id="findRecentLogs" resultMap="DataxSyncLog">
		SELECT <include refid="Base_Column_List" />
		FROM datax_sync_log AS t
		ORDER BY t.create_time DESC
		LIMIT #{limit}
	</select>

	<select id="countByStatus" resultType="int">
		SELECT COUNT(1)
		FROM datax_sync_log AS t
		WHERE t.status = #{status}
	</select>

	<select id="countBySyncStateId" resultType="int">
		SELECT COUNT(1)
		FROM datax_sync_log AS t
		WHERE t.sync_state_id = #{syncStateId}
	</select>

	<select id="findByTimeRange" resultMap="DataxSyncLog">
		SELECT <include refid="Base_Column_List" />
		FROM datax_sync_log AS t
		WHERE t.start_time &gt;= #{startTime}
		  AND t.start_time &lt;= #{endTime}
		ORDER BY t.start_time DESC
	</select>

	<select id="getAverageDuration" resultType="java.lang.Double">
		SELECT 0.0 as avg_duration
		FROM datax_sync_log AS t
		WHERE t.sync_state_id = #{syncStateId}
		  AND t.status = 'SUCCESS'
		  <if test="days > 0">
		  	AND t.create_time &gt;= DATE_SUB(NOW(), INTERVAL #{days} DAY)
		  </if>
		LIMIT 1
	</select>

	<select id="getSuccessRate" resultType="java.lang.Double">
		SELECT 
			CASE 
				WHEN COUNT(1) = 0 THEN 0.0
				ELSE ROUND(SUM(CASE WHEN t.status = 'SUCCESS' THEN 1 ELSE 0 END) * 100.0 / COUNT(1), 2)
			END
		FROM datax_sync_log AS t
		WHERE t.sync_state_id = #{syncStateId}
		  <if test="days > 0">
		  	AND t.create_time &gt;= DATE_SUB(NOW(), INTERVAL #{days} DAY)
		  </if>
	</select>

	<select id="findLastSuccessLog" resultMap="DataxSyncLog">
		SELECT <include refid="Base_Column_List" />
		FROM datax_sync_log AS t
		WHERE t.sync_state_id = #{syncStateId}
		  AND t.status = 'SUCCESS'
		ORDER BY t.create_time DESC
		LIMIT 1
	</select>

	<select id="findLastFailedLog" resultMap="DataxSyncLog">
		SELECT <include refid="Base_Column_List" />
		FROM datax_sync_log AS t
		WHERE t.sync_state_id = #{syncStateId}
		  AND t.status = 'FAILED'
		ORDER BY t.create_time DESC
		LIMIT 1
	</select>

	<select id="findByNodeId" resultMap="DataxSyncLog">
		SELECT <include refid="Base_Column_List" />
		FROM datax_sync_log AS t
		INNER JOIN datax_sync_state AS s ON t.sync_state_id = s.id
		WHERE s.node_id = #{nodeId}
		ORDER BY t.create_time DESC
		LIMIT #{limit}
	</select>

	<select id="hasRunningTask" resultType="boolean">
		SELECT COUNT(1) > 0
		FROM datax_sync_log AS t
		WHERE t.sync_state_id = #{syncStateId}
		  AND t.status = 'RUNNING'
	</select>

	<select id="getTotalSyncRecords" resultType="java.lang.Long">
		SELECT 0
		FROM datax_sync_log AS t
		WHERE t.sync_state_id = #{syncStateId}
		  AND t.status = 'SUCCESS'
		LIMIT 1
	</select>

	<insert id="save" parameterType="com.sevb.admin.core.model.DataxSyncLog" useGeneratedKeys="true" keyProperty="id">
		INSERT INTO datax_sync_log (
			sync_state_id,
			execution_id,
			start_time,
			status,
			<if test="errorMessage != null">error_message,</if>
			<if test="incrementalValueStart != null">incremental_value_start,</if>
			<if test="incrementalValueEnd != null">incremental_value_end,</if>
			create_time
		) VALUES (
			#{syncStateId},
			#{executionId},
			#{startTime},
			#{status},
			<if test="errorMessage != null">#{errorMessage},</if>
			<if test="incrementalValueStart != null">#{incrementalValueStart},</if>
			<if test="incrementalValueEnd != null">#{incrementalValueEnd},</if>
			#{createTime}
		)
	</insert>

	<update id="update" parameterType="com.sevb.admin.core.model.DataxSyncLog">
		UPDATE datax_sync_log
		SET sync_state_id = #{syncStateId},
			execution_id = #{executionId},
			job_log_id = #{jobLogId},
			start_time = #{startTime},
			end_time = #{endTime},
			duration = #{duration},
			status = #{status},
			sync_records = #{syncRecords},
			error_message = #{errorMessage},
			error_code = #{errorCode},
			incremental_value_start = #{incrementalValueStart},
			incremental_value_end = #{incrementalValueEnd},
			datax_config = #{dataxConfig},
			performance_info = #{performanceInfo},
			retry_times = #{retryTimes}
		WHERE id = #{id}
	</update>

	<update id="updateStatus">
		UPDATE datax_sync_log
		SET status = #{status},
			end_time = #{endTime},
			duration = #{duration},
			sync_records = #{syncRecords},
			error_message = #{errorMessage},
			error_code = #{errorCode}
		WHERE id = #{id}
	</update>

	<update id="markSuccess">
		UPDATE datax_sync_log
		SET status = 'SUCCESS',
			end_time = #{endTime},
			duration = #{duration},
			sync_records = #{syncRecords},
			incremental_value_end = #{incrementalValueEnd},
			performance_info = #{performanceInfo}
		WHERE id = #{id}
	</update>

	<update id="markFailed">
		UPDATE datax_sync_log
		SET status = 'FAILED',
			end_time = #{endTime},
			duration = #{duration},
			error_message = #{errorMessage},
			error_code = #{errorCode}
		WHERE id = #{id}
	</update>

	<update id="markCancelled">
		UPDATE datax_sync_log
		SET status = 'CANCELLED',
			end_time = #{endTime},
			duration = #{duration}
		WHERE id = #{id}
	</update>

	<update id="incrementRetryTimes">
		UPDATE datax_sync_log
		SET retry_times = COALESCE(retry_times, 0) + 1
		WHERE id = #{id}
	</update>

	<delete id="delete">
		DELETE FROM datax_sync_log
		WHERE id = #{id}
	</delete>

	<delete id="batchDeleteByTime">
		DELETE FROM datax_sync_log
		WHERE create_time &lt; #{beforeTime}
		<if test="statuses != null and statuses.size() > 0">
			AND status IN
			<foreach collection="statuses" item="status" open="(" separator="," close=")">
				#{status}
			</foreach>
		</if>
	</delete>

	<delete id="cleanupHistoryLogs">
		DELETE FROM datax_sync_log
		WHERE create_time &lt; DATE_SUB(NOW(), INTERVAL #{days} DAY)
		  AND status IN ('SUCCESS', 'FAILED', 'CANCELLED')
	</delete>

</mapper>
