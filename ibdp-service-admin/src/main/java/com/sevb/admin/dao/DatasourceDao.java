package com.sevb.admin.dao;

import com.sevb.admin.core.model.Datasource;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 数据源DAO接口
 * <AUTHOR>
 */
@Mapper
public interface DatasourceDao {

    /**
     * 分页查询数据源列表
     */
    List<Datasource> pageList(@Param("offset") int offset,
                              @Param("pagesize") int pagesize,
                              @Param("name") String name,
                              @Param("type") String type,
                              @Param("status") Integer status);

    /**
     * 分页查询数据源总数
     */
    int pageListCount(@Param("offset") int offset,
                      @Param("pagesize") int pagesize,
                      @Param("name") String name,
                      @Param("type") String type,
                      @Param("status") Integer status);

    /**
     * 查询所有启用的数据源
     */
    List<Datasource> findAllEnabled();

    /**
     * 根据ID查询数据源
     */
    Datasource load(@Param("id") Long id);

    /**
     * 根据名称查询数据源
     */
    Datasource findByName(@Param("name") String name);

    /**
     * 保存数据源
     */
    int save(Datasource datasource);

    /**
     * 更新数据源
     */
    int update(Datasource datasource);

    /**
     * 删除数据源（逻辑删除）
     */
    int delete(@Param("id") Long id, @Param("updateUser") String updateUser);

    /**
     * 更新测试状态
     */
    int updateTestStatus(@Param("id") Long id,
                         @Param("testStatus") Integer testStatus,
                         @Param("testMessage") String testMessage,
                         @Param("updateUser") String updateUser);

    /**
     * 根据类型查询数据源列表
     */
    List<Datasource> findByType(@Param("type") String type);

    /**
     * 检查名称是否存在（排除指定ID）
     */
    int checkNameExists(@Param("name") String name, @Param("excludeId") Long excludeId);
}
