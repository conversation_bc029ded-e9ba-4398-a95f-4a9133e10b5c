package com.sevb.admin.dao;

import com.sevb.admin.core.model.JobUser;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import java.util.List;

@Mapper
public interface JobUserDao {

	List<JobUser> pageList(@Param("offset") int offset,
                                  @Param("pagesize") int pagesize,
                                  @Param("username") String username,
                                  @Param("role") int role);
	int pageListCount(@Param("offset") int offset,
							 @Param("pagesize") int pagesize,
							 @Param("username") String username,
							 @Param("role") int role);

	JobUser loadByUserName(@Param("username") String username);

	int save(JobUser jobUser);

	int update(JobUser jobUser);
	
	int delete(@Param("id") int id);

}
