package com.sevb.admin.dao;

import com.sevb.admin.core.model.AlertTemplate;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 告警模板DAO接口
 * <AUTHOR>
 */
@Mapper
public interface AlertTemplateDao {

    /**
     * 分页查询告警模板列表
     */
    List<AlertTemplate> pageList(@Param("offset") int offset,
                                @Param("pagesize") int pagesize,
                                @Param("templateCode") String templateCode,
                                @Param("templateName") String templateName,
                                @Param("channelType") String channelType,
                                @Param("enabled") Integer enabled);

    /**
     * 分页查询告警模板总数
     */
    int pageListCount(@Param("templateCode") String templateCode,
                     @Param("templateName") String templateName,
                     @Param("channelType") String channelType,
                     @Param("enabled") Integer enabled);

    /**
     * 查询所有启用的告警模板
     */
    List<AlertTemplate> findAllEnabled();

    /**
     * 根据ID查询告警模板
     */
    AlertTemplate load(@Param("id") Long id);

    /**
     * 根据模板编码和通道类型查询告警模板
     */
    AlertTemplate findByCodeAndChannel(@Param("templateCode") String templateCode,
                                      @Param("channelType") String channelType);

    /**
     * 根据模板编码查询所有通道的模板
     */
    List<AlertTemplate> findByTemplateCode(@Param("templateCode") String templateCode);

    /**
     * 根据通道类型查询模板列表
     */
    List<AlertTemplate> findByChannelType(@Param("channelType") String channelType);

    /**
     * 保存告警模板
     */
    int save(AlertTemplate alertTemplate);

    /**
     * 更新告警模板
     */
    int update(AlertTemplate alertTemplate);

    /**
     * 删除告警模板（软删除）
     */
    int delete(@Param("id") Long id, @Param("updateUser") String updateUser);

    /**
     * 启用/禁用告警模板
     */
    int updateEnabled(@Param("id") Long id, 
                     @Param("enabled") Integer enabled, 
                     @Param("updateUser") String updateUser);

    /**
     * 批量删除告警模板
     */
    int batchDelete(@Param("ids") List<Long> ids, @Param("updateUser") String updateUser);

    /**
     * 检查模板编码和通道类型组合是否存在
     */
    int checkCodeAndChannelExists(@Param("templateCode") String templateCode,
                                 @Param("channelType") String channelType,
                                 @Param("excludeId") Long excludeId);
}
