package com.sevb.admin.dao;

import com.sevb.admin.core.model.AlertSendRecord;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

/**
 * 告警发送记录DAO接口
 * <AUTHOR>
 */
@Mapper
public interface AlertSendRecordDao {

    /**
     * 分页查询发送记录列表
     */
    List<AlertSendRecord> pageList(@Param("offset") int offset,
                                  @Param("pagesize") int pagesize,
                                  @Param("requestId") String requestId,
                                  @Param("templateCode") String templateCode,
                                  @Param("channelType") String channelType,
                                  @Param("sendStatus") String sendStatus,
                                  @Param("sourceSystem") String sourceSystem,
                                  @Param("businessId") String businessId,
                                  @Param("startTime") Date startTime,
                                  @Param("endTime") Date endTime);

    /**
     * 分页查询发送记录总数
     */
    int pageListCount(@Param("requestId") String requestId,
                     @Param("templateCode") String templateCode,
                     @Param("channelType") String channelType,
                     @Param("sendStatus") String sendStatus,
                     @Param("sourceSystem") String sourceSystem,
                     @Param("businessId") String businessId,
                     @Param("startTime") Date startTime,
                     @Param("endTime") Date endTime);

    /**
     * 根据ID查询发送记录
     */
    AlertSendRecord load(@Param("id") Long id);

    /**
     * 根据请求ID查询发送记录列表
     */
    List<AlertSendRecord> findByRequestId(@Param("requestId") String requestId);

    /**
     * 根据业务ID查询发送记录列表
     */
    List<AlertSendRecord> findByBusinessId(@Param("businessId") String businessId);

    /**
     * 查询需要重试的记录
     */
    List<AlertSendRecord> findRetryRecords(@Param("currentTime") Date currentTime,
                                          @Param("limit") int limit);

    /**
     * 查询失败的记录
     */
    List<AlertSendRecord> findFailedRecords(@Param("startTime") Date startTime,
                                           @Param("endTime") Date endTime);

    /**
     * 保存发送记录
     */
    int save(AlertSendRecord alertSendRecord);

    /**
     * 更新发送记录
     */
    int update(AlertSendRecord alertSendRecord);

    /**
     * 更新发送状态
     */
    int updateSendStatus(@Param("id") Long id,
                        @Param("sendStatus") String sendStatus,
                        @Param("responseCode") String responseCode,
                        @Param("responseMessage") String responseMessage);

    /**
     * 更新重试信息
     */
    int updateRetryInfo(@Param("id") Long id,
                       @Param("retryCount") Integer retryCount,
                       @Param("nextRetryTime") Date nextRetryTime,
                       @Param("sendStatus") String sendStatus);

    /**
     * 批量删除发送记录
     */
    int batchDelete(@Param("ids") List<Long> ids);

    /**
     * 根据时间范围删除记录（清理历史数据）
     */
    int deleteByTimeRange(@Param("startTime") Date startTime,
                         @Param("endTime") Date endTime);

    /**
     * 统计发送记录数量
     */
    int countByStatus(@Param("sendStatus") String sendStatus,
                     @Param("startTime") Date startTime,
                     @Param("endTime") Date endTime);

    /**
     * 统计各状态的记录数量
     */
    List<AlertSendRecord> countGroupByStatus(@Param("startTime") Date startTime,
                                            @Param("endTime") Date endTime);

    /**
     * 统计各通道类型的记录数量
     */
    List<AlertSendRecord> countGroupByChannelType(@Param("startTime") Date startTime,
                                                 @Param("endTime") Date endTime);
}
