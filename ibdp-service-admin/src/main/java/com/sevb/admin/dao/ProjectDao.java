package com.sevb.admin.dao;

import com.sevb.admin.core.model.Project;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 项目数据访问接口
 * <AUTHOR>
 */
@Mapper
public interface ProjectDao {

    /**
     * 新增项目
     */
    int save(Project project);

    /**
     * 根据ID查询项目
     */
    Project load(@Param("id") Long id);

    /**
     * 根据编码查询项目
     */
    Project loadByCode(@Param("code") String code);

    /**
     * 更新项目
     */
    int update(Project project);

    /**
     * 删除项目（软删除）
     */
    int delete(@Param("id") Long id, @Param("updateUser") String updateUser);

    /**
     * 分页查询项目列表
     */
    List<Project> pageList(@Param("offset") int offset, 
                          @Param("pagesize") int pagesize,
                          @Param("name") String name,
                          @Param("type") String type,
                          @Param("status") Integer status,
                          @Param("userId") String userId);

    /**
     * 查询项目总数
     */
    int pageListCount(@Param("name") String name,
                     @Param("type") String type,
                     @Param("status") Integer status,
                     @Param("userId") String userId);

    /**
     * 查询用户可访问的项目列表
     */
    List<Project> findAccessibleProjects(@Param("userId") String userId);

    /**
     * 查询公共项目列表
     */
    List<Project> findPublicProjects();

    /**
     * 查询用户参与的项目列表
     */
    List<Project> findUserProjects(@Param("userId") String userId);

    /**
     * 检查项目编码是否存在
     */
    int checkCodeExists(@Param("code") String code, @Param("excludeId") Long excludeId);

    /**
     * 更新项目状态
     */
    int updateStatus(@Param("id") Long id, 
                    @Param("status") Integer status, 
                    @Param("updateUser") String updateUser);

    /**
     * 更新项目类型
     */
    int updateType(@Param("id") Long id, 
                  @Param("type") String type, 
                  @Param("updateUser") String updateUser);

    /**
     * 查询启用的项目列表
     */
    List<Project> findAllEnabled();

    /**
     * 根据类型查询项目列表
     */
    List<Project> findByType(@Param("type") String type);
}
