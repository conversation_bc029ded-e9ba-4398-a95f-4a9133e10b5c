package com.sevb.admin.dao;

import com.sevb.admin.core.model.NodeInstance;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

/**
 * 节点实例DAO
 * 
 * <AUTHOR>
 */
@Mapper
public interface NodeInstanceDao {

    /**
     * 保存节点实例
     */
    int save(NodeInstance nodeInstance);

    /**
     * 根据ID查询节点实例
     */
    NodeInstance load(@Param("id") Long id);

    /**
     * 更新节点实例
     */
    int update(NodeInstance nodeInstance);

    /**
     * 删除节点实例
     */
    int delete(@Param("id") Long id);

    /**
     * 分页查询节点实例
     */
    List<NodeInstance> pageList(@Param("offset") int offset,
                               @Param("pagesize") int pagesize,
                               @Param("projectId") Long projectId,
                               @Param("workflowInstanceId") Long workflowInstanceId,
                               @Param("workflowId") Long workflowId,
                               @Param("nodeId") Long nodeId,
                               @Param("nodeName") String nodeName,
                               @Param("nodeType") String nodeType,
                               @Param("status") Integer status,
                               @Param("startTime") Date startTime,
                               @Param("endTime") Date endTime);

    /**
     * 查询节点实例总数
     */
    int pageListCount(@Param("projectId") Long projectId,
                     @Param("workflowInstanceId") Long workflowInstanceId,
                     @Param("workflowId") Long workflowId,
                     @Param("nodeId") Long nodeId,
                     @Param("nodeName") String nodeName,
                     @Param("nodeType") String nodeType,
                     @Param("status") Integer status,
                     @Param("startTime") Date startTime,
                     @Param("endTime") Date endTime);

    /**
     * 根据工作流实例ID查询节点实例列表
     */
    List<NodeInstance> findByWorkflowInstanceId(@Param("workflowInstanceId") Long workflowInstanceId);

    /**
     * 根据工作流ID查询节点实例列表
     */
    List<NodeInstance> findByWorkflowId(@Param("workflowId") Long workflowId,
                                       @Param("limit") Integer limit);

    /**
     * 根据节点ID查询实例列表
     */
    List<NodeInstance> findByNodeId(@Param("nodeId") Long nodeId,
                                   @Param("limit") Integer limit);

    /**
     * 查询正在运行的节点实例
     */
    List<NodeInstance> findRunningInstances(@Param("workflowInstanceId") Long workflowInstanceId);

    /**
     * 批量保存节点实例
     */
    int batchSave(@Param("nodeInstances") List<NodeInstance> nodeInstances);

    /**
     * 批量更新节点实例状态
     */
    int batchUpdateStatus(@Param("ids") List<Long> ids,
                         @Param("status") Integer status,
                         @Param("updateTime") Date updateTime);

    /**
     * 统计节点实例状态
     */
    List<NodeInstance> getNodeStats(@Param("workflowId") Long workflowId,
                                   @Param("nodeId") Long nodeId,
                                   @Param("days") Integer days);

    /**
     * 清理历史节点实例数据
     */
    int cleanHistoryInstances(@Param("beforeDate") Date beforeDate);

    /**
     * 根据执行ID和节点编码查询节点实例
     */
    NodeInstance loadByExecutionAndNode(@Param("executionId") String executionId, @Param("nodeCode") String nodeCode);

    /**
     * 根据执行ID查询所有节点实例
     */
    List<NodeInstance> findByExecutionId(@Param("executionId") String executionId);
}
