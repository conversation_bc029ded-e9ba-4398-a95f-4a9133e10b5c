package com.sevb.admin.dao;

import com.sevb.admin.core.model.NodeType;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 节点类型DAO接口
 * <AUTHOR>
 */
@Mapper
public interface NodeTypeDao {

    /**
     * 分页查询节点类型列表
     */
    List<NodeType> pageList(@Param("offset") int offset,
                           @Param("pagesize") int pagesize,
                           @Param("typeName") String typeName,
                           @Param("category") String category,
                           @Param("status") Integer status);

    /**
     * 分页查询节点类型总数
     */
    int pageListCount(@Param("offset") int offset,
                     @Param("pagesize") int pagesize,
                     @Param("typeName") String typeName,
                     @Param("category") String category,
                     @Param("status") Integer status);

    /**
     * 查询所有启用的节点类型
     */
    List<NodeType> findAllEnabled();

    /**
     * 根据分类查询启用的节点类型
     */
    List<NodeType> findByCategory(@Param("category") String category);

    /**
     * 根据ID查询节点类型
     */
    NodeType load(@Param("id") Long id);

    /**
     * 根据类型编码查询节点类型
     */
    NodeType findByTypeCode(@Param("typeCode") String typeCode);

    /**
     * 保存节点类型
     */
    int save(NodeType nodeType);

    /**
     * 更新节点类型
     */
    int update(NodeType nodeType);

    /**
     * 删除节点类型（逻辑删除）
     */
    int delete(@Param("id") Long id);

    /**
     * 检查类型编码是否存在
     */
    int checkTypeCodeExists(@Param("typeCode") String typeCode, @Param("excludeId") Long excludeId);

    /**
     * 检查类型名称是否存在
     */
    int checkTypeNameExists(@Param("typeName") String typeName, @Param("excludeId") Long excludeId);
}
