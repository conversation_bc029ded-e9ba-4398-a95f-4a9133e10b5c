package com.sevb.admin.dao;

import com.sevb.admin.core.model.WorkflowInstance;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

/**
 * 工作流实例DAO
 * 
 * <AUTHOR>
 */
@Mapper
public interface WorkflowInstanceDao {

    /**
     * 保存工作流实例
     */
    int save(WorkflowInstance workflowInstance);

    /**
     * 根据ID查询工作流实例
     */
    WorkflowInstance load(@Param("id") Long id);

    /**
     * 更新工作流实例
     */
    int update(WorkflowInstance workflowInstance);

    /**
     * 删除工作流实例
     */
    int delete(@Param("id") Long id);

    /**
     * 分页查询工作流实例
     */
    List<WorkflowInstance> pageList(@Param("offset") int offset,
                                   @Param("pagesize") int pagesize,
                                   @Param("projectId") Long projectId,
                                   @Param("workflowId") Long workflowId,
                                   @Param("workflowName") String workflowName,
                                   @Param("status") Integer status,
                                   @Param("triggerType") String triggerType,
                                   @Param("startTime") Date startTime,
                                   @Param("endTime") Date endTime);

    /**
     * 查询工作流实例总数
     */
    int pageListCount(@Param("projectId") Long projectId,
                     @Param("workflowId") Long workflowId,
                     @Param("workflowName") String workflowName,
                     @Param("status") Integer status,
                     @Param("triggerType") String triggerType,
                     @Param("startTime") Date startTime,
                     @Param("endTime") Date endTime);

    /**
     * 根据工作流ID查询实例列表
     */
    List<WorkflowInstance> findByWorkflowId(@Param("workflowId") Long workflowId,
                                           @Param("limit") Integer limit);

    /**
     * 根据XXL-JOB日志ID查询实例
     */
    WorkflowInstance findByJobLogId(@Param("jobLogId") Long jobLogId);

    /**
     * 查询正在运行的实例
     */
    List<WorkflowInstance> findRunningInstances(@Param("workflowId") Long workflowId);

    /**
     * 统计工作流实例状态
     */
    List<WorkflowInstance> getInstanceStats(@Param("workflowId") Long workflowId,
                                           @Param("days") Integer days);

    /**
     * 清理历史实例数据
     */
    int cleanHistoryInstances(@Param("beforeDate") Date beforeDate,
                             @Param("keepCount") Integer keepCount);

    /**
     * 根据执行ID查询工作流实例
     */
    WorkflowInstance loadByExecutionId(@Param("executionId") String executionId);
}
