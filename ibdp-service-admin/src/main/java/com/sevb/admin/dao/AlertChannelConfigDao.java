package com.sevb.admin.dao;

import com.sevb.admin.core.model.AlertChannelConfig;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 告警通道配置DAO接口
 * <AUTHOR>
 */
@Mapper
public interface AlertChannelConfigDao {

    /**
     * 分页查询通道配置列表
     */
    List<AlertChannelConfig> pageList(@Param("offset") int offset,
                                     @Param("pagesize") int pagesize,
                                     @Param("configName") String configName,
                                     @Param("channelType") String channelType,
                                     @Param("enabled") Integer enabled);

    /**
     * 分页查询通道配置总数
     */
    int pageListCount(@Param("configName") String configName,
                     @Param("channelType") String channelType,
                     @Param("enabled") Integer enabled);

    /**
     * 查询所有启用的通道配置
     */
    List<AlertChannelConfig> findAllEnabled();

    /**
     * 根据ID查询通道配置
     */
    AlertChannelConfig load(@Param("id") Long id);

    /**
     * 根据配置名称查询通道配置
     */
    AlertChannelConfig findByConfigName(@Param("configName") String configName);

    /**
     * 根据通道类型查询配置列表
     */
    List<AlertChannelConfig> findByChannelType(@Param("channelType") String channelType);

    /**
     * 根据通道类型查询启用的配置列表
     */
    List<AlertChannelConfig> findEnabledByChannelType(@Param("channelType") String channelType);

    /**
     * 保存通道配置
     */
    int save(AlertChannelConfig alertChannelConfig);

    /**
     * 更新通道配置
     */
    int update(AlertChannelConfig alertChannelConfig);

    /**
     * 删除通道配置（软删除）
     */
    int delete(@Param("id") Long id, @Param("updateUser") String updateUser);

    /**
     * 启用/禁用通道配置
     */
    int updateEnabled(@Param("id") Long id, 
                     @Param("enabled") Integer enabled, 
                     @Param("updateUser") String updateUser);

    /**
     * 批量删除通道配置
     */
    int batchDelete(@Param("ids") List<Long> ids, @Param("updateUser") String updateUser);

    /**
     * 检查配置名称是否存在
     */
    int checkConfigNameExists(@Param("configName") String configName,
                             @Param("excludeId") Long excludeId);

    /**
     * 获取默认通道配置（每种类型的第一个启用配置）
     */
    AlertChannelConfig findDefaultByChannelType(@Param("channelType") String channelType);
}
