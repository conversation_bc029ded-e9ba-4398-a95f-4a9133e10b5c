package com.sevb.admin.dao;

import com.sevb.admin.core.model.WorkflowNode;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 工作流节点数据访问层
 */
@Mapper
public interface WorkflowNodeDao {
    
    /**
     * 根据工作流ID查询所有节点
     */
    List<WorkflowNode> listByWorkflow(@Param("workflowId") Long workflowId);
    
    /**
     * 分页查询工作流节点
     */
    List<WorkflowNode> pageList(@Param("offset") int offset, 
                               @Param("pageSize") int pageSize,
                               @Param("workflowId") Long workflowId,
                               @Param("nodeName") String nodeName,
                               @Param("nodeType") String nodeType);
    
    /**
     * 查询总数
     */
    int count(@Param("workflowId") Long workflowId,
              @Param("nodeName") String nodeName,
              @Param("nodeType") String nodeType);
    
    /**
     * 根据ID获取节点详情
     */
    WorkflowNode load(@Param("id") Long id);
    
    /**
     * 根据工作流ID和节点编码获取节点
     */
    WorkflowNode loadByCode(@Param("workflowId") Long workflowId,
                           @Param("nodeCode") String nodeCode);

    /**
     * 根据节点编码获取节点（全局查询）
     */
    WorkflowNode findByNodeCode(@Param("nodeCode") String nodeCode);



    /**
     * 添加工作流节点
     */
    int save(WorkflowNode workflowNode);
    
    /**
     * 批量添加工作流节点
     */
    int batchSave(@Param("nodes") List<WorkflowNode> nodes);
    
    /**
     * 更新工作流节点
     */
    int update(WorkflowNode workflowNode);
    
    /**
     * 批量更新工作流节点
     */
    int batchUpdate(@Param("nodes") List<WorkflowNode> nodes);
    
    /**
     * 删除工作流节点（逻辑删除）
     */
    int delete(@Param("id") Long id, @Param("updateUser") String updateUser);
    
    /**
     * 批量删除工作流节点（逻辑删除）
     */
    int batchDelete(@Param("ids") List<Long> ids, @Param("updateUser") String updateUser);
    
    /**
     * 根据工作流ID删除所有节点（逻辑删除）
     */
    int deleteByWorkflow(@Param("workflowId") Long workflowId, @Param("updateUser") String updateUser);
    
    /**
     * 物理删除节点（用于先删除再新增的场景）
     */
    int removeByWorkflow(@Param("workflowId") Long workflowId);
    
    /**
     * 更新节点位置
     */
    int updatePosition(@Param("id") Long id, 
                      @Param("positionX") Integer positionX, 
                      @Param("positionY") Integer positionY,
                      @Param("updateUser") String updateUser);
    
    /**
     * 批量更新节点位置
     */
    int batchUpdatePosition(@Param("updates") List<WorkflowNode> updates, 
                           @Param("updateUser") String updateUser);
    
    /**
     * 验证节点编码是否唯一
     */
    int checkNodeCodeUnique(@Param("workflowId") Long workflowId, 
                           @Param("nodeCode") String nodeCode, 
                           @Param("excludeId") Long excludeId);
    
    /**
     * 获取工作流中节点的最大排序号
     */
    Integer getMaxSortOrder(@Param("workflowId") Long workflowId);

    /**
     * 更新节点的JobId
     */
    int updateJobId(@Param("id") Long id, @Param("jobId") Long jobId);
}
