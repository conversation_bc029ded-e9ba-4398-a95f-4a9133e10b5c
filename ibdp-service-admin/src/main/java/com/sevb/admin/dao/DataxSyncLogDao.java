package com.sevb.admin.dao;

import com.sevb.admin.core.model.DataxSyncLog;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDateTime;
import java.util.List;

/**
 * DataX增量同步日志DAO接口
 * 
 * <AUTHOR>
 */
@Mapper
public interface DataxSyncLogDao {

    /**
     * 保存同步日志
     */
    int save(DataxSyncLog syncLog);

    /**
     * 根据ID查询同步日志
     */
    DataxSyncLog load(@Param("id") Long id);

    /**
     * 更新同步日志
     */
    int update(DataxSyncLog syncLog);

    /**
     * 删除同步日志
     */
    int delete(@Param("id") Long id);

    /**
     * 根据同步状态ID查询日志列表
     */
    List<DataxSyncLog> findBySyncStateId(@Param("syncStateId") Long syncStateId);

    /**
     * 根据执行ID查询日志
     */
    DataxSyncLog findByExecutionId(@Param("executionId") String executionId);

    /**
     * 根据XXL-JOB日志ID查询日志
     */
    DataxSyncLog findByJobLogId(@Param("jobLogId") Long jobLogId);

    /**
     * 分页查询同步日志列表
     */
    List<DataxSyncLog> pageList(@Param("offset") int offset,
                               @Param("pagesize") int pagesize,
                               @Param("syncStateId") Long syncStateId,
                               @Param("executionId") String executionId,
                               @Param("status") String status,
                               @Param("startTime") LocalDateTime startTime,
                               @Param("endTime") LocalDateTime endTime);

    /**
     * 分页查询同步日志总数
     */
    int pageListCount(@Param("offset") int offset,
                     @Param("pagesize") int pagesize,
                     @Param("syncStateId") Long syncStateId,
                     @Param("executionId") String executionId,
                     @Param("status") String status,
                     @Param("startTime") LocalDateTime startTime,
                     @Param("endTime") LocalDateTime endTime);

    /**
     * 查询正在运行的日志
     */
    List<DataxSyncLog> findRunningLogs();

    /**
     * 查询失败的日志
     */
    List<DataxSyncLog> findFailedLogs(@Param("limit") int limit);

    /**
     * 查询可重试的日志
     */
    List<DataxSyncLog> findRetryableLogs(@Param("maxRetryTimes") int maxRetryTimes);

    /**
     * 查询最近的日志
     */
    List<DataxSyncLog> findRecentLogs(@Param("limit") int limit);

    /**
     * 根据状态查询日志数量
     */
    int countByStatus(@Param("status") String status);

    /**
     * 根据同步状态ID查询日志数量
     */
    int countBySyncStateId(@Param("syncStateId") Long syncStateId);

    /**
     * 查询指定时间范围内的日志
     */
    List<DataxSyncLog> findByTimeRange(@Param("startTime") LocalDateTime startTime,
                                      @Param("endTime") LocalDateTime endTime);

    /**
     * 更新日志状态
     */
    int updateStatus(@Param("id") Long id,
                    @Param("status") String status,
                    @Param("endTime") LocalDateTime endTime,
                    @Param("duration") Long duration,
                    @Param("syncRecords") Long syncRecords,
                    @Param("errorMessage") String errorMessage,
                    @Param("errorCode") String errorCode);

    /**
     * 标记执行成功
     */
    int markSuccess(@Param("id") Long id,
                   @Param("endTime") LocalDateTime endTime,
                   @Param("duration") Long duration,
                   @Param("syncRecords") Long syncRecords,
                   @Param("incrementalValueEnd") String incrementalValueEnd,
                   @Param("performanceInfo") String performanceInfo);

    /**
     * 标记执行失败
     */
    int markFailed(@Param("id") Long id,
                  @Param("endTime") LocalDateTime endTime,
                  @Param("duration") Long duration,
                  @Param("errorMessage") String errorMessage,
                  @Param("errorCode") String errorCode);

    /**
     * 标记执行取消
     */
    int markCancelled(@Param("id") Long id,
                     @Param("endTime") LocalDateTime endTime,
                     @Param("duration") Long duration);

    /**
     * 增加重试次数
     */
    int incrementRetryTimes(@Param("id") Long id);

    /**
     * 批量删除历史日志
     */
    int batchDeleteByTime(@Param("beforeTime") LocalDateTime beforeTime,
                         @Param("statuses") List<String> statuses);

    /**
     * 清理历史日志
     */
    int cleanupHistoryLogs(@Param("days") int days);

    /**
     * 获取同步统计信息
     */
    List<DataxSyncLog> getSyncStatistics(@Param("startTime") LocalDateTime startTime,
                                        @Param("endTime") LocalDateTime endTime);

    /**
     * 获取性能统计信息
     */
    List<DataxSyncLog> getPerformanceStatistics(@Param("startTime") LocalDateTime startTime,
                                               @Param("endTime") LocalDateTime endTime);

    /**
     * 查询平均执行时长
     */
    Double getAverageDuration(@Param("syncStateId") Long syncStateId,
                             @Param("days") int days);

    /**
     * 查询成功率
     */
    Double getSuccessRate(@Param("syncStateId") Long syncStateId,
                         @Param("days") int days);

    /**
     * 查询最后一次成功的日志
     */
    DataxSyncLog findLastSuccessLog(@Param("syncStateId") Long syncStateId);

    /**
     * 查询最后一次失败的日志
     */
    DataxSyncLog findLastFailedLog(@Param("syncStateId") Long syncStateId);

    /**
     * 根据节点ID查询日志
     */
    List<DataxSyncLog> findByNodeId(@Param("nodeId") Long nodeId,
                                   @Param("limit") int limit);

    /**
     * 检查是否存在运行中的任务
     */
    boolean hasRunningTask(@Param("syncStateId") Long syncStateId);

    /**
     * 获取同步记录总数
     */
    Long getTotalSyncRecords(@Param("syncStateId") Long syncStateId);
}
