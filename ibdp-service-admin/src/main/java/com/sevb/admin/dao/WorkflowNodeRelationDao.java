package com.sevb.admin.dao;

import com.sevb.admin.core.model.WorkflowNodeRelation;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 工作流节点关系数据访问层
 */
@Mapper
public interface WorkflowNodeRelationDao {
    
    /**
     * 根据工作流ID查询所有节点关系
     */
    List<WorkflowNodeRelation> listByWorkflow(@Param("workflowId") Long workflowId);
    
    /**
     * 根据ID获取关系详情
     */
    WorkflowNodeRelation load(@Param("id") Long id);
    
    /**
     * 根据前置节点查询关系
     */
    List<WorkflowNodeRelation> listByPreNode(@Param("workflowId") Long workflowId, 
                                           @Param("preNodeCode") String preNodeCode);
    
    /**
     * 根据后置节点查询关系
     */
    List<WorkflowNodeRelation> listByPostNode(@Param("workflowId") Long workflowId, 
                                            @Param("postNodeCode") String postNodeCode);
    
    /**
     * 添加工作流节点关系
     */
    int save(WorkflowNodeRelation relation);
    
    /**
     * 批量添加工作流节点关系
     */
    int batchSave(@Param("relations") List<WorkflowNodeRelation> relations);
    
    /**
     * 更新工作流节点关系
     */
    int update(WorkflowNodeRelation relation);
    
    /**
     * 批量更新工作流节点关系
     */
    int batchUpdate(@Param("relations") List<WorkflowNodeRelation> relations);
    
    /**
     * 删除工作流节点关系（物理删除）
     */
    int delete(@Param("id") Long id);

    /**
     * 批量删除工作流节点关系（物理删除）
     */
    int batchDelete(@Param("ids") List<Long> ids);

    /**
     * 根据工作流ID删除所有关系（物理删除）
     */
    int deleteByWorkflow(@Param("workflowId") Long workflowId);

    /**
     * 根据节点删除相关关系（物理删除）
     */
    int deleteByNode(@Param("workflowId") Long workflowId,
                    @Param("nodeCode") String nodeCode);
    
    /**
     * 物理删除关系（用于先删除再新增的场景）
     */
    int removeByWorkflow(@Param("workflowId") Long workflowId);
    
    /**
     * 检查是否存在指定关系
     */
    int checkRelationExists(@Param("workflowId") Long workflowId, 
                           @Param("preNodeCode") String preNodeCode, 
                           @Param("postNodeCode") String postNodeCode);
    
    /**
     * 获取节点的所有上游依赖（递归查询）
     */
    List<String> getUpstreamNodes(@Param("workflowId") Long workflowId, 
                                 @Param("nodeCode") String nodeCode);
    
    /**
     * 获取节点的所有下游依赖（递归查询）
     */
    List<String> getDownstreamNodes(@Param("workflowId") Long workflowId, 
                                   @Param("nodeCode") String nodeCode);
    
    /**
     * 检查循环依赖
     * 检查从 preNodeCode 到 postNodeCode 是否会形成循环
     */
    int checkCircularDependency(@Param("workflowId") Long workflowId, 
                               @Param("preNodeCode") String preNodeCode, 
                               @Param("postNodeCode") String postNodeCode);
}
