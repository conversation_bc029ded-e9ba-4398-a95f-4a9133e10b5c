package com.sevb.admin.dao;

import com.sevb.admin.core.model.Workflow;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 工作流DAO接口
 * <AUTHOR>
 */
@Mapper
public interface WorkflowDao {

    /**
     * 分页查询工作流列表
     */
    List<Workflow> pageList(@Param("offset") int offset,
                           @Param("pagesize") int pagesize,
                           @Param("projectId") Long projectId,
                           @Param("name") String name,
                           @Param("category") String category,
                           @Param("status") Integer status);

    /**
     * 分页查询工作流总数
     */
    int pageListCount(@Param("offset") int offset,
                     @Param("pagesize") int pagesize,
                     @Param("projectId") Long projectId,
                     @Param("name") String name,
                     @Param("category") String category,
                     @Param("status") Integer status);

    /**
     * 根据项目ID查询所有工作流
     */
    List<Workflow> findByProjectId(@Param("projectId") Long projectId);

    /**
     * 根据项目ID和状态查询工作流
     */
    List<Workflow> findByProjectIdAndStatus(@Param("projectId") Long projectId, @Param("status") Integer status);

    /**
     * 根据ID查询工作流
     */
    Workflow load(@Param("id") Long id);

    /**
     * 根据项目ID和编码查询工作流
     */
    Workflow findByProjectIdAndCode(@Param("projectId") Long projectId, @Param("code") String code);

    /**
     * 保存工作流
     */
    int save(Workflow workflow);

    /**
     * 更新工作流
     */
    int update(Workflow workflow);

    /**
     * 删除工作流（逻辑删除）
     */
    int delete(@Param("id") Long id, @Param("updateUser") String updateUser);

    /**
     * 更新工作流状态
     */
    int updateStatus(@Param("id") Long id, @Param("status") Integer status, @Param("updateUser") String updateUser);

    /**
     * 更新工作流JobID和状态
     */
    int updateJobId(@Param("id") Long id, @Param("jobId") Integer jobId, @Param("status") Integer status, @Param("updateUser") String updateUser);

    /**
     * 检查工作流编码是否存在
     */
    int checkCodeExists(@Param("projectId") Long projectId, @Param("code") String code, @Param("excludeId") Long excludeId);

    /**
     * 检查工作流名称是否存在
     */
    int checkNameExists(@Param("projectId") Long projectId, @Param("name") String name, @Param("excludeId") Long excludeId);

    /**
     * 统计项目下的工作流数量
     */
    int countByProjectId(@Param("projectId") Long projectId);

    /**
     * 根据分类统计工作流数量
     */
    int countByCategory(@Param("projectId") Long projectId, @Param("category") String category);

    /**
     * 查询所有在线且使用调度中心编排的工作流
     */
    List<Workflow> findOnlineCentralWorkflows();

    /**
     * 更新工作流最后执行ID
     */
    int updateLastExecutionId(@Param("id") Long id, @Param("lastExecutionId") String lastExecutionId);

    /**
     * 更新工作流执行统计信息
     */
    int updateExecutionStats(@Param("id") Long id,
                           @Param("executionCount") Long executionCount,
                           @Param("successCount") Long successCount,
                           @Param("failureCount") Long failureCount);
}
