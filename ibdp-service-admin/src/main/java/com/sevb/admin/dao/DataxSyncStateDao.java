package com.sevb.admin.dao;

import com.sevb.admin.core.model.DataxSyncState;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDateTime;
import java.util.List;

/**
 * DataX增量同步状态DAO接口
 * 
 * <AUTHOR>
 */
@Mapper
public interface DataxSyncStateDao {

    /**
     * 保存同步状态
     */
    int save(DataxSyncState syncState);

    /**
     * 根据ID查询同步状态
     */
    DataxSyncState load(@Param("id") Long id);

    /**
     * 更新同步状态
     */
    int update(DataxSyncState syncState);

    /**
     * 删除同步状态
     */
    int delete(@Param("id") Long id);

    /**
     * 根据节点ID和表名查询同步状态
     */
    DataxSyncState findByNodeAndTables(@Param("nodeId") Long nodeId, 
                                      @Param("sourceTable") String sourceTable, 
                                      @Param("targetTable") String targetTable);

    /**
     * 根据节点ID查询同步状态
     */
    DataxSyncState findByNodeId(@Param("nodeId") Long nodeId);

    /**
     * 根据工作流ID查询同步状态列表
     */
    List<DataxSyncState> findByWorkflowId(@Param("workflowId") Long workflowId);

    /**
     * 分页查询同步状态列表
     */
    List<DataxSyncState> pageList(@Param("offset") int offset,
                                 @Param("pagesize") int pagesize,
                                 @Param("workflowId") Long workflowId,
                                 @Param("nodeId") Long nodeId,
                                 @Param("sourceTable") String sourceTable,
                                 @Param("targetTable") String targetTable,
                                 @Param("status") String status,
                                 @Param("incrementalType") String incrementalType);

    /**
     * 分页查询同步状态总数
     */
    int pageListCount(@Param("offset") int offset,
                     @Param("pagesize") int pagesize,
                     @Param("workflowId") Long workflowId,
                     @Param("nodeId") Long nodeId,
                     @Param("sourceTable") String sourceTable,
                     @Param("targetTable") String targetTable,
                     @Param("status") String status,
                     @Param("incrementalType") String incrementalType);

    /**
     * 查询所有活跃的同步状态
     */
    List<DataxSyncState> findAllActive();

    /**
     * 查询需要同步的状态（长时间未同步）
     */
    List<DataxSyncState> findNeedSync(@Param("hours") int hours);

    /**
     * 查询错误状态的同步任务
     */
    List<DataxSyncState> findErrorStates();

    /**
     * 批量更新状态
     */
    int batchUpdateStatus(@Param("ids") List<Long> ids, 
                         @Param("status") String status, 
                         @Param("updateUser") String updateUser);

    /**
     * 更新最后同步值和时间
     */
    int updateLastSyncValue(@Param("id") Long id,
                           @Param("lastSyncValue") String lastSyncValue,
                           @Param("lastSyncTime") LocalDateTime lastSyncTime,
                           @Param("syncCount") Long syncCount,
                           @Param("totalRecords") Long totalRecords,
                           @Param("updateUser") String updateUser);

    /**
     * 设置错误状态
     */
    int setErrorStatus(@Param("id") Long id,
                      @Param("errorMessage") String errorMessage,
                      @Param("updateUser") String updateUser);

    /**
     * 重置为活跃状态
     */
    int resetToActive(@Param("id") Long id,
                     @Param("updateUser") String updateUser);

    /**
     * 根据数据源ID查询同步状态
     */
    List<DataxSyncState> findByDatasourceId(@Param("datasourceId") Long datasourceId);

    /**
     * 统计同步状态数量
     */
    int countByStatus(@Param("status") String status);

    /**
     * 统计工作流的同步状态数量
     */
    int countByWorkflowId(@Param("workflowId") Long workflowId);

    /**
     * 查询最近同步的状态
     */
    List<DataxSyncState> findRecentSynced(@Param("limit") int limit);

    /**
     * 清理非活跃状态（软删除相关节点的状态）
     */
    int cleanupInactiveStates(@Param("days") int days);

    /**
     * 检查是否存在重复的同步状态
     */
    int checkDuplicate(@Param("nodeId") Long nodeId,
                      @Param("sourceTable") String sourceTable,
                      @Param("targetTable") String targetTable,
                      @Param("excludeId") Long excludeId);

    /**
     * 获取同步状态统计信息
     */
    List<DataxSyncState> getSyncStatistics();

    /**
     * 根据增量类型查询同步状态
     */
    List<DataxSyncState> findByIncrementalType(@Param("incrementalType") String incrementalType);

    /**
     * 更新配置快照
     */
    int updateConfigSnapshot(@Param("id") Long id,
                           @Param("configSnapshot") String configSnapshot,
                           @Param("updateUser") String updateUser);
}
