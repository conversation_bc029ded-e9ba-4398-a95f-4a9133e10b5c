package com.sevb.admin.dao;

import com.sevb.admin.core.model.DataxIncrementalConfig;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * DataX增量同步配置DAO接口
 * 
 * <AUTHOR>
 */
@Mapper
public interface DataxIncrementalConfigDao {

    /**
     * 保存增量配置
     */
    int save(DataxIncrementalConfig config);

    /**
     * 根据ID查询增量配置
     */
    DataxIncrementalConfig load(@Param("id") Long id);

    /**
     * 更新增量配置
     */
    int update(DataxIncrementalConfig config);

    /**
     * 删除增量配置
     */
    int delete(@Param("id") Long id);

    /**
     * 根据节点ID查询增量配置
     */
    DataxIncrementalConfig findByNodeId(@Param("nodeId") Long nodeId);

    /**
     * 查询所有启用的增量配置
     */
    List<DataxIncrementalConfig> findAllEnabled();

    /**
     * 分页查询增量配置列表
     */
    List<DataxIncrementalConfig> pageList(@Param("offset") int offset,
                                         @Param("pagesize") int pagesize,
                                         @Param("nodeId") Long nodeId,
                                         @Param("incrementalType") String incrementalType,
                                         @Param("enabled") Integer enabled);

    /**
     * 分页查询增量配置总数
     */
    int pageListCount(@Param("offset") int offset,
                     @Param("pagesize") int pagesize,
                     @Param("nodeId") Long nodeId,
                     @Param("incrementalType") String incrementalType,
                     @Param("enabled") Integer enabled);

    /**
     * 根据增量类型查询配置
     */
    List<DataxIncrementalConfig> findByIncrementalType(@Param("incrementalType") String incrementalType);

    /**
     * 批量更新启用状态
     */
    int batchUpdateEnabled(@Param("ids") List<Long> ids,
                          @Param("enabled") Integer enabled,
                          @Param("updateUser") String updateUser);

    /**
     * 更新启用状态
     */
    int updateEnabled(@Param("id") Long id,
                     @Param("enabled") Integer enabled,
                     @Param("updateUser") String updateUser);

    /**
     * 检查节点是否已存在配置
     */
    boolean existsByNodeId(@Param("nodeId") Long nodeId);

    /**
     * 根据节点ID列表查询配置
     */
    List<DataxIncrementalConfig> findByNodeIds(@Param("nodeIds") List<Long> nodeIds);

    /**
     * 统计启用的配置数量
     */
    int countEnabled();

    /**
     * 统计总配置数量
     */
    int countTotal();

    /**
     * 根据增量字段查询配置
     */
    List<DataxIncrementalConfig> findByIncrementalColumn(@Param("incrementalColumn") String incrementalColumn);

    /**
     * 保存或更新配置（存在则更新，不存在则插入）
     */
    int saveOrUpdate(DataxIncrementalConfig config);

    /**
     * 批量删除配置
     */
    int batchDelete(@Param("ids") List<Long> ids);

    /**
     * 复制配置
     */
    int copyConfig(@Param("sourceNodeId") Long sourceNodeId,
                  @Param("targetNodeId") Long targetNodeId,
                  @Param("createUser") String createUser);

    /**
     * 获取默认配置模板
     */
    DataxIncrementalConfig getDefaultTemplate();

    /**
     * 验证配置有效性
     */
    boolean validateConfig(@Param("nodeId") Long nodeId,
                          @Param("incrementalColumn") String incrementalColumn,
                          @Param("incrementalType") String incrementalType);
}
