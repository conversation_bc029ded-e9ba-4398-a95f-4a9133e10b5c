package com.sevb.admin.dao;

import com.sevb.admin.core.model.ProjectMember;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 项目成员数据访问接口
 * <AUTHOR>
 */
@Mapper
public interface ProjectMemberDao {

    /**
     * 新增项目成员
     */
    int save(ProjectMember member);

    /**
     * 根据ID查询项目成员
     */
    ProjectMember load(@Param("id") Long id);

    /**
     * 根据项目ID和用户ID查询成员
     */
    ProjectMember loadByProjectAndUser(@Param("projectId") Long projectId, @Param("userId") String userId);

    /**
     * 更新项目成员
     */
    int update(ProjectMember member);

    /**
     * 删除项目成员（软删除）
     */
    int delete(@Param("id") Long id, @Param("updateUser") String updateUser);

    /**
     * 根据项目ID和用户ID删除成员
     */
    int deleteByProjectAndUser(@Param("projectId") Long projectId, 
                              @Param("userId") String userId, 
                              @Param("updateUser") String updateUser);

    /**
     * 查询项目成员列表
     */
    List<ProjectMember> findByProjectId(@Param("projectId") Long projectId);

    /**
     * 查询用户参与的项目成员记录
     */
    List<ProjectMember> findByUserId(@Param("userId") String userId);

    /**
     * 分页查询项目成员列表
     */
    List<ProjectMember> pageList(@Param("offset") int offset,
                                @Param("pagesize") int pagesize,
                                @Param("projectId") Long projectId,
                                @Param("username") String username,
                                @Param("role") String role,
                                @Param("status") Integer status);

    /**
     * 查询项目成员总数
     */
    int pageListCount(@Param("projectId") Long projectId,
                     @Param("username") String username,
                     @Param("role") String role,
                     @Param("status") Integer status);

    /**
     * 检查用户是否为项目成员
     */
    int checkMemberExists(@Param("projectId") Long projectId, @Param("userId") String userId);

    /**
     * 更新成员角色
     */
    int updateRole(@Param("id") Long id, 
                  @Param("role") String role, 
                  @Param("updateUser") String updateUser);

    /**
     * 更新成员状态
     */
    int updateStatus(@Param("id") Long id, 
                    @Param("status") Integer status, 
                    @Param("updateUser") String updateUser);

    /**
     * 查询项目管理员列表
     */
    List<ProjectMember> findAdminsByProjectId(@Param("projectId") Long projectId);

    /**
     * 统计项目成员数量
     */
    int countByProjectId(@Param("projectId") Long projectId);

    /**
     * 根据项目ID删除所有成员（项目删除时使用）
     */
    int deleteByProjectId(@Param("projectId") Long projectId, @Param("updateUser") String updateUser);

    /**
     * 批量添加项目成员
     */
    int batchSave(@Param("members") List<ProjectMember> members);
}
