package com.sevb.admin.service;

import com.sevb.admin.core.model.WorkflowNode;
import com.sevb.admin.core.model.WorkflowNodeRelation;
import com.sevb.admin.dao.WorkflowNodeDao;
import com.sevb.admin.dao.WorkflowNodeRelationDao;
import com.sevb.admin.dto.WorkflowDesignDTO;
import com.sevb.admin.dto.WorkflowDesignIncrementalDTO;
import com.sevb.admin.service.workflow.WorkflowJobService;
import com.sevb.core.biz.model.ReturnT;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import jakarta.annotation.Resource;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 工作流设计器综合服务层
 */
@Slf4j
@Service
public class WorkflowDesignerService {

    @Resource
    private WorkflowNodeDao workflowNodeDao;

    @Resource
    private WorkflowNodeRelationDao workflowNodeRelationDao;

    @Resource
    private WorkflowJobService workflowJobService;

    @Resource
    private WorkflowNodeService workflowNodeService;

    @Resource
    private WorkflowNodeRelationService workflowNodeRelationService;

    /**
     * 加载工作流设计数据（节点+关系）
     */
    public ReturnT<WorkflowDesignDTO> loadWorkflowDesign(Long workflowId) {
        try {
            if (workflowId == null) {
                return new ReturnT<>(ReturnT.FAIL_CODE, "工作流ID不能为空");
            }

            // 查询节点
            List<WorkflowNode> nodes = workflowNodeDao.listByWorkflow(workflowId);

            // 查询关系
            List<WorkflowNodeRelation> relations = workflowNodeRelationDao.listByWorkflow(workflowId);

            // 构造返回数据
            WorkflowDesignDTO designData = new WorkflowDesignDTO();
            designData.setWorkflowId(workflowId);
            designData.setNodes(nodes);
            designData.setRelations(relations);

            log.info("加载工作流设计数据成功，工作流ID：{}，节点数：{}，关系数：{}",
                    workflowId, nodes.size(), relations.size());

            return new ReturnT<>(designData);
        } catch (Exception e) {
            log.error("加载工作流设计数据失败", e);
            return new ReturnT<>(ReturnT.FAIL_CODE, "加载失败：" + e.getMessage());
        }
    }

    /**
     * 保存工作流设计数据（节点+关系）
     * 使用事务确保数据一致性
     */
    @Transactional(rollbackFor = Exception.class)
    public ReturnT<String> saveWorkflowDesign(WorkflowDesignDTO designData, String loginUser) {
        try {
            if (designData == null || designData.getWorkflowId() == null) {
                return new ReturnT<>(ReturnT.FAIL_CODE, "设计数据不能为空");
            }

            Long workflowId = designData.getWorkflowId();

            // 先删除现有数据（物理删除）
            workflowNodeRelationDao.removeByWorkflow(workflowId);
            workflowNodeDao.removeByWorkflow(workflowId);

            // 保存节点
            if (designData.getNodes() != null && !designData.getNodes().isEmpty()) {
                // 设置默认值
                for (WorkflowNode node : designData.getNodes()) {
                    setNodeDefaultValues(node, loginUser);
                }
                int nodeResult = workflowNodeDao.batchSave(designData.getNodes());
                log.info("保存工作流节点成功，数量：{}", nodeResult);
            }

            // 保存关系
            if (designData.getRelations() != null && !designData.getRelations().isEmpty()) {
                // 设置默认值
                for (WorkflowNodeRelation relation : designData.getRelations()) {
                    setRelationDefaultValues(relation, loginUser);
                }
                int relationResult = workflowNodeRelationDao.batchSave(designData.getRelations());
                log.info("保存工作流关系成功，数量：{}", relationResult);
            }

            log.info("保存工作流设计数据成功，工作流ID：{}", workflowId);
            return ReturnT.SUCCESS;
        } catch (Exception e) {
            log.error("保存工作流设计数据失败", e);
            return new ReturnT<>(ReturnT.FAIL_CODE, "保存失败：" + e.getMessage());
        }
    }

    /**
     * 增量保存工作流设计数据
     * 只保存有变更的数据
     */
    @Transactional(rollbackFor = Exception.class)
    public ReturnT<String> saveWorkflowDesignIncremental(WorkflowDesignIncrementalDTO incrementalData, String loginUser) {
        try {
            if (incrementalData == null || incrementalData.getWorkflowId() == null) {
                return new ReturnT<>(ReturnT.FAIL_CODE, "增量数据不能为空");
            }

            Long workflowId = incrementalData.getWorkflowId();

            // 处理节点变更
            processNodeChanges(incrementalData, loginUser);

            // 处理关系变更
            processRelationChanges(incrementalData, loginUser);

            log.info("增量保存工作流设计数据成功，工作流ID：{}", workflowId);
            return ReturnT.SUCCESS;
        } catch (Exception e) {
            log.error("增量保存工作流设计数据失败", e);
            return new ReturnT<>(ReturnT.FAIL_CODE, "增量保存失败：" + e.getMessage());
        }
    }

    /**
     * 验证工作流设计
     */
    public ReturnT<Map<String, Object>> validateWorkflowDesign(Long workflowId) {
        try {
            if (workflowId == null) {
                return new ReturnT<>(ReturnT.FAIL_CODE, "工作流ID不能为空");
            }

            Map<String, Object> result = new HashMap<>();

            // 查询节点和关系
            List<WorkflowNode> nodes = workflowNodeDao.listByWorkflow(workflowId);
            List<WorkflowNodeRelation> relations = workflowNodeRelationDao.listByWorkflow(workflowId);

            // 验证逻辑
            boolean isValid = true;
            StringBuilder errorMsg = new StringBuilder();

            // 检查是否有节点
            if (nodes.isEmpty()) {
                isValid = false;
                errorMsg.append("工作流中没有节点；");
            }

            // 检查是否有孤立节点（没有任何连接的节点）
            // TODO: 添加更多验证逻辑

            result.put("isValid", isValid);
            result.put("errorMsg", errorMsg.toString());
            result.put("nodeCount", nodes.size());
            result.put("relationCount", relations.size());

            return new ReturnT<>(result);
        } catch (Exception e) {
            log.error("验证工作流设计失败", e);
            return new ReturnT<>(ReturnT.FAIL_CODE, "验证失败：" + e.getMessage());
        }
    }

    /**
     * 获取工作流设计统计信息
     */
    public ReturnT<Map<String, Object>> getWorkflowDesignStats(Long workflowId) {
        try {
            if (workflowId == null) {
                return new ReturnT<>(ReturnT.FAIL_CODE, "工作流ID不能为空");
            }

            // 查询节点和关系
            List<WorkflowNode> nodes = workflowNodeDao.listByWorkflow(workflowId);
            List<WorkflowNodeRelation> relations = workflowNodeRelationDao.listByWorkflow(workflowId);

            Map<String, Object> stats = new HashMap<>();
            stats.put("nodeCount", nodes.size());
            stats.put("relationCount", relations.size());

            // 统计节点类型分布
            Map<String, Integer> nodeTypeStats = new HashMap<>();
            for (WorkflowNode node : nodes) {
                nodeTypeStats.merge(node.getNodeType(), 1, Integer::sum);
            }
            stats.put("nodeTypeStats", nodeTypeStats);

            // 统计关系类型分布
            Map<String, Integer> relationTypeStats = new HashMap<>();
            for (WorkflowNodeRelation relation : relations) {
                relationTypeStats.merge(relation.getConditionType(), 1, Integer::sum);
            }
            stats.put("relationTypeStats", relationTypeStats);

            return new ReturnT<>(stats);
        } catch (Exception e) {
            log.error("获取工作流设计统计信息失败", e);
            return new ReturnT<>(ReturnT.FAIL_CODE, "获取统计信息失败：" + e.getMessage());
        }
    }

    /**
     * 清空工作流设计
     */
    @Transactional(rollbackFor = Exception.class)
    public ReturnT<String> clearWorkflowDesign(Long workflowId, String loginUser) {
        try {
            if (workflowId == null) {
                return new ReturnT<>(ReturnT.FAIL_CODE, "工作流ID不能为空");
            }

            // 物理删除所有关系和节点
            workflowNodeRelationDao.deleteByWorkflow(workflowId);
            workflowNodeDao.deleteByWorkflow(workflowId, loginUser);

            log.info("清空工作流设计成功，工作流ID：{}", workflowId);
            return ReturnT.SUCCESS;
        } catch (Exception e) {
            log.error("清空工作流设计失败", e);
            return new ReturnT<>(ReturnT.FAIL_CODE, "清空失败：" + e.getMessage());
        }
    }

    /**
     * 处理节点变更
     */
    private void processNodeChanges(WorkflowDesignIncrementalDTO incrementalData, String loginUser) {
        // 删除节点
        if (incrementalData.getDeletedNodeIds() != null && !incrementalData.getDeletedNodeIds().isEmpty()) {
            workflowNodeDao.batchDelete(incrementalData.getDeletedNodeIds(), loginUser);
            log.info("删除节点数量：{}", incrementalData.getDeletedNodeIds().size());
        }

        // 新增节点
        if (incrementalData.getAddedNodes() != null && !incrementalData.getAddedNodes().isEmpty()) {
            for (WorkflowNode node : incrementalData.getAddedNodes()) {
                setNodeDefaultValues(node, loginUser);
            }
            workflowNodeDao.batchSave(incrementalData.getAddedNodes());
            log.info("新增节点数量：{}", incrementalData.getAddedNodes().size());

            // 为新增节点创建XXL-JOB任务
            createJobsForNodes(incrementalData.getAddedNodes(), incrementalData.getWorkflowId());
        }

        // 更新节点
        if (incrementalData.getUpdatedNodes() != null && !incrementalData.getUpdatedNodes().isEmpty()) {
            for (WorkflowNode node : incrementalData.getUpdatedNodes()) {
                node.setUpdateTime(LocalDateTime.now());
                node.setUpdateUser(loginUser);
                workflowNodeDao.update(node);
            }
            log.info("更新节点数量：{}", incrementalData.getUpdatedNodes().size());

            // 更新节点对应的XXL-JOB任务
            updateJobsForNodes(incrementalData.getUpdatedNodes(), incrementalData.getWorkflowId());
        }
    }

    /**
     * 处理关系变更
     */
    private void processRelationChanges(WorkflowDesignIncrementalDTO incrementalData, String loginUser) {
        // 删除关系
        if (incrementalData.getDeletedRelationIds() != null && !incrementalData.getDeletedRelationIds().isEmpty()) {
            workflowNodeRelationDao.batchDelete(incrementalData.getDeletedRelationIds());
            log.info("删除关系数量：{}", incrementalData.getDeletedRelationIds().size());
        }

        // 新增关系
        if (incrementalData.getAddedRelations() != null && !incrementalData.getAddedRelations().isEmpty()) {
            for (WorkflowNodeRelation relation : incrementalData.getAddedRelations()) {
                setRelationDefaultValues(relation, loginUser);
            }
            workflowNodeRelationDao.batchSave(incrementalData.getAddedRelations());
            log.info("新增关系数量：{}", incrementalData.getAddedRelations().size());
        }

        // 更新关系
        if (incrementalData.getUpdatedRelations() != null && !incrementalData.getUpdatedRelations().isEmpty()) {
            for (WorkflowNodeRelation relation : incrementalData.getUpdatedRelations()) {
                workflowNodeRelationDao.update(relation);
            }
            log.info("更新关系数量：{}", incrementalData.getUpdatedRelations().size());
        }
    }

    /**
     * 设置节点默认值
     */
    private void setNodeDefaultValues(WorkflowNode node, String loginUser) {
        LocalDateTime now = LocalDateTime.now();

        if (node.getPositionX() == null) node.setPositionX(0);
        if (node.getPositionY() == null) node.setPositionY(0);
        if (node.getTimeout() == null) node.setTimeout(0);
        if (node.getRetryTimes() == null) node.setRetryTimes(0);
        if (node.getRetryInterval() == null) node.setRetryInterval(1);
        if (node.getMaxRetryTimes() == null) node.setMaxRetryTimes(3);
        if (node.getFailureStrategy() == null) node.setFailureStrategy("FAIL");
        if (node.getPriority() == null) node.setPriority(2);
        if (node.getWorkerGroup() == null) node.setWorkerGroup("default");
        if (node.getDeleted() == null) node.setDeleted(0);

        if (node.getId() == null) {
            node.setCreateTime(now);
            node.setCreateUser(loginUser);
        }
        node.setUpdateTime(now);
        node.setUpdateUser(loginUser);
    }

    /**
     * 设置关系默认值
     */
    private void setRelationDefaultValues(WorkflowNodeRelation relation, String loginUser) {
        if (relation.getConditionType() == null) relation.setConditionType("NONE");
    }

    /**
     * 为新增节点创建XXL-JOB任务
     */
    private void createJobsForNodes(List<WorkflowNode> nodes, Long workflowId) {
        if (nodes == null || nodes.isEmpty()) {
            return;
        }

        try {
            log.info("开始为工作流{}的{}个新增节点创建XXL-JOB任务", workflowId, nodes.size());

            for (WorkflowNode node : nodes) {
                // 调用WorkflowJobService为单个节点创建任务
                ReturnT<String> result = workflowJobService.createJobForNode(node);

                if (result.getCode() == ReturnT.SUCCESS_CODE) {
                    log.info("为节点{}创建XXL-JOB任务成功", node.getNodeCode());
                } else {
                    log.warn("为节点{}创建XXL-JOB任务失败: {}", node.getNodeCode(), result.getMsg());
                }
            }

            log.info("完成为工作流{}的节点创建XXL-JOB任务", workflowId);

        } catch (Exception e) {
            log.error("为工作流{}的节点创建XXL-JOB任务失败", workflowId, e);
            // 不抛出异常，避免影响节点保存
        }
    }

    /**
     * 更新节点对应的XXL-JOB任务
     */
    private void updateJobsForNodes(List<WorkflowNode> nodes, Long workflowId) {
        if (nodes == null || nodes.isEmpty()) {
            return;
        }

        try {
            log.info("开始为工作流{}的{}个更新节点同步XXL-JOB任务", workflowId, nodes.size());

            for (WorkflowNode node : nodes) {
                // 如果节点已有JobId，更新任务；否则创建新任务
                if (node.getJobId() != null && node.getJobId() > 0) {
                    ReturnT<String> result = workflowJobService.updateJobForNode(node);

                    if (result.getCode() == ReturnT.SUCCESS_CODE) {
                        log.info("更新节点{}的XXL-JOB任务成功", node.getNodeCode());
                    } else {
                        log.warn("更新节点{}的XXL-JOB任务失败: {}", node.getNodeCode(), result.getMsg());
                    }
                } else {
                    // 节点没有JobId，创建新任务
                    ReturnT<String> result = workflowJobService.createJobForNode(node);

                    if (result.getCode() == ReturnT.SUCCESS_CODE) {
                        log.info("为更新节点{}创建XXL-JOB任务成功", node.getNodeCode());
                    } else {
                        log.warn("为更新节点{}创建XXL-JOB任务失败: {}", node.getNodeCode(), result.getMsg());
                    }
                }
            }

            log.info("完成为工作流{}的节点同步XXL-JOB任务", workflowId);

        } catch (Exception e) {
            log.error("为工作流{}的节点同步XXL-JOB任务失败", workflowId, e);
            // 不抛出异常，避免影响节点更新
        }
    }
}
