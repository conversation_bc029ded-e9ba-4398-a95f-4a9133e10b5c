package com.sevb.admin.service;

import com.sevb.admin.core.model.DataxSyncState;
import com.sevb.core.biz.model.ReturnT;
import java.util.List;

/**
 * DataX增量同步状态服务接口
 * 
 * <AUTHOR>
 */
public interface DataxSyncStateService {

    /**
     * 保存同步状态
     */
    ReturnT<String> save(DataxSyncState syncState);

    /**
     * 根据ID查询同步状态
     */
    DataxSyncState load(Long id);

    /**
     * 更新同步状态
     */
    ReturnT<String> update(DataxSyncState syncState);

    /**
     * 删除同步状态
     */
    ReturnT<String> delete(Long id);

    /**
     * 根据节点ID和表名查询同步状态
     */
    DataxSyncState findByNodeAndTables(Long nodeId, String sourceTable, String targetTable);

    /**
     * 根据节点ID查询同步状态
     */
    DataxSyncState findByNodeId(Long nodeId);

    /**
     * 根据工作流ID查询同步状态列表
     */
    List<DataxSyncState> findByWorkflowId(Long workflowId);

    /**
     * 分页查询同步状态列表
     */
    ReturnT<List<DataxSyncState>> pageList(int page, int size, Long workflowId, Long nodeId, 
                                          String sourceTable, String targetTable, 
                                          String status, String incrementalType);

    /**
     * 查询所有活跃的同步状态
     */
    List<DataxSyncState> findAllActive();

    /**
     * 查询需要同步的状态（长时间未同步）
     */
    List<DataxSyncState> findNeedSync(int hours);

    /**
     * 查询错误状态的同步任务
     */
    List<DataxSyncState> findErrorStates();

    /**
     * 批量更新状态
     */
    ReturnT<String> batchUpdateStatus(List<Long> ids, String status, String updateUser);

    /**
     * 获取上次同步值
     */
    String getLastSyncValue(Long nodeId, String sourceTable, String targetTable);

    /**
     * 更新同步状态结果
     */
    ReturnT<String> updateSyncResult(Long nodeId, String sourceTable, String targetTable,
                                   String newSyncValue, Long recordCount, String updateUser);

    /**
     * 设置错误状态
     */
    ReturnT<String> setErrorStatus(Long nodeId, String sourceTable, String targetTable,
                                 String errorMessage, String updateUser);

    /**
     * 重置为活跃状态
     */
    ReturnT<String> resetToActive(Long id, String updateUser);

    /**
     * 初始化同步状态
     */
    ReturnT<String> initializeSyncState(Long nodeId, Long workflowId, 
                                      Long sourceDatasourceId, String sourceTable,
                                      Long targetDatasourceId, String targetTable,
                                      String incrementalColumn, String incrementalType,
                                      String configSnapshot, String createUser);

    /**
     * 获取源表最大增量值
     */
    String getMaxIncrementalValue(Long datasourceId, String tableName, 
                                String incrementalColumn, String incrementalType);

    /**
     * 检查是否可以执行同步
     */
    boolean canSync(Long nodeId, String sourceTable, String targetTable);

    /**
     * 获取同步状态统计信息
     */
    ReturnT<Object> getSyncStatistics();

    /**
     * 清理非活跃状态
     */
    ReturnT<String> cleanupInactiveStates(int days);

    /**
     * 根据数据源ID查询同步状态
     */
    List<DataxSyncState> findByDatasourceId(Long datasourceId);

    /**
     * 统计同步状态数量
     */
    int countByStatus(String status);

    /**
     * 查询最近同步的状态
     */
    List<DataxSyncState> findRecentSynced(int limit);

    /**
     * 更新配置快照
     */
    ReturnT<String> updateConfigSnapshot(Long id, String configSnapshot, String updateUser);

    /**
     * 检查重复的同步状态
     */
    boolean checkDuplicate(Long nodeId, String sourceTable, String targetTable, Long excludeId);

    /**
     * 创建或更新同步状态
     */
    ReturnT<String> createOrUpdateSyncState(Long nodeId, Long workflowId,
                                          Long sourceDatasourceId, String sourceTable,
                                          Long targetDatasourceId, String targetTable,
                                          String incrementalColumn, String incrementalType,
                                          String configSnapshot, String user);

    /**
     * 同步状态健康检查
     */
    ReturnT<Object> healthCheck();

    /**
     * 获取同步状态详情（包含统计信息）
     */
    ReturnT<Object> getSyncStateDetail(Long id);
}
