package com.sevb.admin.service.impl;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.sevb.admin.core.model.Workflow;
import com.sevb.admin.dao.WorkflowDao;
import com.sevb.admin.service.WorkflowService;
import com.sevb.admin.service.workflow.WorkflowScheduleService;
import com.sevb.core.biz.model.ReturnT;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 工作流服务实现类
 * <AUTHOR>
 */
@Slf4j
@Service
public class WorkflowServiceImpl implements WorkflowService {

    @Resource
    private WorkflowDao workflowDao;

    @Resource
    private WorkflowScheduleService workflowScheduleService;

    private final ObjectMapper objectMapper = new ObjectMapper();

    @Override
    public Map<String, Object> pageList(int pageNum, int pageSize, Long projectId, String name, String category, Integer status) {
        // 参数处理
        int offset = (pageNum - 1) * pageSize;

        // 查询数据
        List<Workflow> list = workflowDao.pageList(offset, pageSize, projectId, name, category, status);
        int totalCount = workflowDao.pageListCount(offset, pageSize, projectId, name, category, status);

        // 组装结果
        Map<String, Object> result = new HashMap<>();
        result.put("recordsTotal", totalCount);
        result.put("recordsFiltered", totalCount);
        result.put("data", list);

        return result;
    }

    @Override
    public List<Workflow> findByProjectId(Long projectId) {
        if (projectId == null) {
            return new ArrayList<>();
        }
        return workflowDao.findByProjectId(projectId);
    }

    @Override
    public List<Workflow> findByProjectIdAndStatus(Long projectId, Integer status) {
        if (projectId == null) {
            return new ArrayList<>();
        }
        return workflowDao.findByProjectIdAndStatus(projectId, status);
    }

    @Override
    public Workflow load(Long id) {
        if (id == null) {
            return null;
        }
        return workflowDao.load(id);
    }

    @Override
    public Workflow findByProjectIdAndCode(Long projectId, String code) {
        if (projectId == null || !StringUtils.hasText(code)) {
            return null;
        }
        return workflowDao.findByProjectIdAndCode(projectId, code);
    }

    @Override
    public ReturnT<String> add(Workflow workflow) {
        // 参数校验
        ReturnT<String> validateResult = validateWorkflow(workflow, true);
        if (validateResult.getCode() != ReturnT.SUCCESS_CODE) {
            return validateResult;
        }

        // 检查编码是否重复
        if (workflowDao.checkCodeExists(workflow.getProjectId(), workflow.getCode(), null) > 0) {
            return new ReturnT<>(ReturnT.FAIL_CODE, "工作流编码已存在");
        }

        // 检查名称是否重复
        if (workflowDao.checkNameExists(workflow.getProjectId(), workflow.getName(), null) > 0) {
            return new ReturnT<>(ReturnT.FAIL_CODE, "工作流名称已存在");
        }



        // 设置默认值
        if (workflow.getVersion() == null) {
            workflow.setVersion(1);
        }
        if (workflow.getStatus() == null) {
            workflow.setStatus(Workflow.Status.DRAFT.getCode());
        }
        if (!StringUtils.hasText(workflow.getWarningType())) {
            workflow.setWarningType(Workflow.WarningType.NONE.getCode());
        }
        if (!StringUtils.hasText(workflow.getCreateUser())) {
            workflow.setCreateUser("admin");
        }
        if (!StringUtils.hasText(workflow.getUpdateUser())) {
            workflow.setUpdateUser(workflow.getCreateUser());
        }

        // 确保使用调度中心编排模式
        if (!StringUtils.hasText(workflow.getOrchestrationMode())) {
            workflow.setOrchestrationMode("CENTRAL");
        }

        // 处理Cron表达式：如果为空字符串，设置为null
        if (workflow.getCronExpression() != null && workflow.getCronExpression().trim().isEmpty()) {
            workflow.setCronExpression(null);
        }

        // 保存
        int result = workflowDao.save(workflow);
        return result > 0 ? ReturnT.SUCCESS : new ReturnT<>(ReturnT.FAIL_CODE, "添加失败");
    }

    @Override
    public ReturnT<String> update(Workflow workflow) {
        // 参数校验
        if (workflow.getId() == null) {
            return new ReturnT<>(ReturnT.FAIL_CODE, "工作流ID不能为空");
        }

        ReturnT<String> validateResult = validateWorkflow(workflow, false);
        if (validateResult.getCode() != ReturnT.SUCCESS_CODE) {
            return validateResult;
        }

        // 检查记录是否存在
        Workflow existWorkflow = workflowDao.load(workflow.getId());
        if (existWorkflow == null) {
            return new ReturnT<>(ReturnT.FAIL_CODE, "工作流不存在");
        }

        // 检查是否可以编辑
        if (!existWorkflow.isEditable()) {
            return new ReturnT<>(ReturnT.FAIL_CODE,
                "已上线的工作流不允许编辑，请先下线后再编辑");
        }

        // 检查编码是否重复
        if (workflowDao.checkCodeExists(workflow.getProjectId(), workflow.getCode(), workflow.getId()) > 0) {
            return new ReturnT<>(ReturnT.FAIL_CODE, "工作流编码已存在");
        }

        // 检查名称是否重复
        if (workflowDao.checkNameExists(workflow.getProjectId(), workflow.getName(), workflow.getId()) > 0) {
            return new ReturnT<>(ReturnT.FAIL_CODE, "工作流名称已存在");
        }

        // 设置默认值
        if (!StringUtils.hasText(workflow.getUpdateUser())) {
            workflow.setUpdateUser("admin");
        }

        // 处理Cron表达式：如果为空字符串，设置为null
        if (workflow.getCronExpression() != null && workflow.getCronExpression().trim().isEmpty()) {
            workflow.setCronExpression(null);
        }

        // 更新
        int result = workflowDao.update(workflow);
        return result > 0 ? ReturnT.SUCCESS : new ReturnT<>(ReturnT.FAIL_CODE, "更新失败");
    }

    @Override
    public ReturnT<String> delete(Long id, String updateUser) {
        if (id == null) {
            return new ReturnT<>(ReturnT.FAIL_CODE, "工作流ID不能为空");
        }

        // 检查记录是否存在
        Workflow workflow = workflowDao.load(id);
        if (workflow == null) {
            return new ReturnT<>(ReturnT.FAIL_CODE, "工作流不存在");
        }

        // 检查是否可以删除（只有草稿状态才能删除）
        if (!Workflow.Status.DRAFT.getCode().equals(workflow.getStatus())) {
            return new ReturnT<>(ReturnT.FAIL_CODE, "只有草稿状态的工作流才能删除");
        }

        // TODO: 检查是否有运行中的实例
        // 这里需要检查task_instance表中是否有正在运行的实例

        // 删除
        int result = workflowDao.delete(id, updateUser);
        return result > 0 ? ReturnT.SUCCESS : new ReturnT<>(ReturnT.FAIL_CODE, "删除失败");
    }

    @Override
    public ReturnT<String> publish(Long id, String updateUser) {
        return updateWorkflowStatus(id, Workflow.Status.PUBLISHED.getCode(), updateUser, "发布");
    }

    @Override
    public ReturnT<String> online(Long id, String updateUser) {
        return online(id, "0 0 2 * * ?", updateUser); // 默认每天凌晨2点执行
    }

    @Override
    public ReturnT<String> online(Long id, String cronExpression, String updateUser) {
        // 使用工作流调度服务进行上线
        return workflowScheduleService.onlineWorkflow(id, cronExpression, updateUser);
    }

    @Override
    public ReturnT<String> offline(Long id, String updateUser) {
        // 使用工作流调度服务进行下线
        return workflowScheduleService.offlineWorkflow(id, updateUser);
    }

    @Override
    public List<Map<String, Object>> getCategoryList() {
        List<Map<String, Object>> categoryList = new ArrayList<>();
        for (Workflow.Category category : Workflow.Category.values()) {
            Map<String, Object> categoryMap = new HashMap<>();
            categoryMap.put("code", category.getCode());
            categoryMap.put("name", category.getName());
            categoryList.add(categoryMap);
        }
        return categoryList;
    }

    @Override
    public List<Map<String, Object>> getStatusList() {
        List<Map<String, Object>> statusList = new ArrayList<>();
        for (Workflow.Status status : Workflow.Status.values()) {
            Map<String, Object> statusMap = new HashMap<>();
            statusMap.put("code", status.getCode());
            statusMap.put("name", status.getName());
            statusList.add(statusMap);
        }
        return statusList;
    }



    @Override
    public int countByProjectId(Long projectId) {
        if (projectId == null) {
            return 0;
        }
        return workflowDao.countByProjectId(projectId);
    }

    @Override
    public Map<String, Integer> countByCategory(Long projectId) {
        Map<String, Integer> result = new HashMap<>();
        if (projectId == null) {
            return result;
        }

        for (Workflow.Category category : Workflow.Category.values()) {
            int count = workflowDao.countByCategory(projectId, category.getCode());
            result.put(category.getCode(), count);
        }
        return result;
    }

    /**
     * 验证工作流参数
     */
    private ReturnT<String> validateWorkflow(Workflow workflow, boolean isAdd) {
        if (workflow.getProjectId() == null) {
            return new ReturnT<>(ReturnT.FAIL_CODE, "项目ID不能为空");
        }
        if (!StringUtils.hasText(workflow.getName())) {
            return new ReturnT<>(ReturnT.FAIL_CODE, "工作流名称不能为空");
        }
        if (!StringUtils.hasText(workflow.getCode())) {
            return new ReturnT<>(ReturnT.FAIL_CODE, "工作流编码不能为空");
        }


        // 校验分类
        if (StringUtils.hasText(workflow.getCategory())) {
            Workflow.Category categoryEnum = Workflow.Category.getByCode(workflow.getCategory());
            if (categoryEnum == null) {
                return new ReturnT<>(ReturnT.FAIL_CODE, "无效的工作流分类");
            }
        }

        // 校验告警类型
        if (StringUtils.hasText(workflow.getWarningType())) {
            Workflow.WarningType warningTypeEnum = Workflow.WarningType.getByCode(workflow.getWarningType());
            if (warningTypeEnum == null) {
                return new ReturnT<>(ReturnT.FAIL_CODE, "无效的告警类型");
            }
        }

        return ReturnT.SUCCESS;
    }

    /**
     * 更新工作流状态
     */
    private ReturnT<String> updateWorkflowStatus(Long id, Integer status, String updateUser, String operation) {
        if (id == null) {
            return new ReturnT<>(ReturnT.FAIL_CODE, "工作流ID不能为空");
        }

        // 检查记录是否存在
        Workflow workflow = workflowDao.load(id);
        if (workflow == null) {
            return new ReturnT<>(ReturnT.FAIL_CODE, "工作流不存在");
        }

        // 状态转换校验
        if (!isValidStatusTransition(workflow.getStatus(), status)) {
            return new ReturnT<>(ReturnT.FAIL_CODE, "无效的状态转换");
        }

        // 更新状态
        int result = workflowDao.updateStatus(id, status, updateUser);
        return result > 0 ? ReturnT.SUCCESS : new ReturnT<>(ReturnT.FAIL_CODE, operation + "失败");
    }

    /**
     * 检查状态转换是否有效
     */
    private boolean isValidStatusTransition(Integer currentStatus, Integer targetStatus) {
        if (currentStatus == null || targetStatus == null) {
            return false;
        }

        // 草稿 -> 已发布
        if (currentStatus.equals(Workflow.Status.DRAFT.getCode()) && 
            targetStatus.equals(Workflow.Status.PUBLISHED.getCode())) {
            return true;
        }

        // 已发布 -> 已上线
        if (currentStatus.equals(Workflow.Status.PUBLISHED.getCode()) && 
            targetStatus.equals(Workflow.Status.ONLINE.getCode())) {
            return true;
        }

        // 已上线 -> 已下线
        if (currentStatus.equals(Workflow.Status.ONLINE.getCode()) && 
            targetStatus.equals(Workflow.Status.OFFLINE.getCode())) {
            return true;
        }

        // 已下线 -> 已上线
        if (currentStatus.equals(Workflow.Status.OFFLINE.getCode()) && 
            targetStatus.equals(Workflow.Status.ONLINE.getCode())) {
            return true;
        }

        return false;
    }

    @Override
    public ReturnT<String> execute(Long id, String executeUser) {
        try {
            log.info("开始手动执行工作流: id={}, executeUser={}", id, executeUser);

            // 检查工作流是否存在
            Workflow workflow = workflowDao.load(id);
            if (workflow == null) {
                return new ReturnT<>(ReturnT.FAIL_CODE, "工作流不存在");
            }

            // 检查工作流状态（只有已上线的工作流才能手动执行）
            if (!workflow.isOnline()) {
                return new ReturnT<>(ReturnT.FAIL_CODE, "只有已上线的工作流才能手动执行，当前状态: " + workflow.getStatusName());
            }

            // 调用工作流调度服务执行
            ReturnT<String> result = workflowScheduleService.executeWorkflow(id, executeUser);

            if (result.getCode() == ReturnT.SUCCESS_CODE) {
                log.info("工作流手动执行成功: id={}, executeUser={}", id, executeUser);
            } else {
                log.error("工作流手动执行失败: id={}, executeUser={}, error={}", id, executeUser, result.getMsg());
            }

            return result;

        } catch (Exception e) {
            log.error("手动执行工作流异常: id={}, executeUser={}", id, executeUser, e);
            return new ReturnT<>(ReturnT.FAIL_CODE, "执行失败: " + e.getMessage());
        }
    }
}
