package com.sevb.admin.service.orchestration;

import com.sevb.admin.core.dto.NodeExecutionParam;
import com.sevb.admin.core.dto.NodeExecutionResult;
import com.sevb.admin.core.enums.NodeExecutionState;
import com.sevb.admin.core.enums.TriggerType;
import com.sevb.admin.core.enums.WorkflowExecutionState;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.sevb.admin.core.model.*;
import com.sevb.admin.core.model.DataxSyncState;
import com.sevb.admin.core.util.DataxConfigGenerator;
import com.sevb.admin.dao.*;
import com.sevb.admin.service.DataxSyncStateService;
import com.sevb.admin.service.DataxSyncLogService;
import com.sevb.core.biz.model.ReturnT;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 调度中心工作流编排服务
 * 负责工作流在调度中心的统一编排和管理
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class CentralWorkflowOrchestrationService {

    @Autowired
    private WorkflowDao workflowDao;

    @Autowired
    private WorkflowNodeDao workflowNodeDao;

    @Autowired
    private WorkflowNodeRelationDao workflowNodeRelationDao;

    @Autowired
    private WorkflowInstanceDao workflowInstanceDao;

    @Autowired
    private NodeInstanceDao nodeInstanceDao;

    @Autowired
    private NodeTaskExecutionService nodeTaskService;

    @Autowired
    private DataxConfigGenerator dataxConfigGenerator;

    @Autowired
    private DataxSyncStateService syncStateService;

    @Autowired
    private DataxSyncLogService syncLogService;

    /**
     * 工作流执行实例缓存
     */
    private final Map<String, WorkflowExecution> executionCache = new ConcurrentHashMap<>();

    /**
     * 执行工作流 - 调度中心编排模式（手动触发）
     */
    @Transactional
    public ReturnT<String> executeWorkflow(Long workflowId, String executeUser) {
        return executeWorkflow(workflowId, executeUser, TriggerType.MANUAL.getCode());
    }

    /**
     * 执行工作流 - 调度中心编排模式（支持指定触发类型）
     */
    @Transactional
    public ReturnT<String> executeWorkflow(Long workflowId, String executeUser, String triggerType) {
        try {
            log.info("调度中心开始编排执行工作流: workflowId={}, executeUser={}, triggerType={}",
                workflowId, executeUser, triggerType);

            // 1. 验证工作流状态
            Workflow workflow = workflowDao.load(workflowId);
            if (workflow == null) {
                return new ReturnT<>(ReturnT.FAIL_CODE, "工作流不存在");
            }

            if (!workflow.isOnline()) {
                return new ReturnT<>(ReturnT.FAIL_CODE, "只有已上线的工作流才能执行，当前状态: " + workflow.getStatusName());
            }

            // 2. 创建工作流执行实例
            WorkflowExecution execution = createWorkflowExecution(workflow, executeUser, triggerType);

            // 3. 解析工作流节点和依赖关系
            List<WorkflowNode> nodes = workflowNodeDao.listByWorkflow(workflowId);
            if (nodes.isEmpty()) {
                return new ReturnT<>(ReturnT.FAIL_CODE, "工作流没有配置节点");
            }

            Map<String, List<String>> dependencies = parseDependencies(workflowId);

            // 4. 初始化执行状态
            execution.initializeNodeStates(nodes, dependencies);

            // 5. 保存工作流实例到数据库
            saveWorkflowInstance(execution);

            // 6. 缓存执行实例
            executionCache.put(execution.getExecutionId(), execution);

            // 7. 启动入口节点
            List<WorkflowNode> entryNodes = findEntryNodes(nodes, dependencies);
            if (entryNodes.isEmpty()) {
                return new ReturnT<>(ReturnT.FAIL_CODE, "工作流没有入口节点");
            }

            for (WorkflowNode node : entryNodes) {
                dispatchNodeToExecutor(execution, node);
            }

            log.info("工作流执行已启动: workflowId={}, executionId={}, entryNodes={}",
                    workflowId, execution.getExecutionId(), entryNodes.size());

            return new ReturnT<>(execution.getExecutionId());

        } catch (Exception e) {
            log.error("工作流执行失败: workflowId={}", workflowId, e);
            return new ReturnT<>(ReturnT.FAIL_CODE, "执行失败: " + e.getMessage());
        }
    }

    /**
     * 获取工作流执行详情
     */
    public ReturnT<Map<String, Object>> getExecutionDetails(String executionId) {
        try {
            // log.info("获取工作流执行详情: executionId={}", executionId);

            // 1. 从缓存中获取执行实例
            WorkflowExecution execution = executionCache.get(executionId);

            // 2. 从数据库获取工作流实例
            WorkflowInstance workflowInstance = workflowInstanceDao.loadByExecutionId(executionId);
            if (workflowInstance == null) {
                return new ReturnT<>(ReturnT.FAIL_CODE, "工作流执行实例不存在");
            }

            // 3. 获取节点实例列表
            List<NodeInstance> nodeInstances = nodeInstanceDao.findByExecutionId(executionId);

            // 4. 构建执行详情数据
            Map<String, Object> executionData = new HashMap<>();
            executionData.put("executionId", executionId);
            executionData.put("workflowId", workflowInstance.getWorkflowId());
            executionData.put("workflowName", workflowInstance.getWorkflowName());
            executionData.put("status", workflowInstance.getStatus());
            executionData.put("triggerType", workflowInstance.getTriggerType());
            executionData.put("triggerTime", workflowInstance.getTriggerTime());
            executionData.put("startTime", workflowInstance.getStartTime());
            executionData.put("endTime", workflowInstance.getEndTime());
            executionData.put("duration", workflowInstance.getDuration());
            executionData.put("progress", workflowInstance.getProgress());
            executionData.put("completedNodes", workflowInstance.getCompletedNodes());
            executionData.put("totalNodes", workflowInstance.getTotalNodes());
            executionData.put("errorMessage", workflowInstance.getErrorMessage());
            executionData.put("executeUser", workflowInstance.getExecuteUser());

            // 5. 如果有缓存的执行实例，添加实时状态
            if (execution != null) {
                executionData.put("realTimeStatus", execution.getState().name());
                executionData.put("realTimeProgress", execution.getProgress());
            }

            // 6. 构建符合前端期望的返回结构
            Map<String, Object> result = new HashMap<>();
            result.put("execution", executionData);
            result.put("nodes", nodeInstances);

            return new ReturnT<>(result);

        } catch (Exception e) {
            log.error("获取工作流执行详情失败: executionId={}", executionId, e);
            return new ReturnT<>(ReturnT.FAIL_CODE, "获取失败: " + e.getMessage());
        }
    }

    /**
     * 处理节点执行完成回调
     */
    @Transactional
    public ReturnT<String> handleNodeComplete(NodeExecutionResult result) {
        try {
            log.info("收到节点执行完成回调: executionId={}, nodeCode={}, success={}",
                    result.getExecutionId(), result.getNodeCode(), result.isSuccess());

            WorkflowExecution execution = getWorkflowExecution(result.getExecutionId());
            if (execution == null) {
                log.warn("工作流执行实例不存在: executionId={}", result.getExecutionId());
                return new ReturnT<>(ReturnT.FAIL_CODE, "工作流执行实例不存在");
            }

            // 更新节点实例状态
            updateNodeInstance(execution, result);

            if (result.isSuccess()) {
                // 节点执行成功，检查并启动下游节点
                execution.updateNodeState(result.getNodeCode(), NodeExecutionState.SUCCESS);

                List<String> readyNodeCodes = execution.getReadyNodes();
                for (String nodeCode : readyNodeCodes) {
                    WorkflowNode node = execution.getNodeMap().get(nodeCode);
                    if (node != null) {
                        dispatchNodeToExecutor(execution, node);
                    }
                }

                // 检查工作流是否完成
                if (execution.isAllNodesCompleted()) {
                    completeWorkflowExecution(execution, true);
                }

            } else {
                // 节点执行失败
                execution.updateNodeState(result.getNodeCode(), NodeExecutionState.FAILED);
                handleNodeFailure(execution, result.getNodeCode(), result.getErrorMessage());
            }

            // 更新工作流实例状态
            updateWorkflowInstance(execution);

            return ReturnT.SUCCESS;

        } catch (Exception e) {
            log.error("处理节点完成回调失败", e);
            return new ReturnT<>(ReturnT.FAIL_CODE, "处理回调失败: " + e.getMessage());
        }
    }

    /**
     * 创建工作流执行实例
     */
    private WorkflowExecution createWorkflowExecution(Workflow workflow, String executeUser, String triggerType) {
        WorkflowExecution execution = new WorkflowExecution();
        execution.setWorkflowId(workflow.getId());
        execution.setWorkflowName(workflow.getName());
        execution.setWorkflowVersion(workflow.getVersion() != null ? workflow.getVersion() + "" : "1.0");
        execution.setExecuteUser(executeUser);
        execution.setExecutionType(triggerType != null ? triggerType : TriggerType.MANUAL.getCode());
        execution.setOrchestrationMode("CENTRAL");

        log.info("创建工作流执行实例: executionId={}, workflowId={}, triggerType={}",
            execution.getExecutionId(), workflow.getId(), triggerType);
        return execution;
    }

    /**
     * 解析工作流依赖关系
     */
    private Map<String, List<String>> parseDependencies(Long workflowId) {
        Map<String, List<String>> dependencies = new HashMap<>();

        List<WorkflowNodeRelation> relations = workflowNodeRelationDao.listByWorkflow(workflowId);
        for (WorkflowNodeRelation relation : relations) {
            String targetNode = relation.getPostNodeCode();  // 后置节点作为目标节点
            String sourceNode = relation.getPreNodeCode();   // 前置节点作为源节点

            // 只有当前置节点不为空时才建立依赖关系
            if (sourceNode != null) {
                dependencies.computeIfAbsent(targetNode, k -> new ArrayList<>()).add(sourceNode);
            }
        }

        log.info("解析工作流依赖关系: workflowId={}, relations={}", workflowId, relations.size());
        return dependencies;
    }

    /**
     * 查找入口节点（没有前置依赖的节点）
     */
    private List<WorkflowNode> findEntryNodes(List<WorkflowNode> nodes, Map<String, List<String>> dependencies) {
        List<WorkflowNode> entryNodes = new ArrayList<>();

        for (WorkflowNode node : nodes) {
            List<String> deps = dependencies.get(node.getNodeCode());
            if (deps == null || deps.isEmpty()) {
                entryNodes.add(node);
            }
        }

        log.info("找到入口节点: count={}", entryNodes.size());
        return entryNodes;
    }

    /**
     * 将节点任务分发到执行器
     */
    private void dispatchNodeToExecutor(WorkflowExecution execution, WorkflowNode node) {
        try {
            log.info("分发节点任务到执行器: executionId={}, nodeCode={}",
                    execution.getExecutionId(), node.getNodeCode());

            // 1. 构建节点执行参数
            NodeExecutionParam nodeParam = buildNodeExecutionParam(execution, node);

            // 2. 获取或创建节点执行任务
            Integer jobId = nodeTaskService.getOrCreateNodeTask(node);

            // 3. 创建节点实例
            createNodeInstance(execution, node, jobId);

            // 4. 触发节点执行
            nodeTaskService.executeNodeTask(jobId, nodeParam);

            // 5. 更新节点状态为运行中
            execution.updateNodeState(node.getNodeCode(), NodeExecutionState.RUNNING);

            log.info("节点任务分发成功: executionId={}, nodeCode={}, jobId={}",
                    execution.getExecutionId(), node.getNodeCode(), jobId);

        } catch (Exception e) {
            log.error("分发节点任务失败: nodeCode={}", node.getNodeCode(), e);
            execution.updateNodeState(node.getNodeCode(), NodeExecutionState.FAILED);
            handleNodeFailure(execution, node.getNodeCode(), e.getMessage());
        }
    }

    /**
     * 构建节点执行参数
     */
    private NodeExecutionParam buildNodeExecutionParam(WorkflowExecution execution, WorkflowNode node) {
        NodeExecutionParam param = new NodeExecutionParam();
        param.setExecutionId(execution.getExecutionId());
        param.setWorkflowId(execution.getWorkflowId());
        param.setNodeCode(node.getNodeCode());
        param.setNodeName(node.getNodeName());
        param.setNodeType(node.getNodeType());

        // 🚀 增量同步配置生成
        String nodeConfig = enhanceNodeConfigForIncremental(node, execution);
        param.setNodeConfig(nodeConfig);

        param.setGlobalParams(execution.getGlobalParams());
        param.setTimeout(node.getTimeout() != null ? node.getTimeout().longValue() : 300L);
        param.setRetryTimes(node.getRetryTimes());
        param.setExecuteUser(execution.getExecuteUser());
        param.setOrchestrationMode("CENTRAL");
        param.setCallbackUrl(buildCallbackUrl(execution.getExecutionId(), node.getNodeCode()));

        return param;
    }

    /**
     * 增强节点配置以支持增量同步
     */
    private String enhanceNodeConfigForIncremental(WorkflowNode node, WorkflowExecution execution) {
        try {
            // 检查是否为DataX节点
            if (!"DATAX".equals(node.getNodeType())) {
                return node.getConfigParams();
            }

            log.info("检查DataX节点是否需要增量同步，节点ID: {}", node.getId());

            // 解析节点配置，检查是否为增量同步
            ObjectMapper objectMapper = new ObjectMapper();
            JsonNode nodeConfig = objectMapper.readTree(node.getConfigParams());

            JsonNode syncStrategy = nodeConfig.path("syncStrategy");
            if (syncStrategy.isMissingNode() || !"INCREMENTAL".equals(syncStrategy.path("type").asText())) {
                log.debug("节点不是增量同步，使用原始配置，节点ID: {}", node.getId());
                return node.getConfigParams();
            }

            log.info("节点为增量同步，开始生成增量DataX配置，节点ID: {}", node.getId());

            // 初始化或获取同步状态
            initializeSyncStateIfNeeded(node, execution);

            // 创建同步日志记录
            createSyncLogRecord(node, execution);

            // 生成增量DataX配置
            String incrementalConfig = dataxConfigGenerator.generateDataxConfig(node);

            log.info("增量DataX配置生成成功，节点ID: {}", node.getId());
            return incrementalConfig;

        } catch (Exception e) {
            log.error("增强节点配置失败，使用原始配置，节点ID: {}", node.getId(), e);
            return node.getConfigParams();
        }
    }

    /**
     * 初始化同步状态（如果需要）
     */
    private void initializeSyncStateIfNeeded(WorkflowNode node, WorkflowExecution execution) {
        try {
            // 检查是否已存在同步状态
            if (syncStateService.findByNodeId(node.getId()) != null) {
                log.debug("同步状态已存在，节点ID: {}", node.getId());
                return;
            }

            log.info("初始化同步状态，节点ID: {}", node.getId());

            // 解析节点配置获取数据源和表信息
            ObjectMapper objectMapper = new ObjectMapper();
            JsonNode nodeConfig = objectMapper.readTree(node.getConfigParams());

            // 正确的配置结构
            JsonNode sourceConfig = nodeConfig.path("source");
            JsonNode targetConfig = nodeConfig.path("target");

            Long sourceDatasourceId = sourceConfig.path("datasourceId").asLong();
            String sourceTable = sourceConfig.path("tableName").asText();
            Long targetDatasourceId = targetConfig.path("datasourceId").asLong();
            String targetTable = targetConfig.path("tableName").asText();

            JsonNode syncStrategy = nodeConfig.path("syncStrategy");
            String incrementalColumn = syncStrategy.path("incrementalColumn").asText();
            String incrementalType = syncStrategy.path("incrementalType").asText("TIMESTAMP");

            log.debug("解析节点配置 - 源数据源ID: {}, 源表: {}, 目标数据源ID: {}, 目标表: {}, 增量字段: {}",
                     sourceDatasourceId, sourceTable, targetDatasourceId, targetTable, incrementalColumn);

            // 初始化同步状态
            syncStateService.initializeSyncState(
                node.getId(),
                execution.getWorkflowId(),
                sourceDatasourceId,
                sourceTable,
                targetDatasourceId,
                targetTable,
                incrementalColumn,
                incrementalType,
                node.getConfigParams(),
                execution.getExecuteUser()
            );

            log.info("同步状态初始化成功，节点ID: {}", node.getId());

        } catch (Exception e) {
            log.error("初始化同步状态失败，节点ID: {}", node.getId(), e);
            // 不抛出异常，允许继续执行
        }
    }

    /**
     * 构建回调URL
     */
    private String buildCallbackUrl(String executionId, String nodeCode) {
        // TODO: 从配置中获取调度中心地址
        return String.format("http://localhost:18080/ibdp/workflow/callback/nodeComplete?executionId=%s&nodeCode=%s",
                executionId, nodeCode);
    }

    /**
     * 保存工作流实例到数据库
     */
    private void saveWorkflowInstance(WorkflowExecution execution) {
        try {
            WorkflowInstance instance = new WorkflowInstance();
            instance.setExecutionId(execution.getExecutionId());
            instance.setWorkflowId(execution.getWorkflowId());
            instance.setWorkflowName(execution.getWorkflowName());
            instance.setWorkflowVersion(execution.getWorkflowVersion() != null ? execution.getWorkflowVersion() : "1.0");
            instance.setOrchestrationMode(execution.getOrchestrationMode());
            instance.setStatus(mapExecutionStateToDbStatus(execution.getState()));
            instance.setTriggerType(execution.getExecutionType());
            instance.setExecuteUser(execution.getExecuteUser());
            instance.setProgress(execution.getProgress());
            instance.setCompletedNodes(execution.getCompletedNodes());
            instance.setTotalNodes(execution.getTotalNodes());

            // 设置时间字段
            Date now = new Date();
            instance.setTriggerTime(now);  // ✅ 设置触发时间
            instance.setStartTime(now);    // 设置开始时间
            instance.setCreateTime(now);   // 设置创建时间
            instance.setUpdateTime(now);   // 设置更新时间

            workflowInstanceDao.save(instance);
            log.info("工作流实例保存成功: executionId={}", execution.getExecutionId());

        } catch (Exception e) {
            log.error("保存工作流实例失败: executionId={}", execution.getExecutionId(), e);
            throw new RuntimeException("保存工作流实例失败", e);
        }
    }

    /**
     * 创建节点实例
     */
    private void createNodeInstance(WorkflowExecution execution, WorkflowNode node, Integer jobId) {
        try {
            NodeInstance nodeInstance = new NodeInstance();
            nodeInstance.setExecutionId(execution.getExecutionId());
            nodeInstance.setWorkflowId(execution.getWorkflowId());
            nodeInstance.setNodeId(node.getId());
            nodeInstance.setNodeCode(node.getNodeCode());
            nodeInstance.setNodeName(node.getNodeName());
            nodeInstance.setNodeType(node.getNodeType());
            nodeInstance.setJobId(jobId);
            nodeInstance.setStatus(NodeExecutionState.WAITING.getCode());
            nodeInstance.setStartTime(new Date()); // 设置开始时间
            nodeInstance.setCreateTime(new Date());
            nodeInstance.setUpdateTime(new Date());

            nodeInstanceDao.save(nodeInstance);
            log.info("节点实例创建成功: executionId={}, nodeCode={}", execution.getExecutionId(), node.getNodeCode());

        } catch (Exception e) {
            log.error("创建节点实例失败: nodeCode={}", node.getNodeCode(), e);
            throw new RuntimeException("创建节点实例失败", e);
        }
    }

    /**
     * 更新节点实例状态
     */
    private void updateNodeInstance(WorkflowExecution execution, NodeExecutionResult result) {
        try {
            log.info("开始查询节点实例: executionId={}, nodeCode={}", result.getExecutionId(), result.getNodeCode());
            NodeInstance nodeInstance = nodeInstanceDao.loadByExecutionAndNode(
                    result.getExecutionId(), result.getNodeCode());

            if (nodeInstance != null) {
                log.info("找到节点实例: id={}, 当前状态={}", nodeInstance.getId(), nodeInstance.getStatus());

                Integer newStatus = result.isSuccess() ? NodeExecutionState.SUCCESS.getCode() : NodeExecutionState.FAILED.getCode();
                log.info("准备更新节点状态: 从 {} 更新为 {}", nodeInstance.getStatus(), newStatus);

                nodeInstance.setStatus(newStatus);
                nodeInstance.setStartTime(result.getStartTime() != null ?
                        new Date(result.getStartTime()) : null);
                nodeInstance.setEndTime(result.getEndTime() != null ?
                        new Date(result.getEndTime()) : new Date());
                nodeInstance.setDuration(result.getDuration());
                nodeInstance.setExecutorAddress(result.getExecutorAddress());
                nodeInstance.setErrorMessage(result.getErrorMessage());
                nodeInstance.setJobLogId(result.getJobLogId());
                nodeInstance.setUpdateTime(new Date());

                log.info("执行数据库更新: nodeInstance.id={}, status={}", nodeInstance.getId(), nodeInstance.getStatus());
                int updateCount = nodeInstanceDao.update(nodeInstance);
                log.info("数据库更新结果: 影响行数={}", updateCount);

                if (updateCount > 0) {
                    log.info("节点实例状态更新成功: executionId={}, nodeCode={}, success={}",
                            result.getExecutionId(), result.getNodeCode(), result.isSuccess());
                } else {
                    log.warn("节点实例状态更新失败: 影响行数为0, executionId={}, nodeCode={}",
                            result.getExecutionId(), result.getNodeCode());
                }
            } else {
                log.warn("未找到节点实例: executionId={}, nodeCode={}", result.getExecutionId(), result.getNodeCode());
            }

        } catch (Exception e) {
            log.error("更新节点实例状态失败: nodeCode={}", result.getNodeCode(), e);
        }
    }

    /**
     * 更新工作流实例状态
     */
    private void updateWorkflowInstance(WorkflowExecution execution) {
        try {
            WorkflowInstance instance = workflowInstanceDao.loadByExecutionId(execution.getExecutionId());
            if (instance != null) {
                // 映射WorkflowExecutionState到WorkflowInstance.Status
                Integer dbStatus = mapExecutionStateToDbStatus(execution.getState());
                log.info("状态映射: WorkflowExecutionState.{} ({}) -> WorkflowInstance.Status ({})",
                        execution.getState().name(), execution.getState().getCode(), dbStatus);

                instance.setStatus(dbStatus);
                instance.setProgress(execution.getProgress());
                instance.setCompletedNodes(execution.getCompletedNodes());
                instance.setErrorMessage(execution.getErrorMessage());
                instance.setUpdateTime(new Date());

                if (execution.getEndTime() != null) {
                    instance.setEndTime(new Date(execution.getEndTime()));
                    instance.setDuration(execution.getDuration());
                }

                workflowInstanceDao.update(instance);
                log.info("工作流实例状态更新成功: executionId={}, executionState={}, dbStatus={}",
                        execution.getExecutionId(), execution.getState(), dbStatus);

            // 如果工作流执行完成（成功或失败），更新工作流的最后执行信息
            if (execution.getState() == WorkflowExecutionState.SUCCESS ||
                execution.getState() == WorkflowExecutionState.FAILED) {
                updateWorkflowLastExecution(instance);
            }
            }

        } catch (Exception e) {
            log.error("更新工作流实例状态失败: executionId={}", execution.getExecutionId(), e);
        }
    }

    /**
     * 更新工作流的最后执行信息
     */
    private void updateWorkflowLastExecution(WorkflowInstance instance) {
        try {
            // 更新工作流的最后执行ID和触发时间
            workflowDao.updateLastExecutionId(instance.getWorkflowId(), instance.getExecutionId());

            // 更新执行统计信息
            updateWorkflowExecutionStats(instance.getWorkflowId());

            log.info("工作流最后执行信息更新成功: workflowId={}, executionId={}",
                    instance.getWorkflowId(), instance.getExecutionId());

        } catch (Exception e) {
            log.error("更新工作流最后执行信息失败: workflowId={}, executionId={}",
                    instance.getWorkflowId(), instance.getExecutionId(), e);
        }
    }

    /**
     * 更新工作流执行统计信息
     */
    private void updateWorkflowExecutionStats(Long workflowId) {
        try {
            // 统计执行次数和成功失败次数（获取所有实例用于统计）
            List<WorkflowInstance> instances = workflowInstanceDao.findByWorkflowId(workflowId, null);

            long executionCount = instances.size();
            long successCount = instances.stream()
                    .mapToLong(inst -> WorkflowInstance.Status.SUCCESS.getCode().equals(inst.getStatus()) ? 1 : 0)
                    .sum();
            long failureCount = instances.stream()
                    .mapToLong(inst -> WorkflowInstance.Status.FAILURE.getCode().equals(inst.getStatus()) ? 1 : 0)
                    .sum();

            // 更新统计信息
            workflowDao.updateExecutionStats(workflowId, executionCount, successCount, failureCount);

            log.debug("工作流执行统计更新成功: workflowId={}, total={}, success={}, failure={}",
                    workflowId, executionCount, successCount, failureCount);

        } catch (Exception e) {
            log.error("更新工作流执行统计失败: workflowId={}", workflowId, e);
        }
    }

    /**
     * 映射WorkflowExecutionState到数据库状态
     */
    private Integer mapExecutionStateToDbStatus(WorkflowExecutionState executionState) {
        switch (executionState) {
            case INITIALIZING:
                return WorkflowInstance.Status.SUBMIT_SUCCESS.getCode(); // 0
            case RUNNING:
                return WorkflowInstance.Status.RUNNING.getCode(); // 1
            case PAUSED:
                return WorkflowInstance.Status.PAUSE.getCode(); // 3
            case SUCCESS:
                return WorkflowInstance.Status.SUCCESS.getCode(); // 7 ✅ 正确的成功状态
            case FAILED:
                return WorkflowInstance.Status.FAILURE.getCode(); // 6
            case TERMINATED:
                return WorkflowInstance.Status.STOP.getCode(); // 5
            default:
                log.warn("未知的WorkflowExecutionState: {}", executionState);
                return WorkflowInstance.Status.SUBMIT_SUCCESS.getCode();
        }
    }

    /**
     * 完成工作流执行
     */
    private void completeWorkflowExecution(WorkflowExecution execution, boolean success) {
        try {
            execution.complete(success);
            updateWorkflowInstance(execution);

            // 更新工作流统计
            updateWorkflowStats(execution.getWorkflowId(), success);

            log.info("工作流执行完成: executionId={}, success={}, duration={}ms",
                    execution.getExecutionId(), success, execution.getDuration());

        } catch (Exception e) {
            log.error("完成工作流执行失败: executionId={}", execution.getExecutionId(), e);
        }
    }

    /**
     * 处理节点失败
     */
    private void handleNodeFailure(WorkflowExecution execution, String nodeCode, String errorMessage) {
        try {
            log.warn("节点执行失败: executionId={}, nodeCode={}, error={}",
                    execution.getExecutionId(), nodeCode, errorMessage);

            // 检查是否有其他节点还在运行
            if (execution.getRunningNodes().isEmpty()) {
                // 没有运行中的节点，工作流执行失败
                execution.setErrorMessage("节点执行失败: " + nodeCode + " - " + errorMessage);
                completeWorkflowExecution(execution, false);
            }

        } catch (Exception e) {
            log.error("处理节点失败异常: nodeCode={}", nodeCode, e);
        }
    }

    /**
     * 更新工作流统计
     */
    private void updateWorkflowStats(Long workflowId, boolean success) {
        try {
            Workflow workflow = workflowDao.load(workflowId);
            if (workflow != null) {
                // 更新统计计数
                long currentCount = workflow.getExecutionCount() != null ? workflow.getExecutionCount() : 0L;
                workflow.setExecutionCount(currentCount + 1);

                if (success) {
                    long currentSuccess = workflow.getSuccessCount() != null ? workflow.getSuccessCount() : 0L;
                    workflow.setSuccessCount(currentSuccess + 1);
                } else {
                    long currentFailure = workflow.getFailureCount() != null ? workflow.getFailureCount() : 0L;
                    workflow.setFailureCount(currentFailure + 1);
                }

                workflowDao.update(workflow);
                log.debug("工作流统计更新成功: workflowId={}, success={}", workflowId, success);
            }

        } catch (Exception e) {
            log.error("更新工作流统计失败: workflowId={}", workflowId, e);
        }
    }

    /**
     * 获取工作流执行实例
     */
    public WorkflowExecution getWorkflowExecution(String executionId) {
        return executionCache.get(executionId);
    }

    /**
     * 重试失败的节点
     */
    @Transactional
    public ReturnT<String> retryFailedNode(String executionId, String nodeCode) {
        try {
            WorkflowExecution execution = getWorkflowExecution(executionId);
            if (execution == null) {
                return new ReturnT<>(ReturnT.FAIL_CODE, "工作流执行实例不存在");
            }

            NodeExecutionState currentState = execution.getNodeStates().get(nodeCode);
            if (currentState != NodeExecutionState.FAILED) {
                return new ReturnT<>(ReturnT.FAIL_CODE, "只有失败的节点才能重试，当前状态: " + currentState);
            }

            WorkflowNode node = execution.getNodeMap().get(nodeCode);
            if (node == null) {
                return new ReturnT<>(ReturnT.FAIL_CODE, "节点不存在: " + nodeCode);
            }

            // 重置节点状态
            execution.updateNodeState(nodeCode, NodeExecutionState.WAITING);
            execution.getFailedNodes().remove(nodeCode);

            // 重新分发节点到执行器
            dispatchNodeToExecutor(execution, node);

            log.info("节点重试已启动: executionId={}, nodeCode={}", executionId, nodeCode);
            return new ReturnT<>("节点重试已启动");

        } catch (Exception e) {
            log.error("重试节点失败: executionId={}, nodeCode={}", executionId, nodeCode, e);
            return new ReturnT<>(ReturnT.FAIL_CODE, "重试失败: " + e.getMessage());
        }
    }

    /**
     * 终止工作流执行
     */
    @Transactional
    public ReturnT<String> terminateExecution(String executionId, String reason) {
        try {
            WorkflowExecution execution = getWorkflowExecution(executionId);
            if (execution == null) {
                return new ReturnT<>(ReturnT.FAIL_CODE, "工作流执行实例不存在");
            }

            // 终止正在运行的节点任务
            Set<String> runningNodes = execution.getRunningNodes();
            for (String nodeCode : runningNodes) {
                try {
                    terminateRunningNode(execution, nodeCode);
                } catch (Exception e) {
                    log.warn("终止运行中节点失败: nodeCode={}, error={}", nodeCode, e.getMessage());
                }
            }

            execution.terminate(reason);
            updateWorkflowInstance(execution);

            log.info("工作流执行已终止: executionId={}, reason={}, terminatedNodes={}",
                    executionId, reason, runningNodes.size());
            return new ReturnT<>("工作流执行已终止");

        } catch (Exception e) {
            log.error("终止工作流执行失败: executionId={}", executionId, e);
            return new ReturnT<>(ReturnT.FAIL_CODE, "终止失败: " + e.getMessage());
        }
    }

    /**
     * 终止运行中的节点
     */
    private void terminateRunningNode(WorkflowExecution execution, String nodeCode) {
        try {
            // 查找节点对应的JobId
            WorkflowNode node = execution.getNodeMap().get(nodeCode);
            if (node != null && node.getJobId() != null) {
                // 这里可以调用XXL-JOB的任务终止接口
                // JobTriggerPoolHelper.stop(node.getJobId().intValue());
                log.info("终止节点任务: nodeCode={}, jobId={}", nodeCode, node.getJobId());
            }

            // 更新节点状态为终止
            execution.updateNodeState(nodeCode, NodeExecutionState.TERMINATED);

        } catch (Exception e) {
            log.error("终止节点失败: nodeCode={}", nodeCode, e);
        }
    }

    /**
     * 创建同步日志记录
     */
    private void createSyncLogRecord(WorkflowNode node, WorkflowExecution execution) {
        try {
            // 解析节点配置获取表信息
            ObjectMapper objectMapper = new ObjectMapper();
            JsonNode nodeConfig = objectMapper.readTree(node.getConfigParams());

            JsonNode sourceConfig = nodeConfig.path("source");
            JsonNode targetConfig = nodeConfig.path("target");

            String sourceTable = sourceConfig.path("tableName").asText();
            String targetTable = targetConfig.path("tableName").asText();

            // 获取同步状态记录
            DataxSyncState syncState = syncStateService.findByNodeAndTables(node.getId(), sourceTable, targetTable);
            if (syncState == null) {
                log.warn("未找到同步状态记录，跳过创建同步日志 - 节点ID: {}, 源表: {}, 目标表: {}",
                        node.getId(), sourceTable, targetTable);
                return;
            }

            // 获取当前增量值
            String currentIncrementalValue = syncStateService.getLastSyncValue(node.getId(), sourceTable, targetTable);

            // 创建执行日志
            syncLogService.createExecutionLog(
                syncState.getId(),
                execution.getExecutionId(),
                null, // jobLogId 暂时为空
                currentIncrementalValue,
                null  // dataxConfig 暂时为空
            );

            log.debug("创建同步日志记录成功 - 节点ID: {}, 执行ID: {}, 同步状态ID: {}, 源表: {}, 目标表: {}",
                     node.getId(), execution.getExecutionId(), syncState.getId(), sourceTable, targetTable);

        } catch (Exception e) {
            log.error("创建同步日志记录失败，节点ID: {}, 执行ID: {}",
                     node.getId(), execution.getExecutionId(), e);
        }
    }
}
