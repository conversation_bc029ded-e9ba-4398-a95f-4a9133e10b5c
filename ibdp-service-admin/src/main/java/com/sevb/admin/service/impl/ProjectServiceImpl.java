package com.sevb.admin.service.impl;

import com.sevb.admin.core.model.Project;
import com.sevb.admin.core.model.ProjectMember;
import com.sevb.admin.dao.ProjectDao;
import com.sevb.admin.dao.ProjectMemberDao;
import com.sevb.admin.service.ProjectService;
import com.sevb.core.biz.model.ReturnT;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import jakarta.annotation.Resource;
import java.util.*;

/**
 * 项目管理服务实现类
 * <AUTHOR>
 */
@Slf4j
@Service
public class ProjectServiceImpl implements ProjectService {

    @Resource
    private ProjectDao projectDao;
    
    @Resource
    private ProjectMemberDao projectMemberDao;

    @Override
    public Map<String, Object> pageList(int pageNum, int pageSize, String name, String type, Integer status, String userId) {
        // 计算offset
        int offset = (pageNum - 1) * pageSize;

        List<Project> list = projectDao.pageList(offset, pageSize, name, type, status, userId);
        int listCount = projectDao.pageListCount(name, type, status, userId);

        // 组装分页结果
        Map<String, Object> result = new HashMap<>();
        result.put("recordsTotal", listCount);
        result.put("recordsFiltered", listCount);
        result.put("data", list);

        return result;
    }

    @Override
    @Transactional
    public ReturnT<String> add(Project project, String loginUser) {
        // 参数验证
        if (project == null) {
            return new ReturnT<>(ReturnT.FAIL_CODE, "项目信息不能为空");
        }
        if (!StringUtils.hasText(project.getName())) {
            return new ReturnT<>(ReturnT.FAIL_CODE, "项目名称不能为空");
        }
        if (!StringUtils.hasText(project.getCode())) {
            return new ReturnT<>(ReturnT.FAIL_CODE, "项目编码不能为空");
        }
        if (!StringUtils.hasText(project.getType())) {
            return new ReturnT<>(ReturnT.FAIL_CODE, "项目类型不能为空");
        }

        // 验证项目类型
        Project.ProjectType projectType = Project.ProjectType.getByCode(project.getType());
        if (projectType == null) {
            return new ReturnT<>(ReturnT.FAIL_CODE, "无效的项目类型");
        }

        // 检查项目编码是否已存在
        int codeExists = projectDao.checkCodeExists(project.getCode(), null);
        if (codeExists > 0) {
            return new ReturnT<>(ReturnT.FAIL_CODE, "项目编码已存在");
        }

        // 设置默认值
        if (project.getStatus() == null) {
            project.setStatus(Project.Status.ENABLED.getCode());
        }
        project.setCreateUser(loginUser);
        project.setUpdateUser(loginUser);

        try {
            // 保存项目
            int result = projectDao.save(project);
            if (result < 1) {
                return new ReturnT<>(ReturnT.FAIL_CODE, "新增项目失败");
            }

            // 添加创建者为项目管理员
            ProjectMember creator = new ProjectMember();
            creator.setProjectId(project.getId());
            creator.setUserId(loginUser);
            creator.setUsername(loginUser); // 实际应该从用户服务获取用户名
            creator.setRole(ProjectMember.Role.ADMIN.getCode());
            creator.setJoinTime(new Date());
            creator.setStatus(ProjectMember.Status.ENABLED.getCode());
            creator.setCreateUser(loginUser);
            creator.setUpdateUser(loginUser);

            int memberResult = projectMemberDao.save(creator);
            if (memberResult < 1) {
                throw new RuntimeException("添加项目创建者失败");
            }

            return new ReturnT<>("新增项目成功");
        } catch (Exception e) {
            log.error("新增项目失败", e);
            return new ReturnT<>(ReturnT.FAIL_CODE, "新增项目失败：" + e.getMessage());
        }
    }

    @Override
    public ReturnT<String> update(Project project, String loginUser) {
        // 参数验证
        if (project == null || project.getId() == null) {
            return new ReturnT<>(ReturnT.FAIL_CODE, "项目ID不能为空");
        }
        if (!StringUtils.hasText(project.getName())) {
            return new ReturnT<>(ReturnT.FAIL_CODE, "项目名称不能为空");
        }
        if (!StringUtils.hasText(project.getCode())) {
            return new ReturnT<>(ReturnT.FAIL_CODE, "项目编码不能为空");
        }
        if (!StringUtils.hasText(project.getType())) {
            return new ReturnT<>(ReturnT.FAIL_CODE, "项目类型不能为空");
        }

        // 验证项目类型
        Project.ProjectType projectType = Project.ProjectType.getByCode(project.getType());
        if (projectType == null) {
            return new ReturnT<>(ReturnT.FAIL_CODE, "无效的项目类型");
        }

        // 检查项目是否存在
        Project existProject = projectDao.load(project.getId());
        if (existProject == null) {
            return new ReturnT<>(ReturnT.FAIL_CODE, "项目不存在");
        }

        // 检查项目编码是否已被其他项目使用
        int codeExists = projectDao.checkCodeExists(project.getCode(), project.getId());
        if (codeExists > 0) {
            return new ReturnT<>(ReturnT.FAIL_CODE, "项目编码已被其他项目使用");
        }

        // 权限检查：只有项目管理员可以修改项目
        if (!isProjectAdmin(project.getId(), loginUser)) {
            return new ReturnT<>(ReturnT.FAIL_CODE, "无权限修改项目，仅项目管理员可操作");
        }

        try {
            project.setUpdateUser(loginUser);
            int result = projectDao.update(project);
            if (result < 1) {
                return new ReturnT<>(ReturnT.FAIL_CODE, "更新项目失败");
            }
            return new ReturnT<>("更新项目成功");
        } catch (Exception e) {
            log.error("更新项目失败", e);
            return new ReturnT<>(ReturnT.FAIL_CODE, "更新项目失败：" + e.getMessage());
        }
    }

    @Override
    @Transactional
    public ReturnT<String> delete(Long id, String loginUser) {
        if (id == null) {
            return new ReturnT<>(ReturnT.FAIL_CODE, "项目ID不能为空");
        }

        Project project = projectDao.load(id);
        if (project == null) {
            return new ReturnT<>(ReturnT.FAIL_CODE, "项目不存在");
        }

        // 权限检查：只有项目管理员可以删除项目
        if (!isProjectAdmin(id, loginUser)) {
            return new ReturnT<>(ReturnT.FAIL_CODE, "无权限删除项目，仅项目管理员可操作");
        }

        try {
            // 删除项目（软删除）
            int result = projectDao.delete(id, loginUser);
            if (result < 1) {
                return new ReturnT<>(ReturnT.FAIL_CODE, "删除项目失败");
            }

            // 删除项目成员（软删除）
            projectMemberDao.deleteByProjectId(id, loginUser);

            return new ReturnT<>("删除项目成功");
        } catch (Exception e) {
            log.error("删除项目失败", e);
            return new ReturnT<>(ReturnT.FAIL_CODE, "删除项目失败：" + e.getMessage());
        }
    }

    @Override
    public Project load(Long id) {
        if (id == null) {
            return null;
        }
        return projectDao.load(id);
    }

    @Override
    public Project loadByCode(String code) {
        if (!StringUtils.hasText(code)) {
            return null;
        }
        return projectDao.loadByCode(code);
    }

    @Override
    public List<Project> findAccessibleProjects(String userId) {
        if (!StringUtils.hasText(userId)) {
            return new ArrayList<>();
        }
        return projectDao.findAccessibleProjects(userId);
    }

    @Override
    public List<Project> findPublicProjects() {
        return projectDao.findPublicProjects();
    }

    @Override
    public List<Project> findUserProjects(String userId) {
        if (!StringUtils.hasText(userId)) {
            return new ArrayList<>();
        }
        return projectDao.findUserProjects(userId);
    }

    @Override
    public ReturnT<String> updateStatus(Long id, Integer status, String loginUser) {
        if (id == null) {
            return new ReturnT<>(ReturnT.FAIL_CODE, "项目ID不能为空");
        }
        if (status == null) {
            return new ReturnT<>(ReturnT.FAIL_CODE, "状态不能为空");
        }

        Project.Status projectStatus = Project.Status.getByCode(status);
        if (projectStatus == null) {
            return new ReturnT<>(ReturnT.FAIL_CODE, "无效的状态值");
        }

        // 权限检查：只有项目管理员可以修改状态
        if (!isProjectAdmin(id, loginUser)) {
            return new ReturnT<>(ReturnT.FAIL_CODE, "无权限修改项目状态，仅项目管理员可操作");
        }

        try {
            int result = projectDao.updateStatus(id, status, loginUser);
            if (result < 1) {
                return new ReturnT<>(ReturnT.FAIL_CODE, "更新项目状态失败");
            }
            return new ReturnT<>("更新项目状态成功");
        } catch (Exception e) {
            log.error("更新项目状态失败", e);
            return new ReturnT<>(ReturnT.FAIL_CODE, "更新项目状态失败：" + e.getMessage());
        }
    }

    @Override
    public ReturnT<String> updateType(Long id, String type, String loginUser) {
        if (id == null) {
            return new ReturnT<>(ReturnT.FAIL_CODE, "项目ID不能为空");
        }
        if (!StringUtils.hasText(type)) {
            return new ReturnT<>(ReturnT.FAIL_CODE, "项目类型不能为空");
        }

        Project.ProjectType projectType = Project.ProjectType.getByCode(type);
        if (projectType == null) {
            return new ReturnT<>(ReturnT.FAIL_CODE, "无效的项目类型");
        }

        // 权限检查：只有项目管理员可以修改类型
        if (!isProjectAdmin(id, loginUser)) {
            return new ReturnT<>(ReturnT.FAIL_CODE, "无权限修改项目类型，仅项目管理员可操作");
        }

        try {
            int result = projectDao.updateType(id, type, loginUser);
            if (result < 1) {
                return new ReturnT<>(ReturnT.FAIL_CODE, "更新项目类型失败");
            }
            return new ReturnT<>("更新项目类型成功");
        } catch (Exception e) {
            log.error("更新项目类型失败", e);
            return new ReturnT<>(ReturnT.FAIL_CODE, "更新项目类型失败：" + e.getMessage());
        }
    }

    @Override
    public boolean hasProjectAccess(Long projectId, String userId) {
        if (projectId == null || !StringUtils.hasText(userId)) {
            return false;
        }

        Project project = projectDao.load(projectId);
        if (project == null) {
            return false;
        }

        // 公共项目所有人都可以访问
        if (Project.ProjectType.PUBLIC.getCode().equals(project.getType())) {
            return true;
        }

        // 私有项目需要检查是否为项目成员
        ProjectMember member = projectMemberDao.loadByProjectAndUser(projectId, userId);
        return member != null && ProjectMember.Status.ENABLED.getCode().equals(member.getStatus());
    }

    @Override
    public boolean isProjectAdmin(Long projectId, String userId) {
        if (projectId == null || !StringUtils.hasText(userId)) {
            return false;
        }

        ProjectMember member = projectMemberDao.loadByProjectAndUser(projectId, userId);
        return member != null 
            && ProjectMember.Role.ADMIN.getCode().equals(member.getRole())
            && ProjectMember.Status.ENABLED.getCode().equals(member.getStatus());
    }

    @Override
    public String getUserRole(Long projectId, String userId) {
        if (projectId == null || !StringUtils.hasText(userId)) {
            return null;
        }

        ProjectMember member = projectMemberDao.loadByProjectAndUser(projectId, userId);
        return member != null ? member.getRole() : null;
    }

    @Override
    public List<Map<String, Object>> getProjectTypes() {
        List<Map<String, Object>> types = new ArrayList<>();
        for (Project.ProjectType type : Project.ProjectType.values()) {
            Map<String, Object> typeMap = new HashMap<>();
            typeMap.put("code", type.getCode());
            typeMap.put("name", type.getName());
            types.add(typeMap);
        }
        return types;
    }

    @Override
    public List<Project> findAllEnabled() {
        return projectDao.findAllEnabled();
    }

    @Override
    public List<Project> findByType(String type) {
        if (!StringUtils.hasText(type)) {
            return new ArrayList<>();
        }
        return projectDao.findByType(type);
    }
}
