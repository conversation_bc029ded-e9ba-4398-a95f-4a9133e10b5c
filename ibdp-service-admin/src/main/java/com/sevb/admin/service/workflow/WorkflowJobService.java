package com.sevb.admin.service.workflow;

import com.fasterxml.jackson.databind.JsonNode;
import com.sevb.admin.core.model.JobGroup;
import com.sevb.admin.core.model.JobInfo;
import com.sevb.admin.core.model.WorkflowNode;
import com.sevb.admin.core.util.DataxConfigGenerator;
import com.sevb.admin.dao.JobGroupDao;
import com.sevb.admin.dao.JobInfoDao;
import com.sevb.admin.dao.WorkflowNodeDao;
import com.sevb.core.biz.model.ReturnT;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;

/**
 * 工作流XXL-JOB任务管理服务
 * 负责在工作流上线时自动创建对应的XXL-JOB任务
 * 
 * <AUTHOR>
 */
@Service
@Slf4j
public class WorkflowJobService {
    
    @Autowired
    private JobInfoDao jobInfoDao;
    
    @Autowired
    private JobGroupDao jobGroupDao;
    
    @Autowired
    private WorkflowNodeDao workflowNodeDao;

    @Autowired
    private DataxConfigGenerator dataxConfigGenerator;

    private final ObjectMapper objectMapper = new ObjectMapper();
    
    /**
     * 节点类型与Handler名称的映射
     */
    private static final Map<String, String> NODE_TYPE_HANDLER_MAP = new HashMap<>();
    
    static {
        // 使用工作流执行器统一调度所有节点
        NODE_TYPE_HANDLER_MAP.put("WORKFLOW", "workflowExecutor");
        // 保留单节点执行器用于测试
        NODE_TYPE_HANDLER_MAP.put("SQL", "sqlNodeExecutor");
        NODE_TYPE_HANDLER_MAP.put("DATAX", "dataxNodeExecutor");
        NODE_TYPE_HANDLER_MAP.put("SHELL", "shellNodeExecutor");
        NODE_TYPE_HANDLER_MAP.put("PYTHON", "pythonNodeExecutor");
    }
    
    /**
     * 工作流上线时创建所需的XXL-JOB任务
     *
     * @param workflowId 工作流ID
     * @return 操作结果
     */
    @Transactional
    public ReturnT<String> createJobsForWorkflow(Long workflowId) {
        try {
            log.info("开始为工作流{}创建XXL-JOB任务", workflowId);

            // 1. 获取工作流的所有节点
            List<WorkflowNode> nodes = workflowNodeDao.listByWorkflow(workflowId);
            if (nodes.isEmpty()) {
                log.warn("工作流{}没有节点，跳过创建任务", workflowId);
                return ReturnT.SUCCESS;
            }

            // 2. 为每个节点创建对应的JobInfo（包含具体配置）
            int createdCount = 0;
            int updatedCount = 0;

            for (WorkflowNode node : nodes) {
                try {
                    ReturnT<String> result = createJobForNode(node);
                    if (result.getCode() == ReturnT.SUCCESS_CODE) {
                        String msg = result.getMsg();
                        if (msg != null && msg.contains("创建")) {
                            createdCount++;
                        } else {
                            updatedCount++;
                        }
                        log.info("节点{}任务处理成功: {}", node.getNodeCode(), msg != null ? msg : "成功");
                    } else {
                        log.error("节点{}任务处理失败: {}", node.getNodeCode(), result.getMsg() != null ? result.getMsg() : "未知错误");
                    }
                } catch (Exception e) {
                    log.error("处理节点{}任务异常", node.getNodeCode(), e);
                }
            }

            // 注意：不再创建额外的工作流调度任务，手动执行通过节点依赖关系实现

            String message = String.format("工作流%d任务创建完成，节点任务：创建%d个，更新%d个",
                workflowId, createdCount, updatedCount);
            log.info(message);
            return new ReturnT<>(message);

        } catch (Exception e) {
            log.error("为工作流{}创建XXL-JOB任务失败", workflowId, e);
            return new ReturnT<>(ReturnT.FAIL_CODE, "创建任务失败: " + e.getMessage());
        }
    }

    /**
     * 创建工作流级别的调度任务（用于手动执行，不更新workflow表的job_id）
     */
    private ReturnT<String> createWorkflowJob(Long workflowId, List<WorkflowNode> nodes) {
        try {
            // 构建完整的工作流定义JSON
            String workflowJson = buildWorkflowDefinitionJson(workflowId, nodes);

            // 创建工作流JobInfo
            JobInfo jobInfo = new JobInfo();
            Date now = new Date();

            // 基本信息
            jobInfo.setJobGroup(getDefaultJobGroupId());
            jobInfo.setJobDesc("工作流调度任务 - " + workflowId);
            jobInfo.setAddTime(now);
            jobInfo.setUpdateTime(now);
            jobInfo.setAuthor("system");
            jobInfo.setAlarmEmail("");

            // 调度配置
            jobInfo.setScheduleType("NONE"); // 由工作流调度服务控制
            jobInfo.setScheduleConf("");
            jobInfo.setMisfireStrategy("DO_NOTHING");

            // 执行器配置
            jobInfo.setExecutorRouteStrategy("FIRST");
            jobInfo.setExecutorHandler("workflowExecutor"); // 使用工作流执行器
            jobInfo.setExecutorParam("");
            jobInfo.setExecutorBlockStrategy("SERIAL_EXECUTION");
            jobInfo.setExecutorTimeout(0); // 工作流级别不设置超时
            jobInfo.setExecutorFailRetryCount(0); // 工作流级别不重试

            // Glue配置
            jobInfo.setGlueType("BEAN");
            jobInfo.setGlueSource("");
            jobInfo.setGlueRemark("工作流任务自动生成");
            jobInfo.setGlueUpdatetime(now);

            jobInfo.setChildJobId("");
            jobInfo.setTriggerStatus(0);
            jobInfo.setTriggerLastTime(0);
            jobInfo.setTriggerNextTime(0);

            // 设置工作流定义JSON
            jobInfo.setJobJson(workflowJson);
            log.info("为工作流{}生成job_json: {}", workflowId, workflowJson);

            // 保存JobInfo
            int result = jobInfoDao.save(jobInfo);
            if (result > 0) {
                log.info("为工作流{}创建调度任务成功，ID: {}", workflowId, jobInfo.getId());
                return new ReturnT<>("创建工作流调度任务成功，ID: " + jobInfo.getId());
            } else {
                return new ReturnT<>(ReturnT.FAIL_CODE, "保存工作流调度任务失败");
            }

        } catch (Exception e) {
            log.error("创建工作流{}的JobInfo失败", workflowId, e);
            return new ReturnT<>(ReturnT.FAIL_CODE, "创建失败: " + e.getMessage());
        }
    }

    /**
     * 构建工作流定义JSON
     */
    private String buildWorkflowDefinitionJson(Long workflowId, List<WorkflowNode> nodes) throws Exception {
        Map<String, Object> workflowDef = new HashMap<>();

        // 工作流基本信息
        Map<String, Object> workflow = new HashMap<>();
        workflow.put("workflowId", workflowId);
        workflow.put("workflowName", "工作流-" + workflowId);
        workflowDef.put("workflow", workflow);

        // 执行配置
        Map<String, Object> execution = new HashMap<>();
        execution.put("executionType", "DAG");
        execution.put("parallelism", 1);
        execution.put("createTime", System.currentTimeMillis());
        workflowDef.put("execution", execution);

        // 节点列表
        List<Map<String, Object>> nodeList = new ArrayList<>();
        for (WorkflowNode node : nodes) {
            Map<String, Object> nodeMap = new HashMap<>();
            nodeMap.put("nodeCode", node.getNodeCode());
            nodeMap.put("nodeName", node.getNodeName());
            nodeMap.put("nodeType", node.getNodeType());
            nodeMap.put("description", node.getDescription());
            nodeMap.put("timeout", node.getTimeout());
            nodeMap.put("maxRetryTimes", node.getRetryTimes());
            nodeMap.put("failureStrategy", node.getFailureStrategy());
            nodeMap.put("priority", node.getPriority());
            nodeMap.put("workerGroup", node.getWorkerGroup());

            // 解析配置参数
            if (node.getConfigParams() != null && !node.getConfigParams().trim().isEmpty()) {
                Object configParams = objectMapper.readValue(node.getConfigParams(), Object.class);
                nodeMap.put("configParams", configParams);
            }

            nodeList.add(nodeMap);
        }
        workflowDef.put("nodes", nodeList);

        // 依赖关系（从数据库获取）
        List<Map<String, Object>> relations = getWorkflowRelations(workflowId);
        workflowDef.put("relations", relations);

        return objectMapper.writerWithDefaultPrettyPrinter().writeValueAsString(workflowDef);
    }

    /**
     * 获取工作流依赖关系
     */
    private List<Map<String, Object>> getWorkflowRelations(Long workflowId) {
        // TODO: 从数据库获取工作流的依赖关系
        // 这里先返回空列表，实际实现需要查询workflow_relation表
        return new ArrayList<>();
    }

    /**
     * 为单个节点创建JobInfo（公共方法，供WorkflowDesignerService调用）
     */
    public ReturnT<String> createJobForNode(WorkflowNode node) {
        try {
            String nodeType = node.getNodeType();
            String handlerName = NODE_TYPE_HANDLER_MAP.get(nodeType);

            if (handlerName == null) {
                log.warn("不支持的节点类型: {}", nodeType);
                return new ReturnT<>(ReturnT.FAIL_CODE, "不支持的节点类型: " + nodeType);
            }

            // 检查节点是否已有关联的JobInfo
            JobInfo existingJob = null;
            if (node.getJobId() != null && node.getJobId() > 0) {
                existingJob = jobInfoDao.loadById(node.getJobId().intValue());
            }

            if (existingJob != null) {
                // 更新现有JobInfo
                return updateJobForNode(existingJob, node);
            } else {
                // 创建新的JobInfo
                return createNewJobForNode(node, handlerName);
            }

        } catch (Exception e) {
            log.error("为节点{}创建任务失败", node.getNodeCode(), e);
            return new ReturnT<>(ReturnT.FAIL_CODE, "创建任务失败: " + e.getMessage());
        }
    }
    
    /**
     * 创建新的JobInfo
     */
    private ReturnT<String> createNewJobForNode(WorkflowNode node, String handlerName) {
        try {
            JobInfo jobInfo = new JobInfo();
            Date now = new Date();

            // 基本信息
            jobInfo.setJobGroup(getDefaultJobGroupId());
            jobInfo.setJobDesc(node.getNodeName() + " - " + node.getNodeType());
            jobInfo.setAddTime(now);
            jobInfo.setUpdateTime(now);
            jobInfo.setAuthor("system");
            jobInfo.setAlarmEmail("");

            // 调度配置
            jobInfo.setScheduleType("NONE"); // 由工作流引擎控制
            jobInfo.setScheduleConf("");
            jobInfo.setMisfireStrategy("DO_NOTHING");

            // 执行器配置
            jobInfo.setExecutorRouteStrategy("FIRST");
            jobInfo.setExecutorHandler(handlerName);
            jobInfo.setExecutorParam("");
            jobInfo.setExecutorBlockStrategy("SERIAL_EXECUTION");
            jobInfo.setExecutorTimeout(node.getTimeout() != null ? node.getTimeout() : 0);
            jobInfo.setExecutorFailRetryCount(node.getRetryTimes() != null ? node.getRetryTimes() : 0);

            // Glue配置
            jobInfo.setGlueType("BEAN");
            jobInfo.setGlueSource("");
            jobInfo.setGlueRemark("工作流节点自动生成");
            jobInfo.setGlueUpdatetime(now);

            jobInfo.setChildJobId("");
            jobInfo.setTriggerStatus(0);
            jobInfo.setTriggerLastTime(0);
            jobInfo.setTriggerNextTime(0);

            // 生成job_json配置
            String jobJson = generateJobJsonForNode(node);
            jobInfo.setJobJson(jobJson);
            log.info("为节点{}生成job_json: {}", node.getNodeCode(), jobJson);

            // 保存JobInfo
            int result = jobInfoDao.save(jobInfo);
            if (result > 0) {
                // 更新节点的job_id
                node.setJobId(Long.valueOf(jobInfo.getId()));
                workflowNodeDao.update(node);

                log.info("为节点{}创建JobInfo成功，ID: {}", node.getNodeCode(), jobInfo.getId());
                return new ReturnT<>("创建JobInfo成功，ID: " + jobInfo.getId());
            } else {
                return new ReturnT<>(ReturnT.FAIL_CODE, "保存JobInfo失败");
            }

        } catch (Exception e) {
            log.error("创建节点{}的JobInfo失败", node.getNodeCode(), e);
            return new ReturnT<>(ReturnT.FAIL_CODE, "创建失败: " + e.getMessage());
        }
    }

    /**
     * 更新节点对应的JobInfo（公共方法，供WorkflowDesignerService调用）
     */
    public ReturnT<String> updateJobForNode(WorkflowNode node) {
        try {
            // 检查节点是否已有关联的JobInfo
            if (node.getJobId() == null || node.getJobId() <= 0) {
                return new ReturnT<>(ReturnT.FAIL_CODE, "节点没有关联的JobInfo");
            }

            JobInfo existingJob = jobInfoDao.loadById(node.getJobId().intValue());
            if (existingJob == null) {
                return new ReturnT<>(ReturnT.FAIL_CODE, "关联的JobInfo不存在");
            }

            return updateJobForNode(existingJob, node);

        } catch (Exception e) {
            log.error("更新节点{}的JobInfo失败", node.getNodeCode(), e);
            return new ReturnT<>(ReturnT.FAIL_CODE, "更新失败: " + e.getMessage());
        }
    }

    /**
     * 更新现有的JobInfo（内部方法）
     */
    private ReturnT<String> updateJobForNode(JobInfo jobInfo, WorkflowNode node) {
        try {
            Date now = new Date();

            // 更新基本信息
            jobInfo.setJobDesc(node.getNodeName() + " - " + node.getNodeType());
            jobInfo.setUpdateTime(now);
            jobInfo.setExecutorTimeout(node.getTimeout() != null ? node.getTimeout() : 0);
            jobInfo.setExecutorFailRetryCount(node.getRetryTimes() != null ? node.getRetryTimes() : 0);

            // 重新生成job_json配置
            String jobJson = generateJobJsonForNode(node);
            jobInfo.setJobJson(jobJson);
            log.info("为节点{}更新job_json: {}", node.getNodeCode(), jobJson);

            // 更新JobInfo
            int result = jobInfoDao.update(jobInfo);
            if (result > 0) {
                log.info("更新节点{}的JobInfo成功，ID: {}", node.getNodeCode(), jobInfo.getId());
                return new ReturnT<>("更新JobInfo成功，ID: " + jobInfo.getId());
            } else {
                return new ReturnT<>(ReturnT.FAIL_CODE, "更新JobInfo失败");
            }

        } catch (Exception e) {
            log.error("更新节点{}的JobInfo失败", node.getNodeCode(), e);
            return new ReturnT<>(ReturnT.FAIL_CODE, "更新失败: " + e.getMessage());
        }
    }

    /**
     * 为节点生成job_json配置
     */
    private String generateJobJsonForNode(WorkflowNode node) {
        try {
            String nodeType = node.getNodeType();
            String configParams = node.getConfigParams();

            if ("DATAX".equals(nodeType)) {
                // 生成DataX配置JSON
                if (configParams != null && !configParams.trim().isEmpty()) {
                    String dataxJson = dataxConfigGenerator.generateDataxConfig(configParams);

                    // 将生成的DataX配置保存到节点的config_params中
                    updateNodeConfigWithDataxJson(node, dataxJson);

                    return dataxJson;
                } else {
                    log.warn("DataX节点{}配置参数为空", node.getNodeCode());
                    return "{}";
                }
            } else if ("SQL".equals(nodeType)) {
                // 生成SQL配置JSON
                return generateSqlJobJson(configParams);
            } else {
                // 其他节点类型的通用配置
                return generateGenericJobJson(node);
            }

        } catch (Exception e) {
            log.error("为节点{}生成job_json失败", node.getNodeCode(), e);
            return "{}";
        }
    }

    /**
     * 生成SQL节点的job_json
     */
    private String generateSqlJobJson(String configParams) {
        try {
            Map<String, Object> sqlConfig = new HashMap<>();
            sqlConfig.put("type", "SQL");

            if (configParams != null && !configParams.trim().isEmpty()) {
                Map<String, Object> config = objectMapper.readValue(configParams, Map.class);
                sqlConfig.put("config", config);
            } else {
                sqlConfig.put("config", new HashMap<>());
            }

            return objectMapper.writeValueAsString(sqlConfig);
        } catch (Exception e) {
            log.error("生成SQL配置失败", e);
            return "{\"type\": \"SQL\", \"config\": {}}";
        }
    }

    /**
     * 生成通用节点的job_json
     */
    private String generateGenericJobJson(WorkflowNode node) {
        try {
            Map<String, Object> genericConfig = new HashMap<>();
            genericConfig.put("type", node.getNodeType());
            genericConfig.put("nodeCode", node.getNodeCode());
            genericConfig.put("nodeName", node.getNodeName());

            if (node.getConfigParams() != null && !node.getConfigParams().trim().isEmpty()) {
                Map<String, Object> config = objectMapper.readValue(node.getConfigParams(), Map.class);
                genericConfig.put("config", config);
            } else {
                genericConfig.put("config", new HashMap<>());
            }

            return objectMapper.writeValueAsString(genericConfig);
        } catch (Exception e) {
            log.error("生成通用配置失败", e);
            return "{\"type\": \"" + node.getNodeType() + "\"}";
        }
    }

    /**
     * 获取默认的JobGroup ID
     */
    private int getDefaultJobGroupId() {
        List<JobGroup> jobGroups = jobGroupDao.findAll();
        return jobGroups.isEmpty() ? 1 : jobGroups.get(0).getId();
    }

    /**
     * 为指定节点类型创建XXL-JOB任务
     *
     * @param nodeType 节点类型
     */
    private void createJobForNodeType(String nodeType) {
        String handlerName = NODE_TYPE_HANDLER_MAP.get(nodeType);
        if (handlerName == null) {
            log.warn("不支持的节点类型: {}", nodeType);
            return;
        }
        
        // 检查任务是否已存在
        JobInfo existingJob = jobInfoDao.loadByHandler(handlerName);
        if (existingJob != null) {
            log.debug("Handler {}的任务已存在，跳过创建", handlerName);
            return;
        }
        
        // 创建新任务
        JobInfo jobInfo = buildJobInfo(nodeType, handlerName);
        int result = jobInfoDao.save(jobInfo);
        
        if (result > 0) {
            log.info("成功创建{}节点的XXL-JOB任务，Handler: {}, JobId: {}", 
                nodeType, handlerName, jobInfo.getId());
        } else {
            log.error("创建{}节点的XXL-JOB任务失败", nodeType);
        }
    }
    
    /**
     * 构建JobInfo对象
     * 
     * @param nodeType 节点类型
     * @param handlerName Handler名称
     * @return JobInfo对象
     */
    private JobInfo buildJobInfo(String nodeType, String handlerName) {
        JobInfo jobInfo = new JobInfo();
        
        // 获取执行器组ID（假设使用第一个执行器组）
        List<JobGroup> jobGroups = jobGroupDao.findAll();
        int jobGroupId = jobGroups.isEmpty() ? 1 : jobGroups.get(0).getId();
        
        jobInfo.setJobGroup(jobGroupId);
        jobInfo.setJobDesc(nodeType + "节点执行器");
        jobInfo.setAddTime(new Date());
        jobInfo.setUpdateTime(new Date());
        jobInfo.setAuthor("system");
        jobInfo.setAlarmEmail("");
        
        // 调度配置 - 设置为手动触发
        jobInfo.setScheduleType("NONE");
        jobInfo.setScheduleConf("");
        jobInfo.setMisfireStrategy("DO_NOTHING");

        // 运行模式 - BEAN模式
        jobInfo.setGlueType("BEAN");
        jobInfo.setGlueSource("");
        jobInfo.setGlueRemark("");
        jobInfo.setGlueUpdatetime(new Date());
        jobInfo.setExecutorHandler(handlerName);
        jobInfo.setExecutorParam("");
        jobInfo.setExecutorRouteStrategy("FIRST");
        jobInfo.setExecutorBlockStrategy("SERIAL_EXECUTION");
        jobInfo.setExecutorTimeout(0);
        jobInfo.setExecutorFailRetryCount(0);

        jobInfo.setChildJobId("");
        jobInfo.setTriggerStatus(0); // 停止状态
        jobInfo.setTriggerLastTime(0);
        jobInfo.setTriggerNextTime(0);
        
        return jobInfo;
    }
    
    /**
     * 删除工作流相关的XXL-JOB任务
     * 
     * @param workflowId 工作流ID
     * @return 操作结果
     */
    @Transactional
    public ReturnT<String> deleteJobsForWorkflow(Long workflowId) {
        try {
            log.info("开始删除工作流{}的XXL-JOB任务", workflowId);
            
            // 获取工作流的所有节点
            List<WorkflowNode> nodes = workflowNodeDao.listByWorkflow(workflowId);
            if (nodes.isEmpty()) {
                log.warn("工作流{}没有节点，跳过删除任务", workflowId);
                return ReturnT.SUCCESS;
            }
            
            // 获取需要删除的节点类型
            Set<String> nodeTypes = new HashSet<>();
            for (WorkflowNode node : nodes) {
                nodeTypes.add(node.getNodeType());
            }
            
            // 删除每种节点类型的XXL-JOB任务
            for (String nodeType : nodeTypes) {
                deleteJobForNodeType(nodeType);
            }
            
            log.info("工作流{}的XXL-JOB任务删除完成", workflowId);
            return ReturnT.SUCCESS;
            
        } catch (Exception e) {
            log.error("删除工作流{}的XXL-JOB任务失败", workflowId, e);
            return new ReturnT<>(ReturnT.FAIL_CODE, "删除任务失败: " + e.getMessage());
        }
    }
    
    /**
     * 删除指定节点类型的XXL-JOB任务
     * 
     * @param nodeType 节点类型
     */
    private void deleteJobForNodeType(String nodeType) {
        String handlerName = NODE_TYPE_HANDLER_MAP.get(nodeType);
        if (handlerName == null) {
            log.warn("不支持的节点类型: {}", nodeType);
            return;
        }
        
        // 查找任务
        JobInfo jobInfo = jobInfoDao.loadByHandler(handlerName);
        if (jobInfo == null) {
            log.debug("Handler {}的任务不存在，跳过删除", handlerName);
            return;
        }
        
        // 删除任务
        int result = jobInfoDao.delete(jobInfo.getId());
        
        if (result > 0) {
            log.info("成功删除{}节点的XXL-JOB任务，Handler: {}, JobId: {}", 
                nodeType, handlerName, jobInfo.getId());
        } else {
            log.error("删除{}节点的XXL-JOB任务失败", nodeType);
        }
    }
    
    /**
     * 检查指定节点类型的XXL-JOB任务是否存在
     * 
     * @param nodeType 节点类型
     * @return 是否存在
     */
    public boolean isJobExistForNodeType(String nodeType) {
        String handlerName = NODE_TYPE_HANDLER_MAP.get(nodeType);
        if (handlerName == null) {
            return false;
        }
        
        JobInfo jobInfo = jobInfoDao.loadByHandler(handlerName);
        return jobInfo != null;
    }
    
    /**
     * 获取所有支持的节点类型
     *
     * @return 节点类型列表
     */
    public Set<String> getSupportedNodeTypes() {
        return NODE_TYPE_HANDLER_MAP.keySet();
    }

    /**
     * 更新节点配置，添加生成的DataX JSON（保留原始配置数据）
     */
    private void updateNodeConfigWithDataxJson(WorkflowNode node, String dataxJson) {
        try {
            String configParams = node.getConfigParams();
            ObjectMapper mapper = new ObjectMapper();

            // 解析现有的配置参数（保留原始配置）
            com.fasterxml.jackson.databind.node.ObjectNode configNode;
            if (configParams != null && !configParams.trim().isEmpty()) {
                configNode = (com.fasterxml.jackson.databind.node.ObjectNode) mapper.readTree(configParams);
            } else {
                configNode = mapper.createObjectNode();
            }

            // 将DataX JSON解析为JsonNode，而不是字符串
            JsonNode dataxJsonNode = mapper.readTree(dataxJson);

            // 添加生成的DataX配置（保留原有的source、target、columnMappings等配置）
            configNode.set("dataxConfig", dataxJsonNode);
            configNode.put("configType", "DATAX");
            configNode.put("generatedAt", System.currentTimeMillis());

            // 更新节点的配置参数
            String updatedConfigParams = mapper.writeValueAsString(configNode);
            node.setConfigParams(updatedConfigParams);

            // 保存到数据库
            workflowNodeDao.update(node);

            log.info("已将DataX配置保存到节点{}的config_params中，保留原始配置，配置大小: {} 字符",
                node.getNodeCode(), updatedConfigParams.length());

        } catch (Exception e) {
            log.error("更新节点{}的config_params失败", node.getNodeCode(), e);
        }
    }


}
