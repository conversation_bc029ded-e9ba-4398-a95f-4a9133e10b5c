package com.sevb.admin.service;

import com.sevb.admin.core.model.NodeInstance;
import com.sevb.core.biz.model.ReturnT;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 节点实例服务接口
 * 
 * <AUTHOR>
 */
public interface NodeInstanceService {

    /**
     * 分页查询节点实例
     */
    ReturnT<Map<String, Object>> pageList(int page, int size, Long projectId, Long workflowInstanceId, Long workflowId,
                                         Long nodeId, String nodeName, String nodeType, Integer status,
                                         Date startTime, Date endTime);

    /**
     * 根据ID查询节点实例
     */
    ReturnT<NodeInstance> getById(Long id);

    /**
     * 根据工作流实例ID查询节点实例
     */
    ReturnT<Map<String, Object>> getByWorkflowInstanceId(Long workflowInstanceId);

    /**
     * 重试节点实例
     */
    ReturnT<String> retryNodeInstance(Long id);

    /**
     * 停止节点实例
     */
    ReturnT<String> stopNodeInstance(Long id);

    /**
     * 查询节点实例日志
     */
    ReturnT<String> getNodeInstanceLog(Long id);

    /**
     * 查询节点统计信息
     */
    ReturnT<Map<String, Object>> getNodeStats(Long nodeId, Integer days);

    /**
     * 查询节点的最近实例
     */
    ReturnT<Map<String, Object>> getRecentNodeInstances(Long nodeId, Integer limit);

    /**
     * 创建节点实例
     */
    ReturnT<Long> createNodeInstance(NodeInstance nodeInstance);

    /**
     * 批量创建节点实例
     */
    ReturnT<String> batchCreateNodeInstances(List<NodeInstance> nodeInstances);

    /**
     * 更新节点实例状态
     */
    ReturnT<String> updateNodeInstanceStatus(Long id, Integer status, String errorMessage, String executeLog);

    /**
     * 根据工作流实例ID查询正在运行的节点实例
     */
    ReturnT<List<NodeInstance>> getRunningNodeInstances(Long workflowInstanceId);
}
