package com.sevb.admin.service.impl;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.sevb.admin.core.alarm.template.TemplateEngine;
import com.sevb.admin.core.model.AlertTemplate;
import com.sevb.admin.dao.AlertTemplateDao;
import com.sevb.admin.service.AlertTemplateService;
import com.sevb.core.biz.model.ReturnT;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.*;

/**
 * 告警模板服务实现
 * <AUTHOR>
 */
@Service
public class AlertTemplateServiceImpl implements AlertTemplateService {

    private static final Logger logger = LoggerFactory.getLogger(AlertTemplateServiceImpl.class);

    @Autowired
    private AlertTemplateDao alertTemplateDao;

    @Autowired
    private TemplateEngine templateEngine;

    private final ObjectMapper objectMapper = new ObjectMapper();

    @Override
    public Map<String, Object> pageList(int start, int length, String templateCode, 
                                       String templateName, String channelType, Integer enabled) {
        // 分页查询
        List<AlertTemplate> list = alertTemplateDao.pageList(start, length, templateCode, templateName, channelType, enabled);
        int listCount = alertTemplateDao.pageListCount(templateCode, templateName, channelType, enabled);

        // 构建返回结果
        Map<String, Object> maps = new HashMap<>();
        maps.put("recordsTotal", listCount);
        maps.put("recordsFiltered", listCount);
        maps.put("data", list);
        return maps;
    }

    @Override
    public List<AlertTemplate> findAllEnabled() {
        return alertTemplateDao.findAllEnabled();
    }

    @Override
    public AlertTemplate load(Long id) {
        if (id == null) {
            return null;
        }
        return alertTemplateDao.load(id);
    }

    @Override
    public AlertTemplate findByCodeAndChannel(String templateCode, String channelType) {
        if (!StringUtils.hasText(templateCode) || !StringUtils.hasText(channelType)) {
            return null;
        }
        return alertTemplateDao.findByCodeAndChannel(templateCode, channelType);
    }

    @Override
    public List<AlertTemplate> findByTemplateCode(String templateCode) {
        if (!StringUtils.hasText(templateCode)) {
            return new ArrayList<>();
        }
        return alertTemplateDao.findByTemplateCode(templateCode);
    }

    @Override
    public List<AlertTemplate> findByChannelType(String channelType) {
        if (!StringUtils.hasText(channelType)) {
            return new ArrayList<>();
        }
        return alertTemplateDao.findByChannelType(channelType);
    }

    @Override
    public ReturnT<String> save(AlertTemplate alertTemplate) {
        // 参数验证
        if (alertTemplate == null) {
            return new ReturnT<>(ReturnT.FAIL_CODE, "模板信息不能为空");
        }

        if (!StringUtils.hasText(alertTemplate.getTemplateCode())) {
            return new ReturnT<>(ReturnT.FAIL_CODE, "模板编码不能为空");
        }

        if (!StringUtils.hasText(alertTemplate.getTemplateName())) {
            return new ReturnT<>(ReturnT.FAIL_CODE, "模板名称不能为空");
        }

        if (!StringUtils.hasText(alertTemplate.getChannelType())) {
            return new ReturnT<>(ReturnT.FAIL_CODE, "通道类型不能为空");
        }

        if (!StringUtils.hasText(alertTemplate.getTemplateContent())) {
            return new ReturnT<>(ReturnT.FAIL_CODE, "模板内容不能为空");
        }

        // 验证模板编码和通道类型组合是否已存在
        int existCount = alertTemplateDao.checkCodeAndChannelExists(
            alertTemplate.getTemplateCode(), alertTemplate.getChannelType(), null);
        if (existCount > 0) {
            return new ReturnT<>(ReturnT.FAIL_CODE, "模板编码和通道类型组合已存在");
        }

        // 验证模板内容
        ReturnT<String> validateResult = validateTemplateVariables(
            alertTemplate.getTemplateContent(), alertTemplate.getVariables());
        if (validateResult.getCode() != ReturnT.SUCCESS_CODE) {
            return validateResult;
        }

        // 设置默认值
        if (alertTemplate.getEnabled() == null) {
            alertTemplate.setEnabled(1);
        }

        try {
            int result = alertTemplateDao.save(alertTemplate);
            return result > 0 ? ReturnT.SUCCESS : new ReturnT<>(ReturnT.FAIL_CODE, "保存失败");
        } catch (Exception e) {
            logger.error("保存告警模板失败", e);
            return new ReturnT<>(ReturnT.FAIL_CODE, "保存失败: " + e.getMessage());
        }
    }

    @Override
    public ReturnT<String> update(AlertTemplate alertTemplate) {
        // 参数验证
        if (alertTemplate == null || alertTemplate.getId() == null) {
            return new ReturnT<>(ReturnT.FAIL_CODE, "模板信息不能为空");
        }

        // 检查模板是否存在
        AlertTemplate existTemplate = alertTemplateDao.load(alertTemplate.getId());
        if (existTemplate == null) {
            return new ReturnT<>(ReturnT.FAIL_CODE, "模板不存在");
        }

        if (!StringUtils.hasText(alertTemplate.getTemplateName())) {
            return new ReturnT<>(ReturnT.FAIL_CODE, "模板名称不能为空");
        }

        if (!StringUtils.hasText(alertTemplate.getTemplateContent())) {
            return new ReturnT<>(ReturnT.FAIL_CODE, "模板内容不能为空");
        }

        // 验证模板内容
        ReturnT<String> validateResult = validateTemplateVariables(
            alertTemplate.getTemplateContent(), alertTemplate.getVariables());
        if (validateResult.getCode() != ReturnT.SUCCESS_CODE) {
            return validateResult;
        }

        try {
            int result = alertTemplateDao.update(alertTemplate);
            return result > 0 ? ReturnT.SUCCESS : new ReturnT<>(ReturnT.FAIL_CODE, "更新失败");
        } catch (Exception e) {
            logger.error("更新告警模板失败", e);
            return new ReturnT<>(ReturnT.FAIL_CODE, "更新失败: " + e.getMessage());
        }
    }

    @Override
    public ReturnT<String> delete(Long id, String updateUser) {
        if (id == null) {
            return new ReturnT<>(ReturnT.FAIL_CODE, "模板ID不能为空");
        }

        try {
            int result = alertTemplateDao.delete(id, updateUser);
            return result > 0 ? ReturnT.SUCCESS : new ReturnT<>(ReturnT.FAIL_CODE, "删除失败");
        } catch (Exception e) {
            logger.error("删除告警模板失败", e);
            return new ReturnT<>(ReturnT.FAIL_CODE, "删除失败: " + e.getMessage());
        }
    }

    @Override
    public ReturnT<String> updateEnabled(Long id, Integer enabled, String updateUser) {
        if (id == null) {
            return new ReturnT<>(ReturnT.FAIL_CODE, "模板ID不能为空");
        }

        if (enabled == null || (enabled != 0 && enabled != 1)) {
            return new ReturnT<>(ReturnT.FAIL_CODE, "启用状态值无效");
        }

        try {
            int result = alertTemplateDao.updateEnabled(id, enabled, updateUser);
            return result > 0 ? ReturnT.SUCCESS : new ReturnT<>(ReturnT.FAIL_CODE, "更新状态失败");
        } catch (Exception e) {
            logger.error("更新告警模板状态失败", e);
            return new ReturnT<>(ReturnT.FAIL_CODE, "更新状态失败: " + e.getMessage());
        }
    }

    @Override
    public ReturnT<String> batchDelete(List<Long> ids, String updateUser) {
        if (ids == null || ids.isEmpty()) {
            return new ReturnT<>(ReturnT.FAIL_CODE, "模板ID列表不能为空");
        }

        try {
            int result = alertTemplateDao.batchDelete(ids, updateUser);
            return result > 0 ? ReturnT.SUCCESS : new ReturnT<>(ReturnT.FAIL_CODE, "批量删除失败");
        } catch (Exception e) {
            logger.error("批量删除告警模板失败", e);
            return new ReturnT<>(ReturnT.FAIL_CODE, "批量删除失败: " + e.getMessage());
        }
    }

    @Override
    public String renderTemplate(String templateCode, String channelType, Map<String, Object> variables) {
        if (!StringUtils.hasText(templateCode) || !StringUtils.hasText(channelType)) {
            throw new IllegalArgumentException("模板编码和通道类型不能为空");
        }

        AlertTemplate template = alertTemplateDao.findByCodeAndChannel(templateCode, channelType);
        if (template == null) {
            throw new IllegalArgumentException("模板不存在: " + templateCode + "/" + channelType);
        }

        if (template.getEnabled() != 1) {
            throw new IllegalArgumentException("模板已禁用: " + templateCode + "/" + channelType);
        }

        try {
            return templateEngine.render(template.getTemplateContent(), variables);
        } catch (Exception e) {
            logger.error("渲染模板失败: templateCode={}, channelType={}", templateCode, channelType, e);
            throw new RuntimeException("渲染模板失败: " + e.getMessage(), e);
        }
    }

    @Override
    public ReturnT<String> validateTemplateVariables(String templateContent, String variables) {
        if (!StringUtils.hasText(templateContent)) {
            return new ReturnT<>(ReturnT.FAIL_CODE, "模板内容不能为空");
        }

        // 验证模板语法
        if (!templateEngine.validateTemplate(templateContent)) {
            return new ReturnT<>(ReturnT.FAIL_CODE, "模板语法错误");
        }

        // 验证变量定义格式
        if (StringUtils.hasText(variables)) {
            try {
                List<Map<String, Object>> variableList = objectMapper.readValue(variables, 
                    new TypeReference<List<Map<String, Object>>>() {});
                
                for (Map<String, Object> variable : variableList) {
                    if (!variable.containsKey("name") || !StringUtils.hasText((String) variable.get("name"))) {
                        return new ReturnT<>(ReturnT.FAIL_CODE, "变量定义中缺少name字段");
                    }
                }
            } catch (Exception e) {
                return new ReturnT<>(ReturnT.FAIL_CODE, "变量定义格式错误: " + e.getMessage());
            }
        }

        return ReturnT.SUCCESS;
    }

    @Override
    public List<Map<String, Object>> getTemplateVariables(String templateCode, String channelType) {
        if (!StringUtils.hasText(templateCode) || !StringUtils.hasText(channelType)) {
            return new ArrayList<>();
        }

        AlertTemplate template = alertTemplateDao.findByCodeAndChannel(templateCode, channelType);
        if (template == null || !StringUtils.hasText(template.getVariables())) {
            return new ArrayList<>();
        }

        try {
            return objectMapper.readValue(template.getVariables(), 
                new TypeReference<List<Map<String, Object>>>() {});
        } catch (Exception e) {
            logger.error("解析模板变量失败: templateCode={}, channelType={}", templateCode, channelType, e);
            return new ArrayList<>();
        }
    }
}
