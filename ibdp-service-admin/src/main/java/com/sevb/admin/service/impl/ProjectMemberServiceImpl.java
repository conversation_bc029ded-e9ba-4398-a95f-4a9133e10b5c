package com.sevb.admin.service.impl;

import com.sevb.admin.core.model.ProjectMember;
import com.sevb.admin.dao.ProjectMemberDao;
import com.sevb.admin.service.ProjectMemberService;
import com.sevb.admin.service.ProjectService;
import com.sevb.core.biz.model.ReturnT;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import jakarta.annotation.Resource;
import java.util.*;

/**
 * 项目成员管理服务实现类
 * <AUTHOR>
 */
@Slf4j
@Service
public class ProjectMemberServiceImpl implements ProjectMemberService {

    @Resource
    private ProjectMemberDao projectMemberDao;
    
    @Resource
    private ProjectService projectService;

    @Override
    public Map<String, Object> pageList(int offset, int pagesize, Long projectId, String username, String role, Integer status) {
        List<ProjectMember> list = projectMemberDao.pageList(offset, pagesize, projectId, username, role, status);
        int listCount = projectMemberDao.pageListCount(projectId, username, role, status);

        // 组装分页结果
        Map<String, Object> result = new HashMap<>();
        result.put("recordsTotal", listCount);
        result.put("recordsFiltered", listCount);
        result.put("data", list);

        return result;
    }

    @Override
    public ReturnT<String> addMember(ProjectMember member, String loginUser) {
        // 参数验证
        if (member == null) {
            return new ReturnT<>(ReturnT.FAIL_CODE, "成员信息不能为空");
        }
        if (member.getProjectId() == null) {
            return new ReturnT<>(ReturnT.FAIL_CODE, "项目ID不能为空");
        }
        if (!StringUtils.hasText(member.getUserId())) {
            return new ReturnT<>(ReturnT.FAIL_CODE, "用户ID不能为空");
        }
        if (!StringUtils.hasText(member.getRole())) {
            return new ReturnT<>(ReturnT.FAIL_CODE, "角色不能为空");
        }

        // 验证角色
        ProjectMember.Role memberRole = ProjectMember.Role.getByCode(member.getRole());
        if (memberRole == null) {
            return new ReturnT<>(ReturnT.FAIL_CODE, "无效的角色");
        }

        // 权限检查：只有项目管理员可以添加成员
        if (!projectService.isProjectAdmin(member.getProjectId(), loginUser)) {
            return new ReturnT<>(ReturnT.FAIL_CODE, "无权限添加成员，仅项目管理员可操作");
        }

        // 检查用户是否已经是项目成员
        int memberExists = projectMemberDao.checkMemberExists(member.getProjectId(), member.getUserId());
        if (memberExists > 0) {
            return new ReturnT<>(ReturnT.FAIL_CODE, "用户已经是项目成员");
        }

        try {
            // 设置默认值
            if (member.getStatus() == null) {
                member.setStatus(ProjectMember.Status.ENABLED.getCode());
            }
            if (member.getJoinTime() == null) {
                member.setJoinTime(new Date());
            }
            if (!StringUtils.hasText(member.getUsername())) {
                member.setUsername(member.getUserId()); // 实际应该从用户服务获取用户名
            }
            member.setCreateUser(loginUser);
            member.setUpdateUser(loginUser);

            int result = projectMemberDao.save(member);
            if (result < 1) {
                return new ReturnT<>(ReturnT.FAIL_CODE, "添加成员失败");
            }
            return new ReturnT<>("添加成员成功");
        } catch (Exception e) {
            log.error("添加项目成员失败", e);
            return new ReturnT<>(ReturnT.FAIL_CODE, "添加成员失败：" + e.getMessage());
        }
    }

    @Override
    @Transactional
    public ReturnT<String> batchAddMembers(List<ProjectMember> members, String loginUser) {
        if (members == null || members.isEmpty()) {
            return new ReturnT<>(ReturnT.FAIL_CODE, "成员列表不能为空");
        }

        Long projectId = members.get(0).getProjectId();
        if (projectId == null) {
            return new ReturnT<>(ReturnT.FAIL_CODE, "项目ID不能为空");
        }

        // 权限检查：只有项目管理员可以添加成员
        if (!projectService.isProjectAdmin(projectId, loginUser)) {
            return new ReturnT<>(ReturnT.FAIL_CODE, "无权限添加成员，仅项目管理员可操作");
        }

        try {
            List<ProjectMember> validMembers = new ArrayList<>();
            for (ProjectMember member : members) {
                // 参数验证
                if (!StringUtils.hasText(member.getUserId()) || !StringUtils.hasText(member.getRole())) {
                    continue;
                }

                // 验证角色
                ProjectMember.Role memberRole = ProjectMember.Role.getByCode(member.getRole());
                if (memberRole == null) {
                    continue;
                }

                // 检查用户是否已经是项目成员
                int memberExists = projectMemberDao.checkMemberExists(projectId, member.getUserId());
                if (memberExists > 0) {
                    continue;
                }

                // 设置默认值
                member.setProjectId(projectId);
                if (member.getStatus() == null) {
                    member.setStatus(ProjectMember.Status.ENABLED.getCode());
                }
                if (member.getJoinTime() == null) {
                    member.setJoinTime(new Date());
                }
                if (!StringUtils.hasText(member.getUsername())) {
                    member.setUsername(member.getUserId()); // 实际应该从用户服务获取用户名
                }
                member.setCreateUser(loginUser);
                member.setUpdateUser(loginUser);

                validMembers.add(member);
            }

            if (validMembers.isEmpty()) {
                return new ReturnT<>(ReturnT.FAIL_CODE, "没有有效的成员可添加");
            }

            int result = projectMemberDao.batchSave(validMembers);
            if (result < 1) {
                return new ReturnT<>(ReturnT.FAIL_CODE, "批量添加成员失败");
            }
            return new ReturnT<>("批量添加成员成功，共添加 " + validMembers.size() + " 个成员");
        } catch (Exception e) {
            log.error("批量添加项目成员失败", e);
            return new ReturnT<>(ReturnT.FAIL_CODE, "批量添加成员失败：" + e.getMessage());
        }
    }

    @Override
    public ReturnT<String> removeMember(Long id, String loginUser) {
        if (id == null) {
            return new ReturnT<>(ReturnT.FAIL_CODE, "成员ID不能为空");
        }

        ProjectMember member = projectMemberDao.load(id);
        if (member == null) {
            return new ReturnT<>(ReturnT.FAIL_CODE, "成员不存在");
        }

        // 权限检查：只有项目管理员可以移除成员
        if (!projectService.isProjectAdmin(member.getProjectId(), loginUser)) {
            return new ReturnT<>(ReturnT.FAIL_CODE, "无权限移除成员，仅项目管理员可操作");
        }

        // 检查是否为最后一个管理员
        if (ProjectMember.Role.ADMIN.getCode().equals(member.getRole())) {
            List<ProjectMember> admins = projectMemberDao.findAdminsByProjectId(member.getProjectId());
            if (admins.size() <= 1) {
                return new ReturnT<>(ReturnT.FAIL_CODE, "不能移除最后一个项目管理员");
            }
        }

        try {
            int result = projectMemberDao.delete(id, loginUser);
            if (result < 1) {
                return new ReturnT<>(ReturnT.FAIL_CODE, "移除成员失败");
            }
            return new ReturnT<>("移除成员成功");
        } catch (Exception e) {
            log.error("移除项目成员失败", e);
            return new ReturnT<>(ReturnT.FAIL_CODE, "移除成员失败：" + e.getMessage());
        }
    }

    @Override
    public ReturnT<String> removeMemberByProjectAndUser(Long projectId, String userId, String loginUser) {
        if (projectId == null) {
            return new ReturnT<>(ReturnT.FAIL_CODE, "项目ID不能为空");
        }
        if (!StringUtils.hasText(userId)) {
            return new ReturnT<>(ReturnT.FAIL_CODE, "用户ID不能为空");
        }

        ProjectMember member = projectMemberDao.loadByProjectAndUser(projectId, userId);
        if (member == null) {
            return new ReturnT<>(ReturnT.FAIL_CODE, "成员不存在");
        }

        // 权限检查：只有项目管理员可以移除成员
        if (!projectService.isProjectAdmin(projectId, loginUser)) {
            return new ReturnT<>(ReturnT.FAIL_CODE, "无权限移除成员，仅项目管理员可操作");
        }

        // 检查是否为最后一个管理员
        if (ProjectMember.Role.ADMIN.getCode().equals(member.getRole())) {
            List<ProjectMember> admins = projectMemberDao.findAdminsByProjectId(projectId);
            if (admins.size() <= 1) {
                return new ReturnT<>(ReturnT.FAIL_CODE, "不能移除最后一个项目管理员");
            }
        }

        try {
            int result = projectMemberDao.deleteByProjectAndUser(projectId, userId, loginUser);
            if (result < 1) {
                return new ReturnT<>(ReturnT.FAIL_CODE, "移除成员失败");
            }
            return new ReturnT<>("移除成员成功");
        } catch (Exception e) {
            log.error("移除项目成员失败", e);
            return new ReturnT<>(ReturnT.FAIL_CODE, "移除成员失败：" + e.getMessage());
        }
    }

    @Override
    public ReturnT<String> updateMemberRole(Long id, String role, String loginUser) {
        if (id == null) {
            return new ReturnT<>(ReturnT.FAIL_CODE, "成员ID不能为空");
        }
        if (!StringUtils.hasText(role)) {
            return new ReturnT<>(ReturnT.FAIL_CODE, "角色不能为空");
        }

        // 验证角色
        ProjectMember.Role memberRole = ProjectMember.Role.getByCode(role);
        if (memberRole == null) {
            return new ReturnT<>(ReturnT.FAIL_CODE, "无效的角色");
        }

        ProjectMember member = projectMemberDao.load(id);
        if (member == null) {
            return new ReturnT<>(ReturnT.FAIL_CODE, "成员不存在");
        }

        // 权限检查：只有项目管理员可以修改成员角色
        if (!projectService.isProjectAdmin(member.getProjectId(), loginUser)) {
            return new ReturnT<>(ReturnT.FAIL_CODE, "无权限修改成员角色，仅项目管理员可操作");
        }

        // 如果要将管理员降级，检查是否为最后一个管理员
        if (ProjectMember.Role.ADMIN.getCode().equals(member.getRole()) 
            && !ProjectMember.Role.ADMIN.getCode().equals(role)) {
            List<ProjectMember> admins = projectMemberDao.findAdminsByProjectId(member.getProjectId());
            if (admins.size() <= 1) {
                return new ReturnT<>(ReturnT.FAIL_CODE, "不能降级最后一个项目管理员");
            }
        }

        try {
            int result = projectMemberDao.updateRole(id, role, loginUser);
            if (result < 1) {
                return new ReturnT<>(ReturnT.FAIL_CODE, "更新成员角色失败");
            }
            return new ReturnT<>("更新成员角色成功");
        } catch (Exception e) {
            log.error("更新项目成员角色失败", e);
            return new ReturnT<>(ReturnT.FAIL_CODE, "更新成员角色失败：" + e.getMessage());
        }
    }

    @Override
    public ReturnT<String> updateMemberStatus(Long id, Integer status, String loginUser) {
        if (id == null) {
            return new ReturnT<>(ReturnT.FAIL_CODE, "成员ID不能为空");
        }
        if (status == null) {
            return new ReturnT<>(ReturnT.FAIL_CODE, "状态不能为空");
        }

        ProjectMember.Status memberStatus = ProjectMember.Status.getByCode(status);
        if (memberStatus == null) {
            return new ReturnT<>(ReturnT.FAIL_CODE, "无效的状态值");
        }

        ProjectMember member = projectMemberDao.load(id);
        if (member == null) {
            return new ReturnT<>(ReturnT.FAIL_CODE, "成员不存在");
        }

        // 权限检查：只有项目管理员可以修改成员状态
        if (!projectService.isProjectAdmin(member.getProjectId(), loginUser)) {
            return new ReturnT<>(ReturnT.FAIL_CODE, "无权限修改成员状态，仅项目管理员可操作");
        }

        try {
            int result = projectMemberDao.updateStatus(id, status, loginUser);
            if (result < 1) {
                return new ReturnT<>(ReturnT.FAIL_CODE, "更新成员状态失败");
            }
            return new ReturnT<>("更新成员状态成功");
        } catch (Exception e) {
            log.error("更新项目成员状态失败", e);
            return new ReturnT<>(ReturnT.FAIL_CODE, "更新成员状态失败：" + e.getMessage());
        }
    }

    @Override
    public ProjectMember load(Long id) {
        if (id == null) {
            return null;
        }
        return projectMemberDao.load(id);
    }

    @Override
    public ProjectMember loadByProjectAndUser(Long projectId, String userId) {
        if (projectId == null || !StringUtils.hasText(userId)) {
            return null;
        }
        return projectMemberDao.loadByProjectAndUser(projectId, userId);
    }

    @Override
    public List<ProjectMember> findByProjectId(Long projectId) {
        if (projectId == null) {
            return new ArrayList<>();
        }
        return projectMemberDao.findByProjectId(projectId);
    }

    @Override
    public List<ProjectMember> findByUserId(String userId) {
        if (!StringUtils.hasText(userId)) {
            return new ArrayList<>();
        }
        return projectMemberDao.findByUserId(userId);
    }

    @Override
    public List<ProjectMember> findAdminsByProjectId(Long projectId) {
        if (projectId == null) {
            return new ArrayList<>();
        }
        return projectMemberDao.findAdminsByProjectId(projectId);
    }

    @Override
    public boolean isMember(Long projectId, String userId) {
        if (projectId == null || !StringUtils.hasText(userId)) {
            return false;
        }
        return projectMemberDao.checkMemberExists(projectId, userId) > 0;
    }

    @Override
    public int countByProjectId(Long projectId) {
        if (projectId == null) {
            return 0;
        }
        return projectMemberDao.countByProjectId(projectId);
    }

    @Override
    public List<Map<String, Object>> getProjectRoles() {
        List<Map<String, Object>> roles = new ArrayList<>();
        for (ProjectMember.Role role : ProjectMember.Role.values()) {
            Map<String, Object> roleMap = new HashMap<>();
            roleMap.put("code", role.getCode());
            roleMap.put("name", role.getName());
            roles.add(roleMap);
        }
        return roles;
    }
}
