package com.sevb.admin.service;

import com.sevb.admin.core.model.WorkflowNode;
import com.sevb.admin.core.model.NodeType;
import com.sevb.admin.dao.WorkflowNodeDao;
import com.sevb.admin.dto.NodePositionUpdateDTO;
import com.sevb.core.biz.model.ReturnT;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import jakarta.annotation.Resource;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 工作流节点服务层
 */
@Slf4j
@Service
public class WorkflowNodeService {
    
    @Resource
    private WorkflowNodeDao workflowNodeDao;

    @Resource
    private NodeTypeService nodeTypeService;
    
    /**
     * 根据工作流ID查询所有节点
     */
    public ReturnT<List<WorkflowNode>> listByWorkflow(Long workflowId) {
        try {
            if (workflowId == null) {
                return new ReturnT<>(ReturnT.FAIL_CODE, "工作流ID不能为空");
            }
            
            List<WorkflowNode> nodes = workflowNodeDao.listByWorkflow(workflowId);
            return new ReturnT<>(nodes);
        } catch (Exception e) {
            log.error("查询工作流节点失败", e);
            return new ReturnT<>(ReturnT.FAIL_CODE, "查询失败：" + e.getMessage());
        }
    }
    
    /**
     * 分页查询工作流节点
     */
    public ReturnT<Map<String, Object>> pageList(int pageNum, int pageSize, Long workflowId, 
                                                 String nodeName, String nodeType) {
        try {
            // 参数验证
            if (pageNum < 1 || pageSize < 1) {
                return new ReturnT<>(ReturnT.FAIL_CODE, "分页参数错误");
            }
            if (workflowId == null) {
                return new ReturnT<>(ReturnT.FAIL_CODE, "工作流ID不能为空");
            }
            
            // 计算偏移量
            int offset = (pageNum - 1) * pageSize;
            
            // 查询数据
            List<WorkflowNode> list = workflowNodeDao.pageList(offset, pageSize, workflowId, nodeName, nodeType);
            int totalCount = workflowNodeDao.count(workflowId, nodeName, nodeType);
            
            // 构造返回结果
            Map<String, Object> result = new HashMap<>();
            result.put("recordsTotal", totalCount);
            result.put("recordsFiltered", totalCount);
            result.put("data", list);
            
            return new ReturnT<>(result);
        } catch (Exception e) {
            log.error("分页查询工作流节点失败", e);
            return new ReturnT<>(ReturnT.FAIL_CODE, "查询失败：" + e.getMessage());
        }
    }
    
    /**
     * 根据ID获取节点详情
     */
    public ReturnT<WorkflowNode> load(Long id) {
        try {
            if (id == null) {
                return new ReturnT<>(ReturnT.FAIL_CODE, "节点ID不能为空");
            }
            
            WorkflowNode node = workflowNodeDao.load(id);
            if (node == null) {
                return new ReturnT<>(ReturnT.FAIL_CODE, "节点不存在");
            }
            
            return new ReturnT<>(node);
        } catch (Exception e) {
            log.error("查询节点详情失败", e);
            return new ReturnT<>(ReturnT.FAIL_CODE, "查询失败：" + e.getMessage());
        }
    }
    
    /**
     * 根据工作流ID和节点编码获取节点
     */
    public ReturnT<WorkflowNode> loadByCode(Long workflowId, String nodeCode) {
        try {
            if (workflowId == null || !StringUtils.hasText(nodeCode)) {
                return new ReturnT<>(ReturnT.FAIL_CODE, "参数不能为空");
            }

            WorkflowNode node = workflowNodeDao.loadByCode(workflowId, nodeCode);
            if (node == null) {
                return new ReturnT<>(ReturnT.FAIL_CODE, "节点不存在");
            }

            return new ReturnT<>(node);
        } catch (Exception e) {
            log.error("根据编码查询节点失败", e);
            return new ReturnT<>(ReturnT.FAIL_CODE, "查询失败：" + e.getMessage());
        }
    }

    /**
     * 根据节点编码获取节点（全局查询）
     */
    public WorkflowNode findByNodeCode(String nodeCode) {
        try {
            if (!StringUtils.hasText(nodeCode)) {
                log.warn("节点编码不能为空");
                return null;
            }

            return workflowNodeDao.findByNodeCode(nodeCode);
        } catch (Exception e) {
            log.error("根据节点编码查询节点失败，节点编码: {}", nodeCode, e);
            return null;
        }
    }
    
    /**
     * 添加工作流节点
     */
    @Transactional(rollbackFor = Exception.class)
    public ReturnT<String> add(WorkflowNode workflowNode, String loginUser) {
        try {
            // 参数验证
            ReturnT<String> validResult = validateNode(workflowNode);
            if (validResult.getCode() != ReturnT.SUCCESS_CODE) {
                return validResult;
            }
            
            // 检查节点编码唯一性
            if (workflowNodeDao.checkNodeCodeUnique(workflowNode.getWorkflowId(), 
                    workflowNode.getNodeCode(), null) > 0) {
                return new ReturnT<>(ReturnT.FAIL_CODE, "节点编码已存在");
            }
            
            // 设置默认值
            setDefaultValues(workflowNode, loginUser);
            
            // 保存节点
            int result = workflowNodeDao.save(workflowNode);
            if (result > 0) {
                log.info("添加工作流节点成功，ID：{}", workflowNode.getId());
                return ReturnT.SUCCESS;
            } else {
                return new ReturnT<>(ReturnT.FAIL_CODE, "添加失败");
            }
        } catch (Exception e) {
            log.error("添加工作流节点失败", e);
            return new ReturnT<>(ReturnT.FAIL_CODE, "添加失败：" + e.getMessage());
        }
    }
    
    /**
     * 批量添加工作流节点
     */
    @Transactional(rollbackFor = Exception.class)
    public ReturnT<String> batchAdd(List<WorkflowNode> nodes, String loginUser) {
        try {
            if (nodes == null || nodes.isEmpty()) {
                return new ReturnT<>(ReturnT.FAIL_CODE, "节点列表不能为空");
            }
            
            // 验证每个节点
            for (WorkflowNode node : nodes) {
                ReturnT<String> validResult = validateNode(node);
                if (validResult.getCode() != ReturnT.SUCCESS_CODE) {
                    return validResult;
                }
                setDefaultValues(node, loginUser);
            }
            
            // 批量保存
            int result = workflowNodeDao.batchSave(nodes);
            if (result > 0) {
                log.info("批量添加工作流节点成功，数量：{}", result);
                return ReturnT.SUCCESS;
            } else {
                return new ReturnT<>(ReturnT.FAIL_CODE, "批量添加失败");
            }
        } catch (Exception e) {
            log.error("批量添加工作流节点失败", e);
            return new ReturnT<>(ReturnT.FAIL_CODE, "批量添加失败：" + e.getMessage());
        }
    }
    
    /**
     * 验证节点参数
     */
    private ReturnT<String> validateNode(WorkflowNode node) {
        if (node == null) {
            return new ReturnT<>(ReturnT.FAIL_CODE, "节点信息不能为空");
        }
        if (node.getWorkflowId() == null) {
            return new ReturnT<>(ReturnT.FAIL_CODE, "工作流ID不能为空");
        }
        if (!StringUtils.hasText(node.getNodeCode())) {
            return new ReturnT<>(ReturnT.FAIL_CODE, "节点编码不能为空");
        }
        if (!StringUtils.hasText(node.getNodeName())) {
            return new ReturnT<>(ReturnT.FAIL_CODE, "节点名称不能为空");
        }
        if (!StringUtils.hasText(node.getNodeType())) {
            return new ReturnT<>(ReturnT.FAIL_CODE, "节点类型不能为空");
        }
        return ReturnT.SUCCESS;
    }
    
    /**
     * 设置默认值
     */
    private void setDefaultValues(WorkflowNode node, String loginUser) {
        LocalDateTime now = LocalDateTime.now();
        
        if (node.getPositionX() == null) {
            node.setPositionX(0);
        }
        if (node.getPositionY() == null) {
            node.setPositionY(0);
        }
        if (node.getTimeout() == null) {
            node.setTimeout(0);
        }
        if (node.getRetryTimes() == null) {
            node.setRetryTimes(0);
        }
        if (node.getRetryInterval() == null) {
            node.setRetryInterval(1);
        }
        if (node.getMaxRetryTimes() == null) {
            node.setMaxRetryTimes(3);
        }
        if (!StringUtils.hasText(node.getFailureStrategy())) {
            node.setFailureStrategy("FAIL");
        }
        if (node.getPriority() == null) {
            node.setPriority(2);
        }
        if (!StringUtils.hasText(node.getWorkerGroup())) {
            node.setWorkerGroup("default");
        }
        if (node.getDeleted() == null) {
            node.setDeleted(0);
        }

        // 设置节点颜色（从节点类型获取）
        if (!StringUtils.hasText(node.getColor()) && StringUtils.hasText(node.getNodeType())) {
            try {
                NodeType nodeType = nodeTypeService.findByTypeCode(node.getNodeType());
                if (nodeType != null && StringUtils.hasText(nodeType.getColor())) {
                    node.setColor(nodeType.getColor());
                }
            } catch (Exception e) {
                log.warn("获取节点类型颜色失败，使用默认颜色: {}", e.getMessage());
                node.setColor("#1890ff"); // 默认颜色
            }
        }

        // 设置创建/更新信息
        if (node.getId() == null) {
            node.setCreateTime(now);
            node.setCreateUser(loginUser);
        }
        node.setUpdateTime(now);
        node.setUpdateUser(loginUser);
    }

    /**
     * 更新工作流节点
     */
    @Transactional(rollbackFor = Exception.class)
    public ReturnT<String> update(WorkflowNode workflowNode, String loginUser) {
        try {
            // 参数验证
            if (workflowNode.getId() == null) {
                return new ReturnT<>(ReturnT.FAIL_CODE, "节点ID不能为空");
            }

            ReturnT<String> validResult = validateNode(workflowNode);
            if (validResult.getCode() != ReturnT.SUCCESS_CODE) {
                return validResult;
            }

            // 检查节点是否存在
            WorkflowNode existNode = workflowNodeDao.load(workflowNode.getId());
            if (existNode == null) {
                return new ReturnT<>(ReturnT.FAIL_CODE, "节点不存在");
            }

            // 检查节点编码唯一性（排除自己）
            if (workflowNodeDao.checkNodeCodeUnique(workflowNode.getWorkflowId(),
                    workflowNode.getNodeCode(), workflowNode.getId()) > 0) {
                return new ReturnT<>(ReturnT.FAIL_CODE, "节点编码已存在");
            }

            // 设置更新信息
            workflowNode.setUpdateTime(LocalDateTime.now());
            workflowNode.setUpdateUser(loginUser);

            // 更新节点
            int result = workflowNodeDao.update(workflowNode);
            if (result > 0) {
                log.info("更新工作流节点成功，ID：{}", workflowNode.getId());
                return ReturnT.SUCCESS;
            } else {
                return new ReturnT<>(ReturnT.FAIL_CODE, "更新失败");
            }
        } catch (Exception e) {
            log.error("更新工作流节点失败", e);
            return new ReturnT<>(ReturnT.FAIL_CODE, "更新失败：" + e.getMessage());
        }
    }

    /**
     * 批量更新工作流节点
     */
    @Transactional(rollbackFor = Exception.class)
    public ReturnT<String> batchUpdate(List<WorkflowNode> nodes, String loginUser) {
        try {
            if (nodes == null || nodes.isEmpty()) {
                return new ReturnT<>(ReturnT.FAIL_CODE, "节点列表不能为空");
            }

            // 验证每个节点
            for (WorkflowNode node : nodes) {
                if (node.getId() == null) {
                    return new ReturnT<>(ReturnT.FAIL_CODE, "节点ID不能为空");
                }
                ReturnT<String> validResult = validateNode(node);
                if (validResult.getCode() != ReturnT.SUCCESS_CODE) {
                    return validResult;
                }
                // 设置更新信息
                node.setUpdateTime(LocalDateTime.now());
                node.setUpdateUser(loginUser);
            }

            // 批量更新
            int result = workflowNodeDao.batchUpdate(nodes);
            if (result > 0) {
                log.info("批量更新工作流节点成功，数量：{}", result);
                return ReturnT.SUCCESS;
            } else {
                return new ReturnT<>(ReturnT.FAIL_CODE, "批量更新失败");
            }
        } catch (Exception e) {
            log.error("批量更新工作流节点失败", e);
            return new ReturnT<>(ReturnT.FAIL_CODE, "批量更新失败：" + e.getMessage());
        }
    }

    /**
     * 删除工作流节点
     */
    @Transactional(rollbackFor = Exception.class)
    public ReturnT<String> delete(Long id, String loginUser) {
        try {
            if (id == null) {
                return new ReturnT<>(ReturnT.FAIL_CODE, "节点ID不能为空");
            }

            // 检查节点是否存在
            WorkflowNode existNode = workflowNodeDao.load(id);
            if (existNode == null) {
                return new ReturnT<>(ReturnT.FAIL_CODE, "节点不存在");
            }

            // 逻辑删除
            int result = workflowNodeDao.delete(id, loginUser);
            if (result > 0) {
                log.info("删除工作流节点成功，ID：{}", id);
                return ReturnT.SUCCESS;
            } else {
                return new ReturnT<>(ReturnT.FAIL_CODE, "删除失败");
            }
        } catch (Exception e) {
            log.error("删除工作流节点失败", e);
            return new ReturnT<>(ReturnT.FAIL_CODE, "删除失败：" + e.getMessage());
        }
    }

    /**
     * 批量删除工作流节点
     */
    @Transactional(rollbackFor = Exception.class)
    public ReturnT<String> batchDelete(List<Long> ids, String loginUser) {
        try {
            if (ids == null || ids.isEmpty()) {
                return new ReturnT<>(ReturnT.FAIL_CODE, "节点ID列表不能为空");
            }

            // 批量删除
            int result = workflowNodeDao.batchDelete(ids, loginUser);
            if (result > 0) {
                log.info("批量删除工作流节点成功，数量：{}", result);
                return ReturnT.SUCCESS;
            } else {
                return new ReturnT<>(ReturnT.FAIL_CODE, "批量删除失败");
            }
        } catch (Exception e) {
            log.error("批量删除工作流节点失败", e);
            return new ReturnT<>(ReturnT.FAIL_CODE, "批量删除失败：" + e.getMessage());
        }
    }

    /**
     * 更新节点位置
     */
    @Transactional(rollbackFor = Exception.class)
    public ReturnT<String> updatePosition(Long id, Integer positionX, Integer positionY, String loginUser) {
        try {
            if (id == null) {
                return new ReturnT<>(ReturnT.FAIL_CODE, "节点ID不能为空");
            }
            if (positionX == null || positionY == null) {
                return new ReturnT<>(ReturnT.FAIL_CODE, "坐标位置不能为空");
            }

            // 更新位置
            int result = workflowNodeDao.updatePosition(id, positionX, positionY, loginUser);
            if (result > 0) {
                log.info("更新节点位置成功，ID：{}，位置：({}, {})", id, positionX, positionY);
                return ReturnT.SUCCESS;
            } else {
                return new ReturnT<>(ReturnT.FAIL_CODE, "更新位置失败");
            }
        } catch (Exception e) {
            log.error("更新节点位置失败", e);
            return new ReturnT<>(ReturnT.FAIL_CODE, "更新位置失败：" + e.getMessage());
        }
    }

    /**
     * 验证节点编码是否唯一
     */
    public ReturnT<Boolean> validateNodeCode(Long workflowId, String nodeCode, Long excludeId) {
        try {
            if (workflowId == null || !StringUtils.hasText(nodeCode)) {
                return new ReturnT<>(ReturnT.FAIL_CODE, "参数不能为空");
            }

            int count = workflowNodeDao.checkNodeCodeUnique(workflowId, nodeCode, excludeId);
            return new ReturnT<>(count == 0);
        } catch (Exception e) {
            log.error("验证节点编码失败", e);
            return new ReturnT<>(ReturnT.FAIL_CODE, "验证失败：" + e.getMessage());
        }
    }
}
