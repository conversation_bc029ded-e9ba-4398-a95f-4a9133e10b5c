package com.sevb.admin.service.impl;

import com.sevb.admin.core.model.NodeInstance;
import com.sevb.admin.dao.NodeInstanceDao;
import com.sevb.admin.service.NodeInstanceService;
import com.sevb.core.biz.model.ReturnT;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.*;

/**
 * 节点实例服务实现
 * 
 * <AUTHOR>
 */
@Slf4j
@Service
public class NodeInstanceServiceImpl implements NodeInstanceService {

    @Resource
    private NodeInstanceDao nodeInstanceDao;

    @Override
    public ReturnT<Map<String, Object>> pageList(int page, int size, Long projectId, Long workflowInstanceId, Long workflowId,
                                                 Long nodeId, String nodeName, String nodeType, Integer status,
                                                 Date startTime, Date endTime) {
        try {
            // 参数校验
            if (page < 1) page = 1;
            if (size < 1) size = 10;
            if (size > 100) size = 100;

            // 计算偏移量
            int offset = (page - 1) * size;

            // 查询数据
            List<NodeInstance> list = nodeInstanceDao.pageList(offset, size, projectId, workflowInstanceId, workflowId,
                    nodeId, nodeName, nodeType, status, startTime, endTime);
            int total = nodeInstanceDao.pageListCount(projectId, workflowInstanceId, workflowId, nodeId, nodeName,
                    nodeType, status, startTime, endTime);

            // 构建返回结果
            Map<String, Object> result = new HashMap<>();
            result.put("data", list);
            result.put("recordsTotal", total);
            result.put("recordsFiltered", total);

            return new ReturnT<>(result);

        } catch (Exception e) {
            log.error("分页查询节点实例失败", e);
            return new ReturnT<>(ReturnT.FAIL_CODE, "查询失败: " + e.getMessage());
        }
    }

    @Override
    public ReturnT<NodeInstance> getById(Long id) {
        try {
            if (id == null) {
                return new ReturnT<>(ReturnT.FAIL_CODE, "节点实例ID不能为空");
            }

            NodeInstance instance = nodeInstanceDao.load(id);
            if (instance == null) {
                return new ReturnT<>(ReturnT.FAIL_CODE, "节点实例不存在");
            }

            return new ReturnT<>(instance);

        } catch (Exception e) {
            log.error("查询节点实例失败, id: {}", id, e);
            return new ReturnT<>(ReturnT.FAIL_CODE, "查询失败: " + e.getMessage());
        }
    }

    @Override
    public ReturnT<Map<String, Object>> getByWorkflowInstanceId(Long workflowInstanceId) {
        try {
            if (workflowInstanceId == null) {
                return new ReturnT<>(ReturnT.FAIL_CODE, "工作流实例ID不能为空");
            }

            List<NodeInstance> nodeInstances = nodeInstanceDao.findByWorkflowInstanceId(workflowInstanceId);

            Map<String, Object> result = new HashMap<>();
            result.put("nodeInstances", nodeInstances);
            result.put("count", nodeInstances.size());

            // 统计各状态数量
            Map<String, Integer> statusCount = new HashMap<>();
            for (NodeInstance instance : nodeInstances) {
                String statusName = instance.getStatusName();
                statusCount.put(statusName, statusCount.getOrDefault(statusName, 0) + 1);
            }
            result.put("statusCount", statusCount);

            return new ReturnT<>(result);

        } catch (Exception e) {
            log.error("根据工作流实例ID查询节点实例失败, workflowInstanceId: {}", workflowInstanceId, e);
            return new ReturnT<>(ReturnT.FAIL_CODE, "查询失败: " + e.getMessage());
        }
    }

    @Override
    public ReturnT<String> retryNodeInstance(Long id) {
        try {
            if (id == null) {
                return new ReturnT<>(ReturnT.FAIL_CODE, "节点实例ID不能为空");
            }

            NodeInstance instance = nodeInstanceDao.load(id);
            if (instance == null) {
                return new ReturnT<>(ReturnT.FAIL_CODE, "节点实例不存在");
            }

            if (!instance.canRetry()) {
                return new ReturnT<>(ReturnT.FAIL_CODE, "该节点实例不能重试");
            }

            // TODO: 实现节点重试逻辑
            log.info("节点实例{}重试功能开发中", id);
            return new ReturnT<>(ReturnT.SUCCESS_CODE, "重试功能开发中");

        } catch (Exception e) {
            log.error("重试节点实例失败, id: {}", id, e);
            return new ReturnT<>(ReturnT.FAIL_CODE, "重试失败: " + e.getMessage());
        }
    }

    @Override
    public ReturnT<String> stopNodeInstance(Long id) {
        try {
            if (id == null) {
                return new ReturnT<>(ReturnT.FAIL_CODE, "节点实例ID不能为空");
            }

            NodeInstance instance = nodeInstanceDao.load(id);
            if (instance == null) {
                return new ReturnT<>(ReturnT.FAIL_CODE, "节点实例不存在");
            }

            if (!instance.isRunning()) {
                return new ReturnT<>(ReturnT.FAIL_CODE, "只有运行中的节点实例才能停止");
            }

            // TODO: 调用具体的停止逻辑
            // 更新节点实例状态
            instance.setStatus(NodeInstance.Status.FAILED.getCode());
            instance.setEndTime(new Date());
            instance.setUpdateTime(new Date());
            instance.setErrorMessage("用户手动停止");
            
            if (instance.getStartTime() != null) {
                instance.setDuration(instance.getEndTime().getTime() - instance.getStartTime().getTime());
            }

            nodeInstanceDao.update(instance);

            log.info("节点实例{}停止成功", id);
            return ReturnT.SUCCESS;

        } catch (Exception e) {
            log.error("停止节点实例失败, id: {}", id, e);
            return new ReturnT<>(ReturnT.FAIL_CODE, "停止失败: " + e.getMessage());
        }
    }

    @Override
    public ReturnT<String> getNodeInstanceLog(Long id) {
        try {
            if (id == null) {
                return new ReturnT<>(ReturnT.FAIL_CODE, "节点实例ID不能为空");
            }

            NodeInstance instance = nodeInstanceDao.load(id);
            if (instance == null) {
                return new ReturnT<>(ReturnT.FAIL_CODE, "节点实例不存在");
            }

            // 返回执行日志
            String log = instance.getExecuteLog();
            if (!StringUtils.hasText(log)) {
                log = "暂无日志内容";
            }

            return new ReturnT<>(log);

        } catch (Exception e) {
            log.error("获取节点实例日志失败, id: {}", id, e);
            return new ReturnT<>(ReturnT.FAIL_CODE, "获取日志失败: " + e.getMessage());
        }
    }

    @Override
    public ReturnT<Map<String, Object>> getNodeStats(Long nodeId, Integer days) {
        try {
            if (nodeId == null) {
                return new ReturnT<>(ReturnT.FAIL_CODE, "节点ID不能为空");
            }

            if (days == null || days < 1) {
                days = 7;
            }

            // TODO: 实现节点统计逻辑
            Map<String, Object> stats = new HashMap<>();
            stats.put("totalCount", 0);
            stats.put("successCount", 0);
            stats.put("failedCount", 0);
            stats.put("runningCount", 0);
            stats.put("successRate", 0.0);
            stats.put("avgDuration", 0L);

            return new ReturnT<>(stats);

        } catch (Exception e) {
            log.error("查询节点统计失败, nodeId: {}", nodeId, e);
            return new ReturnT<>(ReturnT.FAIL_CODE, "查询统计失败: " + e.getMessage());
        }
    }

    @Override
    public ReturnT<Map<String, Object>> getRecentNodeInstances(Long nodeId, Integer limit) {
        try {
            if (nodeId == null) {
                return new ReturnT<>(ReturnT.FAIL_CODE, "节点ID不能为空");
            }

            if (limit == null || limit < 1) {
                limit = 10;
            }

            List<NodeInstance> instances = nodeInstanceDao.findByNodeId(nodeId, limit);

            Map<String, Object> result = new HashMap<>();
            result.put("instances", instances);
            result.put("count", instances.size());

            return new ReturnT<>(result);

        } catch (Exception e) {
            log.error("查询最近节点实例失败, nodeId: {}", nodeId, e);
            return new ReturnT<>(ReturnT.FAIL_CODE, "查询失败: " + e.getMessage());
        }
    }

    @Override
    public ReturnT<Long> createNodeInstance(NodeInstance nodeInstance) {
        try {
            if (nodeInstance == null) {
                return new ReturnT<>(ReturnT.FAIL_CODE, "节点实例不能为空");
            }

            // 设置创建时间
            Date now = new Date();
            nodeInstance.setCreateTime(now);
            nodeInstance.setUpdateTime(now);

            // 保存实例
            int result = nodeInstanceDao.save(nodeInstance);
            if (result > 0) {
                log.info("节点实例创建成功, id: {}", nodeInstance.getId());
                return new ReturnT<>(nodeInstance.getId());
            } else {
                return new ReturnT<>(ReturnT.FAIL_CODE, "创建节点实例失败");
            }

        } catch (Exception e) {
            log.error("创建节点实例失败", e);
            return new ReturnT<>(ReturnT.FAIL_CODE, "创建失败: " + e.getMessage());
        }
    }

    @Override
    public ReturnT<String> batchCreateNodeInstances(List<NodeInstance> nodeInstances) {
        try {
            if (nodeInstances == null || nodeInstances.isEmpty()) {
                return new ReturnT<>(ReturnT.FAIL_CODE, "节点实例列表不能为空");
            }

            // 设置创建时间
            Date now = new Date();
            for (NodeInstance instance : nodeInstances) {
                instance.setCreateTime(now);
                instance.setUpdateTime(now);
            }

            // 批量保存
            int result = nodeInstanceDao.batchSave(nodeInstances);
            if (result > 0) {
                log.info("批量创建节点实例成功, count: {}", result);
                return ReturnT.SUCCESS;
            } else {
                return new ReturnT<>(ReturnT.FAIL_CODE, "批量创建节点实例失败");
            }

        } catch (Exception e) {
            log.error("批量创建节点实例失败", e);
            return new ReturnT<>(ReturnT.FAIL_CODE, "批量创建失败: " + e.getMessage());
        }
    }

    @Override
    public ReturnT<String> updateNodeInstanceStatus(Long id, Integer status, String errorMessage, String executeLog) {
        try {
            if (id == null) {
                return new ReturnT<>(ReturnT.FAIL_CODE, "节点实例ID不能为空");
            }

            NodeInstance instance = nodeInstanceDao.load(id);
            if (instance == null) {
                return new ReturnT<>(ReturnT.FAIL_CODE, "节点实例不存在");
            }

            // 更新状态
            instance.setStatus(status);
            instance.setUpdateTime(new Date());

            if (StringUtils.hasText(errorMessage)) {
                instance.setErrorMessage(errorMessage);
            }

            if (StringUtils.hasText(executeLog)) {
                instance.setExecuteLog(executeLog);
            }

            // 如果是完成状态，设置结束时间和耗时
            if (NodeInstance.Status.getByCode(status) != null && 
                instance.isCompleted() && instance.getEndTime() == null) {
                instance.setEndTime(new Date());
                if (instance.getStartTime() != null) {
                    instance.setDuration(instance.getEndTime().getTime() - instance.getStartTime().getTime());
                }
            }

            int result = nodeInstanceDao.update(instance);
            if (result > 0) {
                return ReturnT.SUCCESS;
            } else {
                return new ReturnT<>(ReturnT.FAIL_CODE, "更新状态失败");
            }

        } catch (Exception e) {
            log.error("更新节点实例状态失败, id: {}, status: {}", id, status, e);
            return new ReturnT<>(ReturnT.FAIL_CODE, "更新失败: " + e.getMessage());
        }
    }

    @Override
    public ReturnT<List<NodeInstance>> getRunningNodeInstances(Long workflowInstanceId) {
        try {
            if (workflowInstanceId == null) {
                return new ReturnT<>(ReturnT.FAIL_CODE, "工作流实例ID不能为空");
            }

            List<NodeInstance> runningInstances = nodeInstanceDao.findRunningInstances(workflowInstanceId);
            return new ReturnT<>(runningInstances);

        } catch (Exception e) {
            log.error("查询运行中的节点实例失败, workflowInstanceId: {}", workflowInstanceId, e);
            return new ReturnT<>(ReturnT.FAIL_CODE, "查询失败: " + e.getMessage());
        }
    }
}
