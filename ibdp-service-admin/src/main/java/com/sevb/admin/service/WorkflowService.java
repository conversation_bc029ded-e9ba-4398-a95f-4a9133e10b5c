package com.sevb.admin.service;

import com.sevb.admin.core.model.Workflow;
import com.sevb.core.biz.model.ReturnT;

import java.util.List;
import java.util.Map;

/**
 * 工作流服务接口
 * <AUTHOR>
 */
public interface WorkflowService {

    /**
     * 分页查询工作流列表
     */
    Map<String, Object> pageList(int pageNum, int pageSize, Long projectId, String name, String category, Integer status);

    /**
     * 根据项目ID查询所有工作流
     */
    List<Workflow> findByProjectId(Long projectId);

    /**
     * 根据项目ID和状态查询工作流
     */
    List<Workflow> findByProjectIdAndStatus(Long projectId, Integer status);

    /**
     * 根据ID查询工作流
     */
    Workflow load(Long id);

    /**
     * 根据项目ID和编码查询工作流
     */
    Workflow findByProjectIdAndCode(Long projectId, String code);

    /**
     * 添加工作流
     */
    ReturnT<String> add(Workflow workflow);

    /**
     * 更新工作流
     */
    ReturnT<String> update(Workflow workflow);

    /**
     * 删除工作流
     */
    ReturnT<String> delete(Long id, String updateUser);

    /**
     * 发布工作流
     */
    ReturnT<String> publish(Long id, String updateUser);

    /**
     * 上线工作流（使用默认Cron表达式）
     */
    ReturnT<String> online(Long id, String updateUser);

    /**
     * 上线工作流（指定Cron表达式）
     */
    ReturnT<String> online(Long id, String cronExpression, String updateUser);

    /**
     * 下线工作流
     */
    ReturnT<String> offline(Long id, String updateUser);

    /**
     * 获取工作流分类列表
     */
    List<Map<String, Object>> getCategoryList();

    /**
     * 获取工作流状态列表
     */
    List<Map<String, Object>> getStatusList();



    /**
     * 统计项目下的工作流数量
     */
    int countByProjectId(Long projectId);

    /**
     * 根据分类统计工作流数量
     */
    Map<String, Integer> countByCategory(Long projectId);

    /**
     * 手动执行工作流
     */
    ReturnT<String> execute(Long id, String executeUser);
}
