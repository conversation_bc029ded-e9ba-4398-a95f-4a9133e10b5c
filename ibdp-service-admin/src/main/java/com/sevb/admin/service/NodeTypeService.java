package com.sevb.admin.service;

import com.sevb.admin.core.model.NodeType;
import com.sevb.core.biz.model.ReturnT;

import java.util.List;
import java.util.Map;

/**
 * 节点类型服务接口
 * <AUTHOR>
 */
public interface NodeTypeService {

    /**
     * 分页查询节点类型列表
     */
    Map<String, Object> pageList(int pageNum, int pageSize, String typeName, String category, Integer status);

    /**
     * 查询所有启用的节点类型
     */
    List<NodeType> findAllEnabled();

    /**
     * 根据分类查询启用的节点类型
     */
    List<NodeType> findByCategory(String category);

    /**
     * 根据ID查询节点类型
     */
    NodeType load(Long id);

    /**
     * 根据类型编码查询节点类型
     */
    NodeType findByTypeCode(String typeCode);

    /**
     * 添加节点类型
     */
    ReturnT<String> add(NodeType nodeType);

    /**
     * 更新节点类型
     */
    ReturnT<String> update(NodeType nodeType);

    /**
     * 删除节点类型
     */
    ReturnT<String> delete(Long id);

    /**
     * 启用/禁用节点类型
     */
    ReturnT<String> updateStatus(Long id, Integer status);

    /**
     * 获取节点分类列表
     */
    List<Map<String, Object>> getCategoryList();
}
