package com.sevb.admin.service.orchestration;

import com.sevb.admin.core.enums.TriggerType;
import com.sevb.admin.core.model.Workflow;
import com.sevb.admin.dao.WorkflowDao;
import com.sevb.core.biz.model.ReturnT;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.scheduling.concurrent.ThreadPoolTaskScheduler;
import org.springframework.scheduling.support.CronTrigger;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import jakarta.annotation.PostConstruct;
import jakarta.annotation.PreDestroy;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ScheduledFuture;

/**
 * 调度中心工作流定时调度器
 * 负责调度中心编排模式下的工作流定时调度
 * 
 * <AUTHOR>
 */
@Component
@EnableScheduling
@Slf4j
public class CentralWorkflowScheduler {
    
    @Autowired
    private WorkflowDao workflowDao;
    
    @Autowired
    private CentralWorkflowOrchestrationService orchestrationService;
    
    /**
     * 调度任务缓存
     */
    private final Map<Long, ScheduledFuture<?>> scheduledTasks = new ConcurrentHashMap<>();
    
    /**
     * 任务调度器
     */
    private final ThreadPoolTaskScheduler taskScheduler;
    
    public CentralWorkflowScheduler() {
        // 初始化任务调度器
        ThreadPoolTaskScheduler scheduler = new ThreadPoolTaskScheduler();
        scheduler.setPoolSize(20);
        scheduler.setThreadNamePrefix("central-workflow-scheduler-");
        scheduler.setWaitForTasksToCompleteOnShutdown(true);
        scheduler.setAwaitTerminationSeconds(60);
        scheduler.initialize();
        this.taskScheduler = scheduler;
        
        log.info("调度中心工作流调度器初始化完成");
    }
    
    /**
     * 系统启动时加载所有调度中心编排的工作流
     */
    @PostConstruct
    public void initializeScheduledWorkflows() {
        try {
            log.info("初始化调度中心工作流调度任务...");
            
            // 查询所有在线且使用调度中心编排的工作流
            List<Workflow> centralWorkflows = workflowDao.findOnlineCentralWorkflows();
            
            int scheduledCount = 0;
            for (Workflow workflow : centralWorkflows) {
                if (StringUtils.hasText(workflow.getCronExpression())) {
                    scheduleWorkflow(workflow);
                    scheduledCount++;
                } else {
                    log.info("发现调度中心编排工作流(无Cron表达式): workflowId={}, name={}", workflow.getId(), workflow.getName());
                }
            }
            
            log.info("调度中心工作流调度任务初始化完成，共加载{}个任务", scheduledCount);
            
        } catch (Exception e) {
            log.error("初始化调度中心工作流调度任务失败", e);
        }
    }
    
    /**
     * 为工作流创建定时调度
     */
    public void scheduleWorkflow(Workflow workflow) {
        try {
            String cronExpression = workflow.getCronExpression();
            if (!StringUtils.hasText(cronExpression)) {
                log.warn("工作流{}没有配置Cron表达式，跳过调度", workflow.getId());
                return;
            }
            
            if (!"CENTRAL".equals(workflow.getOrchestrationMode())) {
                log.warn("工作流{}不是调度中心编排模式，跳过调度", workflow.getId());
                return;
            }
            
            // 先取消现有调度（如果存在）
            unscheduleWorkflow(workflow.getId());
            
            // 创建Cron触发器
            CronTrigger cronTrigger = new CronTrigger(cronExpression);
            
            // 创建调度任务
            Runnable task = createScheduleTask(workflow);
            
            // 调度任务
            ScheduledFuture<?> future = taskScheduler.schedule(task, cronTrigger);
            scheduledTasks.put(workflow.getId(), future);
            
            log.info("工作流定时调度已启动: workflowId={}, name={}, cron={}",
                    workflow.getId(), workflow.getName(), cronExpression);
            
        } catch (Exception e) {
            log.error("创建工作流定时调度失败: workflowId={}, name={}", 
                    workflow.getId(), workflow.getName(), e);
        }
    }
    
    /**
     * 创建调度任务
     */
    private Runnable createScheduleTask(Workflow workflow) {
        return () -> {
            try {
                log.info("定时触发工作流执行: workflowId={}, name={}", workflow.getId(), workflow.getName());
                
                // 检查工作流状态是否仍然在线
                Workflow currentWorkflow = workflowDao.load(workflow.getId());
                if (currentWorkflow == null || !currentWorkflow.isOnline()) {
                    log.warn("工作流{}状态已变更，停止调度", workflow.getId());
                    unscheduleWorkflow(workflow.getId());
                    return;
                }
                
                // 检查编排模式是否仍然是调度中心编排
                if (!"CENTRAL".equals(currentWorkflow.getOrchestrationMode())) {
                    log.warn("工作流{}编排模式已变更，停止调度", workflow.getId());
                    unscheduleWorkflow(workflow.getId());
                    return;
                }
                
                // 调用调度中心编排执行工作流（定时触发）
                ReturnT<String> result = orchestrationService.executeWorkflow(
                        workflow.getId(), "central-scheduler", TriggerType.SCHEDULE.getCode());
                
                // 更新调度统计
                updateScheduleStats(workflow.getId(), result.getCode() == ReturnT.SUCCESS_CODE);
                
                if (result.getCode() == ReturnT.SUCCESS_CODE) {
                    log.info("定时工作流执行成功: workflowId={}, result={}", workflow.getId(), result.getContent());
                } else {
                    log.error("定时工作流执行失败: workflowId={}, error={}", workflow.getId(), result.getMsg());
                }
                
            } catch (Exception e) {
                log.error("定时工作流执行异常: workflowId={}", workflow.getId(), e);
                updateScheduleStats(workflow.getId(), false);
            }
        };
    }
    
    /**
     * 取消工作流调度
     */
    public void unscheduleWorkflow(Long workflowId) {
        ScheduledFuture<?> future = scheduledTasks.remove(workflowId);
        if (future != null && !future.isCancelled()) {
            future.cancel(false);
            log.info("工作流定时调度已取消: workflowId={}", workflowId);
        }
    }
    
    /**
     * 更新工作流调度
     */
    public void rescheduleWorkflow(Workflow workflow) {
        log.info("更新工作流调度: workflowId={}, name={}", workflow.getId(), workflow.getName());
        scheduleWorkflow(workflow);
    }
    
    /**
     * 获取调度状态
     */
    public boolean isScheduled(Long workflowId) {
        ScheduledFuture<?> future = scheduledTasks.get(workflowId);
        return future != null && !future.isCancelled() && !future.isDone();
    }

    /**
     * 动态添加工作流调度（用于工作流上线时调用）
     */
    public void addWorkflowSchedule(Long workflowId) {
        try {
            log.info("动态添加工作流调度: workflowId={}", workflowId);

            // 查询工作流信息
            Workflow workflow = workflowDao.load(workflowId);
            if (workflow == null) {
                log.warn("工作流{}不存在，无法添加调度", workflowId);
                return;
            }

            // 检查是否为调度中心编排模式且已上线
            if (!"CENTRAL".equals(workflow.getOrchestrationMode())) {
                log.info("工作流{}不是调度中心编排模式，跳过调度", workflowId);
                return;
            }

            if (!workflow.isOnline()) {
                log.info("工作流{}未上线，跳过调度", workflowId);
                return;
            }

            // 添加调度
            scheduleWorkflow(workflow);

        } catch (Exception e) {
            log.error("动态添加工作流调度失败: workflowId={}", workflowId, e);
        }
    }

    /**
     * 动态移除工作流调度（用于工作流下线时调用）
     */
    public void removeWorkflowSchedule(Long workflowId) {
        try {
            log.info("动态移除工作流调度: workflowId={}", workflowId);
            unscheduleWorkflow(workflowId);
        } catch (Exception e) {
            log.error("动态移除工作流调度失败: workflowId={}", workflowId, e);
        }
    }

    /**
     * 动态更新工作流调度（用于工作流Cron表达式变更时调用）
     */
    public void updateWorkflowSchedule(Long workflowId) {
        try {
            log.info("动态更新工作流调度: workflowId={}", workflowId);

            // 先移除现有调度
            unscheduleWorkflow(workflowId);

            // 重新添加调度
            addWorkflowSchedule(workflowId);

        } catch (Exception e) {
            log.error("动态更新工作流调度失败: workflowId={}", workflowId, e);
        }
    }
    
    /**
     * 获取调度任务数量
     */
    public int getScheduledTaskCount() {
        return scheduledTasks.size();
    }
    
    /**
     * 更新调度统计
     */
    private void updateScheduleStats(Long workflowId, boolean success) {
        try {
            Workflow workflow = workflowDao.load(workflowId);
            if (workflow != null) {
                // 更新最后触发时间
                workflow.setLastTriggerTime(new Date());

                // 更新统计计数
                long currentCount = workflow.getExecutionCount() != null ? workflow.getExecutionCount() : 0L;
                workflow.setExecutionCount(currentCount + 1);

                if (success) {
                    long currentSuccess = workflow.getSuccessCount() != null ? workflow.getSuccessCount() : 0L;
                    workflow.setSuccessCount(currentSuccess + 1);
                } else {
                    long currentFailure = workflow.getFailureCount() != null ? workflow.getFailureCount() : 0L;
                    workflow.setFailureCount(currentFailure + 1);
                }

                workflowDao.update(workflow);
                log.debug("工作流调度统计更新成功: workflowId={}, success={}", workflowId, success);
            }

        } catch (Exception e) {
            log.error("更新工作流调度统计失败: workflowId={}", workflowId, e);
        }
    }
    
    /**
     * 定期检查和清理无效的调度任务
     */
    @Scheduled(fixedDelay = 300000) // 每5分钟检查一次
    public void cleanupInvalidTasks() {
        try {
            log.debug("开始清理无效的调度任务...");
            
            int cleanedCount = 0;
            for (Map.Entry<Long, ScheduledFuture<?>> entry : scheduledTasks.entrySet()) {
                Long workflowId = entry.getKey();
                ScheduledFuture<?> future = entry.getValue();
                
                // 检查任务是否已完成或取消
                if (future.isDone() || future.isCancelled()) {
                    scheduledTasks.remove(workflowId);
                    cleanedCount++;
                    continue;
                }
                
                // 检查工作流是否仍然存在且在线
                Workflow workflow = workflowDao.load(workflowId);
                if (workflow == null || !workflow.isOnline() || !"CENTRAL".equals(workflow.getOrchestrationMode())) {
                    future.cancel(false);
                    scheduledTasks.remove(workflowId);
                    cleanedCount++;
                    log.info("清理无效调度任务: workflowId={}", workflowId);
                }
            }
            
            if (cleanedCount > 0) {
                log.info("清理无效调度任务完成，清理数量: {}", cleanedCount);
            }
            
        } catch (Exception e) {
            log.error("清理无效调度任务失败", e);
        }
    }
    
    /**
     * 系统关闭时清理资源
     */
    @PreDestroy
    public void shutdown() {
        log.info("关闭调度中心工作流调度器...");
        
        // 取消所有调度任务
        for (ScheduledFuture<?> future : scheduledTasks.values()) {
            if (!future.isCancelled()) {
                future.cancel(false);
            }
        }
        scheduledTasks.clear();
        
        // 关闭任务调度器
        if (taskScheduler != null) {
            taskScheduler.shutdown();
        }
        
        log.info("调度中心工作流调度器已关闭");
    }
}
