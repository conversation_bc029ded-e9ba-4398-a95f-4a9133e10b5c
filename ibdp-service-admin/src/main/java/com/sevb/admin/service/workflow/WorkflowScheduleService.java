package com.sevb.admin.service.workflow;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.sevb.admin.core.dto.WorkflowScheduleConfigDTO;
import com.sevb.admin.core.model.JobInfo;
import com.sevb.admin.core.model.Workflow;
import com.sevb.admin.core.model.WorkflowNode;
import com.sevb.admin.core.model.WorkflowNodeRelation;
import com.sevb.admin.core.thread.JobTriggerPoolHelper;
import com.sevb.admin.core.trigger.TriggerTypeEnum;
import com.sevb.admin.dao.*;
import com.sevb.admin.service.orchestration.CentralWorkflowScheduler;
import com.sevb.core.biz.model.ReturnT;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.util.*;

/**
 * 工作流调度服务
 * 负责工作流上线时创建XXL-JOB调度任务
 * 
 * <AUTHOR>
 */
@Service
@Slf4j
public class WorkflowScheduleService {
    
    @Autowired
    private WorkflowDao workflowDao;

    @Autowired
    private JobInfoDao jobInfoDao;

    @Autowired
    private CentralWorkflowScheduler centralWorkflowScheduler;
    
    @Autowired
    private JobGroupDao jobGroupDao;
    
    @Autowired
    private WorkflowJobService workflowJobService;

    @Autowired
    private WorkflowNodeDao workflowNodeDao;

    @Autowired
    private WorkflowNodeRelationDao workflowNodeRelationDao;

    @Autowired
    private WorkflowExecutionService workflowExecutionService;
    
    /**
     * 工作流上线 - 创建调度任务
     * 
     * @param workflowId 工作流ID
     * @param cronExpression Cron表达式
     * @param updateUser 操作用户
     * @return 操作结果
     */
    @Transactional
    public ReturnT<String> onlineWorkflow(Long workflowId, String cronExpression, String updateUser) {
        try {
            log.info("开始上线工作流: {}, Cron: {}", workflowId, cronExpression);
            
            // 1. 验证工作流是否存在且已发布
            Workflow workflow = workflowDao.load(workflowId);
            if (workflow == null) {
                return new ReturnT<>(ReturnT.FAIL_CODE, "工作流不存在");
            }
            
            if (!Workflow.Status.PUBLISHED.getCode().equals(workflow.getStatus()) &&
                !Workflow.Status.OFFLINE.getCode().equals(workflow.getStatus())) {
                return new ReturnT<>(ReturnT.FAIL_CODE, "只有已发布或已下线的工作流才能上线");
            }
            
            // 2. 检查是否已经上线
            if (Workflow.Status.ONLINE.getCode().equals(workflow.getStatus())) {
                return new ReturnT<>(ReturnT.FAIL_CODE, "工作流已经上线");
            }
            
            // 3. 验证Cron表达式
            if (!StringUtils.hasText(cronExpression)) {
                return new ReturnT<>(ReturnT.FAIL_CODE, "Cron表达式不能为空");
            }
            
            // 4. 调度中心编排模式工作流上线
            return onlineCentralWorkflow(workflow, cronExpression, updateUser);

        } catch (Exception e) {
            log.error("工作流{}上线失败", workflowId, e);
            return new ReturnT<>(ReturnT.FAIL_CODE, "上线失败: " + e.getMessage());
        }
    }
    
    /**
     * 工作流下线 - 停止调度任务
     * 
     * @param workflowId 工作流ID
     * @param updateUser 操作用户
     * @return 操作结果
     */
    @Transactional
    public ReturnT<String> offlineWorkflow(Long workflowId, String updateUser) {
        try {
            log.info("开始下线工作流: {}", workflowId);
            
            // 1. 验证工作流是否存在且已上线
            Workflow workflow = workflowDao.load(workflowId);
            if (workflow == null) {
                return new ReturnT<>(ReturnT.FAIL_CODE, "工作流不存在");
            }
            
            if (!Workflow.Status.ONLINE.getCode().equals(workflow.getStatus())) {
                return new ReturnT<>(ReturnT.FAIL_CODE, "工作流未上线");
            }

            // 2. 调度中心编排模式工作流下线
            return offlineCentralWorkflow(workflow, updateUser);
            
        } catch (Exception e) {
            log.error("工作流{}下线失败", workflowId, e);
            return new ReturnT<>(ReturnT.FAIL_CODE, "下线失败: " + e.getMessage());
        }
    }

    /**
     * 检查工作流是否可以安全下线
     *
     * @param workflowId 工作流ID
     * @return 检查结果
     */
    public ReturnT<Map<String, Object>> checkOfflineSafety(Long workflowId) {
        try {
            Workflow workflow = workflowDao.load(workflowId);
            if (workflow == null) {
                return new ReturnT<>(ReturnT.FAIL_CODE, "工作流不存在");
            }

            Map<String, Object> result = new HashMap<>();
            result.put("workflowId", workflowId);
            result.put("workflowStatus", workflow.getStatus());
            result.put("canOffline", true);
            result.put("warnings", new ArrayList<>());

            if (workflow.getJobId() != null) {
                JobInfo jobInfo = jobInfoDao.loadById(workflow.getJobId());
                if (jobInfo != null) {
                    result.put("scheduleStatus", jobInfo.getTriggerStatus());

                    List<String> warnings = new ArrayList<>();
                    if (jobInfo.getTriggerStatus() == 1) {
                        warnings.add("工作流正在调度中，下线将停止自动执行");
                    }

                    result.put("warnings", warnings);
                    result.put("hasWarnings", !warnings.isEmpty());
                }
            }

            return new ReturnT<>(result);

        } catch (Exception e) {
            log.error("检查工作流{}下线安全性失败", workflowId, e);
            return new ReturnT<>(ReturnT.FAIL_CODE, "检查失败: " + e.getMessage());
        }
    }

    /**
     * 启动工作流调度
     */
    public ReturnT<String> startSchedule(Long workflowId) {
        Workflow workflow = workflowDao.load(workflowId);
        if (workflow == null || workflow.getJobId() == null) {
            return new ReturnT<>(ReturnT.FAIL_CODE, "工作流不存在或未上线");
        }
        
        JobInfo jobInfo = jobInfoDao.loadById(workflow.getJobId());
        if (jobInfo == null) {
            return new ReturnT<>(ReturnT.FAIL_CODE, "调度任务不存在");
        }
        
        jobInfo.setTriggerStatus(1); // 启动调度
        jobInfo.setUpdateTime(new Date());
        jobInfoDao.update(jobInfo);

        return new ReturnT<>("调度启动成功");
    }
    
    /**
     * 停止工作流调度
     */
    public ReturnT<String> stopSchedule(Long workflowId) {
        Workflow workflow = workflowDao.load(workflowId);
        if (workflow == null || workflow.getJobId() == null) {
            return new ReturnT<>(ReturnT.FAIL_CODE, "工作流不存在或未上线");
        }
        
        JobInfo jobInfo = jobInfoDao.loadById(workflow.getJobId());
        if (jobInfo == null) {
            return new ReturnT<>(ReturnT.FAIL_CODE, "调度任务不存在");
        }
        
        jobInfo.setTriggerStatus(0); // 停止调度
        jobInfo.setUpdateTime(new Date());
        jobInfoDao.update(jobInfo);

        return new ReturnT<>("调度停止成功");
    }

    /**
     * 启动工作流调度
     *
     * @param workflowId 工作流ID
     * @return 操作结果
     */
    public ReturnT<String> startWorkflowSchedule(Long workflowId) {
        try {
            log.info("启动工作流{}的调度", workflowId);

            Workflow workflow = workflowDao.load(workflowId);
            if (workflow == null) {
                return new ReturnT<>(ReturnT.FAIL_CODE, "工作流不存在");
            }

            if (!Workflow.Status.ONLINE.getCode().equals(workflow.getStatus())) {
                return new ReturnT<>(ReturnT.FAIL_CODE, "只有已上线的工作流才能启动调度");
            }

            if (workflow.getJobId() == null) {
                return new ReturnT<>(ReturnT.FAIL_CODE, "工作流没有关联的调度任务");
            }

            JobInfo jobInfo = jobInfoDao.loadById(workflow.getJobId());
            if (jobInfo == null) {
                return new ReturnT<>(ReturnT.FAIL_CODE, "关联的XXL-JOB任务不存在");
            }

            if (jobInfo.getTriggerStatus() == 1) {
                return new ReturnT<>(ReturnT.FAIL_CODE, "调度任务已经在运行中");
            }

            // 启动调度
            jobInfo.setTriggerStatus(1);
            jobInfo.setUpdateTime(new Date());
            int result = jobInfoDao.update(jobInfo);

            if (result > 0) {
                log.info("工作流{}的调度启动成功", workflowId);
                return new ReturnT<>("调度启动成功");
            } else {
                return new ReturnT<>(ReturnT.FAIL_CODE, "启动调度失败");
            }

        } catch (Exception e) {
            log.error("启动工作流{}的调度失败", workflowId, e);
            return new ReturnT<>(ReturnT.FAIL_CODE, "启动失败: " + e.getMessage());
        }
    }

    /**
     * 停止工作流调度
     *
     * @param workflowId 工作流ID
     * @return 操作结果
     */
    public ReturnT<String> stopWorkflowSchedule(Long workflowId) {
        try {
            log.info("停止工作流{}的调度", workflowId);

            Workflow workflow = workflowDao.load(workflowId);
            if (workflow == null) {
                return new ReturnT<>(ReturnT.FAIL_CODE, "工作流不存在");
            }

            if (!Workflow.Status.ONLINE.getCode().equals(workflow.getStatus())) {
                return new ReturnT<>(ReturnT.FAIL_CODE, "只有已上线的工作流才能停止调度");
            }

            if (workflow.getJobId() == null) {
                return new ReturnT<>(ReturnT.FAIL_CODE, "工作流没有关联的调度任务");
            }

            JobInfo jobInfo = jobInfoDao.loadById(workflow.getJobId());
            if (jobInfo == null) {
                return new ReturnT<>(ReturnT.FAIL_CODE, "关联的XXL-JOB任务不存在");
            }

            if (jobInfo.getTriggerStatus() == 0) {
                return new ReturnT<>(ReturnT.FAIL_CODE, "调度任务已经停止");
            }

            // 停止调度
            jobInfo.setTriggerStatus(0);
            jobInfo.setUpdateTime(new Date());
            int result = jobInfoDao.update(jobInfo);

            if (result > 0) {
                log.info("工作流{}的调度停止成功", workflowId);
                return new ReturnT<>("调度停止成功");
            } else {
                return new ReturnT<>(ReturnT.FAIL_CODE, "停止调度失败");
            }

        } catch (Exception e) {
            log.error("停止工作流{}的调度失败", workflowId, e);
            return new ReturnT<>(ReturnT.FAIL_CODE, "停止失败: " + e.getMessage());
        }
    }

    /**
     * 获取工作流调度状态
     *
     * @param workflowId 工作流ID
     * @return 调度状态信息
     */
    public ReturnT<Map<String, Object>> getScheduleStatus(Long workflowId) {
        try {
            Workflow workflow = workflowDao.load(workflowId);
            if (workflow == null) {
                return new ReturnT<>(ReturnT.FAIL_CODE, "工作流不存在");
            }

            Map<String, Object> status = new HashMap<>();
            status.put("workflowId", workflowId);
            status.put("workflowStatus", workflow.getStatus());
            status.put("workflowStatusName", workflow.getStatusName());

            if (workflow.getJobId() != null) {
                JobInfo jobInfo = jobInfoDao.loadById(workflow.getJobId());
                if (jobInfo != null) {
                    status.put("jobId", jobInfo.getId());
                    status.put("scheduleStatus", jobInfo.getTriggerStatus());
                    status.put("scheduleStatusName", jobInfo.getTriggerStatus() == 1 ? "运行中" : "已停止");
                    status.put("cronExpression", jobInfo.getScheduleConf());
                    status.put("lastTriggerTime", jobInfo.getTriggerLastTime());
                    status.put("nextTriggerTime", jobInfo.getTriggerNextTime());
                    status.put("jobDesc", jobInfo.getJobDesc());
                } else {
                    status.put("jobId", null);
                    status.put("scheduleStatus", -1);
                    status.put("scheduleStatusName", "任务不存在");
                }
            } else {
                status.put("jobId", null);
                status.put("scheduleStatus", -1);
                status.put("scheduleStatusName", "未关联任务");
            }

            return new ReturnT<>(status);

        } catch (Exception e) {
            log.error("获取工作流{}调度状态失败", workflowId, e);
            return new ReturnT<>(ReturnT.FAIL_CODE, "获取状态失败: " + e.getMessage());
        }
    }

    /**
     * 更新工作流调度配置
     *
     * @param config 调度配置
     * @param updateUser 操作用户
     * @return 操作结果
     */
    @Transactional
    public ReturnT<String> updateScheduleConfig(WorkflowScheduleConfigDTO config, String updateUser) {
        try {
            log.info("开始更新工作流{}的调度配置", config.getWorkflowId());

            // 1. 参数验证
            ReturnT<String> validateResult = validateScheduleConfig(config);
            if (validateResult.getCode() != ReturnT.SUCCESS_CODE) {
                return validateResult;
            }

            // 2. 验证工作流是否存在且已上线
            Workflow workflow = workflowDao.load(config.getWorkflowId());
            if (workflow == null) {
                return new ReturnT<>(ReturnT.FAIL_CODE, "工作流不存在");
            }

            if (!Workflow.Status.ONLINE.getCode().equals(workflow.getStatus())) {
                return new ReturnT<>(ReturnT.FAIL_CODE, "只有已上线的工作流才能修改调度配置");
            }

            // 2. 调度中心编排模式：直接更新workflow表
            if (config.getCronExpression() != null) {
                workflow.setCronExpression(config.getCronExpression());
                workflow.setUpdateUser(updateUser);
                workflow.setUpdateTime(new Date());

                int workflowUpdateResult = workflowDao.update(workflow);
                if (workflowUpdateResult <= 0) {
                    return new ReturnT<>(ReturnT.FAIL_CODE, "更新调度配置失败");
                }

                log.info("更新工作流{}的调度配置成功", config.getWorkflowId());
            }

            // 3. 通知调度中心调度器更新调度
            try {
                centralWorkflowScheduler.updateWorkflowSchedule(config.getWorkflowId());
                log.info("已通知调度中心调度器更新工作流调度: workflowId={}", config.getWorkflowId());
            } catch (Exception e) {
                log.warn("通知调度中心调度器失败，但调度配置更新成功: workflowId={}", config.getWorkflowId(), e);
            }

            log.info("工作流{}的调度配置更新成功", config.getWorkflowId());
            return new ReturnT<>("调度配置更新成功");

        } catch (Exception e) {
            log.error("更新工作流{}的调度配置失败", config.getWorkflowId(), e);
            return new ReturnT<>(ReturnT.FAIL_CODE, "更新失败: " + e.getMessage());
        }
    }

    /**
     * 获取工作流调度配置
     *
     * @param workflowId 工作流ID
     * @return 调度配置
     */
    public ReturnT<WorkflowScheduleConfigDTO> getScheduleConfig(Long workflowId) {
        try {
            // 1. 验证工作流是否存在且已上线
            Workflow workflow = workflowDao.load(workflowId);
            if (workflow == null) {
                return new ReturnT<>(ReturnT.FAIL_CODE, "工作流不存在");
            }

            if (!Workflow.Status.ONLINE.getCode().equals(workflow.getStatus())) {
                return new ReturnT<>(ReturnT.FAIL_CODE, "只有已上线的工作流才有调度配置");
            }

            // 2. 调度中心编排模式：返回简化的配置
            WorkflowScheduleConfigDTO config = createCentralScheduleConfig(workflow);

            return new ReturnT<>(config);

        } catch (Exception e) {
            log.error("获取工作流{}的调度配置失败", workflowId, e);
            return new ReturnT<>(ReturnT.FAIL_CODE, "获取失败: " + e.getMessage());
        }
    }

    /**
     * 从配置DTO更新JobInfo
     */
    private void updateJobInfoFromConfig(JobInfo jobInfo, WorkflowScheduleConfigDTO config) {
        // 更新调度配置
        if (config.getCronExpression() != null) {
            jobInfo.setScheduleConf(config.getCronExpression());
        }

        // 更新执行器配置
        if (config.getExecutorRouteStrategy() != null) {
            jobInfo.setExecutorRouteStrategy(config.getExecutorRouteStrategy());
        }
        if (config.getExecutorBlockStrategy() != null) {
            jobInfo.setExecutorBlockStrategy(config.getExecutorBlockStrategy());
        }
        if (config.getExecutorTimeout() != null) {
            jobInfo.setExecutorTimeout(config.getExecutorTimeout());
        }
        if (config.getExecutorFailRetryCount() != null) {
            jobInfo.setExecutorFailRetryCount(config.getExecutorFailRetryCount());
        }

        // 更新其他配置
        if (config.getMisfireStrategy() != null) {
            jobInfo.setMisfireStrategy(config.getMisfireStrategy());
        }
        if (config.getJobDesc() != null) {
            jobInfo.setJobDesc(config.getJobDesc());
        }
        if (config.getAlarmEmail() != null) {
            jobInfo.setAlarmEmail(config.getAlarmEmail());
        }
    }

    /**
     * 将JobInfo转换为配置DTO
     */
    private WorkflowScheduleConfigDTO convertToConfigDTO(JobInfo jobInfo, Workflow workflow) {
        WorkflowScheduleConfigDTO config = new WorkflowScheduleConfigDTO();

        config.setWorkflowId(workflow.getId());

        // 优先使用workflow表中的cron_expression，如果为空则使用XXL-JOB中的配置
        String cronExpression = workflow.getCronExpression();
        if (cronExpression == null || cronExpression.trim().isEmpty()) {
            cronExpression = jobInfo.getScheduleConf();
        }
        config.setCronExpression(cronExpression);

        config.setExecutorRouteStrategy(jobInfo.getExecutorRouteStrategy());
        config.setExecutorBlockStrategy(jobInfo.getExecutorBlockStrategy());
        config.setExecutorTimeout(jobInfo.getExecutorTimeout());
        config.setExecutorFailRetryCount(jobInfo.getExecutorFailRetryCount());
        config.setMisfireStrategy(jobInfo.getMisfireStrategy());
        config.setJobDesc(jobInfo.getJobDesc());
        config.setAlarmEmail(jobInfo.getAlarmEmail());

        return config;
    }

    /**
     * 调度中心编排模式工作流上线
     */
    private ReturnT<String> onlineCentralWorkflow(Workflow workflow, String cronExpression, String updateUser) {
        try {
            log.info("调度中心编排模式工作流上线: workflowId={}, cronExpression={}",
                workflow.getId(), cronExpression);

            // 1. 为工作流节点创建执行器任务（如果不存在）
            ReturnT<String> nodeJobResult = workflowJobService.createJobsForWorkflow(workflow.getId());
            if (nodeJobResult.getCode() != ReturnT.SUCCESS_CODE) {
                return nodeJobResult;
            }

            // 2. 更新工作流状态和Cron表达式
            workflow.setCronExpression(cronExpression);
            workflow.setStatus(Workflow.Status.ONLINE.getCode());
            workflow.setUpdateUser(updateUser);
            workflow.setUpdateTime(new Date());

            int updateResult = workflowDao.update(workflow);
            if (updateResult <= 0) {
                return new ReturnT<>(ReturnT.FAIL_CODE, "更新工作流状态失败");
            }

            // 3. 通知调度中心调度器添加工作流调度
            try {
                centralWorkflowScheduler.addWorkflowSchedule(workflow.getId());
                log.info("已通知调度中心调度器添加工作流调度: workflowId={}", workflow.getId());
            } catch (Exception e) {
                log.warn("通知调度中心调度器失败，但工作流上线成功: workflowId={}", workflow.getId(), e);
            }

            log.info("调度中心编排模式工作流{}上线成功", workflow.getId());
            return new ReturnT<>("工作流上线成功（调度中心编排）");

        } catch (Exception e) {
            log.error("调度中心编排模式工作流上线失败: workflowId={}", workflow.getId(), e);
            return new ReturnT<>(ReturnT.FAIL_CODE, "工作流上线失败: " + e.getMessage());
        }
    }

    /**
     * 调度中心编排模式工作流下线
     */
    private ReturnT<String> offlineCentralWorkflow(Workflow workflow, String updateUser) {
        try {
            log.info("调度中心编排模式工作流下线: workflowId={}", workflow.getId());

            // 1. 更新工作流状态
            workflow.setStatus(Workflow.Status.OFFLINE.getCode());
            workflow.setUpdateUser(updateUser);
            workflow.setUpdateTime(new Date());

            int updateResult = workflowDao.update(workflow);
            if (updateResult <= 0) {
                return new ReturnT<>(ReturnT.FAIL_CODE, "更新工作流状态失败");
            }

            // 2. 通知调度中心调度器移除工作流调度
            try {
                centralWorkflowScheduler.removeWorkflowSchedule(workflow.getId());
                log.info("已通知调度中心调度器移除工作流调度: workflowId={}", workflow.getId());
            } catch (Exception e) {
                log.warn("通知调度中心调度器失败，但工作流下线成功: workflowId={}", workflow.getId(), e);
            }

            log.info("调度中心编排模式工作流{}下线成功", workflow.getId());
            return new ReturnT<>("工作流下线成功（调度中心编排）");

        } catch (Exception e) {
            log.error("调度中心编排模式工作流下线失败: workflowId={}", workflow.getId(), e);
            return new ReturnT<>(ReturnT.FAIL_CODE, "工作流下线失败: " + e.getMessage());
        }
    }

    /**
     * 创建调度中心编排模式的调度配置
     */
    private WorkflowScheduleConfigDTO createCentralScheduleConfig(Workflow workflow) {
        WorkflowScheduleConfigDTO config = new WorkflowScheduleConfigDTO();

        config.setWorkflowId(workflow.getId());
        config.setCronExpression(workflow.getCronExpression());

        // 调度中心编排模式的默认配置
        config.setExecutorRouteStrategy("CENTRAL_ORCHESTRATION");
        config.setExecutorBlockStrategy("SERIAL_EXECUTION");
        config.setExecutorTimeout(0); // 无超时限制
        config.setExecutorFailRetryCount(0); // 不重试
        config.setMisfireStrategy("DO_NOTHING");
        config.setJobDesc("调度中心编排模式 - " + workflow.getName());
        config.setAlarmEmail("");

        return config;
    }

    /**
     * 手动执行工作流
     *
     * @param workflowId 工作流ID
     * @param executeUser 执行用户
     * @return 执行结果
     */
    public ReturnT<String> executeWorkflow(Long workflowId, String executeUser) {
        // 委托给专门的执行服务处理
        return workflowExecutionService.executeWorkflow(workflowId, executeUser, null);
    }

    /**
     * 获取工作流的入口节点JobInfo
     * 入口节点是指没有前置依赖的节点
     */
    private List<JobInfo> getWorkflowEntryJobInfos(Long workflowId) {
        try {
            // 1. 获取工作流的所有节点
            List<WorkflowNode> nodes = workflowNodeDao.listByWorkflow(workflowId);
            if (nodes == null || nodes.isEmpty()) {
                log.warn("工作流{}没有节点", workflowId);
                return Collections.emptyList();
            }

            // 2. 获取所有节点间的依赖关系
            List<WorkflowNodeRelation> relations = workflowNodeRelationDao.listByWorkflow(workflowId);

            // 3. 找出所有作为后置节点的节点代码
            Set<String> postNodeCodes = new HashSet<>();
            if (relations != null && !relations.isEmpty()) {
                for (WorkflowNodeRelation relation : relations) {
                    postNodeCodes.add(relation.getPostNodeCode());
                }
            }

            // 4. 找出没有前置依赖的节点（入口节点）
            List<WorkflowNode> entryNodes = new ArrayList<>();
            for (WorkflowNode node : nodes) {
                if (!postNodeCodes.contains(node.getNodeCode())) {
                    entryNodes.add(node);
                }
            }

            if (entryNodes.isEmpty()) {
                log.warn("工作流{}没有入口节点（可能存在循环依赖）", workflowId);
                // 如果没有入口节点，就使用所有节点
                entryNodes = nodes;
            }

            // 5. 获取入口节点对应的JobInfo
            List<JobInfo> entryJobInfos = new ArrayList<>();
            for (WorkflowNode entryNode : entryNodes) {
                if (entryNode.getJobId() != null && entryNode.getJobId() > 0) {
                    JobInfo jobInfo = jobInfoDao.loadById(entryNode.getJobId().intValue());
                    if (jobInfo != null) {
                        entryJobInfos.add(jobInfo);
                        log.info("找到工作流{}的入口节点任务: nodeCode={}, jobId={}",
                                workflowId, entryNode.getNodeCode(), jobInfo.getId());
                    }
                }
            }

            return entryJobInfos;

        } catch (Exception e) {
            log.error("获取工作流入口节点任务失败: workflowId={}", workflowId, e);
            return Collections.emptyList();
        }
    }

    /**
     * 构建执行参数
     */
    private String buildExecuteParam(Long workflowId, String jobJson) {
        try {
            Map<String, Object> param = new HashMap<>();
            param.put("workflowId", workflowId);
            param.put("executeType", "MANUAL");
            param.put("executeTime", System.currentTimeMillis());

            if (jobJson != null && !jobJson.trim().isEmpty()) {
                param.put("jobJson", jobJson);
            }

            return new ObjectMapper().writeValueAsString(param);
        } catch (Exception e) {
            log.error("构建执行参数失败", e);
            return "{\"workflowId\":" + workflowId + ",\"executeType\":\"MANUAL\"}";
        }
    }

    /**
     * 触发任务执行
     */
    private ReturnT<String> triggerJob(Integer jobId, String executeParam, String executeUser) {
        try {
            log.info("开始触发XXL-JOB任务执行: jobId={}, executeParam={}, executeUser={}",
                    jobId, executeParam, executeUser);

            // 验证JobInfo是否存在
            JobInfo jobInfo = jobInfoDao.loadById(jobId);
            if (jobInfo == null) {
                log.error("JobInfo不存在: jobId={}", jobId);
                return new ReturnT<>(ReturnT.FAIL_CODE, "任务不存在: " + jobId);
            }

            // 使用XXL-JOB的JobTriggerPoolHelper来触发任务
            // 参数说明：
            // - jobId: 任务ID
            // - TriggerTypeEnum.MANUAL: 手动触发类型
            // - -1: 使用任务配置的重试次数
            // - null: 不使用分片参数
            // - executeParam: 执行参数（包含工作流定义JSON）
            // - null: 使用默认执行器地址列表
            JobTriggerPoolHelper.trigger(
                jobId,                    // 任务ID
                TriggerTypeEnum.MANUAL,   // 触发类型：手动触发
                -1,                       // 失败重试次数（-1表示使用任务配置的重试次数）
                null,                     // 分片参数（工作流不需要分片）
                executeParam,             // 执行参数（包含工作流定义JSON）
                null                      // 执行器地址列表（null表示使用默认）
            );

            // 生成执行ID用于跟踪
            String executionId = "manual_" + System.currentTimeMillis() + "_" + jobId;

            log.info("XXL-JOB任务触发成功: jobId={}, executionId={}", jobId, executionId);

            return new ReturnT<>(executionId);

        } catch (Exception e) {
            log.error("触发XXL-JOB任务执行失败: jobId={}", jobId, e);
            return new ReturnT<>(ReturnT.FAIL_CODE, "触发失败: " + e.getMessage());
        }
    }

    /**
     * 验证调度配置参数
     */
    private ReturnT<String> validateScheduleConfig(WorkflowScheduleConfigDTO config) {
        if (config == null) {
            return new ReturnT<>(ReturnT.FAIL_CODE, "调度配置不能为空");
        }

        if (config.getWorkflowId() == null) {
            return new ReturnT<>(ReturnT.FAIL_CODE, "工作流ID不能为空");
        }

        if (config.getCronExpression() == null || config.getCronExpression().trim().isEmpty()) {
            return new ReturnT<>(ReturnT.FAIL_CODE, "Cron表达式不能为空");
        }

        // 验证Cron表达式格式（简单验证）
        String cronExpression = config.getCronExpression().trim();
        String[] cronParts = cronExpression.split("\\s+");
        if (cronParts.length != 6) {
            return new ReturnT<>(ReturnT.FAIL_CODE, "Cron表达式格式错误，应为6位格式：秒 分 时 日 月 周");
        }

        // 验证超时时间
        if (config.getExecutorTimeout() != null && config.getExecutorTimeout() < 0) {
            return new ReturnT<>(ReturnT.FAIL_CODE, "超时时间不能为负数");
        }

        // 验证重试次数
        if (config.getExecutorFailRetryCount() != null && config.getExecutorFailRetryCount() < 0) {
            return new ReturnT<>(ReturnT.FAIL_CODE, "重试次数不能为负数");
        }

        return new ReturnT<>("参数验证通过");
    }

    /**
     * 构建工作流JobInfo对象
     */
    private JobInfo buildWorkflowJobInfo(Workflow workflow, String cronExpression) {
        JobInfo jobInfo = new JobInfo();

        // 获取执行器组ID（使用第一个执行器组）
        int jobGroupId = jobGroupDao.findAll().isEmpty() ? 1 : jobGroupDao.findAll().get(0).getId();

        jobInfo.setJobGroup(jobGroupId);
        jobInfo.setJobDesc("工作流调度-" + workflow.getName());
        jobInfo.setAddTime(new Date());
        jobInfo.setUpdateTime(new Date());
        jobInfo.setAuthor("system");
        jobInfo.setAlarmEmail("");

        // 调度配置
        jobInfo.setScheduleType("CRON");
        jobInfo.setScheduleConf(cronExpression);
        jobInfo.setMisfireStrategy("DO_NOTHING");

        // 运行模式 - BEAN模式，使用工作流执行器
        jobInfo.setGlueType("BEAN");
        jobInfo.setGlueSource("");
        jobInfo.setGlueRemark("");
        jobInfo.setGlueUpdatetime(new Date());
        jobInfo.setExecutorHandler("workflowExecutor");
        jobInfo.setExecutorParam(String.valueOf(workflow.getId())); // 传递工作流ID
        jobInfo.setExecutorRouteStrategy("FIRST");
        jobInfo.setExecutorBlockStrategy("SERIAL_EXECUTION");
        jobInfo.setExecutorTimeout(3600); // 默认1小时超时
        jobInfo.setExecutorFailRetryCount(3); // 默认重试3次

        jobInfo.setChildJobId("");
        jobInfo.setTriggerStatus(0); // 创建时为停止状态，需要手动启动
        jobInfo.setTriggerLastTime(0);
        jobInfo.setTriggerNextTime(0);

        // 构建工作流JSON配置
        String jobJson = buildWorkflowJobJson(workflow);
        jobInfo.setJobJson(jobJson);

        return jobInfo;
    }

    /**
     * 构建工作流的JSON配置
     * 包含工作流基本信息、节点配置、节点关系等
     */
    private String buildWorkflowJobJson(Workflow workflow) {
        try {
            Map<String, Object> jobConfig = new HashMap<>();

            // 1. 工作流基本信息
            Map<String, Object> workflowInfo = new HashMap<>();
            workflowInfo.put("workflowId", workflow.getId());
            workflowInfo.put("workflowName", workflow.getName());
            workflowInfo.put("workflowCode", workflow.getCode());
            workflowInfo.put("category", workflow.getCategory());
            workflowInfo.put("version", workflow.getVersion());
            workflowInfo.put("globalParams", workflow.getGlobalParams());
            workflowInfo.put("warningType", workflow.getWarningType());
            jobConfig.put("workflow", workflowInfo);

            // 2. 获取工作流节点信息
            List<WorkflowNode> nodes = workflowNodeDao.listByWorkflow(workflow.getId());
            List<Map<String, Object>> nodeConfigs = new ArrayList<>();

            for (WorkflowNode node : nodes) {
                Map<String, Object> nodeConfig = new HashMap<>();
                nodeConfig.put("nodeId", node.getId());
                nodeConfig.put("nodeCode", node.getNodeCode());
                nodeConfig.put("nodeName", node.getNodeName());
                nodeConfig.put("nodeType", node.getNodeType());
                nodeConfig.put("description", node.getDescription());
                nodeConfig.put("timeout", node.getTimeout());
                nodeConfig.put("retryTimes", node.getRetryTimes());
                nodeConfig.put("retryInterval", node.getRetryInterval());
                nodeConfig.put("maxRetryTimes", node.getMaxRetryTimes());
                nodeConfig.put("failureStrategy", node.getFailureStrategy());
                nodeConfig.put("priority", node.getPriority());
                nodeConfig.put("workerGroup", node.getWorkerGroup());
                nodeConfig.put("environmentCode", node.getEnvironmentCode());
                nodeConfig.put("datasourceId", node.getDatasourceId());

                // 解析节点配置参数
                if (StringUtils.hasText(node.getConfigParams())) {
                    try {
                        Map<String, Object> configParams = parseJsonToMap(node.getConfigParams());
                        nodeConfig.put("configParams", configParams);
                    } catch (Exception e) {
                        log.warn("解析节点{}配置参数失败: {}", node.getNodeCode(), e.getMessage());
                        nodeConfig.put("configParams", node.getConfigParams());
                    }
                } else {
                    nodeConfig.put("configParams", new HashMap<>());
                }

                nodeConfigs.add(nodeConfig);
            }
            jobConfig.put("nodes", nodeConfigs);

            // 3. 获取节点关系信息
            List<WorkflowNodeRelation> relations = workflowNodeRelationDao.listByWorkflow(workflow.getId());
            List<Map<String, Object>> relationConfigs = new ArrayList<>();

            for (WorkflowNodeRelation relation : relations) {
                Map<String, Object> relationConfig = new HashMap<>();
                relationConfig.put("preNodeCode", relation.getPreNodeCode());
                relationConfig.put("postNodeCode", relation.getPostNodeCode());
                relationConfig.put("conditionType", relation.getConditionType());
                relationConfig.put("conditionParams", relation.getConditionParams());
                relationConfigs.add(relationConfig);
            }
            jobConfig.put("relations", relationConfigs);

            // 4. 执行配置
            Map<String, Object> executionConfig = new HashMap<>();
            executionConfig.put("executionType", "DAG");
            executionConfig.put("parallelism", 1);
            executionConfig.put("createTime", new Date());
            jobConfig.put("execution", executionConfig);

            // 转换为JSON字符串
            return convertMapToJson(jobConfig);

        } catch (Exception e) {
            log.error("构建工作流{}的JSON配置失败", workflow.getId(), e);
            // 返回基本配置
            Map<String, Object> basicConfig = new HashMap<>();
            basicConfig.put("workflowId", workflow.getId());
            basicConfig.put("error", "构建配置失败: " + e.getMessage());
            return convertMapToJson(basicConfig);
        }
    }

    /**
     * 解析JSON字符串为Map
     */
    private Map<String, Object> parseJsonToMap(String jsonStr) {
        // 这里可以使用Jackson或Gson等JSON库
        // 简单实现，实际项目中建议使用专业的JSON库
        try {
            // 假设使用Jackson ObjectMapper
            com.fasterxml.jackson.databind.ObjectMapper mapper = new com.fasterxml.jackson.databind.ObjectMapper();
            return mapper.readValue(jsonStr, Map.class);
        } catch (Exception e) {
            log.warn("JSON解析失败，返回空Map: {}", e.getMessage());
            return new HashMap<>();
        }
    }

    /**
     * 将Map转换为JSON字符串
     */
    private String convertMapToJson(Map<String, Object> map) {
        try {
            // 假设使用Jackson ObjectMapper
            com.fasterxml.jackson.databind.ObjectMapper mapper = new com.fasterxml.jackson.databind.ObjectMapper();
            return mapper.writeValueAsString(map);
        } catch (Exception e) {
            log.error("Map转JSON失败: {}", e.getMessage());
            return "{}";
        }
    }

}
