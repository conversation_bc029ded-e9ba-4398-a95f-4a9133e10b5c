package com.sevb.admin.service;

import com.sevb.admin.core.model.WorkflowInstance;
import com.sevb.core.biz.model.ReturnT;

import java.util.Date;
import java.util.Map;

/**
 * 工作流实例服务接口
 * 
 * <AUTHOR>
 */
public interface WorkflowInstanceService {

    /**
     * 分页查询工作流实例
     */
    ReturnT<Map<String, Object>> pageList(int page, int size, Long projectId, Long workflowId, String workflowName,
                                         Integer status, String triggerType, Date startTime, Date endTime);

    /**
     * 根据ID查询工作流实例
     */
    ReturnT<WorkflowInstance> getById(Long id);

    /**
     * 停止工作流实例
     */
    ReturnT<String> stopInstance(Long id);

    /**
     * 重试工作流实例
     */
    ReturnT<String> retryInstance(Long id);

    /**
     * 查询工作流实例统计信息
     */
    ReturnT<Map<String, Object>> getInstanceStats(Long workflowId, Integer days);

    /**
     * 查询工作流的最近实例
     */
    ReturnT<Map<String, Object>> getRecentInstances(Long workflowId, Integer limit);

    /**
     * 删除工作流实例
     */
    ReturnT<String> deleteInstance(Long id);

    /**
     * 批量删除工作流实例
     */
    ReturnT<String> batchDeleteInstances(Long[] ids);

    /**
     * 创建工作流实例
     */
    ReturnT<Long> createInstance(WorkflowInstance workflowInstance);

    /**
     * 更新工作流实例状态
     */
    ReturnT<String> updateInstanceStatus(Long id, Integer status, String errorMessage);

    /**
     * 根据XXL-JOB日志ID查询实例
     */
    ReturnT<WorkflowInstance> getByJobLogId(Long jobLogId);
}
