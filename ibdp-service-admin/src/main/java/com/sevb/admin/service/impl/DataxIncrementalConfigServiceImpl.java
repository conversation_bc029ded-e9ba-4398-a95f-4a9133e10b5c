package com.sevb.admin.service.impl;

import com.sevb.admin.core.model.DataxIncrementalConfig;
import com.sevb.admin.dao.DataxIncrementalConfigDao;
import com.sevb.admin.service.DataxIncrementalConfigService;
import com.sevb.core.biz.model.ReturnT;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * DataX增量同步配置服务实现类
 * 
 * <AUTHOR>
 */
@Service
public class DataxIncrementalConfigServiceImpl implements DataxIncrementalConfigService {

    private static final Logger logger = LoggerFactory.getLogger(DataxIncrementalConfigServiceImpl.class);

    @Autowired
    private DataxIncrementalConfigDao dataxIncrementalConfigDao;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ReturnT<String> save(DataxIncrementalConfig config) {
        if (config == null) {
            return new ReturnT<>(ReturnT.FAIL_CODE, "增量配置信息不能为空");
        }

        try {
            // 验证配置有效性
            ReturnT<String> validateResult = validateConfig(config);
            if (validateResult.getCode() != ReturnT.SUCCESS_CODE) {
                return validateResult;
            }

            // 检查节点是否已存在配置
            if (existsByNodeId(config.getNodeId())) {
                return new ReturnT<>(ReturnT.FAIL_CODE, "该节点的增量配置已存在");
            }

            // 设置默认值
            config.setDefaults();
            if (config.getCreateTime() == null) {
                config.setCreateTime(LocalDateTime.now());
            }
            if (config.getUpdateTime() == null) {
                config.setUpdateTime(LocalDateTime.now());
            }

            int result = dataxIncrementalConfigDao.save(config);
            if (result > 0) {
                logger.info("保存增量配置成功，ID: {}, 节点ID: {}", config.getId(), config.getNodeId());
                return new ReturnT<>(ReturnT.SUCCESS_CODE, "保存成功");
            } else {
                return new ReturnT<>(ReturnT.FAIL_CODE, "保存失败");
            }
        } catch (Exception e) {
            logger.error("保存增量配置失败", e);
            return new ReturnT<>(ReturnT.FAIL_CODE, "保存失败: " + e.getMessage());
        }
    }

    @Override
    public DataxIncrementalConfig load(Long id) {
        if (id == null) {
            return null;
        }
        try {
            return dataxIncrementalConfigDao.load(id);
        } catch (Exception e) {
            logger.error("查询增量配置失败，ID: {}", id, e);
            return null;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ReturnT<String> update(DataxIncrementalConfig config) {
        if (config == null || config.getId() == null) {
            return new ReturnT<>(ReturnT.FAIL_CODE, "增量配置信息不能为空");
        }

        try {
            // 检查记录是否存在
            DataxIncrementalConfig existing = dataxIncrementalConfigDao.load(config.getId());
            if (existing == null) {
                return new ReturnT<>(ReturnT.FAIL_CODE, "增量配置不存在");
            }

            // 验证配置有效性
            ReturnT<String> validateResult = validateConfig(config);
            if (validateResult.getCode() != ReturnT.SUCCESS_CODE) {
                return validateResult;
            }

            config.setUpdateTime(LocalDateTime.now());
            int result = dataxIncrementalConfigDao.update(config);
            if (result > 0) {
                logger.info("更新增量配置成功，ID: {}", config.getId());
                return new ReturnT<>(ReturnT.SUCCESS_CODE, "更新成功");
            } else {
                return new ReturnT<>(ReturnT.FAIL_CODE, "更新失败");
            }
        } catch (Exception e) {
            logger.error("更新增量配置失败，ID: {}", config.getId(), e);
            return new ReturnT<>(ReturnT.FAIL_CODE, "更新失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ReturnT<String> delete(Long id) {
        if (id == null) {
            return new ReturnT<>(ReturnT.FAIL_CODE, "ID不能为空");
        }

        try {
            DataxIncrementalConfig existing = dataxIncrementalConfigDao.load(id);
            if (existing == null) {
                return new ReturnT<>(ReturnT.FAIL_CODE, "增量配置不存在");
            }

            int result = dataxIncrementalConfigDao.delete(id);
            if (result > 0) {
                logger.info("删除增量配置成功，ID: {}", id);
                return new ReturnT<>(ReturnT.SUCCESS_CODE, "删除成功");
            } else {
                return new ReturnT<>(ReturnT.FAIL_CODE, "删除失败");
            }
        } catch (Exception e) {
            logger.error("删除增量配置失败，ID: {}", id, e);
            return new ReturnT<>(ReturnT.FAIL_CODE, "删除失败: " + e.getMessage());
        }
    }

    @Override
    public DataxIncrementalConfig findByNodeId(Long nodeId) {
        if (nodeId == null) {
            return null;
        }
        try {
            return dataxIncrementalConfigDao.findByNodeId(nodeId);
        } catch (Exception e) {
            logger.error("根据节点ID查询增量配置失败，节点ID: {}", nodeId, e);
            return null;
        }
    }

    @Override
    public List<DataxIncrementalConfig> findAllEnabled() {
        try {
            return dataxIncrementalConfigDao.findAllEnabled();
        } catch (Exception e) {
            logger.error("查询所有启用的增量配置失败", e);
            return null;
        }
    }

    @Override
    public ReturnT<List<DataxIncrementalConfig>> pageList(int page, int size, Long nodeId,
                                                         String incrementalType, Integer enabled) {
        try {
            int offset = (page - 1) * size;
            List<DataxIncrementalConfig> list = dataxIncrementalConfigDao.pageList(offset, size, nodeId,
                                                                                   incrementalType, enabled);
            int count = dataxIncrementalConfigDao.pageListCount(offset, size, nodeId, incrementalType, enabled);
            
            Map<String, Object> result = new HashMap<>();
            result.put("data", list);
            result.put("recordsFiltered", count);
            result.put("recordsTotal", count);
            
            return new ReturnT<>(list);
        } catch (Exception e) {
            logger.error("分页查询增量配置失败", e);
            return new ReturnT<>(ReturnT.FAIL_CODE, "查询失败: " + e.getMessage());
        }
    }

    @Override
    public List<DataxIncrementalConfig> findByIncrementalType(String incrementalType) {
        if (!StringUtils.hasText(incrementalType)) {
            return null;
        }
        try {
            return dataxIncrementalConfigDao.findByIncrementalType(incrementalType);
        } catch (Exception e) {
            logger.error("根据增量类型查询配置失败，增量类型: {}", incrementalType, e);
            return null;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ReturnT<String> batchUpdateEnabled(List<Long> ids, Integer enabled, String updateUser) {
        if (ids == null || ids.isEmpty()) {
            return new ReturnT<>(ReturnT.FAIL_CODE, "ID列表不能为空");
        }
        if (enabled == null) {
            return new ReturnT<>(ReturnT.FAIL_CODE, "启用状态不能为空");
        }

        try {
            int result = dataxIncrementalConfigDao.batchUpdateEnabled(ids, enabled, updateUser);
            if (result > 0) {
                logger.info("批量更新启用状态成功，更新数量: {}, 启用状态: {}", result, enabled);
                return new ReturnT<>(ReturnT.SUCCESS_CODE, "批量更新成功");
            } else {
                return new ReturnT<>(ReturnT.FAIL_CODE, "批量更新失败");
            }
        } catch (Exception e) {
            logger.error("批量更新启用状态失败", e);
            return new ReturnT<>(ReturnT.FAIL_CODE, "批量更新失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ReturnT<String> updateEnabled(Long id, Integer enabled, String updateUser) {
        if (id == null) {
            return new ReturnT<>(ReturnT.FAIL_CODE, "ID不能为空");
        }
        if (enabled == null) {
            return new ReturnT<>(ReturnT.FAIL_CODE, "启用状态不能为空");
        }

        try {
            int result = dataxIncrementalConfigDao.updateEnabled(id, enabled, updateUser);
            if (result > 0) {
                logger.info("更新启用状态成功，ID: {}, 启用状态: {}", id, enabled);
                return new ReturnT<>(ReturnT.SUCCESS_CODE, "更新成功");
            } else {
                return new ReturnT<>(ReturnT.FAIL_CODE, "更新失败");
            }
        } catch (Exception e) {
            logger.error("更新启用状态失败，ID: {}", id, e);
            return new ReturnT<>(ReturnT.FAIL_CODE, "更新失败: " + e.getMessage());
        }
    }

    @Override
    public boolean existsByNodeId(Long nodeId) {
        if (nodeId == null) {
            return false;
        }
        try {
            return dataxIncrementalConfigDao.existsByNodeId(nodeId);
        } catch (Exception e) {
            logger.error("检查节点配置是否存在失败，节点ID: {}", nodeId, e);
            return false;
        }
    }

    @Override
    public List<DataxIncrementalConfig> findByNodeIds(List<Long> nodeIds) {
        if (nodeIds == null || nodeIds.isEmpty()) {
            return null;
        }
        try {
            return dataxIncrementalConfigDao.findByNodeIds(nodeIds);
        } catch (Exception e) {
            logger.error("根据节点ID列表查询配置失败", e);
            return null;
        }
    }

    @Override
    public int countEnabled() {
        try {
            return dataxIncrementalConfigDao.countEnabled();
        } catch (Exception e) {
            logger.error("统计启用配置数量失败", e);
            return 0;
        }
    }

    @Override
    public int countTotal() {
        try {
            return dataxIncrementalConfigDao.countTotal();
        } catch (Exception e) {
            logger.error("统计总配置数量失败", e);
            return 0;
        }
    }

    @Override
    public List<DataxIncrementalConfig> findByIncrementalColumn(String incrementalColumn) {
        if (!StringUtils.hasText(incrementalColumn)) {
            return null;
        }
        try {
            return dataxIncrementalConfigDao.findByIncrementalColumn(incrementalColumn);
        } catch (Exception e) {
            logger.error("根据增量字段查询配置失败，增量字段: {}", incrementalColumn, e);
            return null;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ReturnT<String> saveOrUpdate(DataxIncrementalConfig config) {
        if (config == null) {
            return new ReturnT<>(ReturnT.FAIL_CODE, "增量配置信息不能为空");
        }

        try {
            // 验证配置有效性
            ReturnT<String> validateResult = validateConfig(config);
            if (validateResult.getCode() != ReturnT.SUCCESS_CODE) {
                return validateResult;
            }

            // 设置默认值
            config.setDefaults();
            config.setUpdateTime(LocalDateTime.now());
            if (config.getCreateTime() == null) {
                config.setCreateTime(LocalDateTime.now());
            }

            int result = dataxIncrementalConfigDao.saveOrUpdate(config);
            if (result > 0) {
                logger.info("保存或更新增量配置成功，节点ID: {}", config.getNodeId());
                return new ReturnT<>(ReturnT.SUCCESS_CODE, "保存或更新成功");
            } else {
                return new ReturnT<>(ReturnT.FAIL_CODE, "保存或更新失败");
            }
        } catch (Exception e) {
            logger.error("保存或更新增量配置失败", e);
            return new ReturnT<>(ReturnT.FAIL_CODE, "保存或更新失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ReturnT<String> batchDelete(List<Long> ids) {
        if (ids == null || ids.isEmpty()) {
            return new ReturnT<>(ReturnT.FAIL_CODE, "ID列表不能为空");
        }

        try {
            int result = dataxIncrementalConfigDao.batchDelete(ids);
            if (result > 0) {
                logger.info("批量删除增量配置成功，删除数量: {}", result);
                return new ReturnT<>(ReturnT.SUCCESS_CODE, "批量删除成功，删除数量: " + result);
            } else {
                return new ReturnT<>(ReturnT.FAIL_CODE, "批量删除失败");
            }
        } catch (Exception e) {
            logger.error("批量删除增量配置失败", e);
            return new ReturnT<>(ReturnT.FAIL_CODE, "批量删除失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ReturnT<String> copyConfig(Long sourceNodeId, Long targetNodeId, String createUser) {
        if (sourceNodeId == null || targetNodeId == null) {
            return new ReturnT<>(ReturnT.FAIL_CODE, "源节点ID和目标节点ID不能为空");
        }

        try {
            // 检查源配置是否存在
            if (!existsByNodeId(sourceNodeId)) {
                return new ReturnT<>(ReturnT.FAIL_CODE, "源节点配置不存在");
            }

            // 检查目标节点是否已有配置
            if (existsByNodeId(targetNodeId)) {
                return new ReturnT<>(ReturnT.FAIL_CODE, "目标节点配置已存在");
            }

            int result = dataxIncrementalConfigDao.copyConfig(sourceNodeId, targetNodeId, createUser);
            if (result > 0) {
                logger.info("复制增量配置成功，源节点ID: {}, 目标节点ID: {}", sourceNodeId, targetNodeId);
                return new ReturnT<>(ReturnT.SUCCESS_CODE, "复制配置成功");
            } else {
                return new ReturnT<>(ReturnT.FAIL_CODE, "复制配置失败");
            }
        } catch (Exception e) {
            logger.error("复制增量配置失败", e);
            return new ReturnT<>(ReturnT.FAIL_CODE, "复制配置失败: " + e.getMessage());
        }
    }

    @Override
    public DataxIncrementalConfig getDefaultTemplate() {
        try {
            return dataxIncrementalConfigDao.getDefaultTemplate();
        } catch (Exception e) {
            logger.error("获取默认配置模板失败", e);
            return null;
        }
    }

    @Override
    public ReturnT<String> validateConfig(DataxIncrementalConfig config) {
        if (config == null) {
            return new ReturnT<>(ReturnT.FAIL_CODE, "配置不能为空");
        }

        if (config.getNodeId() == null) {
            return new ReturnT<>(ReturnT.FAIL_CODE, "节点ID不能为空");
        }

        if (!StringUtils.hasText(config.getIncrementalType())) {
            return new ReturnT<>(ReturnT.FAIL_CODE, "增量类型不能为空");
        }

        if (!StringUtils.hasText(config.getIncrementalColumn())) {
            return new ReturnT<>(ReturnT.FAIL_CODE, "增量字段不能为空");
        }

        // 验证增量类型是否有效
        try {
            config.getIncrementalTypeEnum();
        } catch (IllegalArgumentException e) {
            return new ReturnT<>(ReturnT.FAIL_CODE, "无效的增量类型: " + config.getIncrementalType());
        }

        // 验证批次大小
        if (config.getBatchSize() != null && config.getBatchSize() <= 0) {
            return new ReturnT<>(ReturnT.FAIL_CODE, "批次大小必须大于0");
        }

        // 验证重试次数
        if (config.getMaxRetryTimes() != null && config.getMaxRetryTimes() < 0) {
            return new ReturnT<>(ReturnT.FAIL_CODE, "最大重试次数不能小于0");
        }

        // 验证断点间隔
        if (config.getCheckpointInterval() != null && config.getCheckpointInterval() <= 0) {
            return new ReturnT<>(ReturnT.FAIL_CODE, "断点间隔必须大于0");
        }

        return new ReturnT<>(ReturnT.SUCCESS_CODE, "配置验证通过");
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ReturnT<String> createDefaultConfig(Long nodeId, String createUser) {
        if (nodeId == null) {
            return new ReturnT<>(ReturnT.FAIL_CODE, "节点ID不能为空");
        }

        try {
            // 检查是否已存在配置
            if (existsByNodeId(nodeId)) {
                return new ReturnT<>(ReturnT.FAIL_CODE, "该节点的配置已存在");
            }

            DataxIncrementalConfig config = DataxIncrementalConfig.createDefault(nodeId, createUser);
            return save(config);
        } catch (Exception e) {
            logger.error("创建默认配置失败，节点ID: {}", nodeId, e);
            return new ReturnT<>(ReturnT.FAIL_CODE, "创建默认配置失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ReturnT<String> createFromNodeConfig(Long nodeId, String nodeConfigJson, String createUser) {
        if (nodeId == null) {
            return new ReturnT<>(ReturnT.FAIL_CODE, "节点ID不能为空");
        }

        try {
            // TODO: 解析节点配置JSON，提取增量相关配置
            // 这里需要根据实际的节点配置结构来实现
            logger.info("根据节点配置创建增量配置，节点ID: {}", nodeId);

            // 暂时创建默认配置
            return createDefaultConfig(nodeId, createUser);
        } catch (Exception e) {
            logger.error("根据节点配置创建增量配置失败，节点ID: {}", nodeId, e);
            return new ReturnT<>(ReturnT.FAIL_CODE, "创建配置失败: " + e.getMessage());
        }
    }

    @Override
    public ReturnT<Object> getConfigStatistics() {
        try {
            Map<String, Object> statistics = new HashMap<>();
            statistics.put("totalConfigs", countTotal());
            statistics.put("enabledConfigs", countEnabled());
            statistics.put("disabledConfigs", countTotal() - countEnabled());

            // 按增量类型统计
            Map<String, Integer> typeStats = new HashMap<>();
            typeStats.put("TIMESTAMP", findByIncrementalType("TIMESTAMP").size());
            typeStats.put("PRIMARY_KEY", findByIncrementalType("PRIMARY_KEY").size());
            typeStats.put("CDC", findByIncrementalType("CDC").size());
            statistics.put("typeStatistics", typeStats);

            return new ReturnT<>(statistics);
        } catch (Exception e) {
            logger.error("获取配置统计信息失败", e);
            return new ReturnT<>(ReturnT.FAIL_CODE, "获取统计信息失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ReturnT<String> batchEnable(List<Long> ids, String updateUser) {
        return batchUpdateEnabled(ids, 1, updateUser);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ReturnT<String> batchDisable(List<Long> ids, String updateUser) {
        return batchUpdateEnabled(ids, 0, updateUser);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ReturnT<String> resetToDefault(Long id, String updateUser) {
        if (id == null) {
            return new ReturnT<>(ReturnT.FAIL_CODE, "ID不能为空");
        }

        try {
            DataxIncrementalConfig existing = load(id);
            if (existing == null) {
                return new ReturnT<>(ReturnT.FAIL_CODE, "配置不存在");
            }

            // 重置为默认值
            DataxIncrementalConfig defaultConfig = DataxIncrementalConfig.createDefault(existing.getNodeId(), updateUser);
            defaultConfig.setId(id);
            defaultConfig.setCreateTime(existing.getCreateTime());
            defaultConfig.setCreateUser(existing.getCreateUser());

            return update(defaultConfig);
        } catch (Exception e) {
            logger.error("重置配置为默认值失败，ID: {}", id, e);
            return new ReturnT<>(ReturnT.FAIL_CODE, "重置失败: " + e.getMessage());
        }
    }

    @Override
    public ReturnT<String> exportConfig(List<Long> ids) {
        if (ids == null || ids.isEmpty()) {
            return new ReturnT<>(ReturnT.FAIL_CODE, "ID列表不能为空");
        }

        try {
            // TODO: 实现配置导出逻辑
            logger.info("导出配置，ID列表: {}", ids);
            return new ReturnT<>(ReturnT.SUCCESS_CODE, "导出功能待实现");
        } catch (Exception e) {
            logger.error("导出配置失败", e);
            return new ReturnT<>(ReturnT.FAIL_CODE, "导出失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ReturnT<String> importConfig(String configJson, String createUser) {
        if (!StringUtils.hasText(configJson)) {
            return new ReturnT<>(ReturnT.FAIL_CODE, "配置JSON不能为空");
        }

        try {
            // TODO: 实现配置导入逻辑
            logger.info("导入配置，创建人: {}", createUser);
            return new ReturnT<>(ReturnT.SUCCESS_CODE, "导入功能待实现");
        } catch (Exception e) {
            logger.error("导入配置失败", e);
            return new ReturnT<>(ReturnT.FAIL_CODE, "导入失败: " + e.getMessage());
        }
    }
}
