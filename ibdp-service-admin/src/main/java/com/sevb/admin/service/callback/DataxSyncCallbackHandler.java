package com.sevb.admin.service.callback;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.sevb.admin.core.dto.NodeExecutionResult;
import com.sevb.admin.core.model.WorkflowNode;
import com.sevb.admin.service.DataxSyncLogService;
import com.sevb.admin.service.DataxSyncStateService;
import com.sevb.admin.service.WorkflowNodeService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.Map;

/**
 * DataX增量同步回调处理器
 * 处理执行器返回的同步结果，更新同步状态和日志
 * 
 * <AUTHOR>
 */
@Service
public class DataxSyncCallbackHandler {

    private static final Logger logger = LoggerFactory.getLogger(DataxSyncCallbackHandler.class);

    @Autowired
    private DataxSyncStateService syncStateService;

    @Autowired
    private DataxSyncLogService syncLogService;

    @Autowired
    private WorkflowNodeService nodeService;

    private final ObjectMapper objectMapper = new ObjectMapper();

    /**
     * 处理DataX执行成功回调
     *
     * @param nodeCode 节点编码
     * @param executionId 执行ID
     * @param executionResult 执行结果对象
     */
    public void handleDataxSuccess(String nodeCode, String executionId, NodeExecutionResult executionResult) {
        try {
            logger.info("处理DataX执行成功回调，节点: {}, 执行ID: {}", nodeCode, executionId);

            // 获取节点信息
            WorkflowNode node = nodeService.findByNodeCode(nodeCode);
            if (node == null) {
                logger.warn("节点不存在，节点编码: {}", nodeCode);
                return;
            }

            // 检查是否为增量同步节点
            if (!isIncrementalSyncNode(node)) {
                logger.debug("非增量同步节点，跳过回调处理，节点: {}", nodeCode);
                return;
            }

            // 解析执行结果
            DataxExecutionResult dataxResult = parseExecutionResult(executionResult);

            // 更新同步状态
            updateSyncStateOnSuccess(node, dataxResult, executionId);

            // 更新同步日志
            updateSyncLogOnSuccess(node, dataxResult, executionId);

            logger.info("DataX执行成功回调处理完成，节点: {}, 同步记录数: {}",
                       nodeCode, dataxResult.getRecordCount());

        } catch (Exception e) {
            logger.error("处理DataX执行成功回调失败，节点: {}, 执行ID: {}", nodeCode, executionId, e);
        }
    }

    /**
     * 处理DataX执行失败回调
     *
     * @param nodeCode 节点编码
     * @param executionId 执行ID
     * @param executionResult 执行结果对象
     */
    public void handleDataxFailure(String nodeCode, String executionId, NodeExecutionResult executionResult) {
        try {
            String errorMessage = executionResult != null ? executionResult.getErrorMessage() : "未知错误";
            logger.info("处理DataX执行失败回调，节点: {}, 执行ID: {}, 错误: {}",
                       nodeCode, executionId, errorMessage);

            // 获取节点信息
            WorkflowNode node = nodeService.findByNodeCode(nodeCode);
            if (node == null) {
                logger.warn("节点不存在，节点编码: {}", nodeCode);
                return;
            }

            // 检查是否为增量同步节点
            if (!isIncrementalSyncNode(node)) {
                logger.debug("非增量同步节点，跳过回调处理，节点: {}", nodeCode);
                return;
            }

            // 更新同步状态为错误
            updateSyncStateOnFailure(node, errorMessage);

            // 更新同步日志为失败
            updateSyncLogOnFailure(node, executionId, errorMessage);

            logger.info("DataX执行失败回调处理完成，节点: {}", nodeCode);

        } catch (Exception e) {
            logger.error("处理DataX执行失败回调失败，节点: {}, 执行ID: {}", nodeCode, executionId, e);
        }
    }

    /**
     * 检查是否为增量同步节点
     */
    private boolean isIncrementalSyncNode(WorkflowNode node) {
        try {
            if (!"DATAX".equals(node.getNodeType())) {
                return false;
            }

            JsonNode nodeConfig = objectMapper.readTree(node.getConfigParams());
            JsonNode syncStrategy = nodeConfig.path("syncStrategy");
            
            return !syncStrategy.isMissingNode() && 
                   "INCREMENTAL".equals(syncStrategy.path("type").asText());

        } catch (Exception e) {
            logger.error("检查增量同步节点失败，节点ID: {}", node.getId(), e);
            return false;
        }
    }

    /**
     * 解析执行结果
     */
    private DataxExecutionResult parseExecutionResult(NodeExecutionResult executionResult) {
        DataxExecutionResult dataxResult = new DataxExecutionResult();

        try {
            // 从NodeExecutionResult中提取信息
            if (executionResult != null) {
                // 设置执行时长
                if (executionResult.getDuration() != null) {
                    dataxResult.setDuration(executionResult.getDuration());
                }

                // 从outputParams中解析DataX特定的结果
                Map<String, Object> outputParams = executionResult.getOutputParams();
                if (outputParams != null) {
                    // 解析同步记录数
                    Object recordCountObj = outputParams.get("recordCount");
                    if (recordCountObj != null) {
                        dataxResult.setRecordCount(Long.parseLong(recordCountObj.toString()));
                    }

                    // 解析最大增量值
                    Object maxValueObj = outputParams.get("maxIncrementalValue");
                    if (maxValueObj != null) {
                        dataxResult.setMaxIncrementalValue(maxValueObj.toString());
                    }

                    // 解析性能信息
                    Object perfInfoObj = outputParams.get("performanceInfo");
                    if (perfInfoObj != null) {
                        dataxResult.setPerformanceInfo(perfInfoObj.toString());
                    }
                }

                // 如果outputParams中没有，尝试从logContent中解析
                if (dataxResult.getRecordCount() == 0 && StringUtils.hasText(executionResult.getLogContent())) {
                    parseFromLogContent(executionResult.getLogContent(), dataxResult);
                }
            }
        } catch (Exception e) {
            logger.warn("解析执行结果失败，使用默认值: {}", e.getMessage());
        }

        return dataxResult;
    }

    /**
     * 从日志内容中解析DataX结果
     */
    private void parseFromLogContent(String logContent, DataxExecutionResult dataxResult) {
        try {
            // 尝试从日志中提取记录数（DataX通常会在日志中输出同步记录数）
            // 例如：任务启动时刻 : 2023-12-01 10:30:00
            //      任务结束时刻 : 2023-12-01 10:35:00
            //      任务总计耗时 : 300s
            //      任务平均流量 : 1.67MB/s
            //      记录写入速度 : 1000rec/s
            //      读出记录总数 : 150000
            //      读写失败总数 : 0

            if (logContent.contains("读出记录总数")) {
                String[] lines = logContent.split("\n");
                for (String line : lines) {
                    if (line.contains("读出记录总数")) {
                        String recordStr = line.replaceAll(".*读出记录总数\\s*:\\s*(\\d+).*", "$1");
                        try {
                            dataxResult.setRecordCount(Long.parseLong(recordStr));
                        } catch (NumberFormatException e) {
                            logger.debug("解析记录数失败: {}", recordStr);
                        }
                        break;
                    }
                }
            }
        } catch (Exception e) {
            logger.debug("从日志内容解析结果失败: {}", e.getMessage());
        }
    }

    /**
     * 更新同步状态（成功）
     */
    private void updateSyncStateOnSuccess(WorkflowNode node, DataxExecutionResult result, String executionId) {
        try {
            // 获取表信息
            JsonNode nodeConfig = objectMapper.readTree(node.getConfigParams());
            JsonNode sourceConfig = nodeConfig.path("source");
            JsonNode targetConfig = nodeConfig.path("target");

            String sourceTable = sourceConfig.path("tableName").asText();
            String targetTable = targetConfig.path("tableName").asText();

            logger.debug("更新同步状态 - 源表: {}, 目标表: {}, 最大增量值: {}, 记录数: {}",
                        sourceTable, targetTable, result.getMaxIncrementalValue(), result.getRecordCount());

            // 更新同步结果
            syncStateService.updateSyncResult(
                node.getId(),
                sourceTable,
                targetTable,
                result.getMaxIncrementalValue(),
                result.getRecordCount(),
                "system" // TODO: 从执行上下文获取用户
            );

        } catch (Exception e) {
            logger.error("更新同步状态失败，节点ID: {}", node.getId(), e);
        }
    }

    /**
     * 更新同步状态（失败）
     */
    private void updateSyncStateOnFailure(WorkflowNode node, String errorMessage) {
        try {
            // 获取表信息
            JsonNode nodeConfig = objectMapper.readTree(node.getConfigParams());
            JsonNode sourceConfig = nodeConfig.path("source");
            JsonNode targetConfig = nodeConfig.path("target");

            String sourceTable = sourceConfig.path("tableName").asText();
            String targetTable = targetConfig.path("tableName").asText();

            logger.debug("设置错误状态 - 源表: {}, 目标表: {}, 错误信息: {}",
                        sourceTable, targetTable, errorMessage);

            // 设置错误状态
            syncStateService.setErrorStatus(
                node.getId(),
                sourceTable,
                targetTable,
                errorMessage,
                "system" // TODO: 从执行上下文获取用户
            );

        } catch (Exception e) {
            logger.error("设置错误状态失败，节点ID: {}", node.getId(), e);
        }
    }

    /**
     * 更新同步日志（成功）
     */
    private void updateSyncLogOnSuccess(WorkflowNode node, DataxExecutionResult result, String executionId) {
        try {
            // 查找对应的同步日志
            var syncLog = syncLogService.findByExecutionId(executionId);
            if (syncLog != null) {
                syncLogService.markSuccess(
                    syncLog.getId(),
                    result.getRecordCount(),
                    result.getMaxIncrementalValue(),
                    result.getPerformanceInfo()
                );
            }
        } catch (Exception e) {
            logger.error("更新同步日志失败，执行ID: {}", executionId, e);
        }
    }

    /**
     * 更新同步日志（失败）
     */
    private void updateSyncLogOnFailure(WorkflowNode node, String executionId, String errorMessage) {
        try {
            // 查找对应的同步日志
            var syncLog = syncLogService.findByExecutionId(executionId);
            if (syncLog != null) {
                syncLogService.markFailed(
                    syncLog.getId(),
                    errorMessage,
                    "DATAX_EXECUTION_ERROR"
                );
            }
        } catch (Exception e) {
            logger.error("更新同步日志失败，执行ID: {}", executionId, e);
        }
    }

    /**
     * DataX执行结果封装类
     */
    private static class DataxExecutionResult {
        private long recordCount = 0;
        private long duration = 0;
        private String maxIncrementalValue;
        private String performanceInfo;

        // Getters and Setters
        public long getRecordCount() { return recordCount; }
        public void setRecordCount(long recordCount) { this.recordCount = recordCount; }

        public long getDuration() { return duration; }
        public void setDuration(long duration) { this.duration = duration; }

        public String getMaxIncrementalValue() { return maxIncrementalValue; }
        public void setMaxIncrementalValue(String maxIncrementalValue) { this.maxIncrementalValue = maxIncrementalValue; }

        public String getPerformanceInfo() { return performanceInfo; }
        public void setPerformanceInfo(String performanceInfo) { this.performanceInfo = performanceInfo; }
    }
}
