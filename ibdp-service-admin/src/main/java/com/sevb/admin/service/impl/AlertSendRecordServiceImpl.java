package com.sevb.admin.service.impl;

import com.sevb.admin.core.model.AlertSendRecord;
import com.sevb.admin.dao.AlertSendRecordDao;
import com.sevb.admin.service.AlertSendRecordService;
import com.sevb.core.biz.model.ReturnT;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * 告警发送记录服务实现
 * <AUTHOR>
 */
@Service
public class AlertSendRecordServiceImpl implements AlertSendRecordService {

    private static final Logger logger = LoggerFactory.getLogger(AlertSendRecordServiceImpl.class);

    @Autowired
    private AlertSendRecordDao alertSendRecordDao;

    @Override
    public Map<String, Object> pageList(int start, int length, String requestId, String templateCode,
                                       String channelType, String sendStatus, String sourceSystem,
                                       String businessId, Date startTime, Date endTime) {
        // 分页查询
        List<AlertSendRecord> list = alertSendRecordDao.pageList(start, length, requestId, templateCode,
            channelType, sendStatus, sourceSystem, businessId, startTime, endTime);
        int listCount = alertSendRecordDao.pageListCount(requestId, templateCode,
            channelType, sendStatus, sourceSystem, businessId, startTime, endTime);

        // 构建返回结果
        Map<String, Object> maps = new HashMap<>();
        maps.put("recordsTotal", listCount);
        maps.put("recordsFiltered", listCount);
        maps.put("data", list);
        return maps;
    }

    @Override
    public AlertSendRecord load(Long id) {
        if (id == null) {
            return null;
        }
        return alertSendRecordDao.load(id);
    }

    @Override
    public List<AlertSendRecord> findByRequestId(String requestId) {
        if (requestId == null || requestId.trim().isEmpty()) {
            return new ArrayList<>();
        }
        return alertSendRecordDao.findByRequestId(requestId);
    }

    @Override
    public List<AlertSendRecord> findByBusinessId(String businessId) {
        if (businessId == null || businessId.trim().isEmpty()) {
            return new ArrayList<>();
        }
        return alertSendRecordDao.findByBusinessId(businessId);
    }

    @Override
    public List<AlertSendRecord> findRetryRecords(int limit) {
        Date currentTime = new Date();
        return alertSendRecordDao.findRetryRecords(currentTime, limit);
    }

    @Override
    public List<AlertSendRecord> findFailedRecords(Date startTime, Date endTime) {
        return alertSendRecordDao.findFailedRecords(startTime, endTime);
    }

    @Override
    public ReturnT<String> save(AlertSendRecord alertSendRecord) {
        // 参数验证
        if (alertSendRecord == null) {
            return new ReturnT<>(ReturnT.FAIL_CODE, "发送记录信息不能为空");
        }

        if (alertSendRecord.getRequestId() == null || alertSendRecord.getRequestId().trim().isEmpty()) {
            return new ReturnT<>(ReturnT.FAIL_CODE, "请求ID不能为空");
        }

        if (alertSendRecord.getTemplateCode() == null || alertSendRecord.getTemplateCode().trim().isEmpty()) {
            return new ReturnT<>(ReturnT.FAIL_CODE, "模板编码不能为空");
        }

        if (alertSendRecord.getChannelType() == null || alertSendRecord.getChannelType().trim().isEmpty()) {
            return new ReturnT<>(ReturnT.FAIL_CODE, "通道类型不能为空");
        }

        // 设置默认值
        if (alertSendRecord.getSendTime() == null) {
            alertSendRecord.setSendTime(new Date());
        }

        if (alertSendRecord.getSendStatus() == null) {
            alertSendRecord.setSendStatus("PENDING");
        }

        if (alertSendRecord.getRetryCount() == null) {
            alertSendRecord.setRetryCount(0);
        }

        if (alertSendRecord.getMaxRetryCount() == null) {
            alertSendRecord.setMaxRetryCount(3);
        }

        try {
            int result = alertSendRecordDao.save(alertSendRecord);
            return result > 0 ? ReturnT.SUCCESS : new ReturnT<>(ReturnT.FAIL_CODE, "保存失败");
        } catch (Exception e) {
            logger.error("保存发送记录失败", e);
            return new ReturnT<>(ReturnT.FAIL_CODE, "保存失败: " + e.getMessage());
        }
    }

    @Override
    public ReturnT<String> update(AlertSendRecord alertSendRecord) {
        // 参数验证
        if (alertSendRecord == null || alertSendRecord.getId() == null) {
            return new ReturnT<>(ReturnT.FAIL_CODE, "发送记录信息不能为空");
        }

        try {
            int result = alertSendRecordDao.update(alertSendRecord);
            return result > 0 ? ReturnT.SUCCESS : new ReturnT<>(ReturnT.FAIL_CODE, "更新失败");
        } catch (Exception e) {
            logger.error("更新发送记录失败", e);
            return new ReturnT<>(ReturnT.FAIL_CODE, "更新失败: " + e.getMessage());
        }
    }

    @Override
    public ReturnT<String> updateSendStatus(Long id, String sendStatus, String responseCode, String responseMessage) {
        if (id == null) {
            return new ReturnT<>(ReturnT.FAIL_CODE, "记录ID不能为空");
        }

        if (sendStatus == null || sendStatus.trim().isEmpty()) {
            return new ReturnT<>(ReturnT.FAIL_CODE, "发送状态不能为空");
        }

        try {
            int result = alertSendRecordDao.updateSendStatus(id, sendStatus, responseCode, responseMessage);
            return result > 0 ? ReturnT.SUCCESS : new ReturnT<>(ReturnT.FAIL_CODE, "更新状态失败");
        } catch (Exception e) {
            logger.error("更新发送状态失败", e);
            return new ReturnT<>(ReturnT.FAIL_CODE, "更新状态失败: " + e.getMessage());
        }
    }

    @Override
    public ReturnT<String> updateRetryInfo(Long id, Integer retryCount, Date nextRetryTime, String sendStatus) {
        if (id == null) {
            return new ReturnT<>(ReturnT.FAIL_CODE, "记录ID不能为空");
        }

        try {
            int result = alertSendRecordDao.updateRetryInfo(id, retryCount, nextRetryTime, sendStatus);
            return result > 0 ? ReturnT.SUCCESS : new ReturnT<>(ReturnT.FAIL_CODE, "更新重试信息失败");
        } catch (Exception e) {
            logger.error("更新重试信息失败", e);
            return new ReturnT<>(ReturnT.FAIL_CODE, "更新重试信息失败: " + e.getMessage());
        }
    }

    @Override
    public ReturnT<String> batchDelete(List<Long> ids) {
        if (ids == null || ids.isEmpty()) {
            return new ReturnT<>(ReturnT.FAIL_CODE, "记录ID列表不能为空");
        }

        try {
            int result = alertSendRecordDao.batchDelete(ids);
            return result > 0 ? ReturnT.SUCCESS : new ReturnT<>(ReturnT.FAIL_CODE, "批量删除失败");
        } catch (Exception e) {
            logger.error("批量删除发送记录失败", e);
            return new ReturnT<>(ReturnT.FAIL_CODE, "批量删除失败: " + e.getMessage());
        }
    }

    @Override
    public ReturnT<String> deleteByTimeRange(Date startTime, Date endTime) {
        if (startTime == null || endTime == null) {
            return new ReturnT<>(ReturnT.FAIL_CODE, "时间范围不能为空");
        }

        if (startTime.after(endTime)) {
            return new ReturnT<>(ReturnT.FAIL_CODE, "开始时间不能晚于结束时间");
        }

        try {
            int result = alertSendRecordDao.deleteByTimeRange(startTime, endTime);
            return new ReturnT<>(ReturnT.SUCCESS_CODE, "删除了 " + result + " 条记录");
        } catch (Exception e) {
            logger.error("按时间范围删除记录失败", e);
            return new ReturnT<>(ReturnT.FAIL_CODE, "删除失败: " + e.getMessage());
        }
    }

    @Override
    public int countByStatus(String sendStatus, Date startTime, Date endTime) {
        try {
            return alertSendRecordDao.countByStatus(sendStatus, startTime, endTime);
        } catch (Exception e) {
            logger.error("统计发送记录数量失败", e);
            return 0;
        }
    }

    @Override
    public List<Map<String, Object>> countGroupByStatus(Date startTime, Date endTime) {
        try {
            List<AlertSendRecord> records = alertSendRecordDao.countGroupByStatus(startTime, endTime);
            List<Map<String, Object>> result = new ArrayList<>();
            
            for (AlertSendRecord record : records) {
                Map<String, Object> item = new HashMap<>();
                item.put("sendStatus", record.getSendStatus());
                item.put("count", record.getRetryCount()); // 这里复用了retryCount字段存储统计数量
                result.add(item);
            }
            
            return result;
        } catch (Exception e) {
            logger.error("按状态统计发送记录失败", e);
            return new ArrayList<>();
        }
    }

    @Override
    public List<Map<String, Object>> countGroupByChannelType(Date startTime, Date endTime) {
        try {
            List<AlertSendRecord> records = alertSendRecordDao.countGroupByChannelType(startTime, endTime);
            List<Map<String, Object>> result = new ArrayList<>();
            
            for (AlertSendRecord record : records) {
                Map<String, Object> item = new HashMap<>();
                item.put("channelType", record.getChannelType());
                item.put("count", record.getRetryCount()); // 这里复用了retryCount字段存储统计数量
                result.add(item);
            }
            
            return result;
        } catch (Exception e) {
            logger.error("按通道类型统计发送记录失败", e);
            return new ArrayList<>();
        }
    }

    @Override
    public Map<String, Object> getSuccessRateStats(Date startTime, Date endTime) {
        try {
            int totalCount = countByStatus(null, startTime, endTime);
            int successCount = countByStatus("SUCCESS", startTime, endTime);
            int failedCount = countByStatus("FAILED", startTime, endTime);
            int pendingCount = countByStatus("PENDING", startTime, endTime);
            int retryCount = countByStatus("RETRY", startTime, endTime);

            double successRate = totalCount > 0 ? (double) successCount / totalCount * 100 : 0;

            Map<String, Object> stats = new HashMap<>();
            stats.put("totalCount", totalCount);
            stats.put("successCount", successCount);
            stats.put("failedCount", failedCount);
            stats.put("pendingCount", pendingCount);
            stats.put("retryCount", retryCount);
            stats.put("successRate", Math.round(successRate * 100.0) / 100.0);

            return stats;
        } catch (Exception e) {
            logger.error("获取成功率统计失败", e);
            return new HashMap<>();
        }
    }

    @Override
    public Map<String, Object> getRecentStats(int hours) {
        try {
            Calendar calendar = Calendar.getInstance();
            Date endTime = calendar.getTime();
            calendar.add(Calendar.HOUR_OF_DAY, -hours);
            Date startTime = calendar.getTime();

            return getSuccessRateStats(startTime, endTime);
        } catch (Exception e) {
            logger.error("获取最近统计失败", e);
            return new HashMap<>();
        }
    }
}
