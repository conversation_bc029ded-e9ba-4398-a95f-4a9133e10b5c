package com.sevb.admin.service;

import com.sevb.admin.core.model.AlertTemplate;
import com.sevb.core.biz.model.ReturnT;

import java.util.List;
import java.util.Map;

/**
 * 告警模板服务接口
 * <AUTHOR>
 */
public interface AlertTemplateService {

    /**
     * 分页查询告警模板列表
     */
    Map<String, Object> pageList(int start, int length, String templateCode, 
                                String templateName, String channelType, Integer enabled);

    /**
     * 查询所有启用的告警模板
     */
    List<AlertTemplate> findAllEnabled();

    /**
     * 根据ID查询告警模板
     */
    AlertTemplate load(Long id);

    /**
     * 根据模板编码和通道类型查询告警模板
     */
    AlertTemplate findByCodeAndChannel(String templateCode, String channelType);

    /**
     * 根据模板编码查询所有通道的模板
     */
    List<AlertTemplate> findByTemplateCode(String templateCode);

    /**
     * 根据通道类型查询模板列表
     */
    List<AlertTemplate> findByChannelType(String channelType);

    /**
     * 保存告警模板
     */
    ReturnT<String> save(AlertTemplate alertTemplate);

    /**
     * 更新告警模板
     */
    ReturnT<String> update(AlertTemplate alertTemplate);

    /**
     * 删除告警模板
     */
    ReturnT<String> delete(Long id, String updateUser);

    /**
     * 启用/禁用告警模板
     */
    ReturnT<String> updateEnabled(Long id, Integer enabled, String updateUser);

    /**
     * 批量删除告警模板
     */
    ReturnT<String> batchDelete(List<Long> ids, String updateUser);

    /**
     * 渲染模板内容
     */
    String renderTemplate(String templateCode, String channelType, Map<String, Object> variables);

    /**
     * 验证模板变量
     */
    ReturnT<String> validateTemplateVariables(String templateContent, String variables);

    /**
     * 获取模板变量列表
     */
    List<Map<String, Object>> getTemplateVariables(String templateCode, String channelType);
}
