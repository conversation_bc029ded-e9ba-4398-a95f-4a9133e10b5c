package com.sevb.admin.service;

import com.sevb.admin.core.model.DataxIncrementalConfig;
import com.sevb.core.biz.model.ReturnT;

import java.util.List;

/**
 * DataX增量同步配置服务接口
 * 
 * <AUTHOR>
 */
public interface DataxIncrementalConfigService {

    /**
     * 保存增量配置
     */
    ReturnT<String> save(DataxIncrementalConfig config);

    /**
     * 根据ID查询增量配置
     */
    DataxIncrementalConfig load(Long id);

    /**
     * 更新增量配置
     */
    ReturnT<String> update(DataxIncrementalConfig config);

    /**
     * 删除增量配置
     */
    ReturnT<String> delete(Long id);

    /**
     * 根据节点ID查询增量配置
     */
    DataxIncrementalConfig findByNodeId(Long nodeId);

    /**
     * 查询所有启用的增量配置
     */
    List<DataxIncrementalConfig> findAllEnabled();

    /**
     * 分页查询增量配置列表
     */
    ReturnT<List<DataxIncrementalConfig>> pageList(int page, int size, Long nodeId, 
                                                  String incrementalType, Integer enabled);

    /**
     * 根据增量类型查询配置
     */
    List<DataxIncrementalConfig> findByIncrementalType(String incrementalType);

    /**
     * 批量更新启用状态
     */
    ReturnT<String> batchUpdateEnabled(List<Long> ids, Integer enabled, String updateUser);

    /**
     * 更新启用状态
     */
    ReturnT<String> updateEnabled(Long id, Integer enabled, String updateUser);

    /**
     * 检查节点是否已存在配置
     */
    boolean existsByNodeId(Long nodeId);

    /**
     * 根据节点ID列表查询配置
     */
    List<DataxIncrementalConfig> findByNodeIds(List<Long> nodeIds);

    /**
     * 统计启用的配置数量
     */
    int countEnabled();

    /**
     * 统计总配置数量
     */
    int countTotal();

    /**
     * 根据增量字段查询配置
     */
    List<DataxIncrementalConfig> findByIncrementalColumn(String incrementalColumn);

    /**
     * 保存或更新配置（存在则更新，不存在则插入）
     */
    ReturnT<String> saveOrUpdate(DataxIncrementalConfig config);

    /**
     * 批量删除配置
     */
    ReturnT<String> batchDelete(List<Long> ids);

    /**
     * 复制配置
     */
    ReturnT<String> copyConfig(Long sourceNodeId, Long targetNodeId, String createUser);

    /**
     * 获取默认配置模板
     */
    DataxIncrementalConfig getDefaultTemplate();

    /**
     * 验证配置有效性
     */
    ReturnT<String> validateConfig(DataxIncrementalConfig config);

    /**
     * 创建默认配置
     */
    ReturnT<String> createDefaultConfig(Long nodeId, String createUser);

    /**
     * 根据工作流节点配置创建增量配置
     */
    ReturnT<String> createFromNodeConfig(Long nodeId, String nodeConfigJson, String createUser);

    /**
     * 获取配置统计信息
     */
    ReturnT<Object> getConfigStatistics();

    /**
     * 批量启用配置
     */
    ReturnT<String> batchEnable(List<Long> ids, String updateUser);

    /**
     * 批量禁用配置
     */
    ReturnT<String> batchDisable(List<Long> ids, String updateUser);

    /**
     * 重置配置为默认值
     */
    ReturnT<String> resetToDefault(Long id, String updateUser);

    /**
     * 导出配置
     */
    ReturnT<String> exportConfig(List<Long> ids);

    /**
     * 导入配置
     */
    ReturnT<String> importConfig(String configJson, String createUser);
}
