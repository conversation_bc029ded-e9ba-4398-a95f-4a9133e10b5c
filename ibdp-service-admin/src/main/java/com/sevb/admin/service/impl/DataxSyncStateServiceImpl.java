package com.sevb.admin.service.impl;

import com.sevb.admin.core.datax.DatasourceQueryExecutor;
import com.sevb.admin.core.model.DataxSyncState;
import com.sevb.admin.dao.DataxSyncStateDao;
import com.sevb.admin.dao.DatasourceDao;
import com.sevb.admin.service.DataxSyncStateService;
import com.sevb.core.biz.model.ReturnT;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * DataX增量同步状态服务实现类
 * 
 * <AUTHOR>
 */
@Service
public class DataxSyncStateServiceImpl implements DataxSyncStateService {

    private static final Logger logger = LoggerFactory.getLogger(DataxSyncStateServiceImpl.class);

    @Autowired
    private DataxSyncStateDao dataxSyncStateDao;

    @Autowired
    private DatasourceDao datasourceDao;

    @Autowired
    private DatasourceQueryExecutor datasourceQueryExecutor;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ReturnT<String> save(DataxSyncState syncState) {
        if (syncState == null) {
            return new ReturnT<>(ReturnT.FAIL_CODE, "同步状态信息不能为空");
        }

        try {
            // 验证必填字段
            if (syncState.getNodeId() == null) {
                return new ReturnT<>(ReturnT.FAIL_CODE, "节点ID不能为空");
            }
            if (!StringUtils.hasText(syncState.getSourceTable())) {
                return new ReturnT<>(ReturnT.FAIL_CODE, "源表名不能为空");
            }
            if (!StringUtils.hasText(syncState.getTargetTable())) {
                return new ReturnT<>(ReturnT.FAIL_CODE, "目标表名不能为空");
            }
            if (!StringUtils.hasText(syncState.getIncrementalColumn())) {
                return new ReturnT<>(ReturnT.FAIL_CODE, "增量字段不能为空");
            }

            // 检查重复
            if (checkDuplicate(syncState.getNodeId(), syncState.getSourceTable(), 
                             syncState.getTargetTable(), syncState.getId())) {
                return new ReturnT<>(ReturnT.FAIL_CODE, "该节点的同步状态已存在");
            }

            // 设置默认值
            if (syncState.getCreateTime() == null) {
                syncState.setCreateTime(LocalDateTime.now());
            }
            if (syncState.getUpdateTime() == null) {
                syncState.setUpdateTime(LocalDateTime.now());
            }
            if (!StringUtils.hasText(syncState.getStatus())) {
                syncState.setStatus(DataxSyncState.Status.ACTIVE.getCode());
            }
            if (!StringUtils.hasText(syncState.getIncrementalType())) {
                syncState.setIncrementalType(DataxSyncState.IncrementalType.TIMESTAMP.getCode());
            }
            if (syncState.getSyncCount() == null) {
                syncState.setSyncCount(0L);
            }
            if (syncState.getTotalRecords() == null) {
                syncState.setTotalRecords(0L);
            }

            int result = dataxSyncStateDao.save(syncState);
            if (result > 0) {
                logger.info("保存同步状态成功，ID: {}, 节点ID: {}, 源表: {}, 目标表: {}", 
                           syncState.getId(), syncState.getNodeId(), 
                           syncState.getSourceTable(), syncState.getTargetTable());
                return new ReturnT<>(ReturnT.SUCCESS_CODE, "保存成功");
            } else {
                return new ReturnT<>(ReturnT.FAIL_CODE, "保存失败");
            }
        } catch (Exception e) {
            logger.error("保存同步状态失败", e);
            return new ReturnT<>(ReturnT.FAIL_CODE, "保存失败: " + e.getMessage());
        }
    }

    @Override
    public DataxSyncState load(Long id) {
        if (id == null) {
            return null;
        }
        try {
            return dataxSyncStateDao.load(id);
        } catch (Exception e) {
            logger.error("查询同步状态失败，ID: {}", id, e);
            return null;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ReturnT<String> update(DataxSyncState syncState) {
        if (syncState == null || syncState.getId() == null) {
            return new ReturnT<>(ReturnT.FAIL_CODE, "同步状态信息不能为空");
        }

        try {
            // 检查记录是否存在
            DataxSyncState existing = dataxSyncStateDao.load(syncState.getId());
            if (existing == null) {
                return new ReturnT<>(ReturnT.FAIL_CODE, "同步状态不存在");
            }

            // 检查重复（排除自己）
            if (checkDuplicate(syncState.getNodeId(), syncState.getSourceTable(), 
                             syncState.getTargetTable(), syncState.getId())) {
                return new ReturnT<>(ReturnT.FAIL_CODE, "该节点的同步状态已存在");
            }

            syncState.setUpdateTime(LocalDateTime.now());
            int result = dataxSyncStateDao.update(syncState);
            if (result > 0) {
                logger.info("更新同步状态成功，ID: {}", syncState.getId());
                return new ReturnT<>(ReturnT.SUCCESS_CODE, "更新成功");
            } else {
                return new ReturnT<>(ReturnT.FAIL_CODE, "更新失败");
            }
        } catch (Exception e) {
            logger.error("更新同步状态失败，ID: {}", syncState.getId(), e);
            return new ReturnT<>(ReturnT.FAIL_CODE, "更新失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ReturnT<String> delete(Long id) {
        if (id == null) {
            return new ReturnT<>(ReturnT.FAIL_CODE, "ID不能为空");
        }

        try {
            DataxSyncState existing = dataxSyncStateDao.load(id);
            if (existing == null) {
                return new ReturnT<>(ReturnT.FAIL_CODE, "同步状态不存在");
            }

            int result = dataxSyncStateDao.delete(id);
            if (result > 0) {
                logger.info("删除同步状态成功，ID: {}", id);
                return new ReturnT<>(ReturnT.SUCCESS_CODE, "删除成功");
            } else {
                return new ReturnT<>(ReturnT.FAIL_CODE, "删除失败");
            }
        } catch (Exception e) {
            logger.error("删除同步状态失败，ID: {}", id, e);
            return new ReturnT<>(ReturnT.FAIL_CODE, "删除失败: " + e.getMessage());
        }
    }

    @Override
    public DataxSyncState findByNodeAndTables(Long nodeId, String sourceTable, String targetTable) {
        if (nodeId == null || !StringUtils.hasText(sourceTable) || !StringUtils.hasText(targetTable)) {
            return null;
        }
        try {
            return dataxSyncStateDao.findByNodeAndTables(nodeId, sourceTable, targetTable);
        } catch (Exception e) {
            logger.error("根据节点和表名查询同步状态失败，节点ID: {}, 源表: {}, 目标表: {}", 
                        nodeId, sourceTable, targetTable, e);
            return null;
        }
    }

    @Override
    public DataxSyncState findByNodeId(Long nodeId) {
        if (nodeId == null) {
            return null;
        }
        try {
            return dataxSyncStateDao.findByNodeId(nodeId);
        } catch (Exception e) {
            logger.error("根据节点ID查询同步状态失败，节点ID: {}", nodeId, e);
            return null;
        }
    }

    @Override
    public List<DataxSyncState> findByWorkflowId(Long workflowId) {
        if (workflowId == null) {
            return null;
        }
        try {
            return dataxSyncStateDao.findByWorkflowId(workflowId);
        } catch (Exception e) {
            logger.error("根据工作流ID查询同步状态失败，工作流ID: {}", workflowId, e);
            return null;
        }
    }

    @Override
    public ReturnT<List<DataxSyncState>> pageList(int page, int size, Long workflowId, Long nodeId,
                                                 String sourceTable, String targetTable,
                                                 String status, String incrementalType) {
        try {
            int offset = (page - 1) * size;
            List<DataxSyncState> list = dataxSyncStateDao.pageList(offset, size, workflowId, nodeId,
                                                                  sourceTable, targetTable, status, incrementalType);
            int count = dataxSyncStateDao.pageListCount(offset, size, workflowId, nodeId,
                                                       sourceTable, targetTable, status, incrementalType);
            
            Map<String, Object> result = new HashMap<>();
            result.put("data", list);
            result.put("recordsFiltered", count);
            result.put("recordsTotal", count);
            
            return new ReturnT<>(list);
        } catch (Exception e) {
            logger.error("分页查询同步状态失败", e);
            return new ReturnT<>(ReturnT.FAIL_CODE, "查询失败: " + e.getMessage());
        }
    }

    @Override
    public List<DataxSyncState> findAllActive() {
        try {
            return dataxSyncStateDao.findAllActive();
        } catch (Exception e) {
            logger.error("查询所有活跃同步状态失败", e);
            return null;
        }
    }

    @Override
    public List<DataxSyncState> findNeedSync(int hours) {
        try {
            return dataxSyncStateDao.findNeedSync(hours);
        } catch (Exception e) {
            logger.error("查询需要同步的状态失败，小时数: {}", hours, e);
            return null;
        }
    }

    @Override
    public List<DataxSyncState> findErrorStates() {
        try {
            return dataxSyncStateDao.findErrorStates();
        } catch (Exception e) {
            logger.error("查询错误状态失败", e);
            return null;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ReturnT<String> batchUpdateStatus(List<Long> ids, String status, String updateUser) {
        if (ids == null || ids.isEmpty()) {
            return new ReturnT<>(ReturnT.FAIL_CODE, "ID列表不能为空");
        }
        if (!StringUtils.hasText(status)) {
            return new ReturnT<>(ReturnT.FAIL_CODE, "状态不能为空");
        }

        try {
            int result = dataxSyncStateDao.batchUpdateStatus(ids, status, updateUser);
            if (result > 0) {
                logger.info("批量更新状态成功，更新数量: {}, 状态: {}", result, status);
                return new ReturnT<>(ReturnT.SUCCESS_CODE, "批量更新成功");
            } else {
                return new ReturnT<>(ReturnT.FAIL_CODE, "批量更新失败");
            }
        } catch (Exception e) {
            logger.error("批量更新状态失败", e);
            return new ReturnT<>(ReturnT.FAIL_CODE, "批量更新失败: " + e.getMessage());
        }
    }

    @Override
    public String getLastSyncValue(Long nodeId, String sourceTable, String targetTable) {
        DataxSyncState syncState = findByNodeAndTables(nodeId, sourceTable, targetTable);
        if (syncState != null) {
            return syncState.getLastSyncValue();
        }
        return null;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ReturnT<String> updateSyncResult(Long nodeId, String sourceTable, String targetTable,
                                           String newSyncValue, Long recordCount, String updateUser) {
        try {
            DataxSyncState syncState = findByNodeAndTables(nodeId, sourceTable, targetTable);
            if (syncState == null) {
                return new ReturnT<>(ReturnT.FAIL_CODE, "同步状态不存在");
            }

            Long newSyncCount = (syncState.getSyncCount() == null ? 0 : syncState.getSyncCount()) + 1;
            Long newTotalRecords = (syncState.getTotalRecords() == null ? 0 : syncState.getTotalRecords()) + 
                                  (recordCount == null ? 0 : recordCount);

            int result = dataxSyncStateDao.updateLastSyncValue(syncState.getId(), newSyncValue, 
                                                              LocalDateTime.now(), newSyncCount, 
                                                              newTotalRecords, updateUser);
            if (result > 0) {
                logger.info("更新同步结果成功，节点ID: {}, 新同步值: {}, 记录数: {}", 
                           nodeId, newSyncValue, recordCount);
                return new ReturnT<>(ReturnT.SUCCESS_CODE, "更新同步结果成功");
            } else {
                return new ReturnT<>(ReturnT.FAIL_CODE, "更新同步结果失败");
            }
        } catch (Exception e) {
            logger.error("更新同步结果失败", e);
            return new ReturnT<>(ReturnT.FAIL_CODE, "更新同步结果失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ReturnT<String> setErrorStatus(Long nodeId, String sourceTable, String targetTable,
                                         String errorMessage, String updateUser) {
        try {
            DataxSyncState syncState = findByNodeAndTables(nodeId, sourceTable, targetTable);
            if (syncState == null) {
                return new ReturnT<>(ReturnT.FAIL_CODE, "同步状态不存在");
            }

            int result = dataxSyncStateDao.setErrorStatus(syncState.getId(), errorMessage, updateUser);
            if (result > 0) {
                logger.info("设置错误状态成功，节点ID: {}, 错误信息: {}", nodeId, errorMessage);
                return new ReturnT<>(ReturnT.SUCCESS_CODE, "设置错误状态成功");
            } else {
                return new ReturnT<>(ReturnT.FAIL_CODE, "设置错误状态失败");
            }
        } catch (Exception e) {
            logger.error("设置错误状态失败", e);
            return new ReturnT<>(ReturnT.FAIL_CODE, "设置错误状态失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ReturnT<String> resetToActive(Long id, String updateUser) {
        if (id == null) {
            return new ReturnT<>(ReturnT.FAIL_CODE, "ID不能为空");
        }

        try {
            int result = dataxSyncStateDao.resetToActive(id, updateUser);
            if (result > 0) {
                logger.info("重置为活跃状态成功，ID: {}", id);
                return new ReturnT<>(ReturnT.SUCCESS_CODE, "重置成功");
            } else {
                return new ReturnT<>(ReturnT.FAIL_CODE, "重置失败");
            }
        } catch (Exception e) {
            logger.error("重置为活跃状态失败，ID: {}", id, e);
            return new ReturnT<>(ReturnT.FAIL_CODE, "重置失败: " + e.getMessage());
        }
    }

    @Override
    public boolean checkDuplicate(Long nodeId, String sourceTable, String targetTable, Long excludeId) {
        try {
            int count = dataxSyncStateDao.checkDuplicate(nodeId, sourceTable, targetTable, excludeId);
            return count > 0;
        } catch (Exception e) {
            logger.error("检查重复失败", e);
            return false;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ReturnT<String> initializeSyncState(Long nodeId, Long workflowId,
                                              Long sourceDatasourceId, String sourceTable,
                                              Long targetDatasourceId, String targetTable,
                                              String incrementalColumn, String incrementalType,
                                              String configSnapshot, String createUser) {
        try {
            // 检查是否已存在
            DataxSyncState existing = findByNodeAndTables(nodeId, sourceTable, targetTable);
            if (existing != null) {
                // 更新配置快照
                return updateConfigSnapshot(existing.getId(), configSnapshot, createUser);
            }

            // 创建新的同步状态
            DataxSyncState syncState = new DataxSyncState();
            syncState.setNodeId(nodeId);
            syncState.setWorkflowId(workflowId);
            syncState.setSourceDatasourceId(sourceDatasourceId);
            syncState.setSourceTable(sourceTable);
            syncState.setTargetDatasourceId(targetDatasourceId);
            syncState.setTargetTable(targetTable);
            syncState.setIncrementalColumn(incrementalColumn);
            syncState.setIncrementalType(incrementalType);
            syncState.setConfigSnapshot(configSnapshot);
            syncState.setStatus(DataxSyncState.Status.ACTIVE.getCode());
            syncState.setSyncCount(0L);
            syncState.setTotalRecords(0L);
            syncState.setCreateUser(createUser);
            syncState.setUpdateUser(createUser);

            return save(syncState);
        } catch (Exception e) {
            logger.error("初始化同步状态失败", e);
            return new ReturnT<>(ReturnT.FAIL_CODE, "初始化失败: " + e.getMessage());
        }
    }

    @Override
    public String getMaxIncrementalValue(Long datasourceId, String tableName,
                                       String incrementalColumn, String incrementalType) {
        if (datasourceId == null || !StringUtils.hasText(tableName) || !StringUtils.hasText(incrementalColumn)) {
            logger.warn("查询最大增量值参数不完整，数据源ID: {}, 表名: {}, 增量字段: {}",
                       datasourceId, tableName, incrementalColumn);
            return null;
        }

        try {
            logger.info("查询最大增量值，数据源ID: {}, 表名: {}, 增量字段: {}, 类型: {}",
                       datasourceId, tableName, incrementalColumn, incrementalType);

            // 首先验证表和字段是否存在
            if (!datasourceQueryExecutor.validateTableAndColumn(datasourceId, tableName, incrementalColumn)) {
                logger.warn("表或字段不存在，数据源ID: {}, 表名: {}, 字段: {}",
                           datasourceId, tableName, incrementalColumn);
                return null;
            }

            // 查询最大增量值
            String maxValue = datasourceQueryExecutor.queryMaxIncrementalValue(
                datasourceId, tableName, incrementalColumn, incrementalType);

            logger.info("查询最大增量值完成，结果: {}", maxValue);
            return maxValue;

        } catch (Exception e) {
            logger.error("查询最大增量值失败，数据源ID: {}, 表名: {}, 增量字段: {}",
                        datasourceId, tableName, incrementalColumn, e);
            return null;
        }
    }

    @Override
    public boolean canSync(Long nodeId, String sourceTable, String targetTable) {
        DataxSyncState syncState = findByNodeAndTables(nodeId, sourceTable, targetTable);
        return syncState != null && syncState.canSync();
    }

    @Override
    public ReturnT<Object> getSyncStatistics() {
        try {
            Map<String, Object> statistics = new HashMap<>();
            statistics.put("totalStates", dataxSyncStateDao.countByStatus(null));
            statistics.put("activeStates", dataxSyncStateDao.countByStatus("ACTIVE"));
            statistics.put("errorStates", dataxSyncStateDao.countByStatus("ERROR"));
            statistics.put("inactiveStates", dataxSyncStateDao.countByStatus("INACTIVE"));
            statistics.put("recentSynced", findRecentSynced(10));

            return new ReturnT<>(statistics);
        } catch (Exception e) {
            logger.error("获取同步统计信息失败", e);
            return new ReturnT<>(ReturnT.FAIL_CODE, "获取统计信息失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ReturnT<String> cleanupInactiveStates(int days) {
        try {
            int result = dataxSyncStateDao.cleanupInactiveStates(days);
            logger.info("清理非活跃状态成功，清理数量: {}", result);
            return new ReturnT<>(ReturnT.SUCCESS_CODE, "清理成功，清理数量: " + result);
        } catch (Exception e) {
            logger.error("清理非活跃状态失败", e);
            return new ReturnT<>(ReturnT.FAIL_CODE, "清理失败: " + e.getMessage());
        }
    }

    @Override
    public List<DataxSyncState> findByDatasourceId(Long datasourceId) {
        if (datasourceId == null) {
            return null;
        }
        try {
            return dataxSyncStateDao.findByDatasourceId(datasourceId);
        } catch (Exception e) {
            logger.error("根据数据源ID查询同步状态失败，数据源ID: {}", datasourceId, e);
            return null;
        }
    }

    @Override
    public int countByStatus(String status) {
        try {
            return dataxSyncStateDao.countByStatus(status);
        } catch (Exception e) {
            logger.error("统计同步状态数量失败，状态: {}", status, e);
            return 0;
        }
    }

    @Override
    public List<DataxSyncState> findRecentSynced(int limit) {
        try {
            return dataxSyncStateDao.findRecentSynced(limit);
        } catch (Exception e) {
            logger.error("查询最近同步状态失败，限制数量: {}", limit, e);
            return null;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ReturnT<String> updateConfigSnapshot(Long id, String configSnapshot, String updateUser) {
        if (id == null) {
            return new ReturnT<>(ReturnT.FAIL_CODE, "ID不能为空");
        }

        try {
            int result = dataxSyncStateDao.updateConfigSnapshot(id, configSnapshot, updateUser);
            if (result > 0) {
                logger.info("更新配置快照成功，ID: {}", id);
                return new ReturnT<>(ReturnT.SUCCESS_CODE, "更新配置快照成功");
            } else {
                return new ReturnT<>(ReturnT.FAIL_CODE, "更新配置快照失败");
            }
        } catch (Exception e) {
            logger.error("更新配置快照失败，ID: {}", id, e);
            return new ReturnT<>(ReturnT.FAIL_CODE, "更新配置快照失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ReturnT<String> createOrUpdateSyncState(Long nodeId, Long workflowId,
                                                  Long sourceDatasourceId, String sourceTable,
                                                  Long targetDatasourceId, String targetTable,
                                                  String incrementalColumn, String incrementalType,
                                                  String configSnapshot, String user) {
        try {
            DataxSyncState existing = findByNodeAndTables(nodeId, sourceTable, targetTable);
            if (existing != null) {
                // 更新现有状态
                existing.setWorkflowId(workflowId);
                existing.setSourceDatasourceId(sourceDatasourceId);
                existing.setTargetDatasourceId(targetDatasourceId);
                existing.setIncrementalColumn(incrementalColumn);
                existing.setIncrementalType(incrementalType);
                existing.setConfigSnapshot(configSnapshot);
                existing.setUpdateUser(user);
                return update(existing);
            } else {
                // 创建新状态
                return initializeSyncState(nodeId, workflowId, sourceDatasourceId, sourceTable,
                                         targetDatasourceId, targetTable, incrementalColumn,
                                         incrementalType, configSnapshot, user);
            }
        } catch (Exception e) {
            logger.error("创建或更新同步状态失败", e);
            return new ReturnT<>(ReturnT.FAIL_CODE, "操作失败: " + e.getMessage());
        }
    }

    @Override
    public ReturnT<Object> healthCheck() {
        try {
            Map<String, Object> health = new HashMap<>();
            health.put("totalStates", countByStatus(null));
            health.put("errorStates", countByStatus("ERROR"));
            health.put("needSyncStates", findNeedSync(24).size());
            health.put("checkTime", LocalDateTime.now());

            // 健康状态判断
            int errorCount = countByStatus("ERROR");
            int needSyncCount = findNeedSync(24).size();
            String healthStatus = "HEALTHY";
            if (errorCount > 0) {
                healthStatus = "WARNING";
            }
            if (needSyncCount > 10) {
                healthStatus = "CRITICAL";
            }
            health.put("status", healthStatus);

            return new ReturnT<>(health);
        } catch (Exception e) {
            logger.error("健康检查失败", e);
            return new ReturnT<>(ReturnT.FAIL_CODE, "健康检查失败: " + e.getMessage());
        }
    }

    @Override
    public ReturnT<Object> getSyncStateDetail(Long id) {
        try {
            DataxSyncState syncState = load(id);
            if (syncState == null) {
                return new ReturnT<>(ReturnT.FAIL_CODE, "同步状态不存在");
            }

            Map<String, Object> detail = new HashMap<>();
            detail.put("syncState", syncState);
            // 可以添加更多详细信息，如相关的日志统计等

            return new ReturnT<>(detail);
        } catch (Exception e) {
            logger.error("获取同步状态详情失败，ID: {}", id, e);
            return new ReturnT<>(ReturnT.FAIL_CODE, "获取详情失败: " + e.getMessage());
        }
    }


}
