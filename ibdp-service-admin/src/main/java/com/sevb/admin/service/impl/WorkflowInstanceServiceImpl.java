package com.sevb.admin.service.impl;

import com.sevb.admin.core.model.WorkflowInstance;
import com.sevb.admin.dao.WorkflowInstanceDao;
import com.sevb.admin.service.WorkflowInstanceService;
import com.sevb.core.biz.model.ReturnT;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.*;

/**
 * 工作流实例服务实现
 * 
 * <AUTHOR>
 */
@Slf4j
@Service
public class WorkflowInstanceServiceImpl implements WorkflowInstanceService {

    @Resource
    private WorkflowInstanceDao workflowInstanceDao;

    @Override
    public ReturnT<Map<String, Object>> pageList(int page, int size, Long projectId, Long workflowId, String workflowName,
                                                 Integer status, String triggerType, Date startTime, Date endTime) {
        try {
            // 参数校验
            if (page < 1) page = 1;
            if (size < 1) size = 10;
            if (size > 100) size = 100;

            // 计算偏移量
            int offset = (page - 1) * size;

            // 查询数据
            List<WorkflowInstance> list = workflowInstanceDao.pageList(offset, size, projectId, workflowId,
                    workflowName, status, triggerType, startTime, endTime);
            int total = workflowInstanceDao.pageListCount(projectId, workflowId, workflowName, status,
                    triggerType, startTime, endTime);

            // 构建返回结果
            Map<String, Object> result = new HashMap<>();
            result.put("data", list);
            result.put("recordsTotal", total);
            result.put("recordsFiltered", total);

            return new ReturnT<>(result);

        } catch (Exception e) {
            log.error("分页查询工作流实例失败", e);
            return new ReturnT<>(ReturnT.FAIL_CODE, "查询失败: " + e.getMessage());
        }
    }

    @Override
    public ReturnT<WorkflowInstance> getById(Long id) {
        try {
            if (id == null) {
                return new ReturnT<>(ReturnT.FAIL_CODE, "实例ID不能为空");
            }

            WorkflowInstance instance = workflowInstanceDao.load(id);
            if (instance == null) {
                return new ReturnT<>(ReturnT.FAIL_CODE, "工作流实例不存在");
            }

            return new ReturnT<>(instance);

        } catch (Exception e) {
            log.error("查询工作流实例失败, id: {}", id, e);
            return new ReturnT<>(ReturnT.FAIL_CODE, "查询失败: " + e.getMessage());
        }
    }

    @Override
    public ReturnT<String> stopInstance(Long id) {
        try {
            if (id == null) {
                return new ReturnT<>(ReturnT.FAIL_CODE, "实例ID不能为空");
            }

            WorkflowInstance instance = workflowInstanceDao.load(id);
            if (instance == null) {
                return new ReturnT<>(ReturnT.FAIL_CODE, "工作流实例不存在");
            }

            if (!instance.isRunning()) {
                return new ReturnT<>(ReturnT.FAIL_CODE, "只有运行中的实例才能停止");
            }

            // TODO: 调用XXL-JOB停止任务
            // 更新实例状态
            instance.setStatus(WorkflowInstance.Status.STOP.getCode());
            instance.setEndTime(new Date());
            instance.setUpdateTime(new Date());
            
            if (instance.getStartTime() != null) {
                instance.setDuration(instance.getEndTime().getTime() - instance.getStartTime().getTime());
            }

            workflowInstanceDao.update(instance);

            log.info("工作流实例{}停止成功", id);
            return ReturnT.SUCCESS;

        } catch (Exception e) {
            log.error("停止工作流实例失败, id: {}", id, e);
            return new ReturnT<>(ReturnT.FAIL_CODE, "停止失败: " + e.getMessage());
        }
    }

    @Override
    public ReturnT<String> retryInstance(Long id) {
        try {
            if (id == null) {
                return new ReturnT<>(ReturnT.FAIL_CODE, "实例ID不能为空");
            }

            WorkflowInstance instance = workflowInstanceDao.load(id);
            if (instance == null) {
                return new ReturnT<>(ReturnT.FAIL_CODE, "工作流实例不存在");
            }

            if (!instance.isCompleted() || instance.isSuccess()) {
                return new ReturnT<>(ReturnT.FAIL_CODE, "只有失败的实例才能重试");
            }

            // TODO: 创建新的实例或重新触发执行
            log.info("工作流实例{}重试功能开发中", id);
            return new ReturnT<>(ReturnT.SUCCESS_CODE, "重试功能开发中");

        } catch (Exception e) {
            log.error("重试工作流实例失败, id: {}", id, e);
            return new ReturnT<>(ReturnT.FAIL_CODE, "重试失败: " + e.getMessage());
        }
    }

    @Override
    public ReturnT<Map<String, Object>> getInstanceStats(Long workflowId, Integer days) {
        try {
            if (workflowId == null) {
                return new ReturnT<>(ReturnT.FAIL_CODE, "工作流ID不能为空");
            }

            if (days == null || days < 1) {
                days = 7;
            }

            // TODO: 实现统计逻辑
            Map<String, Object> stats = new HashMap<>();
            stats.put("totalCount", 0);
            stats.put("successCount", 0);
            stats.put("failedCount", 0);
            stats.put("runningCount", 0);
            stats.put("successRate", 0.0);

            return new ReturnT<>(stats);

        } catch (Exception e) {
            log.error("查询工作流实例统计失败, workflowId: {}", workflowId, e);
            return new ReturnT<>(ReturnT.FAIL_CODE, "查询统计失败: " + e.getMessage());
        }
    }

    @Override
    public ReturnT<Map<String, Object>> getRecentInstances(Long workflowId, Integer limit) {
        try {
            if (workflowId == null) {
                return new ReturnT<>(ReturnT.FAIL_CODE, "工作流ID不能为空");
            }

            if (limit == null || limit < 1) {
                limit = 10;
            }

            List<WorkflowInstance> instances = workflowInstanceDao.findByWorkflowId(workflowId, limit);

            Map<String, Object> result = new HashMap<>();
            result.put("instances", instances);
            result.put("count", instances.size());

            return new ReturnT<>(result);

        } catch (Exception e) {
            log.error("查询最近工作流实例失败, workflowId: {}", workflowId, e);
            return new ReturnT<>(ReturnT.FAIL_CODE, "查询失败: " + e.getMessage());
        }
    }

    @Override
    public ReturnT<String> deleteInstance(Long id) {
        try {
            if (id == null) {
                return new ReturnT<>(ReturnT.FAIL_CODE, "实例ID不能为空");
            }

            WorkflowInstance instance = workflowInstanceDao.load(id);
            if (instance == null) {
                return new ReturnT<>(ReturnT.FAIL_CODE, "工作流实例不存在");
            }

            if (instance.isRunning()) {
                return new ReturnT<>(ReturnT.FAIL_CODE, "运行中的实例不能删除");
            }

            int result = workflowInstanceDao.delete(id);
            if (result > 0) {
                log.info("工作流实例{}删除成功", id);
                return ReturnT.SUCCESS;
            } else {
                return new ReturnT<>(ReturnT.FAIL_CODE, "删除失败");
            }

        } catch (Exception e) {
            log.error("删除工作流实例失败, id: {}", id, e);
            return new ReturnT<>(ReturnT.FAIL_CODE, "删除失败: " + e.getMessage());
        }
    }

    @Override
    public ReturnT<String> batchDeleteInstances(Long[] ids) {
        try {
            if (ids == null || ids.length == 0) {
                return new ReturnT<>(ReturnT.FAIL_CODE, "请选择要删除的实例");
            }

            int successCount = 0;
            int failCount = 0;

            for (Long id : ids) {
                ReturnT<String> result = deleteInstance(id);
                if (result.getCode() == ReturnT.SUCCESS_CODE) {
                    successCount++;
                } else {
                    failCount++;
                }
            }

            String message = String.format("批量删除完成，成功: %d, 失败: %d", successCount, failCount);
            log.info(message);

            if (failCount == 0) {
                return new ReturnT<>(ReturnT.SUCCESS_CODE, message);
            } else {
                return new ReturnT<>(ReturnT.FAIL_CODE, message);
            }

        } catch (Exception e) {
            log.error("批量删除工作流实例失败", e);
            return new ReturnT<>(ReturnT.FAIL_CODE, "批量删除失败: " + e.getMessage());
        }
    }

    @Override
    public ReturnT<Long> createInstance(WorkflowInstance workflowInstance) {
        try {
            if (workflowInstance == null) {
                return new ReturnT<>(ReturnT.FAIL_CODE, "工作流实例不能为空");
            }

            // 设置创建时间
            Date now = new Date();
            workflowInstance.setCreateTime(now);
            workflowInstance.setUpdateTime(now);

            // 保存实例
            int result = workflowInstanceDao.save(workflowInstance);
            if (result > 0) {
                log.info("工作流实例创建成功, id: {}", workflowInstance.getId());
                return new ReturnT<>(workflowInstance.getId());
            } else {
                return new ReturnT<>(ReturnT.FAIL_CODE, "创建实例失败");
            }

        } catch (Exception e) {
            log.error("创建工作流实例失败", e);
            return new ReturnT<>(ReturnT.FAIL_CODE, "创建失败: " + e.getMessage());
        }
    }

    @Override
    public ReturnT<String> updateInstanceStatus(Long id, Integer status, String errorMessage) {
        try {
            if (id == null) {
                return new ReturnT<>(ReturnT.FAIL_CODE, "实例ID不能为空");
            }

            WorkflowInstance instance = workflowInstanceDao.load(id);
            if (instance == null) {
                return new ReturnT<>(ReturnT.FAIL_CODE, "工作流实例不存在");
            }

            // 更新状态
            instance.setStatus(status);
            instance.setUpdateTime(new Date());

            if (StringUtils.hasText(errorMessage)) {
                instance.setErrorMessage(errorMessage);
            }

            // 如果是完成状态，设置结束时间和耗时
            if (WorkflowInstance.Status.getByCode(status) != null && 
                instance.isCompleted() && instance.getEndTime() == null) {
                instance.setEndTime(new Date());
                if (instance.getStartTime() != null) {
                    instance.setDuration(instance.getEndTime().getTime() - instance.getStartTime().getTime());
                }
            }

            int result = workflowInstanceDao.update(instance);
            if (result > 0) {
                return ReturnT.SUCCESS;
            } else {
                return new ReturnT<>(ReturnT.FAIL_CODE, "更新状态失败");
            }

        } catch (Exception e) {
            log.error("更新工作流实例状态失败, id: {}, status: {}", id, status, e);
            return new ReturnT<>(ReturnT.FAIL_CODE, "更新失败: " + e.getMessage());
        }
    }

    @Override
    public ReturnT<WorkflowInstance> getByJobLogId(Long jobLogId) {
        try {
            if (jobLogId == null) {
                return new ReturnT<>(ReturnT.FAIL_CODE, "任务日志ID不能为空");
            }

            WorkflowInstance instance = workflowInstanceDao.findByJobLogId(jobLogId);
            if (instance == null) {
                return new ReturnT<>(ReturnT.FAIL_CODE, "未找到对应的工作流实例");
            }

            return new ReturnT<>(instance);

        } catch (Exception e) {
            log.error("根据任务日志ID查询工作流实例失败, jobLogId: {}", jobLogId, e);
            return new ReturnT<>(ReturnT.FAIL_CODE, "查询失败: " + e.getMessage());
        }
    }
}
