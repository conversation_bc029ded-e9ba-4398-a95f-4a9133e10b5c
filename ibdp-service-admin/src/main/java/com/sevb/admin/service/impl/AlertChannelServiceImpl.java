package com.sevb.admin.service.impl;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.sevb.admin.core.alarm.channel.NotificationChannelManager;
import com.sevb.admin.core.alarm.model.AuthConfig;
import com.sevb.admin.core.alarm.model.NotificationRequest;
import com.sevb.admin.core.alarm.model.SendResult;
import com.sevb.admin.core.model.AlertChannelConfig;
import com.sevb.admin.dao.AlertChannelConfigDao;
import com.sevb.admin.service.AlertChannelService;
import com.sevb.core.biz.model.ReturnT;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import javax.crypto.Cipher;
import javax.crypto.KeyGenerator;
import javax.crypto.SecretKey;
import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.StandardCharsets;
import java.util.*;

/**
 * 告警通道配置服务实现
 *
 * <AUTHOR>
 */
@Service
public class AlertChannelServiceImpl implements AlertChannelService {

    private static final Logger logger = LoggerFactory.getLogger(AlertChannelServiceImpl.class);

    @Autowired
    private AlertChannelConfigDao alertChannelConfigDao;

    @Autowired
    private NotificationChannelManager channelManager;

    private final ObjectMapper objectMapper = new ObjectMapper();

    // 简单的加密密钥，生产环境应该从配置文件读取
    private static final String ENCRYPT_KEY = "AlertSystem2024!";

    @Override
    public Map<String, Object> pageList(int start, int length, String configName,
                                        String channelType, Integer enabled) {
        // 分页查询
        List<AlertChannelConfig> list = alertChannelConfigDao.pageList(start, length, configName, channelType, enabled);
        int listCount = alertChannelConfigDao.pageListCount(configName, channelType, enabled);

        // 构建返回结果
        Map<String, Object> maps = new HashMap<>();
        maps.put("recordsTotal", listCount);
        maps.put("recordsFiltered", listCount);
        maps.put("data", list);
        return maps;
    }

    @Override
    public List<AlertChannelConfig> findAllEnabled() {
        return alertChannelConfigDao.findAllEnabled();
    }

    @Override
    public AlertChannelConfig load(Long id) {
        if (id == null) {
            return null;
        }
        return alertChannelConfigDao.load(id);
    }

    @Override
    public AlertChannelConfig findByConfigName(String configName) {
        if (!StringUtils.hasText(configName)) {
            return null;
        }
        return alertChannelConfigDao.findByConfigName(configName);
    }

    @Override
    public List<AlertChannelConfig> findByChannelType(String channelType) {
        if (!StringUtils.hasText(channelType)) {
            return new ArrayList<>();
        }
        return alertChannelConfigDao.findByChannelType(channelType);
    }

    @Override
    public List<AlertChannelConfig> findEnabledByChannelType(String channelType) {
        if (!StringUtils.hasText(channelType)) {
            return new ArrayList<>();
        }
        return alertChannelConfigDao.findEnabledByChannelType(channelType);
    }

    @Override
    public AlertChannelConfig findDefaultByChannelType(String channelType) {
        if (!StringUtils.hasText(channelType)) {
            return null;
        }
        return alertChannelConfigDao.findDefaultByChannelType(channelType);
    }

    @Override
    public ReturnT<String> save(AlertChannelConfig alertChannelConfig) {
        // 参数验证
        if (alertChannelConfig == null) {
            return new ReturnT<>(ReturnT.FAIL_CODE, "通道配置信息不能为空");
        }

        if (!StringUtils.hasText(alertChannelConfig.getConfigName())) {
            return new ReturnT<>(ReturnT.FAIL_CODE, "配置名称不能为空");
        }

        if (!StringUtils.hasText(alertChannelConfig.getChannelType())) {
            return new ReturnT<>(ReturnT.FAIL_CODE, "通道类型不能为空");
        }

        if (!StringUtils.hasText(alertChannelConfig.getAuthType())) {
            return new ReturnT<>(ReturnT.FAIL_CODE, "认证类型不能为空");
        }

        if (!StringUtils.hasText(alertChannelConfig.getAuthConfig())) {
            return new ReturnT<>(ReturnT.FAIL_CODE, "认证配置不能为空");
        }

        // 验证配置名称是否已存在
        int existCount = alertChannelConfigDao.checkConfigNameExists(alertChannelConfig.getConfigName(), null);
        if (existCount > 0) {
            return new ReturnT<>(ReturnT.FAIL_CODE, "配置名称已存在");
        }

        // 验证认证配置格式
        ReturnT<String> validateResult = validateAuthConfig(
                alertChannelConfig.getChannelType(),
                alertChannelConfig.getAuthType(),
                alertChannelConfig.getAuthConfig());
        if (validateResult.getCode() != ReturnT.SUCCESS_CODE) {
            return validateResult;
        }

        // 加密认证配置
        String encryptedAuthConfig = encryptAuthConfig(alertChannelConfig.getAuthConfig());
        alertChannelConfig.setAuthConfig(encryptedAuthConfig);

        // 设置默认值
        if (alertChannelConfig.getEnabled() == null) {
            alertChannelConfig.setEnabled(1);
        }

        try {
            int result = alertChannelConfigDao.save(alertChannelConfig);
            return result > 0 ? ReturnT.SUCCESS : new ReturnT<>(ReturnT.FAIL_CODE, "保存失败");
        } catch (Exception e) {
            logger.error("保存通道配置失败", e);
            return new ReturnT<>(ReturnT.FAIL_CODE, "保存失败: " + e.getMessage());
        }
    }

    @Override
    public ReturnT<String> update(AlertChannelConfig alertChannelConfig) {
        // 参数验证
        if (alertChannelConfig == null || alertChannelConfig.getId() == null) {
            return new ReturnT<>(ReturnT.FAIL_CODE, "通道配置信息不能为空");
        }

        // 检查配置是否存在
        AlertChannelConfig existConfig = alertChannelConfigDao.load(alertChannelConfig.getId());
        if (existConfig == null) {
            return new ReturnT<>(ReturnT.FAIL_CODE, "通道配置不存在");
        }

        if (!StringUtils.hasText(alertChannelConfig.getConfigName())) {
            return new ReturnT<>(ReturnT.FAIL_CODE, "配置名称不能为空");
        }

        if (!StringUtils.hasText(alertChannelConfig.getAuthConfig())) {
            return new ReturnT<>(ReturnT.FAIL_CODE, "认证配置不能为空");
        }

        // 验证配置名称是否已存在（排除当前记录）
        int existCount = alertChannelConfigDao.checkConfigNameExists(
                alertChannelConfig.getConfigName(), alertChannelConfig.getId());
        if (existCount > 0) {
            return new ReturnT<>(ReturnT.FAIL_CODE, "配置名称已存在");
        }

        // 验证认证配置格式
        ReturnT<String> validateResult = validateAuthConfig(
                existConfig.getChannelType(),
                existConfig.getAuthType(),
                alertChannelConfig.getAuthConfig());
        if (validateResult.getCode() != ReturnT.SUCCESS_CODE) {
            return validateResult;
        }

        // 加密认证配置
        String encryptedAuthConfig = encryptAuthConfig(alertChannelConfig.getAuthConfig());
        alertChannelConfig.setAuthConfig(encryptedAuthConfig);

        try {
            int result = alertChannelConfigDao.update(alertChannelConfig);
            return result > 0 ? ReturnT.SUCCESS : new ReturnT<>(ReturnT.FAIL_CODE, "更新失败");
        } catch (Exception e) {
            logger.error("更新通道配置失败", e);
            return new ReturnT<>(ReturnT.FAIL_CODE, "更新失败: " + e.getMessage());
        }
    }

    @Override
    public ReturnT<String> delete(Long id, String updateUser) {
        if (id == null) {
            return new ReturnT<>(ReturnT.FAIL_CODE, "配置ID不能为空");
        }

        try {
            int result = alertChannelConfigDao.delete(id, updateUser);
            return result > 0 ? ReturnT.SUCCESS : new ReturnT<>(ReturnT.FAIL_CODE, "删除失败");
        } catch (Exception e) {
            logger.error("删除通道配置失败", e);
            return new ReturnT<>(ReturnT.FAIL_CODE, "删除失败: " + e.getMessage());
        }
    }

    @Override
    public ReturnT<String> updateEnabled(Long id, Integer enabled, String updateUser) {
        if (id == null) {
            return new ReturnT<>(ReturnT.FAIL_CODE, "配置ID不能为空");
        }

        if (enabled == null || (enabled != 0 && enabled != 1)) {
            return new ReturnT<>(ReturnT.FAIL_CODE, "启用状态值无效");
        }

        try {
            int result = alertChannelConfigDao.updateEnabled(id, enabled, updateUser);
            return result > 0 ? ReturnT.SUCCESS : new ReturnT<>(ReturnT.FAIL_CODE, "更新状态失败");
        } catch (Exception e) {
            logger.error("更新通道配置状态失败", e);
            return new ReturnT<>(ReturnT.FAIL_CODE, "更新状态失败: " + e.getMessage());
        }
    }

    @Override
    public ReturnT<String> batchDelete(List<Long> ids, String updateUser) {
        if (ids == null || ids.isEmpty()) {
            return new ReturnT<>(ReturnT.FAIL_CODE, "配置ID列表不能为空");
        }

        try {
            int result = alertChannelConfigDao.batchDelete(ids, updateUser);
            return result > 0 ? ReturnT.SUCCESS : new ReturnT<>(ReturnT.FAIL_CODE, "批量删除失败");
        } catch (Exception e) {
            logger.error("批量删除通道配置失败", e);
            return new ReturnT<>(ReturnT.FAIL_CODE, "批量删除失败: " + e.getMessage());
        }
    }

    @Override
    public ReturnT<String> testSend(Long id) {
        return testSend(id, null);
    }

    @Override
    public ReturnT<String> testSend(Long id, List<String> recipients) {
        if (id == null) {
            return new ReturnT<>(ReturnT.FAIL_CODE, "通道配置ID不能为空");
        }

        try {
            AlertChannelConfig config = alertChannelConfigDao.load(id);
            if (config == null) {
                return new ReturnT<>(ReturnT.FAIL_CODE, "通道配置不存在");
            }

            if (config.getEnabled() != 1) {
                return new ReturnT<>(ReturnT.FAIL_CODE, "通道配置已禁用，无法测试");
            }

            // 解密认证配置
            String decryptedAuthConfig = decryptAuthConfig(config.getAuthConfig());

            // 构建认证配置对象
            AuthConfig authConfig = new AuthConfig();
            authConfig.setAuthType(config.getAuthType());
            authConfig.setAuthData(objectMapper.readValue(decryptedAuthConfig, Map.class));

            // 确定测试收件人
            List<String> testRecipients;
            if (recipients != null && !recipients.isEmpty()) {
                // 使用用户指定的收件人
                testRecipients = recipients;
            } else {
                // 使用默认测试收件人
                testRecipients = getTestRecipients(config.getChannelType(), authConfig);
                if (testRecipients.isEmpty()) {
                    return new ReturnT<>(ReturnT.FAIL_CODE, "无法确定测试收件人，请指定收件人");
                }
            }

            // 构建测试通知请求
            NotificationRequest request = NotificationRequest.builder()
                    .requestId("TEST_" + System.currentTimeMillis())
                    .channelType(config.getChannelType())
                    .authConfig(authConfig)
                    .content(getTestContent(config.getChannelType()))
                    .recipients(testRecipients)
                    .build();

            // 发送测试通知
            SendResult result = channelManager.sendNotification(request);

            if (result.isSuccess()) {
                return new ReturnT<>(ReturnT.SUCCESS_CODE, "测试发送成功: " + result.getMessage());
            } else {
                return new ReturnT<>(ReturnT.FAIL_CODE, "测试发送失败: " + result.getMessage());
            }
        } catch (Exception e) {
            logger.error("测试发送失败", e);
            return new ReturnT<>(ReturnT.FAIL_CODE, "测试发送失败: " + e.getMessage());
        }
    }

    /**
     * 获取测试收件人
     */
    private List<String> getTestRecipients(String channelType, AuthConfig authConfig) {
        switch (channelType) {
            case "EMAIL":
                // 邮件通道使用认证用户的邮箱作为测试收件人
                Map<String, Object> authData = authConfig.getAuthData();
                if (authData != null && authData.containsKey("username")) {
                    String username = authData.get("username").toString();
                    if (StringUtils.hasText(username) && username.contains("@")) {
                        return List.of(username);
                    }
                }
                return List.of();
            case "WECHAT":
            case "DINGTALK":
            case "FEISHU":
                // 其他通道类型使用默认测试收件人
                return List.of("test");
            default:
                return List.of();
        }
    }

    /**
     * 获取测试内容
     */
    private String getTestContent(String channelType) {
        switch (channelType) {
            case "EMAIL":
                return "{\"subject\":\"告警系统测试邮件\",\"body\":\"这是一封来自告警系统的测试邮件，用于验证邮件通道配置是否正确。\\n\\n发送时间：" +
                       new Date() + "\\n\\n如果您收到此邮件，说明邮件通道配置成功！\",\"html\":false}";
            case "WECHAT":
                return "告警系统测试消息\\n\\n这是一条来自告警系统的测试消息，用于验证企业微信通道配置是否正确。\\n\\n发送时间：" + new Date();
            case "DINGTALK":
                return "告警系统测试消息\\n\\n这是一条来自告警系统的测试消息，用于验证钉钉通道配置是否正确。\\n\\n发送时间：" + new Date();
            case "FEISHU":
                return "告警系统测试消息\\n\\n这是一条来自告警系统的测试消息，用于验证飞书通道配置是否正确。\\n\\n发送时间：" + new Date();
            default:
                return "告警系统测试消息";
        }
    }

    @Override
    public String encryptAuthConfig(String authConfig) {
        if (!StringUtils.hasText(authConfig)) {
            return authConfig;
        }

        try {
            SecretKeySpec secretKey = new SecretKeySpec(ENCRYPT_KEY.getBytes(StandardCharsets.UTF_8), "AES");
            Cipher cipher = Cipher.getInstance("AES");
            cipher.init(Cipher.ENCRYPT_MODE, secretKey);
            byte[] encrypted = cipher.doFinal(authConfig.getBytes(StandardCharsets.UTF_8));
            return Base64.getEncoder().encodeToString(encrypted);
        } catch (Exception e) {
            logger.error("加密认证配置失败", e);
            throw new RuntimeException("加密失败", e);
        }
    }

    @Override
    public String decryptAuthConfig(String encryptedAuthConfig) {
        if (!StringUtils.hasText(encryptedAuthConfig)) {
            return encryptedAuthConfig;
        }

        try {
            SecretKeySpec secretKey = new SecretKeySpec(ENCRYPT_KEY.getBytes(StandardCharsets.UTF_8), "AES");
            Cipher cipher = Cipher.getInstance("AES");
            cipher.init(Cipher.DECRYPT_MODE, secretKey);
            byte[] decrypted = cipher.doFinal(Base64.getDecoder().decode(encryptedAuthConfig));
            return new String(decrypted, StandardCharsets.UTF_8);
        } catch (Exception e) {
            logger.error("解密认证配置失败", e);
            throw new RuntimeException("解密失败", e);
        }
    }

    @Override
    public ReturnT<String> validateAuthConfig(String channelType, String authType, String authConfig) {
        if (!StringUtils.hasText(authConfig)) {
            return new ReturnT<>(ReturnT.FAIL_CODE, "认证配置不能为空");
        }

        try {
            // 验证JSON格式
            Map<String, Object> configMap = objectMapper.readValue(authConfig, Map.class);

            // 根据通道类型和认证类型验证必需字段
            switch (channelType) {
                case "WECHAT":
                    if (!configMap.containsKey("webhookUrl")) {
                        return new ReturnT<>(ReturnT.FAIL_CODE, "企业微信配置缺少webhookUrl字段");
                    }
                    break;
                case "DINGTALK":
                    if (!configMap.containsKey("webhookUrl")) {
                        return new ReturnT<>(ReturnT.FAIL_CODE, "钉钉配置缺少webhookUrl字段");
                    }
                    break;
                case "FEISHU":
                    if (!configMap.containsKey("webhookUrl")) {
                        return new ReturnT<>(ReturnT.FAIL_CODE, "飞书配置缺少webhookUrl字段");
                    }
                    break;
                case "EMAIL":
                    if (!configMap.containsKey("smtpHost") || !configMap.containsKey("username")) {
                        return new ReturnT<>(ReturnT.FAIL_CODE, "邮件配置缺少必需字段");
                    }
                    break;
            }

            return ReturnT.SUCCESS;
        } catch (Exception e) {
            return new ReturnT<>(ReturnT.FAIL_CODE, "认证配置格式错误: " + e.getMessage());
        }
    }
}
