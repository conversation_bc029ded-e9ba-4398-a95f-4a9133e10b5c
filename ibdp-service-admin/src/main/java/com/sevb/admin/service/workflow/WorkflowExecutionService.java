package com.sevb.admin.service.workflow;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.sevb.admin.core.model.JobInfo;
import com.sevb.admin.core.model.Workflow;
import com.sevb.admin.core.thread.JobTriggerPoolHelper;
import com.sevb.admin.core.trigger.TriggerTypeEnum;
import com.sevb.admin.dao.JobInfoDao;
import com.sevb.admin.dao.WorkflowDao;
import com.sevb.admin.service.orchestration.CentralWorkflowOrchestrationService;
import com.sevb.core.biz.model.ReturnT;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;

/**
 * 工作流执行服务
 * 负责工作流的手动执行、状态查询等功能
 * 
 * <AUTHOR>
 */
@Service
@Slf4j
public class WorkflowExecutionService {

    @Autowired
    private WorkflowDao workflowDao;

    @Autowired
    private JobInfoDao jobInfoDao;

    @Autowired
    private CentralWorkflowOrchestrationService centralOrchestrationService;

    private final ObjectMapper objectMapper = new ObjectMapper();

    /**
     * 手动执行工作流
     *
     * @param workflowId 工作流ID
     * @param executeUser 执行用户
     * @param executeParams 执行参数（可选）
     * @return 执行结果
     */
    public ReturnT<String> executeWorkflow(Long workflowId, String executeUser, Map<String, Object> executeParams) {
        try {
            log.info("开始手动执行工作流: workflowId={}, executeUser={}", workflowId, executeUser);

            // 1. 验证工作流状态
            Workflow workflow = workflowDao.load(workflowId);
            if (workflow == null) {
                return new ReturnT<>(ReturnT.FAIL_CODE, "工作流不存在");
            }

            if (!workflow.isOnline()) {
                return new ReturnT<>(ReturnT.FAIL_CODE, "只有已上线的工作流才能执行，当前状态: " + workflow.getStatusName());
            }

            // 2. 使用调度中心编排模式执行工作流
            log.info("使用调度中心编排模式执行工作流: workflowId={}", workflowId);
            return centralOrchestrationService.executeWorkflow(workflowId, executeUser);

        } catch (Exception e) {
            log.error("手动执行工作流失败: workflowId={}", workflowId, e);
            return new ReturnT<>(ReturnT.FAIL_CODE, "执行失败: " + e.getMessage());
        }
    }







    /**
     * 查询工作流执行状态
     * 
     * @param executionId 执行ID
     * @return 执行状态
     */
    public ReturnT<Map<String, Object>> getExecutionStatus(String executionId) {
        try {
            // TODO: 实现执行状态查询逻辑
            // 可以通过解析executionId获取jobId，然后查询job_log表
            
            Map<String, Object> status = new HashMap<>();
            status.put("executionId", executionId);
            status.put("status", "RUNNING");
            status.put("message", "执行状态查询功能待实现");
            
            return new ReturnT<>(status);
            
        } catch (Exception e) {
            log.error("查询执行状态失败: executionId={}", executionId, e);
            return new ReturnT<>(ReturnT.FAIL_CODE, "查询失败: " + e.getMessage());
        }
    }

    /**
     * 停止工作流执行
     * 
     * @param executionId 执行ID
     * @return 操作结果
     */
    public ReturnT<String> stopExecution(String executionId) {
        try {
            // TODO: 实现停止执行逻辑
            // 需要解析executionId，获取对应的XXL-JOB日志ID，然后调用kill接口
            
            log.info("停止工作流执行: executionId={}", executionId);
            
            return new ReturnT<>("停止执行功能待实现");
            
        } catch (Exception e) {
            log.error("停止执行失败: executionId={}", executionId, e);
            return new ReturnT<>(ReturnT.FAIL_CODE, "停止失败: " + e.getMessage());
        }
    }
}
