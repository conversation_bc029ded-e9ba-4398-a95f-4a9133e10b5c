package com.sevb.admin.service;

import com.sevb.admin.core.model.Project;
import com.sevb.admin.core.model.ProjectMember;
import com.sevb.core.biz.model.ReturnT;

import java.util.List;
import java.util.Map;

/**
 * 项目管理服务接口
 * <AUTHOR>
 */
public interface ProjectService {

    /**
     * 分页查询项目列表
     */
    Map<String, Object> pageList(int pageNum, int pageSize, String name, String type, Integer status, String userId);

    /**
     * 新增项目
     */
    ReturnT<String> add(Project project, String loginUser);

    /**
     * 更新项目
     */
    ReturnT<String> update(Project project, String loginUser);

    /**
     * 删除项目
     */
    ReturnT<String> delete(Long id, String loginUser);

    /**
     * 根据ID查询项目详情
     */
    Project load(Long id);

    /**
     * 根据编码查询项目
     */
    Project loadByCode(String code);

    /**
     * 查询用户可访问的项目列表
     */
    List<Project> findAccessibleProjects(String userId);

    /**
     * 查询公共项目列表
     */
    List<Project> findPublicProjects();

    /**
     * 查询用户参与的项目列表
     */
    List<Project> findUserProjects(String userId);

    /**
     * 更新项目状态
     */
    ReturnT<String> updateStatus(Long id, Integer status, String loginUser);

    /**
     * 更新项目类型
     */
    ReturnT<String> updateType(Long id, String type, String loginUser);

    /**
     * 检查用户是否有项目访问权限
     */
    boolean hasProjectAccess(Long projectId, String userId);

    /**
     * 检查用户是否为项目管理员
     */
    boolean isProjectAdmin(Long projectId, String userId);

    /**
     * 获取用户在项目中的角色
     */
    String getUserRole(Long projectId, String userId);

    /**
     * 获取项目类型列表
     */
    List<Map<String, Object>> getProjectTypes();

    /**
     * 查询启用的项目列表
     */
    List<Project> findAllEnabled();

    /**
     * 根据类型查询项目列表
     */
    List<Project> findByType(String type);
}
