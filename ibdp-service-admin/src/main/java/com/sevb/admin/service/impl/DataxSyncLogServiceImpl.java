package com.sevb.admin.service.impl;

import com.sevb.admin.core.model.DataxSyncLog;
import com.sevb.admin.dao.DataxSyncLogDao;
import com.sevb.admin.service.DataxSyncLogService;
import com.sevb.core.biz.model.ReturnT;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * DataX增量同步日志服务实现类
 * 
 * <AUTHOR>
 */
@Service
public class DataxSyncLogServiceImpl implements DataxSyncLogService {

    private static final Logger logger = LoggerFactory.getLogger(DataxSyncLogServiceImpl.class);

    @Autowired
    private DataxSyncLogDao dataxSyncLogDao;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ReturnT<String> save(DataxSyncLog syncLog) {
        if (syncLog == null) {
            return new ReturnT<>(ReturnT.FAIL_CODE, "同步日志信息不能为空");
        }

        try {
            // 验证必填字段
            if (syncLog.getSyncStateId() == null) {
                return new ReturnT<>(ReturnT.FAIL_CODE, "同步状态ID不能为空");
            }
            if (!StringUtils.hasText(syncLog.getStatus())) {
                return new ReturnT<>(ReturnT.FAIL_CODE, "状态不能为空");
            }

            // 设置默认值
            if (syncLog.getCreateTime() == null) {
                syncLog.setCreateTime(LocalDateTime.now());
            }
            if (syncLog.getRetryTimes() == null) {
                syncLog.setRetryTimes(0);
            }

            int result = dataxSyncLogDao.save(syncLog);
            if (result > 0) {
                logger.info("保存同步日志成功，ID: {}, 执行ID: {}", syncLog.getId(), syncLog.getExecutionId());
                return new ReturnT<>(ReturnT.SUCCESS_CODE, "保存成功");
            } else {
                return new ReturnT<>(ReturnT.FAIL_CODE, "保存失败");
            }
        } catch (Exception e) {
            logger.error("保存同步日志失败", e);
            return new ReturnT<>(ReturnT.FAIL_CODE, "保存失败: " + e.getMessage());
        }
    }

    @Override
    public DataxSyncLog load(Long id) {
        if (id == null) {
            return null;
        }
        try {
            return dataxSyncLogDao.load(id);
        } catch (Exception e) {
            logger.error("查询同步日志失败，ID: {}", id, e);
            return null;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ReturnT<String> update(DataxSyncLog syncLog) {
        if (syncLog == null || syncLog.getId() == null) {
            return new ReturnT<>(ReturnT.FAIL_CODE, "同步日志信息不能为空");
        }

        try {
            // 检查记录是否存在
            DataxSyncLog existing = dataxSyncLogDao.load(syncLog.getId());
            if (existing == null) {
                return new ReturnT<>(ReturnT.FAIL_CODE, "同步日志不存在");
            }

            int result = dataxSyncLogDao.update(syncLog);
            if (result > 0) {
                logger.info("更新同步日志成功，ID: {}", syncLog.getId());
                return new ReturnT<>(ReturnT.SUCCESS_CODE, "更新成功");
            } else {
                return new ReturnT<>(ReturnT.FAIL_CODE, "更新失败");
            }
        } catch (Exception e) {
            logger.error("更新同步日志失败，ID: {}", syncLog.getId(), e);
            return new ReturnT<>(ReturnT.FAIL_CODE, "更新失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ReturnT<String> delete(Long id) {
        if (id == null) {
            return new ReturnT<>(ReturnT.FAIL_CODE, "ID不能为空");
        }

        try {
            DataxSyncLog existing = dataxSyncLogDao.load(id);
            if (existing == null) {
                return new ReturnT<>(ReturnT.FAIL_CODE, "同步日志不存在");
            }

            int result = dataxSyncLogDao.delete(id);
            if (result > 0) {
                logger.info("删除同步日志成功，ID: {}", id);
                return new ReturnT<>(ReturnT.SUCCESS_CODE, "删除成功");
            } else {
                return new ReturnT<>(ReturnT.FAIL_CODE, "删除失败");
            }
        } catch (Exception e) {
            logger.error("删除同步日志失败，ID: {}", id, e);
            return new ReturnT<>(ReturnT.FAIL_CODE, "删除失败: " + e.getMessage());
        }
    }

    @Override
    public List<DataxSyncLog> findBySyncStateId(Long syncStateId) {
        if (syncStateId == null) {
            return null;
        }
        try {
            return dataxSyncLogDao.findBySyncStateId(syncStateId);
        } catch (Exception e) {
            logger.error("根据同步状态ID查询日志失败，同步状态ID: {}", syncStateId, e);
            return null;
        }
    }

    @Override
    public DataxSyncLog findByExecutionId(String executionId) {
        if (!StringUtils.hasText(executionId)) {
            return null;
        }
        try {
            return dataxSyncLogDao.findByExecutionId(executionId);
        } catch (Exception e) {
            logger.error("根据执行ID查询日志失败，执行ID: {}", executionId, e);
            return null;
        }
    }

    @Override
    public DataxSyncLog findByJobLogId(Long jobLogId) {
        if (jobLogId == null) {
            return null;
        }
        try {
            return dataxSyncLogDao.findByJobLogId(jobLogId);
        } catch (Exception e) {
            logger.error("根据JOB日志ID查询日志失败，JOB日志ID: {}", jobLogId, e);
            return null;
        }
    }

    @Override
    public ReturnT<List<DataxSyncLog>> pageList(int page, int size, Long syncStateId, String executionId,
                                               String status, LocalDateTime startTime, LocalDateTime endTime) {
        try {
            int offset = (page - 1) * size;
            List<DataxSyncLog> list = dataxSyncLogDao.pageList(offset, size, syncStateId, executionId,
                                                              status, startTime, endTime);
            int count = dataxSyncLogDao.pageListCount(offset, size, syncStateId, executionId,
                                                     status, startTime, endTime);
            
            Map<String, Object> result = new HashMap<>();
            result.put("data", list);
            result.put("recordsFiltered", count);
            result.put("recordsTotal", count);
            
            return new ReturnT<>(list);
        } catch (Exception e) {
            logger.error("分页查询同步日志失败", e);
            return new ReturnT<>(ReturnT.FAIL_CODE, "查询失败: " + e.getMessage());
        }
    }

    @Override
    public List<DataxSyncLog> findRunningLogs() {
        try {
            return dataxSyncLogDao.findRunningLogs();
        } catch (Exception e) {
            logger.error("查询运行中日志失败", e);
            return null;
        }
    }

    @Override
    public List<DataxSyncLog> findFailedLogs(int limit) {
        try {
            return dataxSyncLogDao.findFailedLogs(limit);
        } catch (Exception e) {
            logger.error("查询失败日志失败，限制数量: {}", limit, e);
            return null;
        }
    }

    @Override
    public List<DataxSyncLog> findRetryableLogs(int maxRetryTimes) {
        try {
            return dataxSyncLogDao.findRetryableLogs(maxRetryTimes);
        } catch (Exception e) {
            logger.error("查询可重试日志失败，最大重试次数: {}", maxRetryTimes, e);
            return null;
        }
    }

    @Override
    public List<DataxSyncLog> findRecentLogs(int limit) {
        try {
            return dataxSyncLogDao.findRecentLogs(limit);
        } catch (Exception e) {
            logger.error("查询最近日志失败，限制数量: {}", limit, e);
            return null;
        }
    }

    @Override
    public int countByStatus(String status) {
        try {
            return dataxSyncLogDao.countByStatus(status);
        } catch (Exception e) {
            logger.error("统计日志数量失败，状态: {}", status, e);
            return 0;
        }
    }

    @Override
    public int countBySyncStateId(Long syncStateId) {
        try {
            return dataxSyncLogDao.countBySyncStateId(syncStateId);
        } catch (Exception e) {
            logger.error("根据同步状态ID统计日志数量失败，同步状态ID: {}", syncStateId, e);
            return 0;
        }
    }

    @Override
    public List<DataxSyncLog> findByTimeRange(LocalDateTime startTime, LocalDateTime endTime) {
        try {
            return dataxSyncLogDao.findByTimeRange(startTime, endTime);
        } catch (Exception e) {
            logger.error("根据时间范围查询日志失败", e);
            return null;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ReturnT<DataxSyncLog> createExecutionLog(Long syncStateId, String executionId, Long jobLogId,
                                                   String incrementalValueStart, String dataxConfig) {
        try {
            DataxSyncLog syncLog = new DataxSyncLog();
            syncLog.setSyncStateId(syncStateId);
            syncLog.setExecutionId(executionId);
            syncLog.setJobLogId(jobLogId);
            syncLog.setIncrementalValueStart(incrementalValueStart);
            syncLog.setDataxConfig(dataxConfig);
            syncLog.startExecution();

            ReturnT<String> saveResult = save(syncLog);
            if (saveResult.getCode() == ReturnT.SUCCESS_CODE) {
                logger.info("创建执行日志成功，执行ID: {}", executionId);
                return new ReturnT<>(syncLog);
            } else {
                return new ReturnT<>(ReturnT.FAIL_CODE, saveResult.getMsg());
            }
        } catch (Exception e) {
            logger.error("创建执行日志失败", e);
            return new ReturnT<>(ReturnT.FAIL_CODE, "创建执行日志失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ReturnT<String> markSuccess(Long logId, Long syncRecords, String incrementalValueEnd, String performanceInfo) {
        if (logId == null) {
            return new ReturnT<>(ReturnT.FAIL_CODE, "日志ID不能为空");
        }

        try {
            LocalDateTime endTime = LocalDateTime.now();
            DataxSyncLog log = load(logId);
            if (log == null) {
                return new ReturnT<>(ReturnT.FAIL_CODE, "日志不存在");
            }

            Long duration = null;
            if (log.getStartTime() != null) {
                duration = java.time.Duration.between(log.getStartTime(), endTime).toMillis();
            }

            int result = dataxSyncLogDao.markSuccess(logId, endTime, duration, syncRecords, 
                                                   incrementalValueEnd, performanceInfo);
            if (result > 0) {
                logger.info("标记执行成功，日志ID: {}, 同步记录数: {}", logId, syncRecords);
                return new ReturnT<>(ReturnT.SUCCESS_CODE, "标记成功");
            } else {
                return new ReturnT<>(ReturnT.FAIL_CODE, "标记失败");
            }
        } catch (Exception e) {
            logger.error("标记执行成功失败，日志ID: {}", logId, e);
            return new ReturnT<>(ReturnT.FAIL_CODE, "标记失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ReturnT<String> markFailed(Long logId, String errorMessage, String errorCode) {
        if (logId == null) {
            return new ReturnT<>(ReturnT.FAIL_CODE, "日志ID不能为空");
        }

        try {
            LocalDateTime endTime = LocalDateTime.now();
            DataxSyncLog log = load(logId);
            if (log == null) {
                return new ReturnT<>(ReturnT.FAIL_CODE, "日志不存在");
            }

            Long duration = null;
            if (log.getStartTime() != null) {
                duration = java.time.Duration.between(log.getStartTime(), endTime).toMillis();
            }

            int result = dataxSyncLogDao.markFailed(logId, endTime, duration, errorMessage, errorCode);
            if (result > 0) {
                logger.info("标记执行失败，日志ID: {}, 错误信息: {}", logId, errorMessage);
                return new ReturnT<>(ReturnT.SUCCESS_CODE, "标记成功");
            } else {
                return new ReturnT<>(ReturnT.FAIL_CODE, "标记失败");
            }
        } catch (Exception e) {
            logger.error("标记执行失败失败，日志ID: {}", logId, e);
            return new ReturnT<>(ReturnT.FAIL_CODE, "标记失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ReturnT<String> markCancelled(Long logId) {
        if (logId == null) {
            return new ReturnT<>(ReturnT.FAIL_CODE, "日志ID不能为空");
        }

        try {
            LocalDateTime endTime = LocalDateTime.now();
            DataxSyncLog log = load(logId);
            if (log == null) {
                return new ReturnT<>(ReturnT.FAIL_CODE, "日志不存在");
            }

            Long duration = null;
            if (log.getStartTime() != null) {
                duration = java.time.Duration.between(log.getStartTime(), endTime).toMillis();
            }

            int result = dataxSyncLogDao.markCancelled(logId, endTime, duration);
            if (result > 0) {
                logger.info("标记执行取消，日志ID: {}", logId);
                return new ReturnT<>(ReturnT.SUCCESS_CODE, "标记成功");
            } else {
                return new ReturnT<>(ReturnT.FAIL_CODE, "标记失败");
            }
        } catch (Exception e) {
            logger.error("标记执行取消失败，日志ID: {}", logId, e);
            return new ReturnT<>(ReturnT.FAIL_CODE, "标记失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ReturnT<String> incrementRetryTimes(Long logId) {
        if (logId == null) {
            return new ReturnT<>(ReturnT.FAIL_CODE, "日志ID不能为空");
        }

        try {
            int result = dataxSyncLogDao.incrementRetryTimes(logId);
            if (result > 0) {
                logger.info("增加重试次数成功，日志ID: {}", logId);
                return new ReturnT<>(ReturnT.SUCCESS_CODE, "增加重试次数成功");
            } else {
                return new ReturnT<>(ReturnT.FAIL_CODE, "增加重试次数失败");
            }
        } catch (Exception e) {
            logger.error("增加重试次数失败，日志ID: {}", logId, e);
            return new ReturnT<>(ReturnT.FAIL_CODE, "增加重试次数失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ReturnT<DataxSyncLog> createRetryLog(Long originalLogId) {
        if (originalLogId == null) {
            return new ReturnT<>(ReturnT.FAIL_CODE, "原始日志ID不能为空");
        }

        try {
            DataxSyncLog originalLog = load(originalLogId);
            if (originalLog == null) {
                return new ReturnT<>(ReturnT.FAIL_CODE, "原始日志不存在");
            }

            DataxSyncLog retryLog = originalLog.createRetryLog();
            ReturnT<String> saveResult = save(retryLog);
            if (saveResult.getCode() == ReturnT.SUCCESS_CODE) {
                logger.info("创建重试日志成功，原始日志ID: {}, 重试日志ID: {}", originalLogId, retryLog.getId());
                return new ReturnT<>(retryLog);
            } else {
                return new ReturnT<>(ReturnT.FAIL_CODE, saveResult.getMsg());
            }
        } catch (Exception e) {
            logger.error("创建重试日志失败，原始日志ID: {}", originalLogId, e);
            return new ReturnT<>(ReturnT.FAIL_CODE, "创建重试日志失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ReturnT<String> batchDeleteByTime(LocalDateTime beforeTime, List<String> statuses) {
        if (beforeTime == null) {
            return new ReturnT<>(ReturnT.FAIL_CODE, "时间参数不能为空");
        }

        try {
            int result = dataxSyncLogDao.batchDeleteByTime(beforeTime, statuses);
            logger.info("批量删除历史日志成功，删除数量: {}", result);
            return new ReturnT<>(ReturnT.SUCCESS_CODE, "批量删除成功，删除数量: " + result);
        } catch (Exception e) {
            logger.error("批量删除历史日志失败", e);
            return new ReturnT<>(ReturnT.FAIL_CODE, "批量删除失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ReturnT<String> cleanupHistoryLogs(int days) {
        try {
            int result = dataxSyncLogDao.cleanupHistoryLogs(days);
            logger.info("清理历史日志成功，清理数量: {}", result);
            return new ReturnT<>(ReturnT.SUCCESS_CODE, "清理成功，清理数量: " + result);
        } catch (Exception e) {
            logger.error("清理历史日志失败", e);
            return new ReturnT<>(ReturnT.FAIL_CODE, "清理失败: " + e.getMessage());
        }
    }

    @Override
    public ReturnT<Object> getSyncStatistics(LocalDateTime startTime, LocalDateTime endTime) {
        try {
            Map<String, Object> statistics = new HashMap<>();
            statistics.put("totalLogs", countByStatus(null));
            statistics.put("successLogs", countByStatus("SUCCESS"));
            statistics.put("failedLogs", countByStatus("FAILED"));
            statistics.put("runningLogs", countByStatus("RUNNING"));
            statistics.put("cancelledLogs", countByStatus("CANCELLED"));

            if (startTime != null && endTime != null) {
                List<DataxSyncLog> logs = findByTimeRange(startTime, endTime);
                statistics.put("periodLogs", logs.size());
            }

            return new ReturnT<>(statistics);
        } catch (Exception e) {
            logger.error("获取同步统计信息失败", e);
            return new ReturnT<>(ReturnT.FAIL_CODE, "获取统计信息失败: " + e.getMessage());
        }
    }

    @Override
    public ReturnT<Object> getPerformanceStatistics(LocalDateTime startTime, LocalDateTime endTime) {
        try {
            Map<String, Object> performance = new HashMap<>();
            // TODO: 实现性能统计逻辑
            performance.put("averageDuration", 0.0);
            performance.put("maxDuration", 0L);
            performance.put("minDuration", 0L);
            performance.put("totalRecords", 0L);
            performance.put("averageSpeed", 0.0);

            return new ReturnT<>(performance);
        } catch (Exception e) {
            logger.error("获取性能统计信息失败", e);
            return new ReturnT<>(ReturnT.FAIL_CODE, "获取性能统计信息失败: " + e.getMessage());
        }
    }

    @Override
    public Double getAverageDuration(Long syncStateId, int days) {
        try {
            return dataxSyncLogDao.getAverageDuration(syncStateId, days);
        } catch (Exception e) {
            logger.error("查询平均执行时长失败，同步状态ID: {}, 天数: {}", syncStateId, days, e);
            return null;
        }
    }

    @Override
    public Double getSuccessRate(Long syncStateId, int days) {
        try {
            return dataxSyncLogDao.getSuccessRate(syncStateId, days);
        } catch (Exception e) {
            logger.error("查询成功率失败，同步状态ID: {}, 天数: {}", syncStateId, days, e);
            return null;
        }
    }

    @Override
    public DataxSyncLog findLastSuccessLog(Long syncStateId) {
        try {
            return dataxSyncLogDao.findLastSuccessLog(syncStateId);
        } catch (Exception e) {
            logger.error("查询最后成功日志失败，同步状态ID: {}", syncStateId, e);
            return null;
        }
    }

    @Override
    public DataxSyncLog findLastFailedLog(Long syncStateId) {
        try {
            return dataxSyncLogDao.findLastFailedLog(syncStateId);
        } catch (Exception e) {
            logger.error("查询最后失败日志失败，同步状态ID: {}", syncStateId, e);
            return null;
        }
    }

    @Override
    public List<DataxSyncLog> findByNodeId(Long nodeId, int limit) {
        try {
            return dataxSyncLogDao.findByNodeId(nodeId, limit);
        } catch (Exception e) {
            logger.error("根据节点ID查询日志失败，节点ID: {}", nodeId, e);
            return null;
        }
    }

    @Override
    public boolean hasRunningTask(Long syncStateId) {
        try {
            return dataxSyncLogDao.hasRunningTask(syncStateId);
        } catch (Exception e) {
            logger.error("检查运行中任务失败，同步状态ID: {}", syncStateId, e);
            return false;
        }
    }

    @Override
    public Long getTotalSyncRecords(Long syncStateId) {
        try {
            return dataxSyncLogDao.getTotalSyncRecords(syncStateId);
        } catch (Exception e) {
            logger.error("获取同步记录总数失败，同步状态ID: {}", syncStateId, e);
            return 0L;
        }
    }

    @Override
    public ReturnT<Object> getLogDetail(Long logId) {
        try {
            DataxSyncLog log = load(logId);
            if (log == null) {
                return new ReturnT<>(ReturnT.FAIL_CODE, "日志不存在");
            }

            Map<String, Object> detail = new HashMap<>();
            detail.put("log", log);
            detail.put("averageSpeed", log.getAverageSpeed());
            detail.put("durationInSeconds", log.getDurationInSeconds());

            return new ReturnT<>(detail);
        } catch (Exception e) {
            logger.error("获取日志详情失败，日志ID: {}", logId, e);
            return new ReturnT<>(ReturnT.FAIL_CODE, "获取日志详情失败: " + e.getMessage());
        }
    }

    @Override
    public ReturnT<Object> getExecutionTrend(Long syncStateId, int days) {
        try {
            Map<String, Object> trend = new HashMap<>();
            trend.put("successRate", getSuccessRate(syncStateId, days));
            trend.put("averageDuration", getAverageDuration(syncStateId, days));
            trend.put("totalRecords", getTotalSyncRecords(syncStateId));
            trend.put("lastSuccessLog", findLastSuccessLog(syncStateId));
            trend.put("lastFailedLog", findLastFailedLog(syncStateId));

            return new ReturnT<>(trend);
        } catch (Exception e) {
            logger.error("获取执行趋势失败，同步状态ID: {}, 天数: {}", syncStateId, days, e);
            return new ReturnT<>(ReturnT.FAIL_CODE, "获取执行趋势失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ReturnT<String> forceStopRunningTask(Long logId, String updateUser) {
        if (logId == null) {
            return new ReturnT<>(ReturnT.FAIL_CODE, "日志ID不能为空");
        }

        try {
            DataxSyncLog log = load(logId);
            if (log == null) {
                return new ReturnT<>(ReturnT.FAIL_CODE, "日志不存在");
            }

            if (!log.isRunning()) {
                return new ReturnT<>(ReturnT.FAIL_CODE, "任务未在运行中");
            }

            // 标记为取消状态
            ReturnT<String> result = markCancelled(logId);
            if (result.getCode() == ReturnT.SUCCESS_CODE) {
                logger.info("强制停止运行任务成功，日志ID: {}, 操作人: {}", logId, updateUser);
                return new ReturnT<>(ReturnT.SUCCESS_CODE, "强制停止成功");
            } else {
                return result;
            }
        } catch (Exception e) {
            logger.error("强制停止运行任务失败，日志ID: {}", logId, e);
            return new ReturnT<>(ReturnT.FAIL_CODE, "强制停止失败: " + e.getMessage());
        }
    }
}
