package com.sevb.admin.service;

import com.sevb.admin.core.model.ProjectMember;
import com.sevb.core.biz.model.ReturnT;

import java.util.List;
import java.util.Map;

/**
 * 项目成员管理服务接口
 * <AUTHOR>
 */
public interface ProjectMemberService {

    /**
     * 分页查询项目成员列表
     */
    Map<String, Object> pageList(int offset, int pagesize, Long projectId, String username, String role, Integer status);

    /**
     * 添加项目成员
     */
    ReturnT<String> addMember(ProjectMember member, String loginUser);

    /**
     * 批量添加项目成员
     */
    ReturnT<String> batchAddMembers(List<ProjectMember> members, String loginUser);

    /**
     * 移除项目成员
     */
    ReturnT<String> removeMember(Long id, String loginUser);

    /**
     * 根据项目ID和用户ID移除成员
     */
    ReturnT<String> removeMemberByProjectAndUser(Long projectId, String userId, String loginUser);

    /**
     * 更新成员角色
     */
    ReturnT<String> updateMemberRole(Long id, String role, String loginUser);

    /**
     * 更新成员状态
     */
    ReturnT<String> updateMemberStatus(Long id, Integer status, String loginUser);

    /**
     * 根据ID查询项目成员
     */
    ProjectMember load(Long id);

    /**
     * 根据项目ID和用户ID查询成员
     */
    ProjectMember loadByProjectAndUser(Long projectId, String userId);

    /**
     * 查询项目成员列表
     */
    List<ProjectMember> findByProjectId(Long projectId);

    /**
     * 查询用户参与的项目成员记录
     */
    List<ProjectMember> findByUserId(String userId);

    /**
     * 查询项目管理员列表
     */
    List<ProjectMember> findAdminsByProjectId(Long projectId);

    /**
     * 检查用户是否为项目成员
     */
    boolean isMember(Long projectId, String userId);

    /**
     * 统计项目成员数量
     */
    int countByProjectId(Long projectId);

    /**
     * 获取项目角色列表
     */
    List<Map<String, Object>> getProjectRoles();
}
