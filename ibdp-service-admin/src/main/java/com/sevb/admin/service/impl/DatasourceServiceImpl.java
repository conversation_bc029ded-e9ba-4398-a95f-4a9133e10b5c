package com.sevb.admin.service.impl;

import com.sevb.admin.core.model.Datasource;
import com.sevb.admin.core.util.DatasourceConnectionUtil;
import com.sevb.admin.dao.DatasourceDao;
import com.sevb.admin.service.DatasourceService;
import com.sevb.core.biz.model.ReturnT;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.sql.Connection;
import java.sql.DatabaseMetaData;
import java.sql.ResultSet;
import java.sql.Statement;
import java.util.*;

/**
 * 数据源服务实现类
 * <AUTHOR>
 */
@Slf4j
@Service
public class DatasourceServiceImpl implements DatasourceService {

    @Resource
    private DatasourceDao datasourceDao;

    @Override
    public Map<String, Object> pageList(int pageNum, int pageSize, String name, String type, Integer status) {
        // 计算offset
        int offset = (pageNum - 1) * pageSize;

        // 分页查询
        List<Datasource> list = datasourceDao.pageList(offset, pageSize, name, type, status);
        int listCount = datasourceDao.pageListCount(offset, pageSize, name, type, status);

        // 脱敏处理：隐藏密码
        for (Datasource datasource : list) {
            if (StringUtils.hasText(datasource.getPassword())) {
                datasource.setPassword("******");
            }
        }

        // 封装结果
        Map<String, Object> maps = new HashMap<>();
        maps.put("recordsTotal", listCount);
        maps.put("recordsFiltered", listCount);
        maps.put("data", list);
        return maps;
    }

    @Override
    public List<Datasource> findAllEnabled() {
        List<Datasource> list = datasourceDao.findAllEnabled();
        // 脱敏处理
        for (Datasource datasource : list) {
            if (StringUtils.hasText(datasource.getPassword())) {
                datasource.setPassword("******");
            }
        }
        return list;
    }

    @Override
    public Datasource load(Long id) {
        if (id == null) {
            return null;
        }
        Datasource datasource = datasourceDao.load(id);
        if (datasource != null && StringUtils.hasText(datasource.getPassword())) {
            // 脱敏处理
            datasource.setPassword("******");
        }
        return datasource;
    }

    @Override
    public ReturnT<String> add(Datasource datasource, String loginUser) {
        // 参数校验
        ReturnT<String> validResult = validateDatasource(datasource, null);
        if (validResult.getCode() != ReturnT.SUCCESS_CODE) {
            return validResult;
        }

        // 检查名称是否重复
        if (datasourceDao.checkNameExists(datasource.getName(), null) > 0) {
            return new ReturnT<>(ReturnT.FAIL_CODE, "数据源名称已存在");
        }

        // 密码加密
        if (StringUtils.hasText(datasource.getPassword())) {
            datasource.setPassword(DatasourceConnectionUtil.encryptPassword(datasource.getPassword()));
        }

        // 设置默认值
        datasource.setStatus(Datasource.Status.ENABLED.getCode());
        datasource.setTestStatus(Datasource.TestStatus.NOT_TESTED.getCode());
        datasource.setCreateUser(loginUser);
        datasource.setDeleted(0);

        try {
            int result = datasourceDao.save(datasource);
            return result > 0 ? ReturnT.SUCCESS : new ReturnT<>(ReturnT.FAIL_CODE, "保存失败");
        } catch (Exception e) {
            log.error("保存数据源失败", e);
            return new ReturnT<>(ReturnT.FAIL_CODE, "保存失败：" + e.getMessage());
        }
    }

    @Override
    public ReturnT<String> update(Datasource datasource, String loginUser) {
        // 参数校验
        ReturnT<String> validResult = validateDatasource(datasource, datasource.getId());
        if (validResult.getCode() != ReturnT.SUCCESS_CODE) {
            return validResult;
        }

        // 检查数据源是否存在
        Datasource existDatasource = datasourceDao.load(datasource.getId());
        if (existDatasource == null) {
            return new ReturnT<>(ReturnT.FAIL_CODE, "数据源不存在");
        }

        // 检查名称是否重复
        if (datasourceDao.checkNameExists(datasource.getName(), datasource.getId()) > 0) {
            return new ReturnT<>(ReturnT.FAIL_CODE, "数据源名称已存在");
        }

        // 密码处理
        if ("******".equals(datasource.getPassword())) {
            // 如果是脱敏密码，保持原密码不变
            datasource.setPassword(existDatasource.getPassword());
        } else if (StringUtils.hasText(datasource.getPassword())) {
            // 如果是新密码，进行加密
            datasource.setPassword(DatasourceConnectionUtil.encryptPassword(datasource.getPassword()));
        }

        datasource.setUpdateUser(loginUser);

        try {
            int result = datasourceDao.update(datasource);
            return result > 0 ? ReturnT.SUCCESS : new ReturnT<>(ReturnT.FAIL_CODE, "更新失败");
        } catch (Exception e) {
            log.error("更新数据源失败", e);
            return new ReturnT<>(ReturnT.FAIL_CODE, "更新失败：" + e.getMessage());
        }
    }

    @Override
    public ReturnT<String> delete(Long id, String loginUser) {
        if (id == null) {
            return new ReturnT<>(ReturnT.FAIL_CODE, "ID不能为空");
        }

        // 检查数据源是否存在
        Datasource datasource = datasourceDao.load(id);
        if (datasource == null) {
            return new ReturnT<>(ReturnT.FAIL_CODE, "数据源不存在");
        }

        try {
            int result = datasourceDao.delete(id, loginUser);
            return result > 0 ? ReturnT.SUCCESS : new ReturnT<>(ReturnT.FAIL_CODE, "删除失败");
        } catch (Exception e) {
            log.error("删除数据源失败", e);
            return new ReturnT<>(ReturnT.FAIL_CODE, "删除失败：" + e.getMessage());
        }
    }

    @Override
    public ReturnT<String> testConnection(Long id, String loginUser) {
        if (id == null) {
            return new ReturnT<>(ReturnT.FAIL_CODE, "ID不能为空");
        }

        Datasource datasource = datasourceDao.load(id);
        if (datasource == null) {
            return new ReturnT<>(ReturnT.FAIL_CODE, "数据源不存在");
        }

        // 解密密码
        String decryptedPassword = DatasourceConnectionUtil.decryptPassword(datasource.getPassword());
        datasource.setPassword(decryptedPassword);

        // 测试连接
        ReturnT<String> testResult = testConnectionOnly(datasource);

        // 更新测试状态
        Integer testStatus = testResult.getCode() == ReturnT.SUCCESS_CODE ? 
            Datasource.TestStatus.SUCCESS.getCode() : Datasource.TestStatus.FAILED.getCode();
        
        datasourceDao.updateTestStatus(id, testStatus, testResult.getMsg(), loginUser);

        return testResult;
    }

    @Override
    public ReturnT<String> testConnectionOnly(Datasource datasource) {
        try {
            Connection connection = DatasourceConnectionUtil.getConnection(datasource);
            if (connection != null) {
                connection.close();
                return new ReturnT<>(ReturnT.SUCCESS_CODE, "连接成功");
            } else {
                return new ReturnT<>(ReturnT.FAIL_CODE, "连接失败");
            }
        } catch (Exception e) {
            log.error("测试数据源连接失败", e);
            return new ReturnT<>(ReturnT.FAIL_CODE, "连接失败：" + e.getMessage());
        }
    }

    @Override
    public List<Datasource> findByType(String type) {
        List<Datasource> list = datasourceDao.findByType(type);
        // 脱敏处理
        for (Datasource datasource : list) {
            if (StringUtils.hasText(datasource.getPassword())) {
                datasource.setPassword("******");
            }
        }
        return list;
    }

    @Override
    public List<Map<String, Object>> getDatasourceTypes() {
        List<Map<String, Object>> types = new ArrayList<>();
        for (Datasource.DatasourceType type : Datasource.DatasourceType.values()) {
            Map<String, Object> typeMap = new HashMap<>();
            typeMap.put("code", type.getCode());
            typeMap.put("name", type.getName());
            types.add(typeMap);
        }
        return types;
    }

    /**
     * 校验数据源参数
     */
    private ReturnT<String> validateDatasource(Datasource datasource, Long excludeId) {
        if (datasource == null) {
            return new ReturnT<>(ReturnT.FAIL_CODE, "数据源信息不能为空");
        }
        if (!StringUtils.hasText(datasource.getName())) {
            return new ReturnT<>(ReturnT.FAIL_CODE, "数据源名称不能为空");
        }
        if (!StringUtils.hasText(datasource.getType())) {
            return new ReturnT<>(ReturnT.FAIL_CODE, "数据源类型不能为空");
        }
        if (!StringUtils.hasText(datasource.getHost())) {
            return new ReturnT<>(ReturnT.FAIL_CODE, "主机地址不能为空");
        }
        if (datasource.getPort() == null || datasource.getPort() <= 0) {
            return new ReturnT<>(ReturnT.FAIL_CODE, "端口号不能为空且必须大于0");
        }
        if (!StringUtils.hasText(datasource.getDatabaseName())) {
            return new ReturnT<>(ReturnT.FAIL_CODE, "数据库名称不能为空");
        }
        if (!StringUtils.hasText(datasource.getUsername())) {
            return new ReturnT<>(ReturnT.FAIL_CODE, "用户名不能为空");
        }
        if (!StringUtils.hasText(datasource.getPassword()) || "******".equals(datasource.getPassword())) {
            if (excludeId == null) { // 新增时密码必填
                return new ReturnT<>(ReturnT.FAIL_CODE, "密码不能为空");
            }
        }

        // 验证数据源类型
        Datasource.DatasourceType type = Datasource.DatasourceType.getByCode(datasource.getType());
        if (type == null) {
            return new ReturnT<>(ReturnT.FAIL_CODE, "不支持的数据源类型");
        }

        return ReturnT.SUCCESS;
    }

    @Override
    public ReturnT<List<String>> getDatabases(Long datasourceId) {
        try {
            Datasource datasource = datasourceDao.load(datasourceId);
            if (datasource == null) {
                return new ReturnT<>(ReturnT.FAIL_CODE, "数据源不存在");
            }

            // 解密密码
            String decryptedPassword = DatasourceConnectionUtil.decryptPassword(datasource.getPassword());
            datasource.setPassword(decryptedPassword);

            Connection connection = DatasourceConnectionUtil.getConnection(datasource);
            if (connection == null) {
                return new ReturnT<>(ReturnT.FAIL_CODE, "无法连接到数据源");
            }

            List<String> databases = new ArrayList<>();
            DatabaseMetaData metaData = connection.getMetaData();

            // 根据数据源类型获取数据库列表
            if ("MYSQL".equals(datasource.getType()) || "DORIS".equals(datasource.getType())) {
                ResultSet rs = metaData.getCatalogs();
                while (rs.next()) {
                    databases.add(rs.getString("TABLE_CAT"));
                }
                rs.close();
            } else if ("POSTGRESQL".equals(datasource.getType())) {
                ResultSet rs = metaData.getSchemas();
                while (rs.next()) {
                    databases.add(rs.getString("TABLE_SCHEM"));
                }
                rs.close();
            } else if ("ORACLE".equals(datasource.getType())) {
                Statement stmt = connection.createStatement();
                ResultSet rs = stmt.executeQuery("SELECT USERNAME FROM ALL_USERS ORDER BY USERNAME");
                while (rs.next()) {
                    databases.add(rs.getString("USERNAME"));
                }
                rs.close();
                stmt.close();
            }

            connection.close();
            return new ReturnT<>(databases);
        } catch (Exception e) {
            log.error("获取数据库列表失败", e);
            return new ReturnT<>(ReturnT.FAIL_CODE, "获取数据库列表失败：" + e.getMessage());
        }
    }

    @Override
    public ReturnT<List<String>> getTables(Long datasourceId, String database) {
        try {
            Datasource datasource = datasourceDao.load(datasourceId);
            if (datasource == null) {
                return new ReturnT<>(ReturnT.FAIL_CODE, "数据源不存在");
            }

            // 解密密码
            String decryptedPassword = DatasourceConnectionUtil.decryptPassword(datasource.getPassword());
            datasource.setPassword(decryptedPassword);

            Connection connection = DatasourceConnectionUtil.getConnection(datasource);
            if (connection == null) {
                return new ReturnT<>(ReturnT.FAIL_CODE, "无法连接到数据源");
            }

            List<String> tables = new ArrayList<>();
            DatabaseMetaData metaData = connection.getMetaData();

            ResultSet rs = metaData.getTables(database, database, "%", new String[]{"TABLE"});
            while (rs.next()) {
                tables.add(rs.getString("TABLE_NAME"));
            }
            rs.close();
            connection.close();

            return new ReturnT<>(tables);
        } catch (Exception e) {
            log.error("获取表列表失败", e);
            return new ReturnT<>(ReturnT.FAIL_CODE, "获取表列表失败：" + e.getMessage());
        }
    }

    @Override
    public ReturnT<List<Map<String, Object>>> getColumns(Long datasourceId, String database, String table) {
        try {
            Datasource datasource = datasourceDao.load(datasourceId);
            if (datasource == null) {
                return new ReturnT<>(ReturnT.FAIL_CODE, "数据源不存在");
            }

            // 解密密码
            String decryptedPassword = DatasourceConnectionUtil.decryptPassword(datasource.getPassword());
            datasource.setPassword(decryptedPassword);

            Connection connection = DatasourceConnectionUtil.getConnection(datasource);
            if (connection == null) {
                return new ReturnT<>(ReturnT.FAIL_CODE, "无法连接到数据源");
            }

            List<Map<String, Object>> columns = new ArrayList<>();
            DatabaseMetaData metaData = connection.getMetaData();

            ResultSet rs = metaData.getColumns(database, database, table, "%");
            while (rs.next()) {
                Map<String, Object> column = new HashMap<>();
                column.put("columnName", rs.getString("COLUMN_NAME"));
                column.put("dataType", rs.getString("TYPE_NAME"));
                column.put("columnSize", rs.getInt("COLUMN_SIZE"));
                column.put("nullable", rs.getInt("NULLABLE") == DatabaseMetaData.columnNullable);
                column.put("remarks", rs.getString("REMARKS"));
                columns.add(column);
            }
            rs.close();
            connection.close();

            return new ReturnT<>(columns);
        } catch (Exception e) {
            log.error("获取字段列表失败", e);
            return new ReturnT<>(ReturnT.FAIL_CODE, "获取字段列表失败：" + e.getMessage());
        }
    }

    @Override
    public ReturnT<Map<String, Object>> executeQuery(Long datasourceId, String sql, int limit) {
        try {
            Datasource datasource = datasourceDao.load(datasourceId);
            if (datasource == null) {
                return new ReturnT<>(ReturnT.FAIL_CODE, "数据源不存在");
            }

            // 解密密码
            String decryptedPassword = DatasourceConnectionUtil.decryptPassword(datasource.getPassword());
            datasource.setPassword(decryptedPassword);

            Connection connection = DatasourceConnectionUtil.getConnection(datasource);
            if (connection == null) {
                return new ReturnT<>(ReturnT.FAIL_CODE, "无法连接到数据源");
            }

            // 限制查询结果数量
            String limitedSql = sql;
            if (limit > 0) {
                if ("MYSQL".equals(datasource.getType()) || "DORIS".equals(datasource.getType())) {
                    limitedSql += " LIMIT " + limit;
                } else if ("POSTGRESQL".equals(datasource.getType())) {
                    limitedSql += " LIMIT " + limit;
                } else if ("ORACLE".equals(datasource.getType())) {
                    limitedSql = "SELECT * FROM (" + sql + ") WHERE ROWNUM <= " + limit;
                }
            }

            Statement stmt = connection.createStatement();
            ResultSet rs = stmt.executeQuery(limitedSql);

            // 获取列信息
            java.sql.ResultSetMetaData metaData = rs.getMetaData();
            int columnCount = metaData.getColumnCount();

            List<String> columnNames = new ArrayList<>();
            for (int i = 1; i <= columnCount; i++) {
                columnNames.add(metaData.getColumnName(i));
            }

            // 获取数据
            List<List<Object>> rows = new ArrayList<>();
            while (rs.next()) {
                List<Object> row = new ArrayList<>();
                for (int i = 1; i <= columnCount; i++) {
                    row.add(rs.getObject(i));
                }
                rows.add(row);
            }

            rs.close();
            stmt.close();
            connection.close();

            Map<String, Object> result = new HashMap<>();
            result.put("columns", columnNames);
            result.put("rows", rows);
            result.put("total", rows.size());

            return new ReturnT<>(result);
        } catch (Exception e) {
            log.error("执行SQL查询失败", e);
            return new ReturnT<>(ReturnT.FAIL_CODE, "执行SQL查询失败：" + e.getMessage());
        }
    }

    @Override
    public Datasource loadWithPlainPassword(Long id) {
        if (id == null) {
            return null;
        }
        Datasource datasource = datasourceDao.load(id);
        if (datasource != null && StringUtils.hasText(datasource.getPassword())) {
            // 解密密码，返回明文密码
            String decryptedPassword = DatasourceConnectionUtil.decryptPassword(datasource.getPassword());
            datasource.setPassword(decryptedPassword);
        }
        return datasource;
    }
}
