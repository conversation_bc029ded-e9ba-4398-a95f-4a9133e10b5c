package com.sevb.admin.service;

import com.sevb.admin.core.model.Datasource;
import com.sevb.core.biz.model.ReturnT;

import java.util.List;
import java.util.Map;

/**
 * 数据源服务接口
 * <AUTHOR>
 */
public interface DatasourceService {

    /**
     * 分页查询数据源列表
     */
    Map<String, Object> pageList(int pageNum, int pageSize, String name, String type, Integer status);

    /**
     * 查询所有启用的数据源
     */
    List<Datasource> findAllEnabled();

    /**
     * 根据ID查询数据源
     */
    Datasource load(Long id);

    /**
     * 添加数据源
     */
    ReturnT<String> add(Datasource datasource, String loginUser);

    /**
     * 更新数据源
     */
    ReturnT<String> update(Datasource datasource, String loginUser);

    /**
     * 删除数据源
     */
    ReturnT<String> delete(Long id, String loginUser);

    /**
     * 测试数据源连接
     */
    ReturnT<String> testConnection(Long id, String loginUser);

    /**
     * 测试数据源连接（不保存结果）
     */
    ReturnT<String> testConnectionOnly(Datasource datasource);

    /**
     * 根据类型查询数据源列表
     */
    List<Datasource> findByType(String type);

    /**
     * 获取数据源类型列表
     */
    List<Map<String, Object>> getDatasourceTypes();

    /**
     * 获取数据库列表
     */
    ReturnT<List<String>> getDatabases(Long datasourceId);

    /**
     * 获取表列表
     */
    ReturnT<List<String>> getTables(Long datasourceId, String database);

    /**
     * 获取表字段列表
     */
    ReturnT<List<Map<String, Object>>> getColumns(Long datasourceId, String database, String table);

    /**
     * 执行SQL查询
     */
    ReturnT<Map<String, Object>> executeQuery(Long datasourceId, String sql, int limit);

    /**
     * 根据ID查询数据源（包含明文密码，仅供内部使用）
     */
    Datasource loadWithPlainPassword(Long id);
}
