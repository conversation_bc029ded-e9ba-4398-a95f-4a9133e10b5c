package com.sevb.admin.service.impl;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.sevb.admin.core.alarm.channel.NotificationChannelManager;
import com.sevb.admin.core.alarm.config.AlertConfig;
import com.sevb.admin.core.alarm.model.*;
import com.sevb.admin.core.model.AlertChannelConfig;
import com.sevb.admin.core.model.AlertSendRecord;
import com.sevb.admin.core.model.AlertTemplate;
import com.sevb.admin.service.*;
import com.sevb.core.biz.model.ReturnT;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.concurrent.CompletableFuture;

/**
 * 告警服务实现
 * <AUTHOR>
 */
@Service
public class AlertServiceImpl implements AlertService {

    private static final Logger logger = LoggerFactory.getLogger(AlertServiceImpl.class);

    @Autowired
    private AlertTemplateService alertTemplateService;

    @Autowired
    private AlertChannelService alertChannelService;

    @Autowired
    private AlertSendRecordService alertSendRecordService;

    @Autowired
    private NotificationChannelManager channelManager;

    @Autowired
    private AlertConfig alertConfig;

    private final ObjectMapper objectMapper = new ObjectMapper();

    @Override
    public String sendAlert(AlertRequest request) {
        if (request == null) {
            throw new IllegalArgumentException("告警请求不能为空");
        }

        if (!alertConfig.isEnabled()) {
            logger.warn("告警系统已禁用，忽略告警请求");
            return null;
        }

        // 生成请求ID
        String requestId = UUID.randomUUID().toString();
        logger.info("开始处理告警请求: requestId={}, templateCode={}, channelTypes={}", 
            requestId, request.getTemplateCode(), request.getChannelTypes());

        try {
            // 验证请求参数
            validateAlertRequest(request);

            // 异步处理告警发送
            processAlertAsync(requestId, request);

            return requestId;
        } catch (Exception e) {
            logger.error("处理告警请求失败: requestId={}", requestId, e);
            throw new RuntimeException("处理告警请求失败: " + e.getMessage(), e);
        }
    }

    @Override
    public List<String> sendAlertBatch(List<AlertRequest> requests) {
        if (requests == null || requests.isEmpty()) {
            throw new IllegalArgumentException("告警请求列表不能为空");
        }

        List<String> requestIds = new ArrayList<>();
        for (AlertRequest request : requests) {
            try {
                String requestId = sendAlert(request);
                if (requestId != null) {
                    requestIds.add(requestId);
                }
            } catch (Exception e) {
                logger.error("批量发送告警失败", e);
                // 继续处理其他请求
            }
        }

        return requestIds;
    }

    @Override
    public Map<String, Object> getAlertStatus(String requestId) {
        if (!StringUtils.hasText(requestId)) {
            throw new IllegalArgumentException("请求ID不能为空");
        }

        List<AlertSendRecord> records = alertSendRecordService.findByRequestId(requestId);
        
        Map<String, Object> status = new HashMap<>();
        status.put("requestId", requestId);
        status.put("totalCount", records.size());
        
        if (records.isEmpty()) {
            status.put("status", "NOT_FOUND");
            return status;
        }

        // 统计各状态数量
        Map<String, Integer> statusCount = new HashMap<>();
        for (AlertSendRecord record : records) {
            String sendStatus = record.getSendStatus();
            statusCount.put(sendStatus, statusCount.getOrDefault(sendStatus, 0) + 1);
        }

        status.put("statusCount", statusCount);
        status.put("records", records);

        // 判断整体状态
        if (statusCount.containsKey("PENDING") || statusCount.containsKey("RETRY")) {
            status.put("status", "PROCESSING");
        } else if (statusCount.containsKey("FAILED")) {
            status.put("status", "PARTIAL_FAILED");
        } else {
            status.put("status", "SUCCESS");
        }

        return status;
    }

    @Override
    public ReturnT<String> retryAlert(Long recordId) {
        if (recordId == null) {
            return new ReturnT<>(ReturnT.FAIL_CODE, "记录ID不能为空");
        }

        try {
            AlertSendRecord record = alertSendRecordService.load(recordId);
            if (record == null) {
                return new ReturnT<>(ReturnT.FAIL_CODE, "发送记录不存在");
            }

            if (!"FAILED".equals(record.getSendStatus())) {
                return new ReturnT<>(ReturnT.FAIL_CODE, "只能重试失败的记录");
            }

            // 重置重试信息
            Date nextRetryTime = new Date();
            alertSendRecordService.updateRetryInfo(recordId, 0, nextRetryTime, "RETRY");

            // 异步重试
            retryAlertAsync(record);

            return ReturnT.SUCCESS;
        } catch (Exception e) {
            logger.error("重试告警失败: recordId={}", recordId, e);
            return new ReturnT<>(ReturnT.FAIL_CODE, "重试失败: " + e.getMessage());
        }
    }

    @Override
    public ReturnT<String> cancelAlert(String requestId) {
        if (!StringUtils.hasText(requestId)) {
            return new ReturnT<>(ReturnT.FAIL_CODE, "请求ID不能为空");
        }

        try {
            List<AlertSendRecord> records = alertSendRecordService.findByRequestId(requestId);
            
            int cancelCount = 0;
            for (AlertSendRecord record : records) {
                if ("PENDING".equals(record.getSendStatus()) || "RETRY".equals(record.getSendStatus())) {
                    alertSendRecordService.updateSendStatus(record.getId(), "CANCELLED", null, "用户取消");
                    cancelCount++;
                }
            }

            return new ReturnT<>(ReturnT.SUCCESS_CODE, "取消了 " + cancelCount + " 条待发送记录");
        } catch (Exception e) {
            logger.error("取消告警失败: requestId={}", requestId, e);
            return new ReturnT<>(ReturnT.FAIL_CODE, "取消失败: " + e.getMessage());
        }
    }

    @Override
    public Map<String, Object> getAlertStats(int hours) {
        return alertSendRecordService.getRecentStats(hours);
    }

    @Override
    public int processRetryQueue() {
        try {
            List<AlertSendRecord> retryRecords = alertSendRecordService.findRetryRecords(100);
            
            for (AlertSendRecord record : retryRecords) {
                try {
                    retryAlertAsync(record);
                } catch (Exception e) {
                    logger.error("处理重试记录失败: recordId={}", record.getId(), e);
                }
            }

            return retryRecords.size();
        } catch (Exception e) {
            logger.error("处理重试队列失败", e);
            return 0;
        }
    }

    @Override
    public int cleanHistoryRecords(int days) {
        try {
            Calendar calendar = Calendar.getInstance();
            Date endTime = calendar.getTime();
            calendar.add(Calendar.DAY_OF_YEAR, -days);
            Date startTime = calendar.getTime();

            ReturnT<String> result = alertSendRecordService.deleteByTimeRange(startTime, endTime);
            if (result.getCode() == ReturnT.SUCCESS_CODE) {
                String message = result.getMsg();
                // 从消息中提取数字
                String[] parts = message.split(" ");
                for (String part : parts) {
                    try {
                        return Integer.parseInt(part);
                    } catch (NumberFormatException ignored) {
                    }
                }
            }
            return 0;
        } catch (Exception e) {
            logger.error("清理历史记录失败", e);
            return 0;
        }
    }

    @Override
    public ReturnT<String> testAlert(String templateCode, String channelType, 
                                    List<String> recipients, Map<String, Object> variables) {
        try {
            // 构建测试请求
            AlertRequest.Recipients testRecipients = AlertRequest.Recipients.builder()
                .recipients(recipients)
                .build();

            AlertRequest testRequest = AlertRequest.builder()
                .templateCode(templateCode)
                .channelTypes(Arrays.asList(channelType))
                .recipients(testRecipients)
                .variables(variables)
                .sourceSystem("TEST")
                .build();

            // 发送测试告警
            String requestId = sendAlert(testRequest);
            
            return new ReturnT<>(ReturnT.SUCCESS_CODE, "测试告警已发送，请求ID: " + requestId);
        } catch (Exception e) {
            logger.error("测试告警失败", e);
            return new ReturnT<>(ReturnT.FAIL_CODE, "测试失败: " + e.getMessage());
        }
    }

    /**
     * 验证告警请求
     */
    private void validateAlertRequest(AlertRequest request) {
        if (!StringUtils.hasText(request.getTemplateCode())) {
            throw new IllegalArgumentException("模板编码不能为空");
        }

        if (request.getChannelTypes() == null || request.getChannelTypes().isEmpty()) {
            throw new IllegalArgumentException("通道类型列表不能为空");
        }

        if (request.getRecipients() == null) {
            throw new IllegalArgumentException("接收人配置不能为空");
        }
    }

    /**
     * 异步处理告警
     */
    @Async("alertExecutor")
    public void processAlertAsync(String requestId, AlertRequest request) {
        for (String channelType : request.getChannelTypes()) {
            try {
                processChannelAlert(requestId, request, channelType);
            } catch (Exception e) {
                logger.error("处理通道告警失败: requestId={}, channelType={}", requestId, channelType, e);
            }
        }
    }

    /**
     * 异步重试告警
     */
    @Async("alertExecutor")
    public void retryAlertAsync(AlertSendRecord record) {
        try {
            // 重新构建请求
            AlertRequest request = rebuildAlertRequest(record);
            processChannelAlert(record.getRequestId(), request, record.getChannelType());
        } catch (Exception e) {
            logger.error("异步重试告警失败: recordId={}", record.getId(), e);
        }
    }

    /**
     * 处理单个通道的告警
     */
    private void processChannelAlert(String requestId, AlertRequest request, String channelType) {
        AlertSendRecord record = null;
        try {
            // 获取模板
            AlertTemplate template = alertTemplateService.findByCodeAndChannel(
                request.getTemplateCode(), channelType);
            if (template == null) {
                logger.error("模板不存在: templateCode={}, channelType={}",
                    request.getTemplateCode(), channelType);
                return;
            }

            // 获取通道配置
            AlertChannelConfig channelConfig = getChannelConfig(request, channelType);
            if (channelConfig == null) {
                logger.error("通道配置不存在: channelType={}", channelType);
                return;
            }

            // 渲染模板内容
            String content = alertTemplateService.renderTemplate(
                request.getTemplateCode(), channelType, request.getVariables());

            // 获取接收人列表
            List<String> recipients = getRecipients(request.getRecipients(), channelType);
            if (recipients.isEmpty()) {
                logger.warn("接收人列表为空: channelType={}", channelType);
                return;
            }

            // 创建发送记录
            record = createSendRecord(requestId, request, channelType,
                channelConfig.getConfigName(), recipients, content);
            alertSendRecordService.save(record);

            // 构建认证配置
            AuthConfig authConfig = buildAuthConfig(channelConfig);

            // 构建通知请求
            NotificationRequest notificationRequest = NotificationRequest.builder()
                .requestId(requestId)
                .channelType(channelType)
                .recipients(recipients)
                .title(extractTitle(content))
                .content(content)
                .authConfig(authConfig)
                .variables(request.getVariables())
                .sourceSystem(request.getSourceSystem())
                .businessId(request.getBusinessId())
                .priority(request.getPriority() != null ?
                    NotificationRequest.Priority.valueOf(request.getPriority().name()) :
                    NotificationRequest.Priority.NORMAL)
                .build();

            // 发送通知
            SendResult result = channelManager.sendNotification(notificationRequest);

            // 更新发送状态
            if (result.isSuccess()) {
                alertSendRecordService.updateSendStatus(record.getId(), "SUCCESS",
                    result.getResponseCode(), result.getMessage());
                logger.info("告警发送成功: requestId={}, channelType={}", requestId, channelType);
            } else {
                handleSendFailure(record, result);
            }

        } catch (Exception e) {
            logger.error("处理通道告警异常: requestId={}, channelType={}", requestId, channelType, e);
            if (record != null) {
                alertSendRecordService.updateSendStatus(record.getId(), "FAILED",
                    "EXCEPTION", "处理异常: " + e.getMessage());
            }
        }
    }

    /**
     * 重新构建告警请求
     */
    private AlertRequest rebuildAlertRequest(AlertSendRecord record) {
        try {
            // 解析接收人列表
            List<String> recipients = objectMapper.readValue(record.getRecipients(), List.class);
            AlertRequest.Recipients recipientsObj = AlertRequest.Recipients.builder()
                .recipients(recipients)
                .build();

            // 解析变量
            Map<String, Object> variables = new HashMap<>();
            if (StringUtils.hasText(record.getVariables())) {
                variables = objectMapper.readValue(record.getVariables(), Map.class);
            }

            return AlertRequest.builder()
                .templateCode(record.getTemplateCode())
                .channelTypes(Arrays.asList(record.getChannelType()))
                .recipients(recipientsObj)
                .variables(variables)
                .sourceSystem(record.getSourceSystem())
                .businessId(record.getBusinessId())
                .build();

        } catch (Exception e) {
            logger.error("重新构建告警请求失败: recordId={}", record.getId(), e);
            throw new RuntimeException("重新构建告警请求失败", e);
        }
    }

    /**
     * 获取通道配置
     */
    private AlertChannelConfig getChannelConfig(AlertRequest request, String channelType) {
        // 如果指定了配置名称，使用指定的配置
        if (request.getChannelConfigs() != null && request.getChannelConfigs().containsKey(channelType)) {
            String configName = request.getChannelConfigs().get(channelType);
            return alertChannelService.findByConfigName(configName);
        }

        // 否则使用默认配置
        return alertChannelService.findDefaultByChannelType(channelType);
    }

    /**
     * 获取接收人列表
     */
    private List<String> getRecipients(AlertRequest.Recipients recipients, String channelType) {
        List<String> result = new ArrayList<>();

        if (recipients == null) {
            return result;
        }

        // 优先使用通用接收人列表
        if (recipients.getRecipients() != null) {
            result.addAll(recipients.getRecipients());
        }

        // 根据通道类型添加特定接收人
        switch (channelType) {
            case "EMAIL":
                if (recipients.getEmails() != null) {
                    result.addAll(recipients.getEmails());
                }
                break;
            case "WECHAT":
                if (recipients.getWechatUserIds() != null) {
                    result.addAll(recipients.getWechatUserIds());
                }
                break;
            case "DINGTALK":
                if (recipients.getDingtalkUserIds() != null) {
                    result.addAll(recipients.getDingtalkUserIds());
                }
                break;
            case "FEISHU":
                if (recipients.getFeishuUserIds() != null) {
                    result.addAll(recipients.getFeishuUserIds());
                }
                break;
        }

        // 去重
        return new ArrayList<>(new LinkedHashSet<>(result));
    }

    /**
     * 创建发送记录
     */
    private AlertSendRecord createSendRecord(String requestId, AlertRequest request, String channelType,
                                           String configName, List<String> recipients, String content) {
        AlertSendRecord record = new AlertSendRecord();
        record.setRequestId(requestId);
        record.setTemplateCode(request.getTemplateCode());
        record.setChannelType(channelType);
        record.setConfigName(configName);
        record.setTitle(extractTitle(content));
        record.setContent(content);
        record.setSourceSystem(request.getSourceSystem());
        record.setBusinessId(request.getBusinessId());
        record.setSendTime(new Date());
        record.setSendStatus("PENDING");
        record.setRetryCount(0);
        record.setMaxRetryCount(alertConfig.getRetry().getMaxAttempts());

        try {
            record.setRecipients(objectMapper.writeValueAsString(recipients));
            if (request.getVariables() != null) {
                record.setVariables(objectMapper.writeValueAsString(request.getVariables()));
            }
        } catch (Exception e) {
            logger.error("序列化发送记录数据失败", e);
        }

        return record;
    }

    /**
     * 构建认证配置
     */
    private AuthConfig buildAuthConfig(AlertChannelConfig channelConfig) {
        AuthConfig authConfig = new AuthConfig();
        authConfig.setAuthType(channelConfig.getAuthType());

        try {
            // 解密认证配置
            String decryptedConfig = alertChannelService.decryptAuthConfig(channelConfig.getAuthConfig());
            Map<String, Object> authData = objectMapper.readValue(decryptedConfig, Map.class);
            authConfig.setAuthData(authData);
        } catch (Exception e) {
            logger.error("构建认证配置失败: configName={}", channelConfig.getConfigName(), e);
            throw new RuntimeException("构建认证配置失败", e);
        }

        return authConfig;
    }

    /**
     * 提取标题
     */
    private String extractTitle(String content) {
        try {
            Map<String, Object> contentMap = objectMapper.readValue(content, Map.class);
            Object subject = contentMap.get("subject");
            if (subject != null) {
                return subject.toString();
            }
        } catch (Exception ignored) {
        }

        // 如果无法提取标题，返回默认值
        return "告警通知";
    }

    /**
     * 处理发送失败
     */
    private void handleSendFailure(AlertSendRecord record, SendResult result) {
        try {
            int currentRetryCount = record.getRetryCount() + 1;

            if (currentRetryCount < record.getMaxRetryCount()) {
                // 计算下次重试时间
                long delay = calculateRetryDelay(currentRetryCount);
                Date nextRetryTime = new Date(System.currentTimeMillis() + delay);

                alertSendRecordService.updateRetryInfo(record.getId(), currentRetryCount,
                    nextRetryTime, "RETRY");

                logger.warn("告警发送失败，将重试: requestId={}, channelType={}, retryCount={}, nextRetryTime={}",
                    record.getRequestId(), record.getChannelType(), currentRetryCount, nextRetryTime);
            } else {
                alertSendRecordService.updateSendStatus(record.getId(), "FAILED",
                    result.getResponseCode(), result.getMessage());

                logger.error("告警发送失败，已达最大重试次数: requestId={}, channelType={}",
                    record.getRequestId(), record.getChannelType());
            }
        } catch (Exception e) {
            logger.error("处理发送失败异常", e);
        }
    }

    /**
     * 计算重试延迟时间
     */
    private long calculateRetryDelay(int retryCount) {
        long baseDelay = alertConfig.getRetry().getDelay();
        double multiplier = alertConfig.getRetry().getMultiplier();
        long maxDelay = alertConfig.getRetry().getMaxDelay();

        long delay = (long) (baseDelay * Math.pow(multiplier, retryCount - 1));
        return Math.min(delay, maxDelay);
    }
}
