package com.sevb.admin.service;

import com.sevb.admin.core.model.AlertChannelConfig;
import com.sevb.core.biz.model.ReturnT;

import java.util.List;
import java.util.Map;

/**
 * 告警通道配置服务接口
 * <AUTHOR>
 */
public interface AlertChannelService {

    /**
     * 分页查询通道配置列表
     */
    Map<String, Object> pageList(int start, int length, String configName, 
                                String channelType, Integer enabled);

    /**
     * 查询所有启用的通道配置
     */
    List<AlertChannelConfig> findAllEnabled();

    /**
     * 根据ID查询通道配置
     */
    AlertChannelConfig load(Long id);

    /**
     * 根据配置名称查询通道配置
     */
    AlertChannelConfig findByConfigName(String configName);

    /**
     * 根据通道类型查询配置列表
     */
    List<AlertChannelConfig> findByChannelType(String channelType);

    /**
     * 根据通道类型查询启用的配置列表
     */
    List<AlertChannelConfig> findEnabledByChannelType(String channelType);

    /**
     * 获取默认通道配置
     */
    AlertChannelConfig findDefaultByChannelType(String channelType);

    /**
     * 保存通道配置
     */
    ReturnT<String> save(AlertChannelConfig alertChannelConfig);

    /**
     * 更新通道配置
     */
    ReturnT<String> update(AlertChannelConfig alertChannelConfig);

    /**
     * 删除通道配置
     */
    ReturnT<String> delete(Long id, String updateUser);

    /**
     * 启用/禁用通道配置
     */
    ReturnT<String> updateEnabled(Long id, Integer enabled, String updateUser);

    /**
     * 批量删除通道配置
     */
    ReturnT<String> batchDelete(List<Long> ids, String updateUser);

    /**
     * 测试发送
     */
    ReturnT<String> testSend(Long id);

    /**
     * 测试发送（指定收件人）
     */
    ReturnT<String> testSend(Long id, List<String> recipients);

    /**
     * 加密认证配置
     */
    String encryptAuthConfig(String authConfig);

    /**
     * 解密认证配置
     */
    String decryptAuthConfig(String encryptedAuthConfig);

    /**
     * 验证认证配置格式
     */
    ReturnT<String> validateAuthConfig(String channelType, String authType, String authConfig);
}
