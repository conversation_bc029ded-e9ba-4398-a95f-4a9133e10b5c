package com.sevb.admin.service.impl;

import com.sevb.admin.core.model.NodeType;
import com.sevb.admin.dao.NodeTypeDao;
import com.sevb.admin.service.NodeTypeService;
import com.sevb.core.biz.model.ReturnT;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import jakarta.annotation.Resource;
import java.util.*;

/**
 * 节点类型服务实现类
 * <AUTHOR>
 */
@Slf4j
@Service
public class NodeTypeServiceImpl implements NodeTypeService {

    @Resource
    private NodeTypeDao nodeTypeDao;

    @Override
    public Map<String, Object> pageList(int pageNum, int pageSize, String typeName, String category, Integer status) {
        // 参数处理
        int offset = (pageNum - 1) * pageSize;

        // 查询数据
        List<NodeType> list = nodeTypeDao.pageList(offset, pageSize, typeName, category, status);
        int totalCount = nodeTypeDao.pageListCount(offset, pageSize, typeName, category, status);

        // 组装结果
        Map<String, Object> result = new HashMap<>();
        result.put("recordsTotal", totalCount);
        result.put("recordsFiltered", totalCount);
        result.put("data", list);

        return result;
    }

    @Override
    public List<NodeType> findAllEnabled() {
        return nodeTypeDao.findAllEnabled();
    }

    @Override
    public List<NodeType> findByCategory(String category) {
        return nodeTypeDao.findByCategory(category);
    }

    @Override
    public NodeType load(Long id) {
        if (id == null) {
            return null;
        }
        return nodeTypeDao.load(id);
    }

    @Override
    public NodeType findByTypeCode(String typeCode) {
        if (!StringUtils.hasText(typeCode)) {
            return null;
        }
        return nodeTypeDao.findByTypeCode(typeCode);
    }

    @Override
    public ReturnT<String> add(NodeType nodeType) {
        // 参数校验
        if (!StringUtils.hasText(nodeType.getTypeCode())) {
            return new ReturnT<>(ReturnT.FAIL_CODE, "节点类型编码不能为空");
        }
        if (!StringUtils.hasText(nodeType.getTypeName())) {
            return new ReturnT<>(ReturnT.FAIL_CODE, "节点类型名称不能为空");
        }
        if (!StringUtils.hasText(nodeType.getCategory())) {
            return new ReturnT<>(ReturnT.FAIL_CODE, "节点分类不能为空");
        }
        if (!StringUtils.hasText(nodeType.getExecutorClass())) {
            return new ReturnT<>(ReturnT.FAIL_CODE, "执行器类名不能为空");
        }

        // 校验分类是否有效
        NodeType.Category categoryEnum = NodeType.Category.getByCode(nodeType.getCategory());
        if (categoryEnum == null) {
            return new ReturnT<>(ReturnT.FAIL_CODE, "无效的节点分类");
        }

        // 检查编码是否重复
        if (nodeTypeDao.checkTypeCodeExists(nodeType.getTypeCode(), null) > 0) {
            return new ReturnT<>(ReturnT.FAIL_CODE, "节点类型编码已存在");
        }

        // 检查名称是否重复
        if (nodeTypeDao.checkTypeNameExists(nodeType.getTypeName(), null) > 0) {
            return new ReturnT<>(ReturnT.FAIL_CODE, "节点类型名称已存在");
        }

        // 设置默认值
        if (nodeType.getSortOrder() == null) {
            nodeType.setSortOrder(0);
        }
        if (nodeType.getStatus() == null) {
            nodeType.setStatus(NodeType.Status.ENABLED.getCode());
        }
        if (!StringUtils.hasText(nodeType.getColor())) {
            nodeType.setColor("#1890ff");
        }

        // 保存
        int result = nodeTypeDao.save(nodeType);
        return result > 0 ? ReturnT.SUCCESS : new ReturnT<>(ReturnT.FAIL_CODE, "添加失败");
    }

    @Override
    public ReturnT<String> update(NodeType nodeType) {
        // 参数校验
        if (nodeType.getId() == null) {
            return new ReturnT<>(ReturnT.FAIL_CODE, "节点类型ID不能为空");
        }
        if (!StringUtils.hasText(nodeType.getTypeCode())) {
            return new ReturnT<>(ReturnT.FAIL_CODE, "节点类型编码不能为空");
        }
        if (!StringUtils.hasText(nodeType.getTypeName())) {
            return new ReturnT<>(ReturnT.FAIL_CODE, "节点类型名称不能为空");
        }
        if (!StringUtils.hasText(nodeType.getCategory())) {
            return new ReturnT<>(ReturnT.FAIL_CODE, "节点分类不能为空");
        }
        if (!StringUtils.hasText(nodeType.getExecutorClass())) {
            return new ReturnT<>(ReturnT.FAIL_CODE, "执行器类名不能为空");
        }

        // 检查记录是否存在
        NodeType existNodeType = nodeTypeDao.load(nodeType.getId());
        if (existNodeType == null) {
            return new ReturnT<>(ReturnT.FAIL_CODE, "节点类型不存在");
        }

        // 校验分类是否有效
        NodeType.Category categoryEnum = NodeType.Category.getByCode(nodeType.getCategory());
        if (categoryEnum == null) {
            return new ReturnT<>(ReturnT.FAIL_CODE, "无效的节点分类");
        }

        // 检查编码是否重复
        if (nodeTypeDao.checkTypeCodeExists(nodeType.getTypeCode(), nodeType.getId()) > 0) {
            return new ReturnT<>(ReturnT.FAIL_CODE, "节点类型编码已存在");
        }

        // 检查名称是否重复
        if (nodeTypeDao.checkTypeNameExists(nodeType.getTypeName(), nodeType.getId()) > 0) {
            return new ReturnT<>(ReturnT.FAIL_CODE, "节点类型名称已存在");
        }

        // 设置默认值
        if (nodeType.getSortOrder() == null) {
            nodeType.setSortOrder(0);
        }
        if (nodeType.getStatus() == null) {
            nodeType.setStatus(NodeType.Status.ENABLED.getCode());
        }
        if (!StringUtils.hasText(nodeType.getColor())) {
            nodeType.setColor("#1890ff");
        }

        // 更新
        int result = nodeTypeDao.update(nodeType);
        return result > 0 ? ReturnT.SUCCESS : new ReturnT<>(ReturnT.FAIL_CODE, "更新失败");
    }

    @Override
    public ReturnT<String> delete(Long id) {
        if (id == null) {
            return new ReturnT<>(ReturnT.FAIL_CODE, "节点类型ID不能为空");
        }

        // 检查记录是否存在
        NodeType nodeType = nodeTypeDao.load(id);
        if (nodeType == null) {
            return new ReturnT<>(ReturnT.FAIL_CODE, "节点类型不存在");
        }

        // TODO: 检查是否有工作流节点在使用此类型
        // 这里需要检查workflow_node表中是否有使用此node_type的记录

        // 删除
        int result = nodeTypeDao.delete(id);
        return result > 0 ? ReturnT.SUCCESS : new ReturnT<>(ReturnT.FAIL_CODE, "删除失败");
    }

    @Override
    public ReturnT<String> updateStatus(Long id, Integer status) {
        if (id == null) {
            return new ReturnT<>(ReturnT.FAIL_CODE, "节点类型ID不能为空");
        }
        if (status == null) {
            return new ReturnT<>(ReturnT.FAIL_CODE, "状态不能为空");
        }

        // 校验状态值
        NodeType.Status statusEnum = NodeType.Status.getByCode(status);
        if (statusEnum == null) {
            return new ReturnT<>(ReturnT.FAIL_CODE, "无效的状态值");
        }

        // 检查记录是否存在
        NodeType nodeType = nodeTypeDao.load(id);
        if (nodeType == null) {
            return new ReturnT<>(ReturnT.FAIL_CODE, "节点类型不存在");
        }

        // 更新状态
        nodeType.setStatus(status);
        int result = nodeTypeDao.update(nodeType);
        return result > 0 ? ReturnT.SUCCESS : new ReturnT<>(ReturnT.FAIL_CODE, "状态更新失败");
    }

    @Override
    public List<Map<String, Object>> getCategoryList() {
        List<Map<String, Object>> categoryList = new ArrayList<>();
        for (NodeType.Category category : NodeType.Category.values()) {
            Map<String, Object> categoryMap = new HashMap<>();
            categoryMap.put("code", category.getCode());
            categoryMap.put("name", category.getName());
            categoryList.add(categoryMap);
        }
        return categoryList;
    }
}
