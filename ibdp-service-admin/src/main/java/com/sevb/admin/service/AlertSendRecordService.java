package com.sevb.admin.service;

import com.sevb.admin.core.model.AlertSendRecord;
import com.sevb.core.biz.model.ReturnT;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 告警发送记录服务接口
 * <AUTHOR>
 */
public interface AlertSendRecordService {

    /**
     * 分页查询发送记录列表
     */
    Map<String, Object> pageList(int start, int length, String requestId, String templateCode,
                                String channelType, String sendStatus, String sourceSystem,
                                String businessId, Date startTime, Date endTime);

    /**
     * 根据ID查询发送记录
     */
    AlertSendRecord load(Long id);

    /**
     * 根据请求ID查询发送记录列表
     */
    List<AlertSendRecord> findByRequestId(String requestId);

    /**
     * 根据业务ID查询发送记录列表
     */
    List<AlertSendRecord> findByBusinessId(String businessId);

    /**
     * 查询需要重试的记录
     */
    List<AlertSendRecord> findRetryRecords(int limit);

    /**
     * 查询失败的记录
     */
    List<AlertSendRecord> findFailedRecords(Date startTime, Date endTime);

    /**
     * 保存发送记录
     */
    ReturnT<String> save(AlertSendRecord alertSendRecord);

    /**
     * 更新发送记录
     */
    ReturnT<String> update(AlertSendRecord alertSendRecord);

    /**
     * 更新发送状态
     */
    ReturnT<String> updateSendStatus(Long id, String sendStatus, String responseCode, String responseMessage);

    /**
     * 更新重试信息
     */
    ReturnT<String> updateRetryInfo(Long id, Integer retryCount, Date nextRetryTime, String sendStatus);

    /**
     * 批量删除发送记录
     */
    ReturnT<String> batchDelete(List<Long> ids);

    /**
     * 根据时间范围删除记录（清理历史数据）
     */
    ReturnT<String> deleteByTimeRange(Date startTime, Date endTime);

    /**
     * 统计发送记录数量
     */
    int countByStatus(String sendStatus, Date startTime, Date endTime);

    /**
     * 统计各状态的记录数量
     */
    List<Map<String, Object>> countGroupByStatus(Date startTime, Date endTime);

    /**
     * 统计各通道类型的记录数量
     */
    List<Map<String, Object>> countGroupByChannelType(Date startTime, Date endTime);

    /**
     * 获取发送成功率统计
     */
    Map<String, Object> getSuccessRateStats(Date startTime, Date endTime);

    /**
     * 获取最近发送记录统计
     */
    Map<String, Object> getRecentStats(int hours);
}
