package com.sevb.admin.service.orchestration;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.sevb.admin.core.dto.NodeExecutionParam;
import com.sevb.admin.core.model.JobInfo;
import com.sevb.admin.core.model.WorkflowNode;
import com.sevb.admin.core.route.ExecutorRouteStrategyEnum;
import com.sevb.admin.core.thread.JobTriggerPoolHelper;
import com.sevb.admin.core.trigger.TriggerTypeEnum;
import com.sevb.admin.dao.JobInfoDao;
import com.sevb.admin.dao.WorkflowNodeDao;
import com.sevb.core.biz.model.ReturnT;
import com.sevb.core.enums.ExecutorBlockStrategyEnum;
import com.sevb.core.glue.GlueTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;

/**
 * 节点任务执行服务
 * 负责为工作流节点创建和管理XXL-JOB任务
 * 
 * <AUTHOR>
 */
@Service
@Slf4j
public class NodeTaskExecutionService {

    @Autowired
    private JobInfoDao jobInfoDao;

    @Autowired
    private WorkflowNodeDao workflowNodeDao;

    /**
     * JSON对象映射器
     */
    private final ObjectMapper objectMapper = new ObjectMapper();

    /**
     * 节点执行器组ID（调度中心编排专用）
     */
    private static final int CENTRAL_NODE_EXECUTOR_GROUP_ID = 2;
    
    /**
     * 获取或创建节点执行任务
     */
    public Integer getOrCreateNodeTask(WorkflowNode node) {
        try {
            // 如果节点已经有关联的JobId，直接返回
            if (node.getJobId() != null && node.getJobId() > 0) {
                JobInfo existingJob = jobInfoDao.loadById(node.getJobId().intValue());
                if (existingJob != null) {
                    log.info("使用现有节点任务: nodeCode={}, jobId={}", node.getNodeCode(), node.getJobId());
                    return node.getJobId().intValue();
                }
            }
            
            // 动态创建节点执行任务
            Integer jobId = createNodeTask(node);
            
            // 更新节点的jobId
            workflowNodeDao.updateJobId(node.getId(), Long.valueOf(jobId));
            node.setJobId(Long.valueOf(jobId));
            log.info("节点任务创建成功: nodeCode={}, jobId={}", node.getNodeCode(), jobId);

            return jobId;
            
        } catch (Exception e) {
            log.error("获取或创建节点任务失败: nodeCode={}", node.getNodeCode(), e);
            throw new RuntimeException("获取或创建节点任务失败: " + e.getMessage(), e);
        }
    }
    
    /**
     * 创建节点执行任务
     */
    private Integer createNodeTask(WorkflowNode node) {
        try {
            JobInfo nodeJob = new JobInfo();
            
            // 基本信息
            nodeJob.setJobGroup(CENTRAL_NODE_EXECUTOR_GROUP_ID);
            nodeJob.setJobDesc("调度中心节点任务: " + node.getNodeName());
            nodeJob.setExecutorHandler("centralNodeExecutor"); // 统一的节点执行Handler
            
            // 调度配置
            nodeJob.setScheduleType("NONE"); // 不使用定时调度
            nodeJob.setScheduleConf("");
            
            // 执行配置
            nodeJob.setExecutorRouteStrategy(selectRouteStrategy(node));
            nodeJob.setExecutorBlockStrategy(ExecutorBlockStrategyEnum.SERIAL_EXECUTION.name());
            nodeJob.setExecutorTimeout(node.getTimeout() != null ? node.getTimeout().intValue() : 300);
            nodeJob.setExecutorFailRetryCount(node.getRetryTimes() != null ? node.getRetryTimes() : 0);
            
            // 代码配置
            nodeJob.setGlueType(GlueTypeEnum.BEAN.name());
            nodeJob.setGlueSource("");
            nodeJob.setGlueRemark("调度中心编排节点任务");
            nodeJob.setGlueUpdatetime(new Date());
            
            // 状态配置
            nodeJob.setTriggerStatus(0); // 暂停状态，只用于手动触发
            nodeJob.setTriggerLastTime(0L);
            nodeJob.setTriggerNextTime(0L);
            
            // 关联信息
            nodeJob.setWorkflowId(node.getWorkflowId());
            nodeJob.setNodeCode(node.getNodeCode());
            nodeJob.setOrchestrationMode("CENTRAL");
            
            // 时间信息
            nodeJob.setAddTime(new Date());
            nodeJob.setUpdateTime(new Date());
            nodeJob.setAuthor("central-orchestrator");
            
            // 保存任务
            int result = jobInfoDao.save(nodeJob);
            if (result > 0) {
                log.info("节点任务创建成功: nodeCode={}, jobId={}", node.getNodeCode(), nodeJob.getId());
                return nodeJob.getId();
            } else {
                throw new RuntimeException("保存节点任务失败");
            }
            
        } catch (Exception e) {
            log.error("创建节点任务失败: nodeCode={}", node.getNodeCode(), e);
            throw new RuntimeException("创建节点任务失败: " + e.getMessage(), e);
        }
    }
    
    /**
     * 选择路由策略
     */
    private String selectRouteStrategy(WorkflowNode node) {
        // 根据节点类型选择合适的路由策略
        if (node.getNodeType() != null) {
            switch (node.getNodeType().toUpperCase()) {
                case "SQL":
                    // SQL节点优先选择有数据库连接的执行器
                    return ExecutorRouteStrategyEnum.ROUND.name();
                case "PYTHON":
                    // Python节点优先选择有Python环境的执行器
                    return ExecutorRouteStrategyEnum.ROUND.name();
                case "SHELL":
                    // Shell节点使用轮询策略
                    return ExecutorRouteStrategyEnum.ROUND.name();
                case "DATAX":
                    // DataX节点使用最少使用策略
                    return ExecutorRouteStrategyEnum.RANDOM.name();
                default:
                    return ExecutorRouteStrategyEnum.ROUND.name();
            }
        }
        return ExecutorRouteStrategyEnum.ROUND.name();
    }
    
    /**
     * 触发节点执行
     */
    public void executeNodeTask(Integer jobId, NodeExecutionParam param) {
        try {
            String paramJson = objectMapper.writeValueAsString(param);

            log.info("触发节点任务执行: jobId={}, nodeCode={}, executionId={}",
                    jobId, param.getNodeCode(), param.getExecutionId());

            // 使用XXL-JOB的触发机制
            JobTriggerPoolHelper.trigger(
                jobId,                           // 任务ID
                TriggerTypeEnum.MANUAL,          // 手动触发
                -1,                              // 使用任务配置的重试次数
                null,                            // 不使用分片
                paramJson,                       // 节点执行参数
                null                             // 使用默认执行器地址
            );

            log.info("节点任务触发成功: jobId={}, nodeCode={}, executionId={}",
                    jobId, param.getNodeCode(), param.getExecutionId());

        } catch (Exception e) {
            log.error("触发节点任务失败: jobId={}, nodeCode={}", jobId, param.getNodeCode(), e);
            throw new RuntimeException("触发节点任务失败: " + e.getMessage(), e);
        }
    }
    
    /**
     * 删除节点任务
     */
    public ReturnT<String> deleteNodeTask(Integer jobId) {
        try {
            if (jobId == null || jobId <= 0) {
                return new ReturnT<>(ReturnT.FAIL_CODE, "任务ID无效");
            }
            
            JobInfo jobInfo = jobInfoDao.loadById(jobId);
            if (jobInfo == null) {
                return new ReturnT<>(ReturnT.FAIL_CODE, "任务不存在");
            }
            
            // 检查任务状态
            if (jobInfo.getTriggerStatus() == 1) {
                return new ReturnT<>(ReturnT.FAIL_CODE, "任务正在运行中，无法删除");
            }
            
            // 删除任务
            int result = jobInfoDao.delete(jobId);
            if (result > 0) {
                log.info("节点任务删除成功: jobId={}", jobId);
                return new ReturnT<>("删除成功");
            } else {
                return new ReturnT<>(ReturnT.FAIL_CODE, "删除失败");
            }
            
        } catch (Exception e) {
            log.error("删除节点任务失败: jobId={}", jobId, e);
            return new ReturnT<>(ReturnT.FAIL_CODE, "删除失败: " + e.getMessage());
        }
    }
    
    /**
     * 更新节点任务配置
     */
    public ReturnT<String> updateNodeTask(WorkflowNode node) {
        try {
            if (node.getJobId() == null || node.getJobId() <= 0) {
                return new ReturnT<>(ReturnT.FAIL_CODE, "节点未关联任务");
            }
            
            JobInfo jobInfo = jobInfoDao.loadById(node.getJobId().intValue());
            if (jobInfo == null) {
                return new ReturnT<>(ReturnT.FAIL_CODE, "关联的任务不存在");
            }
            
            // 更新任务配置
            jobInfo.setJobDesc("调度中心节点任务: " + node.getNodeName());
            jobInfo.setExecutorRouteStrategy(selectRouteStrategy(node));
            jobInfo.setExecutorTimeout(node.getTimeout() != null ? node.getTimeout().intValue() : 300);
            jobInfo.setExecutorFailRetryCount(node.getRetryTimes() != null ? node.getRetryTimes() : 0);
            jobInfo.setUpdateTime(new Date());
            
            int result = jobInfoDao.update(jobInfo);
            if (result > 0) {
                log.info("节点任务更新成功: nodeCode={}, jobId={}", node.getNodeCode(), node.getJobId());
                return new ReturnT<>("更新成功");
            } else {
                return new ReturnT<>(ReturnT.FAIL_CODE, "更新失败");
            }
            
        } catch (Exception e) {
            log.error("更新节点任务失败: nodeCode={}", node.getNodeCode(), e);
            return new ReturnT<>(ReturnT.FAIL_CODE, "更新失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取节点执行器组ID
     */
    public int getNodeExecutorGroupId() {
        return CENTRAL_NODE_EXECUTOR_GROUP_ID;
    }
}
