package com.sevb.admin.service;

import com.sevb.admin.core.model.WorkflowNodeRelation;
import com.sevb.admin.dao.WorkflowNodeRelationDao;
import com.sevb.core.biz.model.ReturnT;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import jakarta.annotation.Resource;
import java.util.List;

/**
 * 工作流节点关系服务层
 */
@Slf4j
@Service
public class WorkflowNodeRelationService {
    
    @Resource
    private WorkflowNodeRelationDao workflowNodeRelationDao;
    
    /**
     * 根据工作流ID查询所有节点关系
     */
    public ReturnT<List<WorkflowNodeRelation>> listByWorkflow(Long workflowId) {
        try {
            if (workflowId == null) {
                return new ReturnT<>(ReturnT.FAIL_CODE, "工作流ID不能为空");
            }
            
            List<WorkflowNodeRelation> relations = workflowNodeRelationDao.listByWorkflow(workflowId);
            return new ReturnT<>(relations);
        } catch (Exception e) {
            log.error("查询工作流节点关系失败", e);
            return new ReturnT<>(ReturnT.FAIL_CODE, "查询失败：" + e.getMessage());
        }
    }
    
    /**
     * 根据ID获取关系详情
     */
    public ReturnT<WorkflowNodeRelation> load(Long id) {
        try {
            if (id == null) {
                return new ReturnT<>(ReturnT.FAIL_CODE, "关系ID不能为空");
            }
            
            WorkflowNodeRelation relation = workflowNodeRelationDao.load(id);
            if (relation == null) {
                return new ReturnT<>(ReturnT.FAIL_CODE, "关系不存在");
            }
            
            return new ReturnT<>(relation);
        } catch (Exception e) {
            log.error("查询关系详情失败", e);
            return new ReturnT<>(ReturnT.FAIL_CODE, "查询失败：" + e.getMessage());
        }
    }
    
    /**
     * 根据前置节点查询关系
     */
    public ReturnT<List<WorkflowNodeRelation>> listByPreNode(Long workflowId, String preNodeCode) {
        try {
            if (workflowId == null || !StringUtils.hasText(preNodeCode)) {
                return new ReturnT<>(ReturnT.FAIL_CODE, "参数不能为空");
            }
            
            List<WorkflowNodeRelation> relations = workflowNodeRelationDao.listByPreNode(workflowId, preNodeCode);
            return new ReturnT<>(relations);
        } catch (Exception e) {
            log.error("根据前置节点查询关系失败", e);
            return new ReturnT<>(ReturnT.FAIL_CODE, "查询失败：" + e.getMessage());
        }
    }
    
    /**
     * 根据后置节点查询关系
     */
    public ReturnT<List<WorkflowNodeRelation>> listByPostNode(Long workflowId, String postNodeCode) {
        try {
            if (workflowId == null || !StringUtils.hasText(postNodeCode)) {
                return new ReturnT<>(ReturnT.FAIL_CODE, "参数不能为空");
            }
            
            List<WorkflowNodeRelation> relations = workflowNodeRelationDao.listByPostNode(workflowId, postNodeCode);
            return new ReturnT<>(relations);
        } catch (Exception e) {
            log.error("根据后置节点查询关系失败", e);
            return new ReturnT<>(ReturnT.FAIL_CODE, "查询失败：" + e.getMessage());
        }
    }
    
    /**
     * 添加工作流节点关系
     */
    @Transactional(rollbackFor = Exception.class)
    public ReturnT<String> add(WorkflowNodeRelation relation, String loginUser) {
        try {
            // 参数验证
            ReturnT<String> validResult = validateRelation(relation);
            if (validResult.getCode() != ReturnT.SUCCESS_CODE) {
                return validResult;
            }
            
            // 检查关系是否已存在
            if (workflowNodeRelationDao.checkRelationExists(relation.getWorkflowId(), 
                    relation.getPreNodeCode(), relation.getPostNodeCode()) > 0) {
                return new ReturnT<>(ReturnT.FAIL_CODE, "关系已存在");
            }
            
            // 检查循环依赖
            if (workflowNodeRelationDao.checkCircularDependency(relation.getWorkflowId(), 
                    relation.getPreNodeCode(), relation.getPostNodeCode()) > 0) {
                return new ReturnT<>(ReturnT.FAIL_CODE, "存在循环依赖，无法创建关系");
            }
            
            // 设置默认值
            setDefaultValues(relation, loginUser);
            
            // 保存关系
            int result = workflowNodeRelationDao.save(relation);
            if (result > 0) {
                log.info("添加工作流节点关系成功，ID：{}", relation.getId());
                return ReturnT.SUCCESS;
            } else {
                return new ReturnT<>(ReturnT.FAIL_CODE, "添加失败");
            }
        } catch (Exception e) {
            log.error("添加工作流节点关系失败", e);
            return new ReturnT<>(ReturnT.FAIL_CODE, "添加失败：" + e.getMessage());
        }
    }
    
    /**
     * 批量添加工作流节点关系
     */
    @Transactional(rollbackFor = Exception.class)
    public ReturnT<String> batchAdd(List<WorkflowNodeRelation> relations, String loginUser) {
        try {
            if (relations == null || relations.isEmpty()) {
                return new ReturnT<>(ReturnT.FAIL_CODE, "关系列表不能为空");
            }
            
            // 验证每个关系
            for (WorkflowNodeRelation relation : relations) {
                ReturnT<String> validResult = validateRelation(relation);
                if (validResult.getCode() != ReturnT.SUCCESS_CODE) {
                    return validResult;
                }
                setDefaultValues(relation, loginUser);
            }
            
            // 批量保存
            int result = workflowNodeRelationDao.batchSave(relations);
            if (result > 0) {
                log.info("批量添加工作流节点关系成功，数量：{}", result);
                return ReturnT.SUCCESS;
            } else {
                return new ReturnT<>(ReturnT.FAIL_CODE, "批量添加失败");
            }
        } catch (Exception e) {
            log.error("批量添加工作流节点关系失败", e);
            return new ReturnT<>(ReturnT.FAIL_CODE, "批量添加失败：" + e.getMessage());
        }
    }
    
    /**
     * 更新工作流节点关系
     */
    @Transactional(rollbackFor = Exception.class)
    public ReturnT<String> update(WorkflowNodeRelation relation, String loginUser) {
        try {
            // 参数验证
            if (relation.getId() == null) {
                return new ReturnT<>(ReturnT.FAIL_CODE, "关系ID不能为空");
            }
            
            ReturnT<String> validResult = validateRelation(relation);
            if (validResult.getCode() != ReturnT.SUCCESS_CODE) {
                return validResult;
            }
            
            // 检查关系是否存在
            WorkflowNodeRelation existRelation = workflowNodeRelationDao.load(relation.getId());
            if (existRelation == null) {
                return new ReturnT<>(ReturnT.FAIL_CODE, "关系不存在");
            }
            
            // 不需要手动设置更新时间，数据库会自动处理
            
            // 更新关系
            int result = workflowNodeRelationDao.update(relation);
            if (result > 0) {
                log.info("更新工作流节点关系成功，ID：{}", relation.getId());
                return ReturnT.SUCCESS;
            } else {
                return new ReturnT<>(ReturnT.FAIL_CODE, "更新失败");
            }
        } catch (Exception e) {
            log.error("更新工作流节点关系失败", e);
            return new ReturnT<>(ReturnT.FAIL_CODE, "更新失败：" + e.getMessage());
        }
    }
    
    /**
     * 删除工作流节点关系
     */
    @Transactional(rollbackFor = Exception.class)
    public ReturnT<String> delete(Long id, String loginUser) {
        try {
            if (id == null) {
                return new ReturnT<>(ReturnT.FAIL_CODE, "关系ID不能为空");
            }
            
            // 检查关系是否存在
            WorkflowNodeRelation existRelation = workflowNodeRelationDao.load(id);
            if (existRelation == null) {
                return new ReturnT<>(ReturnT.FAIL_CODE, "关系不存在");
            }
            
            // 物理删除
            int result = workflowNodeRelationDao.delete(id);
            if (result > 0) {
                log.info("删除工作流节点关系成功，ID：{}", id);
                return ReturnT.SUCCESS;
            } else {
                return new ReturnT<>(ReturnT.FAIL_CODE, "删除失败");
            }
        } catch (Exception e) {
            log.error("删除工作流节点关系失败", e);
            return new ReturnT<>(ReturnT.FAIL_CODE, "删除失败：" + e.getMessage());
        }
    }
    
    /**
     * 检查循环依赖
     */
    public ReturnT<Boolean> checkCircularDependency(Long workflowId, String preNodeCode, String postNodeCode) {
        try {
            if (workflowId == null || !StringUtils.hasText(preNodeCode) || !StringUtils.hasText(postNodeCode)) {
                return new ReturnT<>(ReturnT.FAIL_CODE, "参数不能为空");
            }
            
            int count = workflowNodeRelationDao.checkCircularDependency(workflowId, preNodeCode, postNodeCode);
            return new ReturnT<>(count > 0);
        } catch (Exception e) {
            log.error("检查循环依赖失败", e);
            return new ReturnT<>(ReturnT.FAIL_CODE, "检查失败：" + e.getMessage());
        }
    }
    
    /**
     * 验证关系参数
     */
    private ReturnT<String> validateRelation(WorkflowNodeRelation relation) {
        if (relation == null) {
            return new ReturnT<>(ReturnT.FAIL_CODE, "关系信息不能为空");
        }
        if (relation.getWorkflowId() == null) {
            return new ReturnT<>(ReturnT.FAIL_CODE, "工作流ID不能为空");
        }
        if (!StringUtils.hasText(relation.getPostNodeCode())) {
            return new ReturnT<>(ReturnT.FAIL_CODE, "后置节点编码不能为空");
        }
        return ReturnT.SUCCESS;
    }
    
    /**
     * 设置默认值
     */
    private void setDefaultValues(WorkflowNodeRelation relation, String loginUser) {
        if (!StringUtils.hasText(relation.getConditionType())) {
            relation.setConditionType("NONE");
        }
    }
}
