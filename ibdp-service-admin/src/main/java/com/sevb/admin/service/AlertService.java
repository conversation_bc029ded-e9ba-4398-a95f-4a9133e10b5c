package com.sevb.admin.service;

import com.sevb.admin.core.alarm.model.AlertRequest;
import com.sevb.core.biz.model.ReturnT;

import java.util.List;
import java.util.Map;

/**
 * 告警服务接口
 * <AUTHOR>
 */
public interface AlertService {

    /**
     * 发送告警
     * @param request 告警请求
     * @return 请求ID
     */
    String sendAlert(AlertRequest request);

    /**
     * 批量发送告警
     * @param requests 告警请求列表
     * @return 请求ID列表
     */
    List<String> sendAlertBatch(List<AlertRequest> requests);

    /**
     * 查询告警发送状态
     * @param requestId 请求ID
     * @return 发送状态信息
     */
    Map<String, Object> getAlertStatus(String requestId);

    /**
     * 重试失败的告警
     * @param recordId 发送记录ID
     * @return 操作结果
     */
    ReturnT<String> retryAlert(Long recordId);

    /**
     * 取消待发送的告警
     * @param requestId 请求ID
     * @return 操作结果
     */
    ReturnT<String> cancelAlert(String requestId);

    /**
     * 获取告警统计信息
     * @param hours 统计时间范围（小时）
     * @return 统计信息
     */
    Map<String, Object> getAlertStats(int hours);

    /**
     * 处理重试队列
     * @return 处理的记录数量
     */
    int processRetryQueue();

    /**
     * 清理历史记录
     * @param days 保留天数
     * @return 清理的记录数量
     */
    int cleanHistoryRecords(int days);

    /**
     * 测试告警发送
     * @param templateCode 模板编码
     * @param channelType 通道类型
     * @param recipients 接收人列表
     * @param variables 模板变量
     * @return 测试结果
     */
    ReturnT<String> testAlert(String templateCode, String channelType, 
                             List<String> recipients, Map<String, Object> variables);
}
