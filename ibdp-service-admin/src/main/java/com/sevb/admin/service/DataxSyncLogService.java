package com.sevb.admin.service;

import com.sevb.admin.core.model.DataxSyncLog;
import com.sevb.core.biz.model.ReturnT;
import java.time.LocalDateTime;
import java.util.List;

/**
 * DataX增量同步日志服务接口
 * 
 * <AUTHOR>
 */
public interface DataxSyncLogService {

    /**
     * 保存同步日志
     */
    ReturnT<String> save(DataxSyncLog syncLog);

    /**
     * 根据ID查询同步日志
     */
    DataxSyncLog load(Long id);

    /**
     * 更新同步日志
     */
    ReturnT<String> update(DataxSyncLog syncLog);

    /**
     * 删除同步日志
     */
    ReturnT<String> delete(Long id);

    /**
     * 根据同步状态ID查询日志列表
     */
    List<DataxSyncLog> findBySyncStateId(Long syncStateId);

    /**
     * 根据执行ID查询日志
     */
    DataxSyncLog findByExecutionId(String executionId);

    /**
     * 根据XXL-JOB日志ID查询日志
     */
    DataxSyncLog findByJobLogId(Long jobLogId);

    /**
     * 分页查询同步日志列表
     */
    ReturnT<List<DataxSyncLog>> pageList(int page, int size, Long syncStateId, String executionId,
                                        String status, LocalDateTime startTime, LocalDateTime endTime);

    /**
     * 查询正在运行的日志
     */
    List<DataxSyncLog> findRunningLogs();

    /**
     * 查询失败的日志
     */
    List<DataxSyncLog> findFailedLogs(int limit);

    /**
     * 查询可重试的日志
     */
    List<DataxSyncLog> findRetryableLogs(int maxRetryTimes);

    /**
     * 查询最近的日志
     */
    List<DataxSyncLog> findRecentLogs(int limit);

    /**
     * 根据状态查询日志数量
     */
    int countByStatus(String status);

    /**
     * 根据同步状态ID查询日志数量
     */
    int countBySyncStateId(Long syncStateId);

    /**
     * 查询指定时间范围内的日志
     */
    List<DataxSyncLog> findByTimeRange(LocalDateTime startTime, LocalDateTime endTime);

    /**
     * 创建执行日志
     */
    ReturnT<DataxSyncLog> createExecutionLog(Long syncStateId, String executionId, Long jobLogId,
                                           String incrementalValueStart, String dataxConfig);

    /**
     * 标记执行成功
     */
    ReturnT<String> markSuccess(Long logId, Long syncRecords, String incrementalValueEnd, String performanceInfo);

    /**
     * 标记执行失败
     */
    ReturnT<String> markFailed(Long logId, String errorMessage, String errorCode);

    /**
     * 标记执行取消
     */
    ReturnT<String> markCancelled(Long logId);

    /**
     * 增加重试次数
     */
    ReturnT<String> incrementRetryTimes(Long logId);

    /**
     * 创建重试日志
     */
    ReturnT<DataxSyncLog> createRetryLog(Long originalLogId);

    /**
     * 批量删除历史日志
     */
    ReturnT<String> batchDeleteByTime(LocalDateTime beforeTime, List<String> statuses);

    /**
     * 清理历史日志
     */
    ReturnT<String> cleanupHistoryLogs(int days);

    /**
     * 获取同步统计信息
     */
    ReturnT<Object> getSyncStatistics(LocalDateTime startTime, LocalDateTime endTime);

    /**
     * 获取性能统计信息
     */
    ReturnT<Object> getPerformanceStatistics(LocalDateTime startTime, LocalDateTime endTime);

    /**
     * 查询平均执行时长
     */
    Double getAverageDuration(Long syncStateId, int days);

    /**
     * 查询成功率
     */
    Double getSuccessRate(Long syncStateId, int days);

    /**
     * 查询最后一次成功的日志
     */
    DataxSyncLog findLastSuccessLog(Long syncStateId);

    /**
     * 查询最后一次失败的日志
     */
    DataxSyncLog findLastFailedLog(Long syncStateId);

    /**
     * 根据节点ID查询日志
     */
    List<DataxSyncLog> findByNodeId(Long nodeId, int limit);

    /**
     * 检查是否存在运行中的任务
     */
    boolean hasRunningTask(Long syncStateId);

    /**
     * 获取同步记录总数
     */
    Long getTotalSyncRecords(Long syncStateId);

    /**
     * 获取日志详情（包含统计信息）
     */
    ReturnT<Object> getLogDetail(Long logId);

    /**
     * 获取执行趋势统计
     */
    ReturnT<Object> getExecutionTrend(Long syncStateId, int days);

    /**
     * 强制停止运行中的任务
     */
    ReturnT<String> forceStopRunningTask(Long logId, String updateUser);
}
