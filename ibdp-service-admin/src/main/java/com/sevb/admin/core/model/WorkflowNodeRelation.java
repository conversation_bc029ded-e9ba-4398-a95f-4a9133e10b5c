package com.sevb.admin.core.model;

import lombok.Data;
import java.time.LocalDateTime;

/**
 * 工作流节点关系实体类
 * 对应数据库表：workflow_node_relation
 */
@Data
public class WorkflowNodeRelation {
    
    /**
     * 关系ID
     */
    private Long id;
    
    /**
     * 工作流ID
     */
    private Long workflowId;
    
    /**
     * 前置节点编码，NULL表示起始节点
     */
    private String preNodeCode;
    
    /**
     * 后置节点编码
     */
    private String postNodeCode;
    
    /**
     * 条件类型：NONE-无条件，SUCCESS-成功条件，FAILURE-失败条件
     */
    private String conditionType;
    
    /**
     * 条件参数JSON
     */
    private String conditionParams;
    
    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
}
