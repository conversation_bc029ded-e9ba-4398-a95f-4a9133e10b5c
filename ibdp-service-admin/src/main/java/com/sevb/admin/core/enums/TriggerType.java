package com.sevb.admin.core.enums;

import lombok.Getter;

/**
 * 工作流触发类型枚举
 * 
 * <AUTHOR>
 */
@Getter
public enum TriggerType {
    
    /**
     * 手动触发
     */
    MANUAL("MANUAL", "手动触发"),
    
    /**
     * 定时调度触发
     */
    SCHEDULE("SCHEDULE", "定时调度"),
    
    /**
     * API触发
     */
    API("API", "API触发"),
    
    /**
     * 事件触发
     */
    EVENT("EVENT", "事件触发"),
    
    /**
     * 依赖触发
     */
    DEPENDENCY("DEPENDENCY", "依赖触发");
    
    private final String code;
    private final String name;
    
    TriggerType(String code, String name) {
        this.code = code;
        this.name = name;
    }

    /**
     * 根据代码获取枚举
     */
    public static TriggerType fromCode(String code) {
        if (code == null) {
            return MANUAL;
        }

        // 向后兼容：CRON 映射到 SCHEDULE
        if ("CRON".equals(code)) {
            return SCHEDULE;
        }

        for (TriggerType type : values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }

        return MANUAL; // 默认返回手动触发
    }

    /**
     * 获取显示名称
     */
    public static String getDisplayName(String code) {
        if (code == null) {
            return MANUAL.getName();
        }

        // 向后兼容：CRON 显示为"定时触发"
        if ("CRON".equals(code)) {
            return "定时触发";
        }

        TriggerType type = fromCode(code);
        return type.getName();
    }
}
