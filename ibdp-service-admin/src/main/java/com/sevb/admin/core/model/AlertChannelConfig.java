package com.sevb.admin.core.model;

import lombok.Data;
import lombok.Getter;

import java.util.Date;

/**
 * 告警通道配置实体类
 * <AUTHOR>
 */
@Data
public class AlertChannelConfig {

    private Long id;                    // 主键ID
    private String configName;          // 配置名称
    private String channelType;         // 通道类型：WECHAT,FEISHU,DINGTALK,EMAIL
    private String authType;            // 认证类型：WEBHOOK,TOKEN,OAUTH,SMTP
    private String authConfig;          // 认证配置JSON（加密存储）
    private String channelConfig;       // 通道特定配置JSON
    private Integer enabled;            // 是否启用：0-禁用，1-启用
    private String description;         // 配置描述
    private Date createTime;            // 创建时间
    private Date updateTime;            // 更新时间
    private String createUser;          // 创建人
    private String updateUser;          // 更新人
    private Integer deleted;            // 删除标记：0-未删除，1-已删除

    // 认证类型枚举
    @Getter
    public enum AuthType {
        WEBHOOK("WEBHOOK", "Webhook认证"),
        TOKEN("TOKEN", "Token认证"),
        OAUTH("OAUTH", "OAuth认证"),
        SMTP("SMTP", "SMTP认证"),
        APP_SECRET("APP_SECRET", "应用密钥认证");

        private final String code;
        private final String name;

        AuthType(String code, String name) {
            this.code = code;
            this.name = name;
        }

        public static AuthType fromCode(String code) {
            for (AuthType type : values()) {
                if (type.code.equals(code)) {
                    return type;
                }
            }
            throw new IllegalArgumentException("Unknown auth type: " + code);
        }
    }

    // 通道类型枚举（复用AlertTemplate中的定义）
    @Getter
    public enum ChannelType {
        WECHAT("WECHAT", "企业微信"),
        FEISHU("FEISHU", "飞书"),
        DINGTALK("DINGTALK", "钉钉"),
        EMAIL("EMAIL", "邮件");

        private final String code;
        private final String name;

        ChannelType(String code, String name) {
            this.code = code;
            this.name = name;
        }

        public static ChannelType fromCode(String code) {
            for (ChannelType type : values()) {
                if (type.code.equals(code)) {
                    return type;
                }
            }
            throw new IllegalArgumentException("Unknown channel type: " + code);
        }
    }
}
