package com.sevb.admin.core.alarm.channel;

import com.sevb.admin.core.alarm.model.NotificationRequest;
import com.sevb.admin.core.alarm.model.SendResult;
import com.sevb.admin.core.alarm.model.AuthConfig;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 通知渠道管理器
 * <AUTHOR>
 */
@Component
public class NotificationChannelManager implements ApplicationContextAware, InitializingBean {
    
    private static final Logger logger = LoggerFactory.getLogger(NotificationChannelManager.class);

    private ApplicationContext applicationContext;
    private Map<String, NotificationChannel> channelMap = new HashMap<>();

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        this.applicationContext = applicationContext;
    }

    @Override
    public void afterPropertiesSet() throws Exception {
        // 自动发现所有NotificationChannel实现
        Map<String, NotificationChannel> channelBeans = applicationContext.getBeansOfType(NotificationChannel.class);
        for (NotificationChannel channel : channelBeans.values()) {
            if (channel.isEnabled()) {
                channelMap.put(channel.getChannelType(), channel);
                logger.info("注册通知渠道: {} - {}", channel.getChannelType(), channel.getChannelName());
            }
        }
        logger.info("通知渠道管理器初始化完成，共注册 {} 个渠道", channelMap.size());
    }

    /**
     * 发送通知
     */
    public SendResult sendNotification(NotificationRequest request) {
        String channelType = request.getChannelType();
        NotificationChannel channel = channelMap.get(channelType);
        
        if (channel == null) {
            String message = "不支持的通知渠道类型: " + channelType;
            logger.error(message);
            return SendResult.failure(message, "UNSUPPORTED_CHANNEL");
        }

        try {
            long startTime = System.currentTimeMillis();
            SendResult result = channel.sendNotification(request);
            long endTime = System.currentTimeMillis();
            
            result.setResponseTime(endTime - startTime);
            
            if (result.isSuccess()) {
                logger.info("通知发送成功: channelType={}, requestId={}, responseTime={}ms", 
                    channelType, request.getRequestId(), result.getResponseTime());
            } else {
                logger.warn("通知发送失败: channelType={}, requestId={}, message={}", 
                    channelType, request.getRequestId(), result.getMessage());
            }
            
            return result;
        } catch (Exception e) {
            logger.error("通知发送异常: channelType={}, requestId={}", channelType, request.getRequestId(), e);
            return SendResult.failure("发送异常: " + e.getMessage(), e);
        }
    }



    /**
     * 验证认证配置
     */
    public boolean validateAuthConfig(String channelType, AuthConfig authConfig) {
        NotificationChannel channel = channelMap.get(channelType);
        
        if (channel == null) {
            logger.error("不支持的通知渠道类型: {}", channelType);
            return false;
        }

        try {
            return channel.validateAuthConfig(authConfig);
        } catch (Exception e) {
            logger.error("验证认证配置异常: channelType={}", channelType, e);
            return false;
        }
    }

    /**
     * 获取支持的通道类型列表
     */
    public List<String> getSupportedChannelTypes() {
        return List.copyOf(channelMap.keySet());
    }

    /**
     * 获取通道信息
     */
    public Map<String, Object> getChannelInfo(String channelType) {
        NotificationChannel channel = channelMap.get(channelType);
        if (channel == null) {
            return null;
        }

        Map<String, Object> info = new HashMap<>();
        info.put("channelType", channel.getChannelType());
        info.put("channelName", channel.getChannelName());
        info.put("description", channel.getChannelDescription());
        info.put("supportedAuthType", channel.getSupportedAuthType());
        info.put("enabled", channel.isEnabled());
        
        return info;
    }

    /**
     * 获取所有通道信息
     */
    public Map<String, Map<String, Object>> getAllChannelInfo() {
        Map<String, Map<String, Object>> allInfo = new HashMap<>();
        for (String channelType : channelMap.keySet()) {
            allInfo.put(channelType, getChannelInfo(channelType));
        }
        return allInfo;
    }

    /**
     * 检查通道是否可用
     */
    public boolean isChannelAvailable(String channelType) {
        NotificationChannel channel = channelMap.get(channelType);
        return channel != null && channel.isEnabled();
    }
}
