package com.sevb.admin.core.model;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import java.time.LocalDateTime;

/**
 * 工作流节点实体类
 * 对应数据库表：workflow_node
 */
@Data
public class WorkflowNode {

    /**
     * 节点ID
     */
    private Long id;

    /**
     * 工作流ID
     */
    private Long workflowId;

    /**
     * 节点编码
     */
    private String nodeCode;

    /**
     * 节点名称
     */
    private String nodeName;
    
    /**
     * 节点类型编码
     */
    @Schema(description = "节点类型编码", example = "SQL", required = true,
            allowableValues = {"START", "END", "SQL", "SHELL", "PYTHON", "SPARK", "FLINK"})
    private String nodeType;

    /**
     * 节点颜色（从节点类型继承）
     */
    @Schema(description = "节点颜色", example = "#1890ff")
    private String color;

    /**
     * 节点描述
     */
    @Schema(description = "节点描述", example = "这是一个数据处理节点")
    private String description;

    /**
     * 节点配置参数JSON
     */
    @Schema(description = "节点配置参数JSON", example = "{\"sql\": \"SELECT * FROM table\"}")
    private String configParams;

    /**
     * X坐标位置
     */
    @Schema(description = "X坐标位置", example = "100")
    private Integer positionX;

    /**
     * Y坐标位置
     */
    @Schema(description = "Y坐标位置", example = "200")
    private Integer positionY;
    
    /**
     * 超时时间(分钟)，0表示不限制
     */
    private Integer timeout;
    
    /**
     * 重试次数
     */
    private Integer retryTimes;
    
    /**
     * 重试间隔(分钟)
     */
    private Integer retryInterval;
    
    /**
     * 最大重试次数
     */
    private Integer maxRetryTimes;
    
    /**
     * 失败策略：FAIL-失败，SKIP-跳过
     */
    private String failureStrategy;
    
    /**
     * 优先级：1-最高，2-高，3-中，4-低，5-最低
     */
    private Integer priority;
    
    /**
     * 工作组
     */
    private String workerGroup;
    
    /**
     * 环境变量编码
     */
    private String environmentCode;
    
    /**
     * 关联数据源ID
     */
    private Long datasourceId;

    /**
     * XXL-JOB任务ID，关联job_info表的id字段，用于节点级别的任务调度
     */
    @Schema(description = "XXL-JOB任务ID", example = "123")
    private Long jobId;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    
    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
    
    /**
     * 创建人
     */
    private String createUser;
    
    /**
     * 更新人
     */
    private String updateUser;
    
    /**
     * 删除标记：0-未删除，1-已删除
     */
    private Integer deleted;
}
