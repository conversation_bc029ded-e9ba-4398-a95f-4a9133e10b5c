package com.sevb.admin.core.alarm.template;

import java.util.Map;

/**
 * 模板引擎接口
 * <AUTHOR>
 */
public interface TemplateEngine {

    /**
     * 渲染模板
     * @param template 模板内容
     * @param variables 变量映射
     * @return 渲染后的内容
     */
    String render(String template, Map<String, Object> variables);

    /**
     * 验证模板语法
     * @param template 模板内容
     * @return 是否有效
     */
    boolean validateTemplate(String template);

    /**
     * 提取模板中的变量
     * @param template 模板内容
     * @return 变量列表
     */
    java.util.Set<String> extractVariables(String template);

    /**
     * 检查必需变量是否都已提供
     * @param template 模板内容
     * @param variables 提供的变量
     * @return 缺失的变量列表
     */
    java.util.Set<String> getMissingVariables(String template, Map<String, Object> variables);
}
