package com.sevb.admin.core.util;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.sevb.admin.core.model.Datasource;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.StringUtils;

import javax.crypto.Cipher;
import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.StandardCharsets;
import java.sql.Connection;
import java.sql.DriverManager;
import java.util.Base64;
import java.util.Map;
import java.util.Properties;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;

/**
 * 数据源连接工具类
 * <AUTHOR>
 */
@Slf4j
public class DatasourceConnectionUtil {

    private static final String AES_KEY = "DataSourceKey123"; // 16位密钥
    private static final String ALGORITHM = "AES";

    // 连接测试结果缓存，避免频繁连接
    private static final ConcurrentHashMap<String, CacheEntry> connectionCache = new ConcurrentHashMap<>();
    private static final long CACHE_EXPIRE_TIME = TimeUnit.MINUTES.toMillis(5); // 5分钟缓存

    /**
     * 缓存条目
     */
    @Getter
    private static class CacheEntry {
        private final boolean success;
        private final String message;
        private final long timestamp;

        public CacheEntry(boolean success, String message) {
            this.success = success;
            this.message = message;
            this.timestamp = System.currentTimeMillis();
        }

        public boolean isExpired() {
            return System.currentTimeMillis() - timestamp > CACHE_EXPIRE_TIME;
        }
    }
    private static final ObjectMapper objectMapper = new ObjectMapper();

    /**
     * 获取数据库连接
     */
    public static Connection getConnection(Datasource datasource) throws Exception {
        if (datasource == null) {
            throw new IllegalArgumentException("数据源信息不能为空");
        }

        Datasource.DatasourceType type = Datasource.DatasourceType.getByCode(datasource.getType());
        if (type == null) {
            throw new IllegalArgumentException("不支持的数据源类型：" + datasource.getType());
        }

        // 加载驱动
        Class.forName(type.getDriverClass());

        // 构建连接URL
        String url = buildConnectionUrl(datasource, type);
        
        // 构建连接属性
        Properties props = buildConnectionProperties(datasource);

        log.info("正在连接数据源：{}, URL：{}", datasource.getName(), url);
        
        return DriverManager.getConnection(url, props);
    }

    /**
     * 构建连接URL
     */
    private static String buildConnectionUrl(Datasource datasource, Datasource.DatasourceType type) throws Exception {
        String url = type.getUrlTemplate()
                .replace("{host}", datasource.getHost())
                .replace("{port}", String.valueOf(datasource.getPort()))
                .replace("{database}", datasource.getDatabaseName());

        // 添加连接参数
        if (StringUtils.hasText(datasource.getConnectionParams())) {
            Map<String, String> params = parseConnectionParams(datasource.getConnectionParams());
            if (!params.isEmpty()) {
                StringBuilder paramBuilder = new StringBuilder();
                for (Map.Entry<String, String> entry : params.entrySet()) {
                    if (!paramBuilder.isEmpty()) {
                        paramBuilder.append("&");
                    }
                    paramBuilder.append(entry.getKey()).append("=").append(entry.getValue());
                }
                
                if (url.contains("?")) {
                    url += "&" + paramBuilder.toString();
                } else {
                    url += "?" + paramBuilder.toString();
                }
            }
        }

        return url;
    }

    /**
     * 构建连接属性
     */
    private static Properties buildConnectionProperties(Datasource datasource) {
        Properties props = new Properties();
        props.setProperty("user", datasource.getUsername());
        props.setProperty("password", datasource.getPassword());

        // 设置连接超时
        props.setProperty("connectTimeout", "10000");
        props.setProperty("socketTimeout", "30000");

        // 针对MySQL的SSL和兼容性配置
        Datasource.DatasourceType type = Datasource.DatasourceType.getByCode(datasource.getType());
        if (type == Datasource.DatasourceType.MYSQL || type == Datasource.DatasourceType.DORIS) {
            // SSL配置
            props.setProperty("useSSL", "false");
            props.setProperty("allowPublicKeyRetrieval", "true");
            props.setProperty("requireSSL", "false");
            props.setProperty("verifyServerCertificate", "false");

            // 连接配置
            props.setProperty("autoReconnect", "true");
            props.setProperty("failOverReadOnly", "false");
            props.setProperty("maxReconnects", "3");
            props.setProperty("initialTimeout", "2");

            // 字符集和时区配置
            props.setProperty("characterEncoding", "UTF-8");
            props.setProperty("useUnicode", "true");
            props.setProperty("serverTimezone", "Asia/Shanghai");

            // 性能优化配置
            props.setProperty("useLocalSessionState", "true");
            props.setProperty("useLocalTransactionState", "true");
            props.setProperty("rewriteBatchedStatements", "true");
        }

        return props;
    }

    /**
     * 解析连接参数
     */
    private static Map<String, String> parseConnectionParams(String connectionParams) throws Exception {
        if (!StringUtils.hasText(connectionParams)) {
            return Map.of();
        }
        
        try {
            return objectMapper.readValue(connectionParams, new TypeReference<Map<String, String>>() {});
        } catch (Exception e) {
            log.warn("解析连接参数失败，使用空参数：{}", connectionParams, e);
            return Map.of();
        }
    }

    /**
     * 加密密码
     */
    public static String encryptPassword(String password) {
        if (!StringUtils.hasText(password)) {
            return password;
        }
        
        try {
            SecretKeySpec keySpec = new SecretKeySpec(AES_KEY.getBytes(StandardCharsets.UTF_8), ALGORITHM);
            Cipher cipher = Cipher.getInstance(ALGORITHM);
            cipher.init(Cipher.ENCRYPT_MODE, keySpec);
            byte[] encrypted = cipher.doFinal(password.getBytes(StandardCharsets.UTF_8));
            return Base64.getEncoder().encodeToString(encrypted);
        } catch (Exception e) {
            log.error("密码加密失败", e);
            return password; // 加密失败时返回原密码
        }
    }

    /**
     * 解密密码
     */
    public static String decryptPassword(String encryptedPassword) {
        if (!StringUtils.hasText(encryptedPassword)) {
            return encryptedPassword;
        }

        try {
            // 检查是否为有效的Base64字符串
            if (!isValidBase64(encryptedPassword)) {
                log.warn("密码不是有效的Base64格式，可能是明文密码，直接返回: {}", encryptedPassword.substring(0, Math.min(3, encryptedPassword.length())) + "***");
                return encryptedPassword;
            }

            SecretKeySpec keySpec = new SecretKeySpec(AES_KEY.getBytes(StandardCharsets.UTF_8), ALGORITHM);
            Cipher cipher = Cipher.getInstance(ALGORITHM);
            cipher.init(Cipher.DECRYPT_MODE, keySpec);
            byte[] decrypted = cipher.doFinal(Base64.getDecoder().decode(encryptedPassword));
            String decryptedPassword = new String(decrypted, StandardCharsets.UTF_8);
            log.debug("密码解密成功");
            return decryptedPassword;
        } catch (Exception e) {
            log.error("密码解密失败，可能是加密格式不匹配，返回原密码: {}", e.getMessage());
            return encryptedPassword; // 解密失败时返回原密码
        }
    }

    /**
     * 检查字符串是否为有效的Base64格式
     */
    private static boolean isValidBase64(String str) {
        try {
            // Base64字符串只能包含A-Z, a-z, 0-9, +, /, = 字符
            if (!str.matches("^[A-Za-z0-9+/]*={0,2}$")) {
                return false;
            }
            // 长度必须是4的倍数（除去padding）
            if (str.replace("=", "").length() % 4 == 1) {
                return false;
            }
            // 尝试解码
            Base64.getDecoder().decode(str);
            return true;
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * 测试连接（带缓存）
     */
    public static boolean testConnection(Datasource datasource) {
        // 生成缓存key
        String cacheKey = generateCacheKey(datasource);

        // 检查缓存
        CacheEntry cached = connectionCache.get(cacheKey);
        if (cached != null && !cached.isExpired()) {
            log.debug("使用缓存的连接测试结果，数据源: {}", datasource.getName());
            return cached.isSuccess();
        }

        // 执行实际连接测试
        boolean success = false;
        String message = "";
        try {
            Connection connection = getConnection(datasource);
            if (connection != null) {
                connection.close();
                success = true;
                message = "连接成功";
                log.debug("数据源连接测试成功: {}", datasource.getName());
            } else {
                message = "连接失败：无法建立连接";
            }
        } catch (Exception e) {
            message = "连接失败：" + e.getMessage();
            log.error("测试连接失败：{}", e.getMessage());
        }

        // 缓存结果
        connectionCache.put(cacheKey, new CacheEntry(success, message));

        return success;
    }

    /**
     * 生成缓存key
     */
    private static String generateCacheKey(Datasource datasource) {
        return String.format("%s_%s_%s_%s",
            datasource.getType(),
            datasource.getHost(),
            datasource.getPort(),
            datasource.getDatabaseName());
    }

    /**
     * 获取默认端口
     */
    public static int getDefaultPort(String type) {
        Datasource.DatasourceType datasourceType = Datasource.DatasourceType.getByCode(type);
        if (datasourceType == null) {
            return 0;
        }

        return switch (datasourceType) {
            case MYSQL, DORIS -> 3306;
            case POSTGRESQL -> 5432;
            case ORACLE -> 1521;
            case HIVE -> 10000;
            default -> 0;
        };
    }

    /**
     * 获取默认连接参数
     */
    public static String getDefaultConnectionParams(String type) {
        Datasource.DatasourceType datasourceType = Datasource.DatasourceType.getByCode(type);
        if (datasourceType == null) {
            return "{}";
        }

        return switch (datasourceType) {
            case MYSQL, DORIS ->
                    "{\"useSSL\":\"false\",\"allowPublicKeyRetrieval\":\"true\",\"serverTimezone\":\"Asia/Shanghai\",\"characterEncoding\":\"UTF-8\",\"autoReconnect\":\"true\",\"failOverReadOnly\":\"false\"}";
            case POSTGRESQL -> "{\"currentSchema\":\"public\"}";
            case ORACLE -> "{\"serviceName\":\"ORCL\"}";
            case HIVE -> "{\"auth\":\"noSasl\"}";
            default -> "{}";
        };
    }
}
