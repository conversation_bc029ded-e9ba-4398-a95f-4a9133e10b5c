package com.sevb.admin.core.alarm.config;

import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * 告警系统配置
 * <AUTHOR>
 */
@Setter
@Getter
@Component
@ConfigurationProperties(prefix = "alert")
public class AlertConfig {

    /**
     * 是否启用告警系统
     */
    private boolean enabled = true;

    /**
     * 异步执行器配置
     */
    private AsyncConfig async = new AsyncConfig();

    /**
     * 重试配置
     */
    private RetryConfig retry = new RetryConfig();

    /**
     * 模板配置
     */
    private TemplateConfig template = new TemplateConfig();

    /**
     * 异步执行器配置
     */
    @Setter
    @Getter
    public static class AsyncConfig {
        private int corePoolSize = 5;
        private int maxPoolSize = 20;
        private int queueCapacity = 1000;
        private String threadNamePrefix = "alert-";

    }

    /**
     * 重试配置
     */
    @Setter
    @Getter
    public static class RetryConfig {
        private int maxAttempts = 3;
        private long delay = 5000; // 毫秒
        private double multiplier = 2.0;
        private long maxDelay = 300000; // 5分钟

    }

    /**
     * 模板配置
     */
    @Setter
    @Getter
    public static class TemplateConfig {
        private String engine = "default";
        private boolean cacheEnabled = true;
        private int cacheSize = 1000;
        private long cacheTtl = 3600000; // 1小时

    }
}
