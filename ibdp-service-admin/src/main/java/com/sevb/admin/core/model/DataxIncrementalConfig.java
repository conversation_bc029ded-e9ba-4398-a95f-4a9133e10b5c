package com.sevb.admin.core.model;

import lombok.Data;
import java.time.LocalDateTime;

/**
 * DataX增量同步配置实体类
 * 对应数据库表：datax_incremental_config
 * 
 * <AUTHOR>
 */
@Data
public class DataxIncrementalConfig {

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 节点ID
     */
    private Long nodeId;

    /**
     * 增量类型：TIMESTAMP,PRIMARY_KEY,CDC
     */
    private String incrementalType;

    /**
     * 增量字段
     */
    private String incrementalColumn;

    /**
     * 增量值表达式
     */
    private String incrementalValue;

    /**
     * 批次大小
     */
    private Integer batchSize;

    /**
     * 最大重试次数
     */
    private Integer maxRetryTimes;

    /**
     * 自定义增量条件
     */
    private String customCondition;

    /**
     * 是否启用断点续传：0-否，1-是
     */
    private Integer enableCheckpoint;

    /**
     * 断点保存间隔（记录数）
     */
    private Integer checkpointInterval;

    /**
     * 是否启用：0-否，1-是
     */
    private Integer enabled;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 创建人
     */
    private String createUser;

    /**
     * 更新人
     */
    private String updateUser;

    /**
     * 检查是否启用
     */
    public boolean isEnabled() {
        return Integer.valueOf(1).equals(this.enabled);
    }

    /**
     * 检查是否启用断点续传
     */
    public boolean isCheckpointEnabled() {
        return Integer.valueOf(1).equals(this.enableCheckpoint);
    }

    /**
     * 获取有效的批次大小
     */
    public int getEffectiveBatchSize() {
        return this.batchSize != null && this.batchSize > 0 ? this.batchSize : 10000;
    }

    /**
     * 获取有效的最大重试次数
     */
    public int getEffectiveMaxRetryTimes() {
        return this.maxRetryTimes != null && this.maxRetryTimes >= 0 ? this.maxRetryTimes : 3;
    }

    /**
     * 获取有效的断点间隔
     */
    public int getEffectiveCheckpointInterval() {
        return this.checkpointInterval != null && this.checkpointInterval > 0 ? this.checkpointInterval : 1000;
    }

    /**
     * 验证配置有效性
     */
    public boolean isValid() {
        return this.nodeId != null 
            && this.incrementalType != null && !this.incrementalType.trim().isEmpty()
            && this.incrementalColumn != null && !this.incrementalColumn.trim().isEmpty();
    }

    /**
     * 获取增量类型枚举
     */
    public DataxSyncState.IncrementalType getIncrementalTypeEnum() {
        try {
            return DataxSyncState.IncrementalType.fromCode(this.incrementalType);
        } catch (IllegalArgumentException e) {
            return DataxSyncState.IncrementalType.TIMESTAMP; // 默认值
        }
    }

    /**
     * 设置默认值
     */
    public void setDefaults() {
        if (this.batchSize == null || this.batchSize <= 0) {
            this.batchSize = 10000;
        }
        if (this.maxRetryTimes == null || this.maxRetryTimes < 0) {
            this.maxRetryTimes = 3;
        }
        if (this.enableCheckpoint == null) {
            this.enableCheckpoint = 1;
        }
        if (this.checkpointInterval == null || this.checkpointInterval <= 0) {
            this.checkpointInterval = 1000;
        }
        if (this.enabled == null) {
            this.enabled = 1;
        }
        if (this.incrementalType == null || this.incrementalType.trim().isEmpty()) {
            this.incrementalType = DataxSyncState.IncrementalType.TIMESTAMP.getCode();
        }
    }

    /**
     * 创建默认配置
     */
    public static DataxIncrementalConfig createDefault(Long nodeId, String createUser) {
        DataxIncrementalConfig config = new DataxIncrementalConfig();
        config.setNodeId(nodeId);
        config.setIncrementalType(DataxSyncState.IncrementalType.TIMESTAMP.getCode());
        config.setIncrementalColumn("update_time");
        config.setIncrementalValue("${lastSyncTime}");
        config.setBatchSize(10000);
        config.setMaxRetryTimes(3);
        config.setEnableCheckpoint(1);
        config.setCheckpointInterval(1000);
        config.setEnabled(1);
        config.setCreateUser(createUser);
        config.setUpdateUser(createUser);
        config.setCreateTime(LocalDateTime.now());
        config.setUpdateTime(LocalDateTime.now());
        return config;
    }

    /**
     * 复制配置
     */
    public DataxIncrementalConfig copy() {
        DataxIncrementalConfig copy = new DataxIncrementalConfig();
        copy.setNodeId(this.nodeId);
        copy.setIncrementalType(this.incrementalType);
        copy.setIncrementalColumn(this.incrementalColumn);
        copy.setIncrementalValue(this.incrementalValue);
        copy.setBatchSize(this.batchSize);
        copy.setMaxRetryTimes(this.maxRetryTimes);
        copy.setCustomCondition(this.customCondition);
        copy.setEnableCheckpoint(this.enableCheckpoint);
        copy.setCheckpointInterval(this.checkpointInterval);
        copy.setEnabled(this.enabled);
        return copy;
    }
}
