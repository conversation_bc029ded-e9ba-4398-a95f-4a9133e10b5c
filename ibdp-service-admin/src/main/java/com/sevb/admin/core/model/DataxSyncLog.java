package com.sevb.admin.core.model;

import lombok.Data;
import java.time.LocalDateTime;

/**
 * DataX增量同步日志实体类
 * 对应数据库表：datax_sync_log
 * 
 * <AUTHOR>
 */
@Data
public class DataxSyncLog {

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 同步状态ID
     */
    private Long syncStateId;

    /**
     * 执行ID
     */
    private String executionId;

    /**
     * XXL-JOB日志ID
     */
    private Long jobLogId;

    /**
     * 开始时间
     */
    private LocalDateTime startTime;

    /**
     * 结束时间
     */
    private LocalDateTime endTime;

    /**
     * 执行时长(毫秒)
     */
    private Long duration;

    /**
     * 状态：RUNNING-运行中,SUCCESS-成功,FAILED-失败,CANCELLED-取消
     */
    private String status;

    /**
     * 本次同步记录数
     */
    private Long syncRecords;

    /**
     * 错误信息
     */
    private String errorMessage;

    /**
     * 错误代码
     */
    private String errorCode;

    /**
     * 增量起始值
     */
    private String incrementalValueStart;

    /**
     * 增量结束值
     */
    private String incrementalValueEnd;

    /**
     * DataX配置JSON
     */
    private String dataxConfig;

    /**
     * 性能信息JSON
     */
    private String performanceInfo;

    /**
     * 重试次数
     */
    private Integer retryTimes;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 执行状态枚举
     */
    public enum ExecutionStatus {
        RUNNING("RUNNING", "运行中"),
        SUCCESS("SUCCESS", "成功"),
        FAILED("FAILED", "失败"),
        CANCELLED("CANCELLED", "取消");

        private final String code;
        private final String name;

        ExecutionStatus(String code, String name) {
            this.code = code;
            this.name = name;
        }

        public String getCode() {
            return code;
        }

        public String getName() {
            return name;
        }

        public static ExecutionStatus fromCode(String code) {
            for (ExecutionStatus status : values()) {
                if (status.code.equals(code)) {
                    return status;
                }
            }
            throw new IllegalArgumentException("Unknown execution status: " + code);
        }
    }

    /**
     * 开始执行
     */
    public void startExecution() {
        this.status = ExecutionStatus.RUNNING.getCode();
        this.startTime = LocalDateTime.now();
        this.createTime = LocalDateTime.now();
    }

    /**
     * 执行成功
     */
    public void markSuccess(Long recordCount, String endValue) {
        this.status = ExecutionStatus.SUCCESS.getCode();
        this.endTime = LocalDateTime.now();
        this.syncRecords = recordCount;
        this.incrementalValueEnd = endValue;
        this.calculateDuration();
    }

    /**
     * 执行失败
     */
    public void markFailed(String errorMessage, String errorCode) {
        this.status = ExecutionStatus.FAILED.getCode();
        this.endTime = LocalDateTime.now();
        this.errorMessage = errorMessage;
        this.errorCode = errorCode;
        this.calculateDuration();
    }

    /**
     * 执行取消
     */
    public void markCancelled() {
        this.status = ExecutionStatus.CANCELLED.getCode();
        this.endTime = LocalDateTime.now();
        this.calculateDuration();
    }

    /**
     * 计算执行时长
     */
    private void calculateDuration() {
        if (this.startTime != null && this.endTime != null) {
            this.duration = java.time.Duration.between(this.startTime, this.endTime).toMillis();
        }
    }

    /**
     * 检查是否正在运行
     */
    public boolean isRunning() {
        return ExecutionStatus.RUNNING.getCode().equals(this.status);
    }

    /**
     * 检查是否执行成功
     */
    public boolean isSuccess() {
        return ExecutionStatus.SUCCESS.getCode().equals(this.status);
    }

    /**
     * 检查是否执行失败
     */
    public boolean isFailed() {
        return ExecutionStatus.FAILED.getCode().equals(this.status);
    }

    /**
     * 检查是否被取消
     */
    public boolean isCancelled() {
        return ExecutionStatus.CANCELLED.getCode().equals(this.status);
    }

    /**
     * 检查是否已完成（成功、失败或取消）
     */
    public boolean isCompleted() {
        return !isRunning();
    }

    /**
     * 获取执行时长（秒）
     */
    public Double getDurationInSeconds() {
        if (this.duration == null) {
            return null;
        }
        return this.duration / 1000.0;
    }

    /**
     * 获取平均处理速度（记录/秒）
     */
    public Double getAverageSpeed() {
        if (this.syncRecords == null || this.duration == null || this.duration == 0) {
            return null;
        }
        return (this.syncRecords * 1000.0) / this.duration;
    }

    /**
     * 增加重试次数
     */
    public void incrementRetryTimes() {
        this.retryTimes = (this.retryTimes == null ? 0 : this.retryTimes) + 1;
    }

    /**
     * 检查是否可以重试
     */
    public boolean canRetry(int maxRetryTimes) {
        return isFailed() && (this.retryTimes == null || this.retryTimes < maxRetryTimes);
    }

    /**
     * 创建重试日志
     */
    public DataxSyncLog createRetryLog() {
        DataxSyncLog retryLog = new DataxSyncLog();
        retryLog.setSyncStateId(this.syncStateId);
        retryLog.setExecutionId(this.executionId + "_RETRY_" + (this.retryTimes == null ? 1 : this.retryTimes + 1));
        retryLog.setJobLogId(this.jobLogId);
        retryLog.setIncrementalValueStart(this.incrementalValueStart);
        retryLog.setDataxConfig(this.dataxConfig);
        retryLog.setRetryTimes((this.retryTimes == null ? 0 : this.retryTimes) + 1);
        return retryLog;
    }
}
