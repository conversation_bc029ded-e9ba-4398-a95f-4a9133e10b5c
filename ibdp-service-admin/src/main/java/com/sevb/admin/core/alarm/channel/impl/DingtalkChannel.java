package com.sevb.admin.core.alarm.channel.impl;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.sevb.admin.core.alarm.channel.NotificationChannel;
import com.sevb.admin.core.alarm.model.AuthConfig;
import com.sevb.admin.core.alarm.model.NotificationRequest;
import com.sevb.admin.core.alarm.model.SendResult;
import com.sevb.admin.core.alarm.util.AuthConfigUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.*;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;
import org.springframework.web.client.RestTemplate;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.Base64;
import java.util.HashMap;
import java.util.Map;

/**
 * 钉钉通知渠道实现
 * <AUTHOR>
 */
@Component
public class DingtalkChannel implements NotificationChannel {

    private static final Logger logger = LoggerFactory.getLogger(DingtalkChannel.class);

    private final RestTemplate restTemplate = new RestTemplate();
    private final ObjectMapper objectMapper = new ObjectMapper();

    @Override
    public String getChannelType() {
        return "DINGTALK";
    }

    @Override
    public String getSupportedAuthType() {
        return "TOKEN";
    }

    @Override
    public String getChannelName() {
        return "钉钉";
    }

    @Override
    public String getChannelDescription() {
        return "钉钉群机器人通知";
    }

    @Override
    public SendResult sendNotification(NotificationRequest request) {
        if (request == null) {
            return SendResult.failure("请求参数不能为空");
        }

        if (request.getAuthConfig() == null) {
            return SendResult.failure("认证配置不能为空");
        }

        try {
            // 获取配置信息
            String accessToken = AuthConfigUtil.getAuthDataAsString(request.getAuthConfig(), "accessToken");
            String secret = AuthConfigUtil.getAuthDataAsString(request.getAuthConfig(), "secret");

            if (!StringUtils.hasText(accessToken)) {
                return SendResult.failure("Access Token不能为空");
            }

            // 构建请求URL
            String webhookUrl = buildWebhookUrl(accessToken, secret);

            // 构建消息内容
            Map<String, Object> messageBody = buildMessageBody(request);

            // 发送HTTP请求
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            HttpEntity<Map<String, Object>> entity = new HttpEntity<>(messageBody, headers);

            long startTime = System.currentTimeMillis();
            ResponseEntity<String> response = restTemplate.postForEntity(webhookUrl, entity, String.class);
            long endTime = System.currentTimeMillis();

            // 处理响应
            if (response.getStatusCode() == HttpStatus.OK) {
                Map<String, Object> responseData = parseResponse(response.getBody());
                
                if (isSuccessResponse(responseData)) {
                    SendResult result = SendResult.success("钉钉消息发送成功");
                    result.setResponseTime(endTime - startTime);
                    result.setResponseCode(String.valueOf(response.getStatusCodeValue()));
                    result.setResponseData(responseData);
                    return result;
                } else {
                    String errorMsg = getErrorMessage(responseData);
                    return SendResult.failure("钉钉消息发送失败: " + errorMsg, 
                        getErrorCode(responseData), String.valueOf(response.getStatusCodeValue()));
                }
            } else {
                return SendResult.failure("HTTP请求失败", 
                    "HTTP_ERROR", String.valueOf(response.getStatusCodeValue()));
            }

        } catch (Exception e) {
            logger.error("钉钉消息发送异常: requestId={}", request.getRequestId(), e);
            return SendResult.failure("发送异常: " + e.getMessage(), e);
        }
    }



    @Override
    public boolean validateAuthConfig(AuthConfig authConfig) {
        if (authConfig == null) {
            return false;
        }

        String accessToken = AuthConfigUtil.getAuthDataAsString(authConfig, "accessToken");
        return StringUtils.hasText(accessToken);
    }

    /**
     * 构建Webhook URL
     */
    private String buildWebhookUrl(String accessToken, String secret) throws Exception {
        String baseUrl = "https://oapi.dingtalk.com/robot/send?access_token=" + accessToken;
        
        if (StringUtils.hasText(secret)) {
            // 如果有密钥，需要计算签名
            long timestamp = System.currentTimeMillis();
            String stringToSign = timestamp + "\n" + secret;
            
            Mac mac = Mac.getInstance("HmacSHA256");
            mac.init(new SecretKeySpec(secret.getBytes(StandardCharsets.UTF_8), "HmacSHA256"));
            byte[] signData = mac.doFinal(stringToSign.getBytes(StandardCharsets.UTF_8));
            String sign = URLEncoder.encode(Base64.getEncoder().encodeToString(signData), "UTF-8");
            
            baseUrl += "&timestamp=" + timestamp + "&sign=" + sign;
        }
        
        return baseUrl;
    }

    /**
     * 构建消息体
     */
    private Map<String, Object> buildMessageBody(NotificationRequest request) throws Exception {
        // 尝试解析content为JSON，如果失败则作为纯文本处理
        try {
            return objectMapper.readValue(request.getContent(), Map.class);
        } catch (Exception e) {
            // 如果不是JSON格式，构建为文本消息
            Map<String, Object> messageBody = new HashMap<>();
            messageBody.put("msgtype", "text");
            
            Map<String, Object> textContent = new HashMap<>();
            textContent.put("content", request.getContent());
            messageBody.put("text", textContent);
            
            return messageBody;
        }
    }

    /**
     * 解析响应
     */
    private Map<String, Object> parseResponse(String responseBody) {
        try {
            if (StringUtils.hasText(responseBody)) {
                return objectMapper.readValue(responseBody, Map.class);
            }
        } catch (Exception e) {
            logger.warn("解析钉钉响应失败: {}", responseBody, e);
        }
        return new HashMap<>();
    }

    /**
     * 判断是否成功响应
     */
    private boolean isSuccessResponse(Map<String, Object> responseData) {
        if (responseData == null || responseData.isEmpty()) {
            return false;
        }

        Object errcode = responseData.get("errcode");
        return errcode != null && "0".equals(errcode.toString());
    }

    /**
     * 获取错误消息
     */
    private String getErrorMessage(Map<String, Object> responseData) {
        if (responseData == null) {
            return "未知错误";
        }

        Object errmsg = responseData.get("errmsg");
        return errmsg != null ? errmsg.toString() : "未知错误";
    }

    /**
     * 获取错误码
     */
    private String getErrorCode(Map<String, Object> responseData) {
        if (responseData == null) {
            return "UNKNOWN";
        }

        Object errcode = responseData.get("errcode");
        return errcode != null ? errcode.toString() : "UNKNOWN";
    }
}
