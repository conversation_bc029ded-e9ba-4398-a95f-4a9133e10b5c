package com.sevb.admin.core.model;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import java.util.Date;

/**
 * 节点类型实体类
 * <AUTHOR>
 */
@Data
@Schema(description = "节点类型实体")
public class NodeType {

    @Schema(description = "节点类型ID", example = "1")
    private Long id;

    @Schema(description = "节点类型编码", example = "SQL", required = true)
    private String typeCode;

    @Schema(description = "节点类型名称", example = "SQL节点", required = true)
    private String typeName;

    @Schema(description = "节点分类", example = "COMPUTE", allowableValues = {"DATA_SYNC", "COMPUTE", "CONTROL"}, required = true)
    private String category;

    @Schema(description = "节点图标", example = "database")
    private String icon;

    @Schema(description = "节点颜色", example = "#1890ff")
    private String color;

    @Schema(description = "节点描述", example = "SQL查询和执行节点，支持多种数据库")
    private String description;

    @Schema(description = "配置参数Schema(JSON)", example = "{\"datasourceId\":\"number\",\"sqlContent\":\"string\"}")
    private String configSchema;

    @Schema(description = "执行器类名", example = "com.sevb.workflow.executor.SqlExecutor", required = true)
    private String executorClass;

    @Schema(description = "排序序号", example = "1")
    private Integer sortOrder;

    @Schema(description = "状态", example = "1", allowableValues = {"0", "1"})
    private Integer status;

    @Schema(description = "创建时间")
    private Date createTime;

    @Schema(description = "更新时间")
    private Date updateTime;

    /**
     * 节点分类枚举
     */
    public enum Category {
        DATA_SYNC("DATA_SYNC", "数据同步"),
        COMPUTE("COMPUTE", "计算处理"),
        CONTROL("CONTROL", "流程控制");

        private final String code;
        private final String name;

        Category(String code, String name) {
            this.code = code;
            this.name = name;
        }

        public String getCode() {
            return code;
        }

        public String getName() {
            return name;
        }

        public static Category getByCode(String code) {
            for (Category category : values()) {
                if (category.getCode().equals(code)) {
                    return category;
                }
            }
            return null;
        }
    }

    /**
     * 状态枚举
     */
    public enum Status {
        DISABLED(0, "禁用"),
        ENABLED(1, "启用");

        private final Integer code;
        private final String name;

        Status(Integer code, String name) {
            this.code = code;
            this.name = name;
        }

        public Integer getCode() {
            return code;
        }

        public String getName() {
            return name;
        }

        public static Status getByCode(Integer code) {
            for (Status status : values()) {
                if (status.getCode().equals(code)) {
                    return status;
                }
            }
            return null;
        }
    }
}
