package com.sevb.admin.core.alarm.model;

import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.Data;

import java.util.HashMap;
import java.util.Map;

/**
 * 认证配置模型
 * <AUTHOR>
 */
@Data
public class AuthConfig {

    /**
     * 认证类型
     */
    private String authType;

    /**
     * 认证数据（解密后）
     */
    private Map<String, Object> authData;

    /**
     * 获取指定类型的认证数据
     */
    public <T> T getAuthData(Class<T> clazz) {
        if (authData == null) {
            return null;
        }
        ObjectMapper mapper = new ObjectMapper();
        return mapper.convertValue(authData, clazz);
    }

    /**
     * 获取字符串类型的认证数据
     */
    public String getAuthDataAsString(String key) {
        if (authData == null || !authData.containsKey(key)) {
            return null;
        }
        Object value = authData.get(key);
        return value != null ? value.toString() : null;
    }

    /**
     * 获取整数类型的认证数据
     */
    public Integer getAuthDataAsInteger(String key) {
        if (authData == null || !authData.containsKey(key)) {
            return null;
        }
        Object value = authData.get(key);
        if (value instanceof Integer) {
            return (Integer) value;
        }
        if (value instanceof Number) {
            return ((Number) value).intValue();
        }
        if (value instanceof String) {
            try {
                return Integer.parseInt((String) value);
            } catch (NumberFormatException e) {
                return null;
            }
        }
        return null;
    }

    /**
     * 获取布尔类型的认证数据
     */
    public Boolean getAuthDataAsBoolean(String key) {
        if (authData == null || !authData.containsKey(key)) {
            return null;
        }
        Object value = authData.get(key);
        if (value instanceof Boolean) {
            return (Boolean) value;
        }
        if (value instanceof String) {
            return Boolean.parseBoolean((String) value);
        }
        return null;
    }

    /**
     * 设置认证数据
     */
    public void setAuthDataValue(String key, Object value) {
        if (authData == null) {
            authData = new HashMap<>();
        }
        authData.put(key, value);
    }

    /**
     * 检查是否包含指定的认证数据
     */
    public boolean hasAuthData(String key) {
        return authData != null && authData.containsKey(key);
    }

    /**
     * 认证类型枚举
     */
    public enum AuthType {
        WEBHOOK("WEBHOOK", "Webhook认证"),
        TOKEN("TOKEN", "Token认证"),
        OAUTH("OAUTH", "OAuth认证"),
        SMTP("SMTP", "SMTP认证"),
        APP_SECRET("APP_SECRET", "应用密钥认证");

        private final String code;
        private final String name;

        AuthType(String code, String name) {
            this.code = code;
            this.name = name;
        }

        public String getCode() {
            return code;
        }

        public String getName() {
            return name;
        }

        public static AuthType fromCode(String code) {
            for (AuthType type : values()) {
                if (type.code.equals(code)) {
                    return type;
                }
            }
            throw new IllegalArgumentException("Unknown auth type: " + code);
        }
    }
}
