package com.sevb.admin.core.model;

import lombok.Data;
import java.util.Date;
import java.util.List;

/**
 * 项目实体类
 * <AUTHOR>
 */
@Data
public class Project {

    private Long id;                    // 项目ID
    private String name;                // 项目名称
    private String code;                // 项目编码
    private String type;                // 项目类型：PUBLIC-公共项目，PRIVATE-私有项目
    private String description;         // 项目描述
    private Integer status;             // 状态：0-禁用，1-启用
    private Date createTime;            // 创建时间
    private Date updateTime;            // 更新时间
    private String createUser;          // 创建人
    private String updateUser;          // 更新人
    private Integer deleted;            // 删除标记：0-未删除，1-已删除

    // 扩展字段
    private List<ProjectMember> members; // 项目成员列表
    private Integer memberCount;         // 成员数量
    private String currentUserRole;      // 当前用户在项目中的角色

    // 项目类型枚举
    public enum ProjectType {
        PUBLIC("PUBLIC", "公共项目"),
        PRIVATE("PRIVATE", "私有项目");

        private final String code;
        private final String name;

        ProjectType(String code, String name) {
            this.code = code;
            this.name = name;
        }

        public String getCode() {
            return code;
        }

        public String getName() {
            return name;
        }

        public static ProjectType getByCode(String code) {
            for (ProjectType type : values()) {
                if (type.getCode().equals(code)) {
                    return type;
                }
            }
            return null;
        }
    }

    // 项目状态枚举
    public enum Status {
        DISABLED(0, "禁用"),
        ENABLED(1, "启用");

        private final Integer code;
        private final String name;

        Status(Integer code, String name) {
            this.code = code;
            this.name = name;
        }

        public Integer getCode() {
            return code;
        }

        public String getName() {
            return name;
        }

        public static Status getByCode(Integer code) {
            for (Status status : values()) {
                if (status.getCode().equals(code)) {
                    return status;
                }
            }
            return null;
        }
    }
}
