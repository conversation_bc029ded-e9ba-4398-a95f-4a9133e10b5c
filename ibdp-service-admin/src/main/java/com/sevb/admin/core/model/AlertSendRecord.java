package com.sevb.admin.core.model;

import lombok.Data;
import java.util.Date;

/**
 * 告警发送记录实体类
 * <AUTHOR>
 */
@Data
public class AlertSendRecord {

    private Long id;                    // 主键ID
    private String requestId;           // 请求ID
    private String templateCode;        // 模板编码
    private String channelType;         // 通道类型
    private String configName;          // 使用的配置名称
    private String recipients;          // 接收人列表JSON
    private String title;               // 消息标题
    private String content;             // 消息内容
    private String variables;           // 模板变量JSON
    private Date sendTime;              // 发送时间
    private String sendStatus;          // 发送状态：PENDING,SUCCESS,FAILED,RETRY
    private String responseCode;        // 响应状态码
    private String responseMessage;     // 响应消息
    private Integer retryCount;         // 重试次数
    private Integer maxRetryCount;      // 最大重试次数
    private Date nextRetryTime;         // 下次重试时间
    private String sourceSystem;        // 来源系统
    private String businessId;          // 业务ID（如任务ID、工作流ID等）
    private Date createTime;            // 创建时间

    // 发送状态枚举
    public enum SendStatus {
        PENDING("PENDING", "待发送"),
        SUCCESS("SUCCESS", "发送成功"),
        FAILED("FAILED", "发送失败"),
        RETRY("RETRY", "重试中");

        private final String code;
        private final String name;

        SendStatus(String code, String name) {
            this.code = code;
            this.name = name;
        }

        public String getCode() {
            return code;
        }

        public String getName() {
            return name;
        }

        public static SendStatus fromCode(String code) {
            for (SendStatus status : values()) {
                if (status.code.equals(code)) {
                    return status;
                }
            }
            throw new IllegalArgumentException("Unknown send status: " + code);
        }
    }

    // 通道类型枚举
    public enum ChannelType {
        WECHAT("WECHAT", "企业微信"),
        FEISHU("FEISHU", "飞书"),
        DINGTALK("DINGTALK", "钉钉"),
        EMAIL("EMAIL", "邮件");

        private final String code;
        private final String name;

        ChannelType(String code, String name) {
            this.code = code;
            this.name = name;
        }

        public String getCode() {
            return code;
        }

        public String getName() {
            return name;
        }

        public static ChannelType fromCode(String code) {
            for (ChannelType type : values()) {
                if (type.code.equals(code)) {
                    return type;
                }
            }
            throw new IllegalArgumentException("Unknown channel type: " + code);
        }
    }
}
