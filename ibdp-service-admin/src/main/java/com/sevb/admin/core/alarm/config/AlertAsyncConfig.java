package com.sevb.admin.core.alarm.config;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.util.concurrent.Executor;
import java.util.concurrent.ThreadPoolExecutor;

/**
 * 告警系统异步配置
 * <AUTHOR>
 */
@Configuration
@EnableAsync
public class AlertAsyncConfig {

    private static final Logger logger = LoggerFactory.getLogger(AlertAsyncConfig.class);

    @Autowired
    private AlertConfig alertConfig;

    /**
     * 告警异步执行器
     */
    @Bean("alertExecutor")
    public Executor alertExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        
        AlertConfig.AsyncConfig asyncConfig = alertConfig.getAsync();
        
        // 核心线程数
        executor.setCorePoolSize(asyncConfig.getCorePoolSize());
        
        // 最大线程数
        executor.setMaxPoolSize(asyncConfig.getMaxPoolSize());
        
        // 队列容量
        executor.setQueueCapacity(asyncConfig.getQueueCapacity());
        
        // 线程名前缀
        executor.setThreadNamePrefix(asyncConfig.getThreadNamePrefix());
        
        // 线程空闲时间（秒）
        executor.setKeepAliveSeconds(60);
        
        // 拒绝策略：由调用线程处理
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        
        // 等待所有任务结束后再关闭线程池
        executor.setWaitForTasksToCompleteOnShutdown(true);
        
        // 等待时间
        executor.setAwaitTerminationSeconds(60);
        
        // 初始化
        executor.initialize();
        
        logger.info("告警异步执行器初始化完成: corePoolSize={}, maxPoolSize={}, queueCapacity={}", 
            asyncConfig.getCorePoolSize(), asyncConfig.getMaxPoolSize(), asyncConfig.getQueueCapacity());
        
        return executor;
    }
}
