package com.sevb.admin.core.model;

import com.sevb.admin.core.enums.TriggerType;
import lombok.Data;
import lombok.Getter;

import java.util.Date;

/**
 * 工作流实例
 * 记录工作流的每次执行情况
 * 
 * <AUTHOR>
 */
@Data
public class WorkflowInstance {

    /**
     * 实例ID
     */
    private Long id;

    /**
     * 工作流ID
     */
    private Long workflowId;

    /**
     * 工作流名称（冗余字段，便于查询）
     */
    private String workflowName;

    /**
     * 工作流版本（预留字段）
     */
    private String workflowVersion;

    /**
     * XXL-JOB任务ID
     */
    private Integer jobId;

    /**
     * XXL-JOB日志ID
     */
    private Long jobLogId;

    /**
     * 执行实例ID（UUID）
     */
    private String executionId;

    /**
     * 编排模式：EXECUTOR-执行器编排，CENTRAL-调度中心编排
     */
    private String orchestrationMode;

    /**
     * 执行进度（百分比）
     */
    private Double progress;

    /**
     * 已完成节点数
     */
    private Integer completedNodes;

    /**
     * 总节点数
     */
    private Integer totalNodes;

    /**
     * 运行中的节点列表（JSON）
     */
    private String runningNodes;

    /**
     * 失败的节点列表（JSON）
     */
    private String failedNodes;

    /**
     * 实例状态：0-等待中，1-运行中，2-成功，3-失败，4-停止，5-超时
     */
    private Integer status;

    /**
     * 触发类型：CRON-定时触发，MANUAL-手动触发，API-接口触发
     */
    private String triggerType;

    /**
     * 触发时间
     */
    private Date triggerTime;

    /**
     * 开始执行时间
     */
    private Date startTime;

    /**
     * 结束时间
     */
    private Date endTime;

    /**
     * 执行耗时（毫秒）
     */
    private Long duration;

    /**
     * 执行参数
     */
    private String executeParams;

    /**
     * 执行结果
     */
    private String executeResult;

    /**
     * 错误信息
     */
    private String errorMessage;

    /**
     * 执行机器地址
     */
    private String executorAddress;

    /**
     * 执行器分片参数
     */
    private String executorShardingParam;

    /**
     * 重试次数
     */
    private Integer retryCount;

    /**
     * 执行用户
     */
    private String executeUser;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 构造函数 - 初始化默认值
     */
    public WorkflowInstance() {
        this.orchestrationMode = "CENTRAL"; // 统一使用调度中心编排
        this.progress = 0.0;
        this.completedNodes = 0;
        this.totalNodes = 0;
        this.retryCount = 0;
        // 所有数据库字段都已存在，功能完全可用
    }

    /**
     * 实例状态枚举（对应数据库state字段）
     */
    @Getter
    public enum Status {
        SUBMIT_SUCCESS(0, "提交成功"),
        RUNNING(1, "正在运行"),
        READY_PAUSE(2, "准备暂停"),
        PAUSE(3, "暂停"),
        READY_STOP(4, "准备停止"),
        STOP(5, "停止"),
        FAILURE(6, "失败"),
        SUCCESS(7, "成功"),
        NEED_FAULT_TOLERANCE(8, "需要容错"),
        KILL(9, "kill"),
        WAITING_THREAD(10, "等待线程"),
        WAITING_DEPEND(11, "等待依赖完成");

        private final Integer code;
        private final String name;

        Status(Integer code, String name) {
            this.code = code;
            this.name = name;
        }

        public static Status getByCode(Integer code) {
            if (code == null) return null;
            for (Status status : values()) {
                if (status.getCode().equals(code)) {
                    return status;
                }
            }
            return null;
        }
    }



    /**
     * 获取状态名称
     */
    public String getStatusName() {
        Status statusEnum = Status.getByCode(this.status);
        return statusEnum != null ? statusEnum.getName() : "未知";
    }

    /**
     * 获取触发类型名称
     */
    public String getTriggerTypeName() {
        return TriggerType.getDisplayName(this.triggerType);
    }

    /**
     * 判断实例是否正在运行
     */
    public boolean isRunning() {
        return Status.RUNNING.getCode().equals(this.status);
    }

    /**
     * 判断实例是否已完成（成功或失败）
     */
    public boolean isCompleted() {
        return Status.SUCCESS.getCode().equals(this.status) ||
               Status.FAILURE.getCode().equals(this.status) ||
               Status.STOP.getCode().equals(this.status) ||
               Status.KILL.getCode().equals(this.status) ||
               Status.PAUSE.getCode().equals(this.status);
    }

    /**
     * 判断实例是否成功
     */
    public boolean isSuccess() {
        return Status.SUCCESS.getCode().equals(this.status);
    }

    /**
     * 判断实例是否失败
     */
    public boolean isFailed() {
        return Status.FAILURE.getCode().equals(this.status) ||
               Status.KILL.getCode().equals(this.status);
    }

    /**
     * 判断实例是否可以重试
     */
    public boolean canRetry() {
        return isFailed() || Status.STOP.getCode().equals(this.status);
    }
}
