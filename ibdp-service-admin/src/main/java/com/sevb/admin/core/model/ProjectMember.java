package com.sevb.admin.core.model;

import lombok.Data;
import java.util.Date;

/**
 * 项目成员实体类
 * <AUTHOR>
 */
@Data
public class ProjectMember {

    private Long id;                    // 成员ID
    private Long projectId;             // 项目ID
    private String userId;              // 用户ID
    private String username;            // 用户名
    private String role;                // 角色：ADMIN-项目管理员，DEVELOPER-开发者，VIEWER-查看者
    private Date joinTime;              // 加入时间
    private Integer status;             // 状态：0-禁用，1-启用
    private Date createTime;            // 创建时间
    private Date updateTime;            // 更新时间
    private String createUser;          // 创建人
    private String updateUser;          // 更新人
    private Integer deleted;            // 删除标记：0-未删除，1-已删除

    // 扩展字段
    private String projectName;         // 项目名称
    private String projectCode;         // 项目编码
    private String roleName;            // 角色名称

    // 项目角色枚举
    public enum Role {
        ADMIN("ADMIN", "项目管理员"),
        DEVELOPER("DEVELOPER", "开发者"),
        VIEWER("VIEWER", "查看者");

        private final String code;
        private final String name;

        Role(String code, String name) {
            this.code = code;
            this.name = name;
        }

        public String getCode() {
            return code;
        }

        public String getName() {
            return name;
        }

        public static Role getByCode(String code) {
            for (Role role : values()) {
                if (role.getCode().equals(code)) {
                    return role;
                }
            }
            return null;
        }
    }

    // 成员状态枚举
    public enum Status {
        DISABLED(0, "禁用"),
        ENABLED(1, "启用");

        private final Integer code;
        private final String name;

        Status(Integer code, String name) {
            this.code = code;
            this.name = name;
        }

        public Integer getCode() {
            return code;
        }

        public String getName() {
            return name;
        }

        public static Status getByCode(Integer code) {
            for (Status status : values()) {
                if (status.getCode().equals(code)) {
                    return status;
                }
            }
            return null;
        }
    }
}
