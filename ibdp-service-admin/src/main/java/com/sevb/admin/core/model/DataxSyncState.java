package com.sevb.admin.core.model;

import lombok.Data;
import java.time.LocalDateTime;

/**
 * DataX增量同步状态实体类
 * 对应数据库表：datax_sync_state
 * 
 * <AUTHOR>
 */
@Data
public class DataxSyncState {

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 节点ID
     */
    private Long nodeId;

    /**
     * 工作流ID
     */
    private Long workflowId;

    /**
     * 源数据源ID
     */
    private Long sourceDatasourceId;

    /**
     * 源表名
     */
    private String sourceTable;

    /**
     * 目标数据源ID
     */
    private Long targetDatasourceId;

    /**
     * 目标表名
     */
    private String targetTable;

    /**
     * 增量字段名
     */
    private String incrementalColumn;

    /**
     * 增量类型：TIMESTAMP,PRIMARY_KEY,CDC
     */
    private String incrementalType;

    /**
     * 上次同步的最大值
     */
    private String lastSyncValue;

    /**
     * 上次同步时间
     */
    private LocalDateTime lastSyncTime;

    /**
     * 同步次数
     */
    private Long syncCount;

    /**
     * 累计同步记录数
     */
    private Long totalRecords;

    /**
     * 状态：ACTIVE-活跃,INACTIVE-非活跃,ERROR-错误
     */
    private String status;

    /**
     * 错误信息
     */
    private String errorMessage;

    /**
     * 配置快照JSON
     */
    private String configSnapshot;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 创建人
     */
    private String createUser;

    /**
     * 更新人
     */
    private String updateUser;

    /**
     * 增量类型枚举
     */
    public enum IncrementalType {
        TIMESTAMP("TIMESTAMP", "时间戳增量"),
        PRIMARY_KEY("PRIMARY_KEY", "主键增量"),
        CDC("CDC", "变更数据捕获");

        private final String code;
        private final String name;

        IncrementalType(String code, String name) {
            this.code = code;
            this.name = name;
        }

        public String getCode() {
            return code;
        }

        public String getName() {
            return name;
        }

        public static IncrementalType fromCode(String code) {
            for (IncrementalType type : values()) {
                if (type.code.equals(code)) {
                    return type;
                }
            }
            throw new IllegalArgumentException("Unknown incremental type: " + code);
        }
    }

    /**
     * 状态枚举
     */
    public enum Status {
        ACTIVE("ACTIVE", "活跃"),
        INACTIVE("INACTIVE", "非活跃"),
        ERROR("ERROR", "错误");

        private final String code;
        private final String name;

        Status(String code, String name) {
            this.code = code;
            this.name = name;
        }

        public String getCode() {
            return code;
        }

        public String getName() {
            return name;
        }

        public static Status fromCode(String code) {
            for (Status status : values()) {
                if (status.code.equals(code)) {
                    return status;
                }
            }
            throw new IllegalArgumentException("Unknown status: " + code);
        }
    }

    /**
     * 检查是否可以执行同步
     */
    public boolean canSync() {
        return Status.ACTIVE.getCode().equals(this.status);
    }

    /**
     * 检查是否需要初始化
     */
    public boolean needsInitialization() {
        return this.lastSyncValue == null || this.lastSyncTime == null;
    }

    /**
     * 获取下次同步的起始值
     */
    public String getNextSyncStartValue() {
        if (needsInitialization()) {
            return null;
        }
        return this.lastSyncValue;
    }

    /**
     * 更新同步状态
     */
    public void updateSyncResult(String newSyncValue, Long recordCount, boolean success) {
        if (success) {
            this.lastSyncValue = newSyncValue;
            this.lastSyncTime = LocalDateTime.now();
            this.syncCount = (this.syncCount == null ? 0 : this.syncCount) + 1;
            this.totalRecords = (this.totalRecords == null ? 0 : this.totalRecords) + (recordCount == null ? 0 : recordCount);
            this.status = Status.ACTIVE.getCode();
            this.errorMessage = null;
        } else {
            this.status = Status.ERROR.getCode();
        }
        this.updateTime = LocalDateTime.now();
    }

    /**
     * 设置错误状态
     */
    public void setError(String errorMessage) {
        this.status = Status.ERROR.getCode();
        this.errorMessage = errorMessage;
        this.updateTime = LocalDateTime.now();
    }

    /**
     * 重置为活跃状态
     */
    public void resetToActive() {
        this.status = Status.ACTIVE.getCode();
        this.errorMessage = null;
        this.updateTime = LocalDateTime.now();
    }
}
