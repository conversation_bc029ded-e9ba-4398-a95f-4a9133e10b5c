package com.sevb.admin.core.datax;

import com.sevb.admin.core.model.DataxSyncState;
import com.sevb.admin.core.model.WorkflowNode;
import com.sevb.admin.service.DataxSyncStateService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 变量解析器
 * 负责解析和替换增量同步配置中的动态变量
 * 
 * <AUTHOR>
 */
@Component
public class VariableResolver {

    private static final Logger logger = LoggerFactory.getLogger(VariableResolver.class);

    // 变量匹配模式：${variableName} 或 ${variableName:defaultValue}
    private static final Pattern VARIABLE_PATTERN = Pattern.compile("\\$\\{([^}:]+)(?::([^}]*))?\\}");

    // 预定义的时间格式
    private static final Map<String, DateTimeFormatter> TIME_FORMATTERS = new HashMap<>();
    
    static {
        TIME_FORMATTERS.put("yyyy-MM-dd HH:mm:ss", DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
        TIME_FORMATTERS.put("yyyy-MM-dd", DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        TIME_FORMATTERS.put("yyyyMMdd", DateTimeFormatter.ofPattern("yyyyMMdd"));
        TIME_FORMATTERS.put("yyyyMMddHHmmss", DateTimeFormatter.ofPattern("yyyyMMddHHmmss"));
        TIME_FORMATTERS.put("timestamp", null); // 时间戳格式
    }

    @Autowired
    private DataxSyncStateService syncStateService;

    /**
     * 解析变量
     * 
     * @param node 工作流节点
     * @param expression 包含变量的表达式
     * @return 解析后的表达式
     */
    public String resolveVariables(WorkflowNode node, String expression) {
        if (!StringUtils.hasText(expression)) {
            return expression;
        }

        logger.debug("开始解析变量，节点ID: {}, 表达式: {}", node.getId(), expression);

        try {
            String result = expression;
            Matcher matcher = VARIABLE_PATTERN.matcher(expression);
            
            while (matcher.find()) {
                String fullMatch = matcher.group(0); // ${variableName} 或 ${variableName:defaultValue}
                String variableName = matcher.group(1); // variableName
                String defaultValue = matcher.group(2); // defaultValue (可能为null)
                
                String resolvedValue = resolveVariable(node, variableName, defaultValue);
                result = result.replace(fullMatch, resolvedValue);
                
                logger.debug("变量解析: {} -> {}", fullMatch, resolvedValue);
            }

            logger.info("变量解析完成，节点ID: {}, 原表达式: {}, 解析结果: {}", 
                       node.getId(), expression, result);
            return result;

        } catch (Exception e) {
            logger.error("变量解析失败，节点ID: {}, 表达式: {}", node.getId(), expression, e);
            throw new RuntimeException("变量解析失败: " + e.getMessage(), e);
        }
    }

    /**
     * 解析单个变量
     */
    private String resolveVariable(WorkflowNode node, String variableName, String defaultValue) {
        logger.debug("解析变量: {}, 默认值: {}", variableName, defaultValue);

        try {
            // 处理时间相关变量
            if (variableName.startsWith("lastSyncTime")) {
                return resolveLastSyncTime(node, variableName, defaultValue);
            }
            
            if (variableName.startsWith("currentTime")) {
                return resolveCurrentTime(variableName, defaultValue);
            }
            
            if (variableName.startsWith("yesterday")) {
                return resolveYesterday(variableName, defaultValue);
            }
            
            if (variableName.startsWith("today")) {
                return resolveToday(variableName, defaultValue);
            }

            // 处理自定义变量
            if (variableName.startsWith("custom.")) {
                return resolveCustomVariable(node, variableName, defaultValue);
            }

            // 处理系统变量
            return resolveSystemVariable(variableName, defaultValue);

        } catch (Exception e) {
            logger.warn("变量解析失败，使用默认值，变量: {}, 错误: {}", variableName, e.getMessage());
            return StringUtils.hasText(defaultValue) ? defaultValue : "";
        }
    }

    /**
     * 解析上次同步时间变量
     * 支持格式：
     * - ${lastSyncTime} - 默认格式 yyyy-MM-dd HH:mm:ss
     * - ${lastSyncTime:yyyy-MM-dd} - 指定格式
     * - ${lastSyncTime:timestamp} - 时间戳格式
     */
    private String resolveLastSyncTime(WorkflowNode node, String variableName, String defaultValue) {
        // 获取上次同步时间
        DataxSyncState syncState = syncStateService.findByNodeId(node.getId());
        LocalDateTime lastSyncTime = null;

        if (syncState != null && syncState.getLastSyncTime() != null) {
            lastSyncTime = syncState.getLastSyncTime();
            logger.debug("找到上次同步时间: {}", lastSyncTime);
        } else {
            // 第一次同步：如果没有上次同步时间，使用默认值或一个很早的时间进行全量同步
            if (StringUtils.hasText(defaultValue)) {
                logger.info("第一次同步，使用配置的默认值: {}", defaultValue);
                return defaultValue;
            } else {
                // 使用一个很早的时间，确保能同步所有历史数据
                lastSyncTime = LocalDateTime.of(1970, 1, 1, 0, 0, 0);
                logger.info("第一次同步，使用最早时间进行全量同步: {}", lastSyncTime);
            }
        }

        // 解析时间格式
        String format = extractTimeFormat(variableName);
        return formatDateTime(lastSyncTime, format);
    }

    /**
     * 解析当前时间变量
     * 支持格式：
     * - ${currentTime} - 默认格式
     * - ${currentTime:yyyy-MM-dd} - 指定格式
     */
    private String resolveCurrentTime(String variableName, String defaultValue) {
        LocalDateTime currentTime = LocalDateTime.now();
        String format = extractTimeFormat(variableName);
        return formatDateTime(currentTime, format);
    }

    /**
     * 解析昨天时间变量
     */
    private String resolveYesterday(String variableName, String defaultValue) {
        LocalDateTime yesterday = LocalDateTime.now().minusDays(1);
        String format = extractTimeFormat(variableName);
        return formatDateTime(yesterday, format);
    }

    /**
     * 解析今天时间变量
     */
    private String resolveToday(String variableName, String defaultValue) {
        LocalDateTime today = LocalDateTime.now().withHour(0).withMinute(0).withSecond(0).withNano(0);
        String format = extractTimeFormat(variableName);
        return formatDateTime(today, format);
    }

    /**
     * 解析自定义变量
     */
    private String resolveCustomVariable(WorkflowNode node, String variableName, String defaultValue) {
        // TODO: 实现自定义变量解析逻辑
        // 可以从节点配置、环境变量或配置文件中读取
        logger.debug("解析自定义变量: {}", variableName);
        
        // 暂时返回默认值
        return StringUtils.hasText(defaultValue) ? defaultValue : "";
    }

    /**
     * 解析系统变量
     */
    private String resolveSystemVariable(String variableName, String defaultValue) {
        return switch (variableName) {
            case "nodeId" -> String.valueOf(System.currentTimeMillis()); // 示例
            case "executionId" -> "EXEC_" + System.currentTimeMillis();
            case "batchId" -> "BATCH_" + System.currentTimeMillis();
            default -> StringUtils.hasText(defaultValue) ? defaultValue : "";
        };
    }

    /**
     * 从变量名中提取时间格式
     * 例如：lastSyncTime:yyyy-MM-dd -> yyyy-MM-dd
     */
    private String extractTimeFormat(String variableName) {
        if (variableName.contains(":")) {
            return variableName.substring(variableName.indexOf(":") + 1);
        }
        return "yyyy-MM-dd HH:mm:ss"; // 默认格式
    }

    /**
     * 格式化日期时间
     */
    private String formatDateTime(LocalDateTime dateTime, String format) {
        if ("timestamp".equals(format)) {
            // 返回时间戳（秒）
            return String.valueOf(dateTime.atZone(java.time.ZoneId.systemDefault()).toEpochSecond());
        }
        
        if ("timestampMs".equals(format)) {
            // 返回时间戳（毫秒）
            return String.valueOf(dateTime.atZone(java.time.ZoneId.systemDefault()).toInstant().toEpochMilli());
        }

        DateTimeFormatter formatter = TIME_FORMATTERS.get(format);
        if (formatter == null) {
            // 尝试创建自定义格式
            try {
                formatter = DateTimeFormatter.ofPattern(format);
            } catch (Exception e) {
                logger.warn("无效的时间格式: {}, 使用默认格式", format);
                formatter = TIME_FORMATTERS.get("yyyy-MM-dd HH:mm:ss");
            }
        }

        return dateTime.format(formatter);
    }

    /**
     * 验证变量表达式格式
     */
    public boolean isValidVariableExpression(String expression) {
        if (!StringUtils.hasText(expression)) {
            return true; // 空表达式认为是有效的
        }

        try {
            Matcher matcher = VARIABLE_PATTERN.matcher(expression);
            while (matcher.find()) {
                String variableName = matcher.group(1);
                if (!isValidVariableName(variableName)) {
                    return false;
                }
            }
            return true;
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * 验证变量名是否有效
     */
    private boolean isValidVariableName(String variableName) {
        if (!StringUtils.hasText(variableName)) {
            return false;
        }

        // 检查是否为支持的变量类型
        return variableName.startsWith("lastSyncTime") ||
               variableName.startsWith("currentTime") ||
               variableName.startsWith("yesterday") ||
               variableName.startsWith("today") ||
               variableName.startsWith("custom.") ||
               isSystemVariable(variableName);
    }

    /**
     * 检查是否为系统变量
     */
    private boolean isSystemVariable(String variableName) {
        return "nodeId".equals(variableName) ||
               "executionId".equals(variableName) ||
               "batchId".equals(variableName);
    }

    /**
     * 获取变量帮助信息
     */
    public Map<String, String> getVariableHelp() {
        Map<String, String> help = new HashMap<>();
        help.put("${lastSyncTime}", "上次同步时间，默认格式：yyyy-MM-dd HH:mm:ss");
        help.put("${lastSyncTime:yyyy-MM-dd}", "上次同步时间，指定格式");
        help.put("${lastSyncTime:timestamp}", "上次同步时间戳（秒）");
        help.put("${currentTime}", "当前时间");
        help.put("${yesterday}", "昨天时间");
        help.put("${today}", "今天开始时间");
        help.put("${nodeId}", "节点ID");
        help.put("${executionId}", "执行ID");
        help.put("${custom.variableName}", "自定义变量");
        return help;
    }
}
