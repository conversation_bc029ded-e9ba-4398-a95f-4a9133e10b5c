package com.sevb.admin.core.enums;

/**
 * 工作流执行状态枚举
 * 
 * <AUTHOR>
 */
public enum WorkflowExecutionState {
    
    /**
     * 初始化中
     */
    INITIALIZING(0, "初始化中"),
    
    /**
     * 运行中
     */
    RUNNING(1, "运行中"),
    
    /**
     * 暂停
     */
    PAUSED(2, "暂停"),
    
    /**
     * 成功
     */
    SUCCESS(3, "成功"),
    
    /**
     * 失败
     */
    FAILED(4, "失败"),
    
    /**
     * 终止
     */
    TERMINATED(5, "终止");
    
    private final int code;
    private final String desc;
    
    WorkflowExecutionState(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }
    
    public int getCode() {
        return code;
    }
    
    public String getDesc() {
        return desc;
    }
    
    public static WorkflowExecutionState fromCode(int code) {
        for (WorkflowExecutionState state : values()) {
            if (state.code == code) {
                return state;
            }
        }
        throw new IllegalArgumentException("未知的工作流执行状态码: " + code);
    }
}
