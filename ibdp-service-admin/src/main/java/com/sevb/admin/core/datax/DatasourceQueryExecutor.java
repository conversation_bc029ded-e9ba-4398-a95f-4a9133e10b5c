package com.sevb.admin.core.datax;

import com.sevb.admin.core.model.Datasource;
import com.sevb.admin.core.util.DatasourceConnectionUtil;
import com.sevb.admin.dao.DatasourceDao;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.sql.*;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 数据源查询执行器
 * 负责执行对不同数据源的查询操作
 * 
 * <AUTHOR>
 */
@Component
public class DatasourceQueryExecutor {

    private static final Logger logger = LoggerFactory.getLogger(DatasourceQueryExecutor.class);

    @Autowired
    private DatasourceDao datasourceDao;

    /**
     * 查询表的最大增量值
     * 
     * @param datasourceId 数据源ID
     * @param tableName 表名
     * @param incrementalColumn 增量字段
     * @param incrementalType 增量类型
     * @return 最大增量值
     */
    public String queryMaxIncrementalValue(Long datasourceId, String tableName, 
                                         String incrementalColumn, String incrementalType) {
        if (datasourceId == null || !StringUtils.hasText(tableName) || !StringUtils.hasText(incrementalColumn)) {
            throw new IllegalArgumentException("数据源ID、表名和增量字段不能为空");
        }

        try {
            logger.info("查询最大增量值，数据源ID: {}, 表: {}, 字段: {}", 
                       datasourceId, tableName, incrementalColumn);

            // 获取数据源信息
            Datasource datasource = datasourceDao.load(datasourceId);
            if (datasource == null) {
                throw new IllegalArgumentException("数据源不存在，ID: " + datasourceId);
            }

            // 构建查询SQL
            String sql = buildMaxValueQuery(tableName, incrementalColumn, incrementalType, datasource.getType());
            
            // 执行查询
            String result = executeQuery(datasource, sql);
            
            logger.info("查询最大增量值完成，结果: {}", result);
            return result;

        } catch (Exception e) {
            logger.error("查询最大增量值失败，数据源ID: {}, 表: {}, 字段: {}", 
                        datasourceId, tableName, incrementalColumn, e);
            throw new RuntimeException("查询最大增量值失败: " + e.getMessage(), e);
        }
    }

    /**
     * 验证表和字段是否存在
     */
    public boolean validateTableAndColumn(Long datasourceId, String tableName, String columnName) {
        try {
            Datasource datasource = datasourceDao.load(datasourceId);
            if (datasource == null) {
                return false;
            }

            String sql = buildColumnExistsQuery(tableName, columnName, datasource.getType());
            String result = executeQuery(datasource, sql);
            
            return "1".equals(result);

        } catch (Exception e) {
            logger.error("验证表和字段失败，数据源ID: {}, 表: {}, 字段: {}", 
                        datasourceId, tableName, columnName, e);
            return false;
        }
    }

    /**
     * 查询表的记录数
     */
    public long queryTableRowCount(Long datasourceId, String tableName, String whereCondition) {
        try {
            Datasource datasource = datasourceDao.load(datasourceId);
            if (datasource == null) {
                throw new IllegalArgumentException("数据源不存在");
            }

            String sql = buildRowCountQuery(tableName, whereCondition);
            String result = executeQuery(datasource, sql);
            
            return StringUtils.hasText(result) ? Long.parseLong(result) : 0L;

        } catch (Exception e) {
            logger.error("查询表记录数失败，数据源ID: {}, 表: {}", datasourceId, tableName, e);
            return 0L;
        }
    }

    /**
     * 构建查询最大值的SQL
     */
    private String buildMaxValueQuery(String tableName, String incrementalColumn, 
                                    String incrementalType, String datasourceType) {
        StringBuilder sql = new StringBuilder();
        
        switch (datasourceType.toUpperCase()) {
            case "MYSQL":
                sql.append("SELECT MAX(").append(escapeColumnName(incrementalColumn, "MYSQL")).append(") ");
                sql.append("FROM ").append(escapeTableName(tableName, "MYSQL"));
                break;
                
            case "POSTGRESQL":
                sql.append("SELECT MAX(").append(escapeColumnName(incrementalColumn, "POSTGRESQL")).append(") ");
                sql.append("FROM ").append(escapeTableName(tableName, "POSTGRESQL"));
                break;
                
            case "ORACLE":
                sql.append("SELECT MAX(").append(escapeColumnName(incrementalColumn, "ORACLE")).append(") ");
                sql.append("FROM ").append(escapeTableName(tableName, "ORACLE"));
                break;
                
            case "SQLSERVER":
                sql.append("SELECT MAX([").append(incrementalColumn).append("]) ");
                sql.append("FROM [").append(tableName).append("]");
                break;
                
            default:
                // 默认使用MySQL语法
                sql.append("SELECT MAX(`").append(incrementalColumn).append("`) ");
                sql.append("FROM `").append(tableName).append("`");
                break;
        }

        // 添加WHERE条件过滤NULL值
        sql.append(" WHERE ").append(escapeColumnName(incrementalColumn, datasourceType)).append(" IS NOT NULL");

        return sql.toString();
    }

    /**
     * 构建字段存在性检查SQL
     */
    private String buildColumnExistsQuery(String tableName, String columnName, String datasourceType) {
        return switch (datasourceType.toUpperCase()) {
            case "MYSQL" -> String.format(
                "SELECT 1 FROM information_schema.COLUMNS WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = '%s' AND COLUMN_NAME = '%s'",
                tableName, columnName);
            case "POSTGRESQL" -> String.format(
                "SELECT 1 FROM information_schema.columns WHERE table_name = '%s' AND column_name = '%s'",
                tableName.toLowerCase(), columnName.toLowerCase());
            case "ORACLE" -> String.format(
                "SELECT 1 FROM user_tab_columns WHERE table_name = '%s' AND column_name = '%s'",
                tableName.toUpperCase(), columnName.toUpperCase());
            case "SQLSERVER" -> String.format(
                "SELECT 1 FROM sys.columns c JOIN sys.tables t ON c.object_id = t.object_id WHERE t.name = '%s' AND c.name = '%s'",
                tableName, columnName);
            default -> String.format(
                "SELECT 1 FROM information_schema.COLUMNS WHERE TABLE_NAME = '%s' AND COLUMN_NAME = '%s'",
                tableName, columnName);
        };
    }

    /**
     * 构建行数统计SQL
     */
    private String buildRowCountQuery(String tableName, String whereCondition) {
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT COUNT(*) FROM ").append(tableName);
        
        if (StringUtils.hasText(whereCondition)) {
            sql.append(" WHERE ").append(whereCondition);
        }
        
        return sql.toString();
    }

    /**
     * 转义表名
     */
    private String escapeTableName(String tableName, String datasourceType) {
        return switch (datasourceType.toUpperCase()) {
            case "MYSQL" -> "`" + tableName + "`";
            case "POSTGRESQL" -> "\"" + tableName + "\"";
            case "ORACLE" -> "\"" + tableName.toUpperCase() + "\"";
            case "SQLSERVER" -> "[" + tableName + "]";
            default -> tableName;
        };
    }

    /**
     * 转义字段名
     */
    private String escapeColumnName(String columnName, String datasourceType) {
        return switch (datasourceType.toUpperCase()) {
            case "MYSQL" -> "`" + columnName + "`";
            case "POSTGRESQL" -> "\"" + columnName + "\"";
            case "ORACLE" -> "\"" + columnName.toUpperCase() + "\"";
            case "SQLSERVER" -> "[" + columnName + "]";
            default -> columnName;
        };
    }

    /**
     * 执行查询
     */
    private String executeQuery(Datasource datasource, String sql) throws SQLException {
        logger.debug("执行查询SQL: {}", sql);

        try (Connection connection = DatasourceConnectionUtil.getConnection(datasource);
             PreparedStatement statement = connection.prepareStatement(sql);
             ResultSet resultSet = statement.executeQuery()) {

            if (resultSet.next()) {
                String result = resultSet.getString(1);
                logger.debug("查询结果: {}", result);
                return result;
            }

            return null;

        } catch (Exception e) {
            logger.error("执行查询失败，SQL: {}", sql, e);
            throw new SQLException("查询执行失败: " + e.getMessage(), e);
        }
    }



    /**
     * 测试数据源连接
     */
    public boolean testConnection(Long datasourceId) {
        try {
            Datasource datasource = datasourceDao.load(datasourceId);
            if (datasource == null) {
                return false;
            }

            // 直接使用现有的连接测试方法
            return DatasourceConnectionUtil.testConnection(datasource);

        } catch (Exception e) {
            logger.error("测试数据源连接失败，数据源ID: {}", datasourceId, e);
            return false;
        }
    }
}
