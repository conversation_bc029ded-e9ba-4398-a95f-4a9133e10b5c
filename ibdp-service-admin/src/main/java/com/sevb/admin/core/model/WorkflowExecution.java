package com.sevb.admin.core.model;

import com.sevb.admin.core.enums.NodeExecutionState;
import com.sevb.admin.core.enums.WorkflowExecutionState;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 工作流执行实例
 * 用于调度中心编排模式下的工作流执行状态管理
 * 
 * <AUTHOR>
 */
@Data
@Slf4j
public class WorkflowExecution {
    
    /**
     * 执行实例ID（UUID）
     */
    private String executionId;
    
    /**
     * 工作流ID
     */
    private Long workflowId;
    
    /**
     * 工作流名称
     */
    private String workflowName;

    /**
     * 工作流版本
     */
    private String workflowVersion;

    /**
     * 执行类型
     */
    private String executionType;
    
    /**
     * 编排模式
     */
    private String orchestrationMode;
    
    /**
     * 执行状态
     */
    private WorkflowExecutionState state;
    
    /**
     * 开始时间
     */
    private Long startTime;
    
    /**
     * 结束时间
     */
    private Long endTime;
    
    /**
     * 执行用户
     */
    private String executeUser;
    
    /**
     * 全局参数
     */
    private Map<String, Object> globalParams;
    
    /**
     * 错误信息
     */
    private String errorMessage;
    
    /**
     * 执行进度（百分比）
     */
    private Double progress;
    
    /**
     * 已完成节点数
     */
    private Integer completedNodes;
    
    /**
     * 总节点数
     */
    private Integer totalNodes;
    
    /**
     * 运行中的节点列表
     */
    private Set<String> runningNodes;
    
    /**
     * 失败的节点列表
     */
    private Set<String> failedNodes;
    
    /**
     * 节点执行状态映射
     */
    private Map<String, NodeExecutionState> nodeStates;
    
    /**
     * 节点依赖关系
     */
    private Map<String, List<String>> dependencies;
    
    /**
     * 节点映射
     */
    private Map<String, WorkflowNode> nodeMap;
    
    public WorkflowExecution() {
        this.executionId = UUID.randomUUID().toString();
        this.state = WorkflowExecutionState.INITIALIZING;
        this.startTime = System.currentTimeMillis();
        this.progress = 0.0;
        this.completedNodes = 0;
        this.totalNodes = 0;
        this.runningNodes = ConcurrentHashMap.newKeySet();
        this.failedNodes = ConcurrentHashMap.newKeySet();
        this.nodeStates = new ConcurrentHashMap<>();
        this.globalParams = new HashMap<>();
        this.orchestrationMode = "CENTRAL";
        this.workflowVersion = "1.0"; // 默认版本
    }
    
    /**
     * 初始化节点状态
     */
    public void initializeNodeStates(List<WorkflowNode> nodes, Map<String, List<String>> dependencies) {
        this.totalNodes = nodes.size();
        this.dependencies = dependencies;
        this.nodeMap = new HashMap<>();
        
        for (WorkflowNode node : nodes) {
            this.nodeMap.put(node.getNodeCode(), node);
            this.nodeStates.put(node.getNodeCode(), NodeExecutionState.WAITING);
        }
        
        this.state = WorkflowExecutionState.RUNNING;
        log.info("工作流执行实例初始化完成: executionId={}, totalNodes={}", executionId, totalNodes);
    }
    
    /**
     * 更新节点状态
     */
    public synchronized void updateNodeState(String nodeCode, NodeExecutionState newState) {
        NodeExecutionState oldState = nodeStates.get(nodeCode);
        if (oldState == newState) {
            return;
        }
        
        nodeStates.put(nodeCode, newState);
        
        // 更新运行中和失败节点集合
        switch (newState) {
            case RUNNING:
                runningNodes.add(nodeCode);
                failedNodes.remove(nodeCode);
                break;
            case SUCCESS:
                runningNodes.remove(nodeCode);
                failedNodes.remove(nodeCode);
                completedNodes++;
                break;
            case FAILED:
                runningNodes.remove(nodeCode);
                failedNodes.add(nodeCode);
                break;
            case SKIPPED:
                runningNodes.remove(nodeCode);
                failedNodes.remove(nodeCode);
                completedNodes++;
                break;
            default:
                break;
        }
        
        // 更新进度
        updateProgress();
        
        log.info("节点状态更新: executionId={}, nodeCode={}, {} -> {}", 
                executionId, nodeCode, oldState, newState);
    }
    
    /**
     * 更新执行进度
     */
    private void updateProgress() {
        if (totalNodes > 0) {
            this.progress = (double) completedNodes / totalNodes * 100.0;
        }
    }
    
    /**
     * 检查是否所有节点都已完成
     */
    public boolean isAllNodesCompleted() {
        return completedNodes.equals(totalNodes);
    }
    
    /**
     * 检查是否有失败的节点
     */
    public boolean hasFailedNodes() {
        return !failedNodes.isEmpty();
    }
    
    /**
     * 获取可以执行的节点
     */
    public List<String> getReadyNodes() {
        List<String> readyNodes = new ArrayList<>();
        
        for (String nodeCode : nodeMap.keySet()) {
            NodeExecutionState state = nodeStates.get(nodeCode);
            if (state != NodeExecutionState.WAITING) {
                continue;
            }
            
            // 检查依赖是否都已完成
            List<String> deps = dependencies.get(nodeCode);
            if (deps == null || deps.isEmpty()) {
                // 没有依赖的节点可以直接执行
                readyNodes.add(nodeCode);
            } else {
                // 检查所有依赖是否都已完成
                boolean allDepsCompleted = true;
                for (String dep : deps) {
                    NodeExecutionState depState = nodeStates.get(dep);
                    if (depState != NodeExecutionState.SUCCESS && depState != NodeExecutionState.SKIPPED) {
                        allDepsCompleted = false;
                        break;
                    }
                }
                if (allDepsCompleted) {
                    readyNodes.add(nodeCode);
                }
            }
        }
        
        return readyNodes;
    }
    
    /**
     * 完成工作流执行
     */
    public void complete(boolean success) {
        this.endTime = System.currentTimeMillis();
        this.state = success ? WorkflowExecutionState.SUCCESS : WorkflowExecutionState.FAILED;
        
        log.info("工作流执行完成: executionId={}, success={}, duration={}ms", 
                executionId, success, getDuration());
    }
    
    /**
     * 获取执行时长
     */
    public Long getDuration() {
        if (startTime == null) {
            return null;
        }
        Long end = endTime != null ? endTime : System.currentTimeMillis();
        return end - startTime;
    }
    
    /**
     * 终止工作流执行
     */
    public void terminate(String reason) {
        this.endTime = System.currentTimeMillis();
        this.state = WorkflowExecutionState.TERMINATED;
        this.errorMessage = reason;
        
        log.info("工作流执行被终止: executionId={}, reason={}", executionId, reason);
    }
}
