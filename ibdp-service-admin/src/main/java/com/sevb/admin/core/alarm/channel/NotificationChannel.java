package com.sevb.admin.core.alarm.channel;

import com.sevb.admin.core.alarm.model.NotificationRequest;
import com.sevb.admin.core.alarm.model.SendResult;
import com.sevb.admin.core.alarm.model.AuthConfig;

/**
 * 通知渠道接口
 * <AUTHOR>
 */
public interface NotificationChannel {

    /**
     * 获取通道类型
     */
    String getChannelType();

    /**
     * 获取支持的认证类型
     */
    String getSupportedAuthType();

    /**
     * 发送通知
     */
    SendResult sendNotification(NotificationRequest request);

    /**
     * 验证认证配置
     */
    boolean validateAuthConfig(AuthConfig authConfig);



    /**
     * 是否启用
     */
    default boolean isEnabled() {
        return true;
    }

    /**
     * 获取通道名称
     */
    String getChannelName();

    /**
     * 获取通道描述
     */
    default String getChannelDescription() {
        return getChannelName();
    }
}
