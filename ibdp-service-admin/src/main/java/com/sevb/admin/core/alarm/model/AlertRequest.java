package com.sevb.admin.core.alarm.model;

import lombok.Builder;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * 告警请求模型
 * <AUTHOR>
 */
@Data
@Builder
public class AlertRequest {

    /**
     * 模板编码
     */
    private String templateCode;

    /**
     * 通道类型列表（支持多通道发送）
     */
    private List<String> channelTypes;

    /**
     * 接收人配置
     */
    private Recipients recipients;

    /**
     * 模板变量
     */
    private Map<String, Object> variables;

    /**
     * 来源系统（可选）
     */
    private String sourceSystem;

    /**
     * 业务ID（可选）
     */
    private String businessId;

    /**
     * 优先级（可选）
     */
    private Priority priority;

    /**
     * 指定通道配置名称（可选，不指定则使用默认配置）
     */
    private Map<String, String> channelConfigs;

    /**
     * 接收人配置
     */
    @Data
    @Builder
    public static class Recipients {
        /**
         * 用户ID列表
         */
        private List<String> userIds;

        /**
         * 手机号列表
         */
        private List<String> phones;

        /**
         * 邮箱列表
         */
        private List<String> emails;

        /**
         * 企业微信用户ID列表
         */
        private List<String> wechatUserIds;

        /**
         * 钉钉用户ID列表
         */
        private List<String> dingtalkUserIds;

        /**
         * 飞书用户ID列表
         */
        private List<String> feishuUserIds;

        /**
         * 通用接收人列表（根据通道类型自动匹配）
         */
        private List<String> recipients;
    }

    /**
     * 优先级枚举
     */
    public enum Priority {
        LOW(1, "低"),
        NORMAL(2, "普通"),
        HIGH(3, "高"),
        URGENT(4, "紧急");

        private final int level;
        private final String name;

        Priority(int level, String name) {
            this.level = level;
            this.name = name;
        }

        public int getLevel() {
            return level;
        }

        public String getName() {
            return name;
        }
    }
}
