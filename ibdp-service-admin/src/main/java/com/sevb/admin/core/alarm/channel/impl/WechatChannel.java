package com.sevb.admin.core.alarm.channel.impl;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.sevb.admin.core.alarm.channel.NotificationChannel;
import com.sevb.admin.core.alarm.model.AuthConfig;
import com.sevb.admin.core.alarm.model.NotificationRequest;
import com.sevb.admin.core.alarm.model.SendResult;
import com.sevb.admin.core.alarm.util.AuthConfigUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.*;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;
import org.springframework.web.client.RestTemplate;

import java.util.HashMap;
import java.util.Map;

/**
 * 企业微信通知渠道实现
 * <AUTHOR>
 */
@Component
public class WechatChannel implements NotificationChannel {

    private static final Logger logger = LoggerFactory.getLogger(WechatChannel.class);

    private final RestTemplate restTemplate = new RestTemplate();
    private final ObjectMapper objectMapper = new ObjectMapper();

    @Override
    public String getChannelType() {
        return "WECHAT";
    }

    @Override
    public String getSupportedAuthType() {
        return "WEBHOOK";
    }

    @Override
    public String getChannelName() {
        return "企业微信";
    }

    @Override
    public String getChannelDescription() {
        return "企业微信群机器人通知";
    }

    @Override
    public SendResult sendNotification(NotificationRequest request) {
        if (request == null) {
            return SendResult.failure("请求参数不能为空");
        }

        if (request.getAuthConfig() == null) {
            return SendResult.failure("认证配置不能为空");
        }

        try {
            // 获取Webhook URL
            String webhookUrl = AuthConfigUtil.getAuthDataAsString(request.getAuthConfig(), "webhookUrl");
            if (!StringUtils.hasText(webhookUrl)) {
                return SendResult.failure("Webhook URL不能为空");
            }

            // 构建消息内容
            Map<String, Object> messageBody = buildMessageBody(request);

            // 发送HTTP请求
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            HttpEntity<Map<String, Object>> entity = new HttpEntity<>(messageBody, headers);

            long startTime = System.currentTimeMillis();
            ResponseEntity<String> response = restTemplate.postForEntity(webhookUrl, entity, String.class);
            long endTime = System.currentTimeMillis();

            // 处理响应
            if (response.getStatusCode() == HttpStatus.OK) {
                Map<String, Object> responseData = parseResponse(response.getBody());
                
                if (isSuccessResponse(responseData)) {
                    SendResult result = SendResult.success("企业微信消息发送成功");
                    result.setResponseTime(endTime - startTime);
                    result.setResponseCode(String.valueOf(response.getStatusCodeValue()));
                    result.setResponseData(responseData);
                    return result;
                } else {
                    String errorMsg = getErrorMessage(responseData);
                    return SendResult.failure("企业微信消息发送失败: " + errorMsg, 
                        getErrorCode(responseData), String.valueOf(response.getStatusCodeValue()));
                }
            } else {
                return SendResult.failure("HTTP请求失败", 
                    "HTTP_ERROR", String.valueOf(response.getStatusCodeValue()));
            }

        } catch (Exception e) {
            logger.error("企业微信消息发送异常: requestId={}", request.getRequestId(), e);
            return SendResult.failure("发送异常: " + e.getMessage(), e);
        }
    }



    @Override
    public boolean validateAuthConfig(AuthConfig authConfig) {
        if (authConfig == null) {
            return false;
        }

        String webhookUrl = AuthConfigUtil.getAuthDataAsString(authConfig, "webhookUrl");
        if (!StringUtils.hasText(webhookUrl)) {
            return false;
        }

        // 验证URL格式
        return webhookUrl.startsWith("https://qyapi.weixin.qq.com/cgi-bin/webhook/send");
    }

    /**
     * 构建消息体
     */
    private Map<String, Object> buildMessageBody(NotificationRequest request) throws Exception {
        // 尝试解析content为JSON，如果失败则作为纯文本处理
        try {
            return objectMapper.readValue(request.getContent(), Map.class);
        } catch (Exception e) {
            // 如果不是JSON格式，构建为文本消息
            Map<String, Object> messageBody = new HashMap<>();
            messageBody.put("msgtype", "text");
            
            Map<String, Object> textContent = new HashMap<>();
            textContent.put("content", request.getContent());
            messageBody.put("text", textContent);
            
            return messageBody;
        }
    }

    /**
     * 解析响应
     */
    private Map<String, Object> parseResponse(String responseBody) {
        try {
            if (StringUtils.hasText(responseBody)) {
                return objectMapper.readValue(responseBody, Map.class);
            }
        } catch (Exception e) {
            logger.warn("解析企业微信响应失败: {}", responseBody, e);
        }
        return new HashMap<>();
    }

    /**
     * 判断是否成功响应
     */
    private boolean isSuccessResponse(Map<String, Object> responseData) {
        if (responseData == null || responseData.isEmpty()) {
            return false;
        }

        Object errcode = responseData.get("errcode");
        return errcode != null && "0".equals(errcode.toString());
    }

    /**
     * 获取错误消息
     */
    private String getErrorMessage(Map<String, Object> responseData) {
        if (responseData == null) {
            return "未知错误";
        }

        Object errmsg = responseData.get("errmsg");
        return errmsg != null ? errmsg.toString() : "未知错误";
    }

    /**
     * 获取错误码
     */
    private String getErrorCode(Map<String, Object> responseData) {
        if (responseData == null) {
            return "UNKNOWN";
        }

        Object errcode = responseData.get("errcode");
        return errcode != null ? errcode.toString() : "UNKNOWN";
    }
}
