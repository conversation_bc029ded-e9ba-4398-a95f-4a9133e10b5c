package com.sevb.admin.core.alarm.task;

import com.sevb.admin.core.alarm.config.AlertConfig;
import com.sevb.admin.service.AlertService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

/**
 * 告警重试定时任务
 * <AUTHOR>
 */
@Component
@ConditionalOnProperty(prefix = "alert", name = "enabled", havingValue = "true", matchIfMissing = true)
public class AlertRetryTask {

    private static final Logger logger = LoggerFactory.getLogger(AlertRetryTask.class);

    @Autowired
    private AlertService alertService;

    @Autowired
    private AlertConfig alertConfig;

    /**
     * 处理重试队列
     * 每分钟执行一次
     */
    @Scheduled(fixedRate = 60000)
    public void processRetryQueue() {
        if (!alertConfig.isEnabled()) {
            return;
        }

        try {
            int processedCount = alertService.processRetryQueue();
            if (processedCount > 0) {
                logger.info("处理重试队列完成，处理记录数: {}", processedCount);
            }
        } catch (Exception e) {
            logger.error("处理重试队列失败", e);
        }
    }

    /**
     * 清理历史记录
     * 每天凌晨2点执行
     */
    @Scheduled(cron = "0 0 2 * * ?")
    public void cleanHistoryRecords() {
        if (!alertConfig.isEnabled()) {
            return;
        }

        try {
            // 保留30天的记录
            int cleanedCount = alertService.cleanHistoryRecords(30);
            if (cleanedCount > 0) {
                logger.info("清理历史记录完成，清理记录数: {}", cleanedCount);
            }
        } catch (Exception e) {
            logger.error("清理历史记录失败", e);
        }
    }
}
