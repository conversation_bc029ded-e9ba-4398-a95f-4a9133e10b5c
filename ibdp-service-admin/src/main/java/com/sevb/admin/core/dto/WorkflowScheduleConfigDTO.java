package com.sevb.admin.core.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 工作流调度配置DTO
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@Schema(description = "工作流调度配置")
public class WorkflowScheduleConfigDTO {

    @Schema(description = "工作流ID", example = "1", required = true)
    private Long workflowId;

    @Schema(description = "Cron表达式", example = "0 0 2 * * ?", required = true)
    private String cronExpression;

    @Schema(description = "执行器路由策略", example = "FIRST")
    private String executorRouteStrategy;

    @Schema(description = "阻塞处理策略", example = "SERIAL_EXECUTION")
    private String executorBlockStrategy;

    @Schema(description = "任务执行超时时间(秒)", example = "3600")
    private Integer executorTimeout;

    @Schema(description = "失败重试次数", example = "3")
    private Integer executorFailRetryCount;

    @Schema(description = "调度过期策略", example = "DO_NOTHING")
    private String misfireStrategy;

    @Schema(description = "任务描述", example = "用户数据ETL工作流")
    private String jobDesc;

    @Schema(description = "告警邮箱", example = "<EMAIL>")
    private String alarmEmail;

    /**
     * 执行器路由策略枚举
     */
    public enum ExecutorRouteStrategy {
        FIRST("FIRST", "第一个"),
        LAST("LAST", "最后一个"),
        ROUND("ROUND", "轮询"),
        RANDOM("RANDOM", "随机"),
        CONSISTENT_HASH("CONSISTENT_HASH", "一致性HASH"),
        LEAST_FREQUENTLY_USED("LEAST_FREQUENTLY_USED", "最不经常使用"),
        LEAST_RECENTLY_USED("LEAST_RECENTLY_USED", "最近最久未使用"),
        FAILOVER("FAILOVER", "故障转移"),
        BUSYOVER("BUSYOVER", "忙碌转移"),
        SHARDING_BROADCAST("SHARDING_BROADCAST", "分片广播");

        private final String code;
        private final String name;

        ExecutorRouteStrategy(String code, String name) {
            this.code = code;
            this.name = name;
        }

        public String getCode() {
            return code;
        }

        public String getName() {
            return name;
        }
    }

    /**
     * 阻塞处理策略枚举
     */
    public enum ExecutorBlockStrategy {
        SERIAL_EXECUTION("SERIAL_EXECUTION", "单机串行"),
        DISCARD_LATER("DISCARD_LATER", "丢弃后续调度"),
        COVER_EARLY("COVER_EARLY", "覆盖之前调度");

        private final String code;
        private final String name;

        ExecutorBlockStrategy(String code, String name) {
            this.code = code;
            this.name = name;
        }

        public String getCode() {
            return code;
        }

        public String getName() {
            return name;
        }
    }

    /**
     * 调度过期策略枚举
     */
    public enum MisfireStrategy {
        DO_NOTHING("DO_NOTHING", "忽略"),
        FIRE_ONCE_NOW("FIRE_ONCE_NOW", "立即执行一次");

        private final String code;
        private final String name;

        MisfireStrategy(String code, String name) {
            this.code = code;
            this.name = name;
        }

        public String getCode() {
            return code;
        }

        public String getName() {
            return name;
        }
    }
}
