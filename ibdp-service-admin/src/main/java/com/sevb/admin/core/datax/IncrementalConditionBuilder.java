package com.sevb.admin.core.datax;

import com.fasterxml.jackson.databind.JsonNode;
import com.sevb.admin.core.model.DataxSyncState;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * 增量条件构建器
 * 负责根据增量配置构建WHERE条件
 * 
 * <AUTHOR>
 */
@Component
public class IncrementalConditionBuilder {

    private static final Logger logger = LoggerFactory.getLogger(IncrementalConditionBuilder.class);

    /**
     * 构建增量WHERE条件
     * 
     * @param incrementalColumn 增量字段
     * @param incrementalValue 增量值
     * @param syncStrategy 同步策略配置
     * @return WHERE条件字符串
     */
    public String buildIncrementalWhere(String incrementalColumn, String incrementalValue, JsonNode syncStrategy) {
        if (!StringUtils.hasText(incrementalColumn)) {
            throw new IllegalArgumentException("增量字段不能为空");
        }

        try {
            logger.debug("构建增量WHERE条件，字段: {}, 值: {}", incrementalColumn, incrementalValue);

            List<String> conditions = new ArrayList<>();

            // 检查是否为第一次同步
            boolean isFirstSync = isFirstTimeSync(incrementalValue);

            // 构建主要增量条件（第一次同步时跳过）
            if (!isFirstSync) {
                String mainCondition = buildMainIncrementalCondition(incrementalColumn, incrementalValue, syncStrategy);
                if (StringUtils.hasText(mainCondition)) {
                    conditions.add(mainCondition);
                    logger.debug("添加增量时间条件: {}", mainCondition);
                }
            } else {
                logger.info("第一次同步，跳过增量时间条件，但保留其他过滤条件");
            }

            // 添加自定义条件（无论是否第一次同步都要保留）
            String customCondition = getCustomCondition(syncStrategy);
            if (StringUtils.hasText(customCondition)) {
                conditions.add("(" + customCondition + ")");
                logger.debug("添加自定义条件: {}", customCondition);
            }

            // 添加数据有效性条件（无论是否第一次同步都要保留）
            String validityCondition = buildValidityCondition(incrementalColumn, syncStrategy);
            if (StringUtils.hasText(validityCondition)) {
                conditions.add(validityCondition);
                logger.debug("添加数据有效性条件: {}", validityCondition);
            }

            // 组合所有条件
            String result = String.join(" AND ", conditions);
            
            logger.info("构建增量WHERE条件完成: {}", result);
            return result;

        } catch (Exception e) {
            logger.error("构建增量WHERE条件失败", e);
            throw new RuntimeException("构建增量WHERE条件失败: " + e.getMessage(), e);
        }
    }

    /**
     * 构建主要增量条件
     */
    private String buildMainIncrementalCondition(String incrementalColumn, String incrementalValue, JsonNode syncStrategy) {
        // 获取增量类型
        String incrementalType = syncStrategy.path("incrementalType").asText("TIMESTAMP");

        // 获取比较操作符
        String operator = getComparisonOperator(syncStrategy);

        // 处理增量值
        String formattedValue = formatIncrementalValue(incrementalValue, incrementalType);

        if (!StringUtils.hasText(formattedValue)) {
            logger.warn("增量值为空，跳过主要增量条件");
            return null;
        }

        // 检查是否为第一次同步（使用1970-01-01作为标识）
        if (isFirstTimeSync(incrementalValue)) {
            logger.info("检测到第一次同步，跳过增量条件进行全量同步");
            return null;
        }

        return String.format("%s %s %s", incrementalColumn, operator, formattedValue);
    }

    /**
     * 判断是否为第一次同步
     */
    private boolean isFirstTimeSync(String incrementalValue) {
        if (!StringUtils.hasText(incrementalValue)) {
            return true;
        }

        // 检查是否为1970-01-01的各种格式
        return incrementalValue.equals("1970-01-01 00:00:00") ||
               incrementalValue.equals("1970-01-01") ||
               incrementalValue.equals("19700101") ||
               incrementalValue.equals("0") ||
               incrementalValue.equals("1970-01-01T00:00:00");
    }

    /**
     * 获取比较操作符
     */
    private String getComparisonOperator(JsonNode syncStrategy) {
        String operator = syncStrategy.path("operator").asText(">");
        
        // 验证操作符
        return switch (operator.toUpperCase()) {
            case ">", "GT" -> ">";
            case ">=", "GTE" -> ">=";
            case "<", "LT" -> "<";
            case "<=", "LTE" -> "<=";
            case "=", "EQ" -> "=";
            case "!=", "NE" -> "!=";
            default -> ">"; // 默认使用大于
        };
    }

    /**
     * 格式化增量值
     */
    private String formatIncrementalValue(String incrementalValue, String incrementalType) {
        if (!StringUtils.hasText(incrementalValue)) {
            return null;
        }

        return switch (incrementalType.toUpperCase()) {
            case "TIMESTAMP", "DATETIME" -> formatTimestampValue(incrementalValue);
            case "DATE" -> formatDateValue(incrementalValue);
            case "PRIMARY_KEY", "NUMERIC" -> formatNumericValue(incrementalValue);
            case "STRING" -> formatStringValue(incrementalValue);
            default -> formatTimestampValue(incrementalValue); // 默认按时间戳处理
        };
    }

    /**
     * 格式化时间戳值
     */
    private String formatTimestampValue(String value) {
        // 检查是否为纯数字（时间戳）
        if (value.matches("\\d+")) {
            // 时间戳格式，需要转换
            if (value.length() == 10) {
                // 秒级时间戳
                return "FROM_UNIXTIME(" + value + ")";
            } else if (value.length() == 13) {
                // 毫秒级时间戳
                return "FROM_UNIXTIME(" + value + "/1000)";
            }
        }
        
        // 字符串格式的时间，添加引号
        return "'" + value + "'";
    }

    /**
     * 格式化日期值
     */
    private String formatDateValue(String value) {
        if (value.matches("\\d{4}-\\d{2}-\\d{2}")) {
            return "'" + value + "'";
        }
        
        if (value.matches("\\d{8}")) {
            // yyyyMMdd格式
            return "'" + value.substring(0, 4) + "-" + value.substring(4, 6) + "-" + value.substring(6, 8) + "'";
        }
        
        return "'" + value + "'";
    }

    /**
     * 格式化数值
     */
    private String formatNumericValue(String value) {
        // 验证是否为数字
        try {
            Long.parseLong(value);
            return value; // 数字不需要引号
        } catch (NumberFormatException e) {
            logger.warn("增量值不是有效数字: {}", value);
            return "0";
        }
    }

    /**
     * 格式化字符串值
     */
    private String formatStringValue(String value) {
        // 字符串值需要添加引号，并转义特殊字符
        String escaped = value.replace("'", "''").replace("\\", "\\\\");
        return "'" + escaped + "'";
    }

    /**
     * 获取自定义条件
     */
    private String getCustomCondition(JsonNode syncStrategy) {
        return syncStrategy.path("customCondition").asText();
    }

    /**
     * 构建数据有效性条件
     */
    private String buildValidityCondition(String incrementalColumn, JsonNode syncStrategy) {
        List<String> validityConditions = new ArrayList<>();

        // 添加非空条件
        if (syncStrategy.path("excludeNull").asBoolean(true)) {
            validityConditions.add(incrementalColumn + " IS NOT NULL");
        }

        // 添加非空字符串条件
        if (syncStrategy.path("excludeEmpty").asBoolean(false)) {
            validityConditions.add(incrementalColumn + " != ''");
        }

        // 添加范围条件
        String minValue = syncStrategy.path("minValue").asText();
        if (StringUtils.hasText(minValue)) {
            String incrementalType = syncStrategy.path("incrementalType").asText("TIMESTAMP");
            String formattedMinValue = formatIncrementalValue(minValue, incrementalType);
            validityConditions.add(incrementalColumn + " >= " + formattedMinValue);
        }

        String maxValue = syncStrategy.path("maxValue").asText();
        if (StringUtils.hasText(maxValue)) {
            String incrementalType = syncStrategy.path("incrementalType").asText("TIMESTAMP");
            String formattedMaxValue = formatIncrementalValue(maxValue, incrementalType);
            validityConditions.add(incrementalColumn + " <= " + formattedMaxValue);
        }

        return validityConditions.isEmpty() ? null : String.join(" AND ", validityConditions);
    }

    /**
     * 构建复合增量条件（支持多个增量字段）
     */
    public String buildCompositeIncrementalWhere(List<IncrementalField> fields, JsonNode syncStrategy) {
        if (fields == null || fields.isEmpty()) {
            throw new IllegalArgumentException("增量字段列表不能为空");
        }

        List<String> conditions = new ArrayList<>();

        for (IncrementalField field : fields) {
            String condition = buildMainIncrementalCondition(
                field.getColumnName(), 
                field.getValue(), 
                syncStrategy
            );
            if (StringUtils.hasText(condition)) {
                conditions.add("(" + condition + ")");
            }
        }

        // 添加自定义条件
        String customCondition = getCustomCondition(syncStrategy);
        if (StringUtils.hasText(customCondition)) {
            conditions.add("(" + customCondition + ")");
        }

        String logicOperator = syncStrategy.path("logicOperator").asText("AND");
        return String.join(" " + logicOperator + " ", conditions);
    }

    /**
     * 验证WHERE条件的有效性
     */
    public boolean isValidWhereCondition(String whereCondition) {
        if (!StringUtils.hasText(whereCondition)) {
            return true; // 空条件认为是有效的
        }

        try {
            // 基本的SQL注入检查
            String upperCondition = whereCondition.toUpperCase();
            
            // 检查危险关键字
            String[] dangerousKeywords = {
                "DROP", "DELETE", "INSERT", "UPDATE", "CREATE", "ALTER", 
                "TRUNCATE", "EXEC", "EXECUTE", "UNION", "SCRIPT"
            };
            
            for (String keyword : dangerousKeywords) {
                if (upperCondition.contains(keyword)) {
                    logger.warn("WHERE条件包含危险关键字: {}", keyword);
                    return false;
                }
            }

            // 检查括号匹配
            int openCount = 0;
            for (char c : whereCondition.toCharArray()) {
                if (c == '(') openCount++;
                else if (c == ')') openCount--;
                if (openCount < 0) return false;
            }
            
            return openCount == 0;

        } catch (Exception e) {
            logger.error("验证WHERE条件失败", e);
            return false;
        }
    }

    /**
     * 增量字段定义
     */
    public static class IncrementalField {
        private String columnName;
        private String value;
        private String type;

        public IncrementalField(String columnName, String value, String type) {
            this.columnName = columnName;
            this.value = value;
            this.type = type;
        }

        // Getters and Setters
        public String getColumnName() { return columnName; }
        public void setColumnName(String columnName) { this.columnName = columnName; }
        
        public String getValue() { return value; }
        public void setValue(String value) { this.value = value; }
        
        public String getType() { return type; }
        public void setType(String type) { this.type = type; }
    }
}
