package com.sevb.admin.core.alarm.model;

import lombok.Data;

import java.util.Map;

/**
 * 发送结果模型
 * <AUTHOR>
 */
@Data
public class SendResult {

    /**
     * 是否成功
     */
    private boolean success;

    /**
     * 响应消息
     */
    private String message;

    /**
     * 响应状态码
     */
    private String responseCode;

    /**
     * 响应数据
     */
    private Map<String, Object> responseData;

    /**
     * 错误码
     */
    private String errorCode;

    /**
     * 异常信息
     */
    private String exception;

    /**
     * 响应时间（毫秒）
     */
    private Long responseTime;

    /**
     * 创建成功结果
     */
    public static SendResult success(String message) {
        SendResult result = new SendResult();
        result.setSuccess(true);
        result.setMessage(message);
        result.setResponseCode("200");
        return result;
    }

    /**
     * 创建成功结果（带响应数据）
     */
    public static SendResult success(String message, Map<String, Object> responseData) {
        SendResult result = success(message);
        result.setResponseData(responseData);
        return result;
    }

    /**
     * 创建失败结果
     */
    public static SendResult failure(String message) {
        SendResult result = new SendResult();
        result.setSuccess(false);
        result.setMessage(message);
        result.setResponseCode("500");
        return result;
    }

    /**
     * 创建失败结果（带错误码）
     */
    public static SendResult failure(String message, String errorCode) {
        SendResult result = failure(message);
        result.setErrorCode(errorCode);
        return result;
    }

    /**
     * 创建失败结果（带异常）
     */
    public static SendResult failure(String message, Exception exception) {
        SendResult result = failure(message);
        result.setException(exception.getMessage());
        return result;
    }

    /**
     * 创建失败结果（完整信息）
     */
    public static SendResult failure(String message, String errorCode, String responseCode) {
        SendResult result = new SendResult();
        result.setSuccess(false);
        result.setMessage(message);
        result.setErrorCode(errorCode);
        result.setResponseCode(responseCode);
        return result;
    }
}
