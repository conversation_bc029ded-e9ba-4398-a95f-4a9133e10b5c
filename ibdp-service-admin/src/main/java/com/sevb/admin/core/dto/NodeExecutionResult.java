package com.sevb.admin.core.dto;

import lombok.Data;

import java.util.Map;

/**
 * 节点执行结果
 * 用于执行器向调度中心回调节点执行结果
 * 
 * <AUTHOR>
 */
@Data
public class NodeExecutionResult {
    
    /**
     * 工作流执行ID
     */
    private String executionId;
    
    /**
     * 节点编码
     */
    private String nodeCode;
    
    /**
     * 是否成功
     */
    private boolean success;
    
    /**
     * 错误信息
     */
    private String errorMessage;
    
    /**
     * 输出参数
     */
    private Map<String, Object> outputParams;
    
    /**
     * 开始时间
     */
    private Long startTime;
    
    /**
     * 结束时间
     */
    private Long endTime;
    
    /**
     * 执行时长（毫秒）
     */
    private Long duration;
    
    /**
     * 执行器地址
     */
    private String executorAddress;
    
    /**
     * 回调URL
     */
    private String callbackUrl;
    
    /**
     * 执行日志
     */
    private String logContent;
    
    /**
     * XXL-JOB任务ID
     */
    private Integer jobId;
    
    /**
     * XXL-JOB日志ID
     */
    private Long jobLogId;
    
    /**
     * 重试次数
     */
    private Integer retryCount;
    
    /**
     * 构造成功结果
     */
    public static NodeExecutionResult success(String executionId, String nodeCode) {
        NodeExecutionResult result = new NodeExecutionResult();
        result.setExecutionId(executionId);
        result.setNodeCode(nodeCode);
        result.setSuccess(true);
        result.setEndTime(System.currentTimeMillis());
        return result;
    }
    
    /**
     * 构造失败结果
     */
    public static NodeExecutionResult failure(String executionId, String nodeCode, String errorMessage) {
        NodeExecutionResult result = new NodeExecutionResult();
        result.setExecutionId(executionId);
        result.setNodeCode(nodeCode);
        result.setSuccess(false);
        result.setErrorMessage(errorMessage);
        result.setEndTime(System.currentTimeMillis());
        return result;
    }
}
