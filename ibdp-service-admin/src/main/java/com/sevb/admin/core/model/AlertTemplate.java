package com.sevb.admin.core.model;

import lombok.Data;
import java.util.Date;

/**
 * 告警模板实体类
 * <AUTHOR>
 */
@Data
public class AlertTemplate {

    private Long id;                    // 主键ID
    private String templateCode;        // 模板编码
    private String templateName;        // 模板名称
    private String channelType;         // 通道类型：WECHAT,FEISHU,DINGTALK,EMAIL
    private String templateContent;     // 模板内容JSON
    private String variables;           // 模板变量说明JSON
    private Integer enabled;            // 是否启用：0-禁用，1-启用
    private String description;         // 模板描述
    private Date createTime;            // 创建时间
    private Date updateTime;            // 更新时间
    private String createUser;          // 创建人
    private String updateUser;          // 更新人
    private Integer deleted;            // 删除标记：0-未删除，1-已删除

    // 通道类型枚举
    public enum ChannelType {
        WECHAT("WECHAT", "企业微信"),
        FEISHU("FEISHU", "飞书"),
        DINGTALK("DINGTALK", "钉钉"),
        EMAIL("EMAIL", "邮件");

        private final String code;
        private final String name;

        ChannelType(String code, String name) {
            this.code = code;
            this.name = name;
        }

        public String getCode() {
            return code;
        }

        public String getName() {
            return name;
        }

        public static ChannelType fromCode(String code) {
            for (ChannelType type : values()) {
                if (type.code.equals(code)) {
                    return type;
                }
            }
            throw new IllegalArgumentException("Unknown channel type: " + code);
        }
    }

    // 启用状态枚举
    public enum EnabledStatus {
        DISABLED(0, "禁用"),
        ENABLED(1, "启用");

        private final Integer code;
        private final String name;

        EnabledStatus(Integer code, String name) {
            this.code = code;
            this.name = name;
        }

        public Integer getCode() {
            return code;
        }

        public String getName() {
            return name;
        }
    }
}
