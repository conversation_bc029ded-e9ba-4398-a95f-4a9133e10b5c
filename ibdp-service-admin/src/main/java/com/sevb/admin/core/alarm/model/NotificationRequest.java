package com.sevb.admin.core.alarm.model;

import lombok.Builder;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * 通知请求模型
 * <AUTHOR>
 */
@Data
@Builder
public class NotificationRequest {

    /**
     * 请求ID
     */
    private String requestId;

    /**
     * 通道类型
     */
    private String channelType;

    /**
     * 接收人列表
     */
    private List<String> recipients;

    /**
     * 消息标题
     */
    private String title;

    /**
     * 消息内容
     */
    private String content;

    /**
     * 认证配置
     */
    private AuthConfig authConfig;

    /**
     * 通道特定配置
     */
    private Map<String, Object> channelConfig;

    /**
     * 模板变量
     */
    private Map<String, Object> variables;

    /**
     * 来源系统
     */
    private String sourceSystem;

    /**
     * 业务ID
     */
    private String businessId;

    /**
     * 优先级
     */
    private Priority priority;

    /**
     * 优先级枚举
     */
    public enum Priority {
        LOW(1, "低"),
        NORMAL(2, "普通"),
        HIGH(3, "高"),
        URGENT(4, "紧急");

        private final int level;
        private final String name;

        Priority(int level, String name) {
            this.level = level;
            this.name = name;
        }

        public int getLevel() {
            return level;
        }

        public String getName() {
            return name;
        }
    }
}
