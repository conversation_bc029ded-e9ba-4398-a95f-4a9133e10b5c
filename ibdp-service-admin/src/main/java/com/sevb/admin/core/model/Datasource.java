package com.sevb.admin.core.model;

import lombok.Data;
import java.util.Date;

/**
 * 数据源实体类
 * <AUTHOR>
 */
@Data
public class Datasource {

    private Long id;
    private String name;                // 数据源名称
    private String type;                // 数据源类型：MYSQL、POSTGRESQL、ORACLE、DORIS、HIVE
    private String host;                // 主机地址
    private Integer port;               // 端口号
    private String databaseName;        // 数据库名称
    private String username;            // 用户名
    private String password;            // 密码（加密存储）
    private String connectionParams;    // 连接参数（JSON格式）
    private String description;         // 描述
    private Integer status;             // 状态：0-禁用，1-启用
    private Integer testStatus;         // 测试状态：0-未测试，1-连接成功，2-连接失败
    private String testMessage;         // 测试结果信息
    private Date testTime;              // 最后测试时间
    private Date createTime;            // 创建时间
    private Date updateTime;            // 更新时间
    private String createUser;          // 创建人
    private String updateUser;          // 更新人
    private Integer deleted;            // 删除标记：0-未删除，1-已删除

    // 数据源类型枚举
    public enum DatasourceType {
        MYSQL("MYSQL", "MySQL数据库", "com.mysql.cj.jdbc.Driver", "jdbc:mysql://{host}:{port}/{database}"),
        POSTGRESQL("POSTGRESQL", "PostgreSQL数据库", "org.postgresql.Driver", "jdbc:postgresql://{host}:{port}/{database}"),
        ORACLE("ORACLE", "Oracle数据库", "oracle.jdbc.driver.OracleDriver", "jdbc:oracle:thin:@{host}:{port}:{database}"),
        DORIS("DORIS", "Apache Doris", "com.mysql.cj.jdbc.Driver", "jdbc:mysql://{host}:{port}/{database}"),
        HIVE("HIVE", "Apache Hive", "org.apache.hive.jdbc.HiveDriver", "jdbc:hive2://{host}:{port}/{database}");

        private final String code;
        private final String name;
        private final String driverClass;
        private final String urlTemplate;

        DatasourceType(String code, String name, String driverClass, String urlTemplate) {
            this.code = code;
            this.name = name;
            this.driverClass = driverClass;
            this.urlTemplate = urlTemplate;
        }

        public String getCode() {
            return code;
        }

        public String getName() {
            return name;
        }

        public String getDriverClass() {
            return driverClass;
        }

        public String getUrlTemplate() {
            return urlTemplate;
        }

        public static DatasourceType getByCode(String code) {
            for (DatasourceType type : values()) {
                if (type.getCode().equals(code)) {
                    return type;
                }
            }
            return null;
        }
    }

    // 状态枚举
    public enum Status {
        DISABLED(0, "禁用"),
        ENABLED(1, "启用");

        private final Integer code;
        private final String name;

        Status(Integer code, String name) {
            this.code = code;
            this.name = name;
        }

        public Integer getCode() {
            return code;
        }

        public String getName() {
            return name;
        }
    }

    // 测试状态枚举
    public enum TestStatus {
        NOT_TESTED(0, "未测试"),
        SUCCESS(1, "连接成功"),
        FAILED(2, "连接失败");

        private final Integer code;
        private final String name;

        TestStatus(Integer code, String name) {
            this.code = code;
            this.name = name;
        }

        public Integer getCode() {
            return code;
        }

        public String getName() {
            return name;
        }
    }
}
