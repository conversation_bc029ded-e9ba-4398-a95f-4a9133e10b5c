package com.sevb.admin.core.model;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.Getter;

import java.util.Date;

/**
 * 工作流实体类
 * <AUTHOR>
 */
@Data
@Schema(description = "工作流实体")
public class Workflow {

    @Schema(description = "工作流ID", example = "1")
    private Long id;

    @Schema(description = "项目ID", example = "1", required = true)
    private Long projectId;

    @Schema(description = "工作流名称", example = "用户数据ETL流程", required = true)
    private String name;

    @Schema(description = "工作流编码", example = "USER_DATA_ETL", required = true)
    private String code;

    @Schema(description = "工作流描述", example = "从多个数据源提取用户数据，进行清洗和转换")
    private String description;

    @Schema(description = "标签，逗号分隔", example = "数据同步,ETL,用户数据")
    private String tags;

    @Schema(description = "分类", example = "ETL", allowableValues = {"ETL", "REPORT", "ANALYSIS", "SYNC"})
    private String category;

    @Schema(description = "版本号", example = "1")
    private Integer version;



    @Schema(description = "全局参数JSON", example = "{\"batchDate\":\"${yyyyMMdd}\"}")
    private String globalParams;

    @Schema(description = "告警类型", example = "FAILURE", allowableValues = {"NONE", "SUCCESS", "FAILURE", "ALL"})
    private String warningType;

    @Schema(description = "告警组ID", example = "1")
    private Long warningGroupId;

    @Schema(description = "XXL-JOB任务ID", example = "1001")
    private Integer jobId;

    @Schema(description = "编排模式", example = "EXECUTOR", allowableValues = {"EXECUTOR", "CENTRAL"})
    private String orchestrationMode;

    @Schema(description = "最后执行ID", example = "exec_20231201_001")
    private String lastExecutionId;

    @Schema(description = "执行次数", example = "10")
    private Long executionCount;

    @Schema(description = "成功次数", example = "8")
    private Long successCount;

    @Schema(description = "失败次数", example = "2")
    private Long failureCount;

    @Schema(description = "Cron表达式", example = "0 0 2 * * ?")
    private String cronExpression;

    @Schema(description = "最后触发时间")
    private Date lastTriggerTime;

    @Schema(description = "状态", example = "1", allowableValues = {"0", "1", "2", "3"})
    private Integer status;

    @Schema(description = "创建时间")
    private Date createTime;

    @Schema(description = "更新时间")
    private Date updateTime;

    @Schema(description = "创建人", example = "admin")
    private String createUser;

    @Schema(description = "更新人", example = "admin")
    private String updateUser;

    @Schema(description = "删除标记：0-未删除，1-已删除", example = "0")
    private Integer deleted;

    /**
     * 构造函数 - 初始化默认值
     */
    public Workflow() {
        this.orchestrationMode = "CENTRAL"; // 统一使用调度中心编排
        this.executionCount = 0L;
        this.successCount = 0L;
        this.failureCount = 0L;
    }

    /**
     * 工作流分类枚举
     */
    @Getter
    public enum Category {
        ETL("ETL", "数据处理"),
        REPORT("REPORT", "报表生成"),
        ANALYSIS("ANALYSIS", "数据分析"),
        SYNC("SYNC", "数据同步");

        private final String code;
        private final String name;

        Category(String code, String name) {
            this.code = code;
            this.name = name;
        }

        public static Category getByCode(String code) {
            for (Category category : values()) {
                if (category.getCode().equals(code)) {
                    return category;
                }
            }
            return null;
        }
    }

    /**
     * 工作流状态枚举
     */
    @Getter
    public enum Status {
        DRAFT(0, "草稿"),
        PUBLISHED(1, "已发布"),  // 保留发布状态，用于审核
        ONLINE(2, "已上线"),
        OFFLINE(3, "已下线");

        private final Integer code;
        private final String name;

        Status(Integer code, String name) {
            this.code = code;
            this.name = name;
        }

        public static Status getByCode(Integer code) {
            for (Status status : values()) {
                if (status.getCode().equals(code)) {
                    return status;
                }
            }
            return null;
        }
    }

    /**
     * 告警类型枚举
     */
    @Getter
    public enum WarningType {
        NONE("NONE", "不告警"),
        SUCCESS("SUCCESS", "成功告警"),
        FAILURE("FAILURE", "失败告警"),
        ALL("ALL", "全部告警");

        private final String code;
        private final String name;

        WarningType(String code, String name) {
            this.code = code;
            this.name = name;
        }

        public static WarningType getByCode(String code) {
            for (WarningType warningType : values()) {
                if (warningType.getCode().equals(code)) {
                    return warningType;
                }
            }
            return null;
        }
    }

    /**
     * 判断工作流是否可以编辑
     * 已上线的工作流不允许编辑，需要先下线
     *
     * @return true-可编辑，false-不可编辑
     */
    public boolean isEditable() {
        return !Status.ONLINE.getCode().equals(this.status);
    }

    /**
     * 判断工作流是否可以删除
     * 只有草稿状态的工作流可以删除
     *
     * @return true-可删除，false-不可删除
     */
    public boolean isDeletable() {
        return this.status == null ||
               Status.DRAFT.getCode().equals(this.status);
    }

    /**
     * 判断工作流是否可以发布
     * 只有草稿状态的工作流可以发布
     *
     * @return true-可发布，false-不可发布
     */
    public boolean isPublishable() {
        return this.status == null ||
               Status.DRAFT.getCode().equals(this.status);
    }

    /**
     * 判断工作流是否可以上线
     * 已发布状态和已下线状态的工作流可以上线
     *
     * @return true-可上线，false-不可上线
     */
    public boolean isOnlineable() {
        return Status.PUBLISHED.getCode().equals(this.status) ||
               Status.OFFLINE.getCode().equals(this.status);
    }

    /**
     * 判断工作流是否已上线
     *
     * @return true-已上线，false-未上线
     */
    public boolean isOnline() {
        return Status.ONLINE.getCode().equals(this.status);
    }

    /**
     * 获取状态显示名称
     *
     * @return 状态名称
     */
    public String getStatusName() {
        Status statusEnum = Status.getByCode(this.status);
        return statusEnum != null ? statusEnum.getName() : "未知";
    }
}
