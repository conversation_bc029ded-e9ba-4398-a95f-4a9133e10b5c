package com.sevb.admin.core.dto;

import lombok.Data;

import java.util.Map;

/**
 * 节点执行参数
 * 用于调度中心向执行器传递节点执行信息
 * 
 * <AUTHOR>
 */
@Data
public class NodeExecutionParam {
    
    /**
     * 工作流执行ID
     */
    private String executionId;
    
    /**
     * 工作流ID
     */
    private Long workflowId;
    
    /**
     * 节点编码
     */
    private String nodeCode;
    
    /**
     * 节点名称
     */
    private String nodeName;
    
    /**
     * 节点类型
     */
    private String nodeType;
    
    /**
     * 节点配置JSON
     */
    private String nodeConfig;
    
    /**
     * 全局参数
     */
    private Map<String, Object> globalParams;
    
    /**
     * 输入参数
     */
    private Map<String, Object> inputParams;
    
    /**
     * 回调URL
     */
    private String callbackUrl;
    
    /**
     * 超时时间（秒）
     */
    private Long timeout;
    
    /**
     * 重试次数
     */
    private Integer retryTimes;
    
    /**
     * 执行用户
     */
    private String executeUser;
    
    /**
     * 编排模式
     */
    private String orchestrationMode;
}
