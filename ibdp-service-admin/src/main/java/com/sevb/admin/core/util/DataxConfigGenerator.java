package com.sevb.admin.core.util;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.sevb.admin.core.datax.IncrementalConditionBuilder;
import com.sevb.admin.core.datax.VariableResolver;
import com.sevb.admin.core.model.Datasource;
import com.sevb.admin.core.model.WorkflowNode;
import com.sevb.admin.service.DatasourceService;
import com.sevb.admin.service.DataxSyncStateService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

/**
 * DataX配置生成器
 * 将前端的DataX节点配置转换为标准的DataX JSON格式
 * 
 * <AUTHOR>
 */
@Slf4j
@Component
public class DataxConfigGenerator {

    @Autowired
    private DatasourceService datasourceService;

    @Autowired
    private DataxSyncStateService syncStateService;

    @Autowired
    private VariableResolver variableResolver;

    @Autowired
    private IncrementalConditionBuilder conditionBuilder;

    private final ObjectMapper objectMapper = new ObjectMapper();

    /**
     * 根据工作流节点生成DataX JSON配置（支持增量同步）
     *
     * @param node 工作流节点
     * @return DataX标准JSON配置
     */
    public String generateDataxConfig(WorkflowNode node) {
        if (node == null) {
            throw new IllegalArgumentException("工作流节点不能为空");
        }

        try {
            log.info("开始生成DataX配置，节点ID: {}, 节点名称: {}", node.getId(), node.getNodeName());

            JsonNode nodeConfig = objectMapper.readTree(node.getConfigParams());

            // 检查是否为增量同步
            if (isIncrementalSync(nodeConfig)) {
                return generateIncrementalDataxConfig(node, nodeConfig);
            } else {
                return generateDataxConfig(node.getConfigParams());
            }

        } catch (Exception e) {
            log.error("生成DataX配置失败，节点ID: {}", node.getId(), e);
            throw new RuntimeException("生成DataX配置失败: " + e.getMessage(), e);
        }
    }

    /**
     * 根据节点配置生成DataX JSON配置
     *
     * @param nodeConfig 节点配置JSON字符串
     * @return DataX标准JSON配置
     */
    public String generateDataxConfig(String nodeConfig) {
        try {
            JsonNode config = objectMapper.readTree(nodeConfig);
            
            // 创建DataX配置根节点
            ObjectNode dataxConfig = objectMapper.createObjectNode();
            ObjectNode job = objectMapper.createObjectNode();
            dataxConfig.set("job", job);
            
            // 设置全局配置
            ObjectNode setting = createJobSetting(config);
            job.set("setting", setting);
            
            // 创建内容配置
            ArrayNode content = objectMapper.createArrayNode();
            ObjectNode contentItem = objectMapper.createObjectNode();
            
            // 配置Reader
            ObjectNode reader = createReader(config);
            contentItem.set("reader", reader);
            
            // 配置Writer
            ObjectNode writer = createWriter(config);
            contentItem.set("writer", writer);
            
            content.add(contentItem);
            job.set("content", content);
            
            return objectMapper.writerWithDefaultPrettyPrinter().writeValueAsString(dataxConfig);
            
        } catch (Exception e) {
            log.error("生成DataX配置失败", e);
            throw new RuntimeException("生成DataX配置失败: " + e.getMessage());
        }
    }

    /**
     * 创建Job级别设置
     */
    private ObjectNode createJobSetting(JsonNode config) {
        ObjectNode setting = objectMapper.createObjectNode();
        
        // 速度设置
        ObjectNode speed = objectMapper.createObjectNode();
        speed.put("channel", 3); // 默认3个并发通道
        
        // 如果配置中有速度设置，使用配置的值
        if (config.has("speed") && config.get("speed").has("channel")) {
            speed.put("channel", config.get("speed").get("channel").asInt());
        }
        
        setting.set("speed", speed);
        
        // 错误限制设置
        ObjectNode errorLimit = objectMapper.createObjectNode();
        errorLimit.put("record", 0);
        errorLimit.put("percentage", 0.02);
        setting.set("errorLimit", errorLimit);
        
        return setting;
    }

    /**
     * 创建Reader配置
     */
    private ObjectNode createReader(JsonNode config) {
        ObjectNode reader = objectMapper.createObjectNode();
        JsonNode sourceConfig = config.get("source");
        
        if (sourceConfig == null) {
            throw new RuntimeException("源数据源配置不能为空");
        }
        
        // 获取数据源信息（包含明文密码）
        Long datasourceId = sourceConfig.get("datasourceId").asLong();
        Datasource datasource = datasourceService.loadWithPlainPassword(datasourceId);
        if (datasource == null) {
            throw new RuntimeException("数据源不存在: " + datasourceId);
        }
        
        // 设置Reader名称
        String readerName = getReaderName(datasource.getType());
        reader.put("name", readerName);
        
        // 创建参数配置
        ObjectNode parameter = objectMapper.createObjectNode();
        
        // 基本连接信息
        parameter.put("username", datasource.getUsername());
        parameter.put("password", datasource.getPassword());
        
        // 字段列表 - 从columnMappings中提取源字段
        ArrayNode columns = objectMapper.createArrayNode();
        if (config.has("columnMappings")) {
            JsonNode columnMappings = config.get("columnMappings");
            if (columnMappings.isArray()) {
                for (JsonNode mapping : columnMappings) {
                    if (mapping.has("sourceColumn")) {
                        columns.add(mapping.get("sourceColumn").asText());
                    }
                }
            }
        }
        parameter.set("column", columns);
        
        // 分片字段
        if (sourceConfig.has("splitPk") && !sourceConfig.get("splitPk").asText().isEmpty()) {
            parameter.put("splitPk", sourceConfig.get("splitPk").asText());
        }
        
        // 连接配置
        ArrayNode connection = objectMapper.createArrayNode();
        ObjectNode connectionItem = objectMapper.createObjectNode();
        
        // JDBC URL
        ArrayNode jdbcUrls = objectMapper.createArrayNode();
        String jdbcUrl = buildJdbcUrl(datasource, sourceConfig.get("database").asText());
        jdbcUrls.add(jdbcUrl);
        connectionItem.set("jdbcUrl", jdbcUrls);
        
        // 表名
        ArrayNode tables = objectMapper.createArrayNode();
        tables.add(sourceConfig.get("tableName").asText());
        connectionItem.set("table", tables);
        
        connection.add(connectionItem);
        parameter.set("connection", connection);
        
        // WHERE条件
        if (sourceConfig.has("whereCondition") && !sourceConfig.get("whereCondition").asText().isEmpty()) {
            parameter.put("where", sourceConfig.get("whereCondition").asText());
        }
        
        reader.set("parameter", parameter);
        return reader;
    }

    /**
     * 创建Writer配置
     */
    private ObjectNode createWriter(JsonNode config) {
        ObjectNode writer = objectMapper.createObjectNode();
        JsonNode targetConfig = config.get("target");
        
        if (targetConfig == null) {
            throw new RuntimeException("目标数据源配置不能为空");
        }
        
        // 获取数据源信息（包含明文密码）
        Long datasourceId = targetConfig.get("datasourceId").asLong();
        Datasource datasource = datasourceService.loadWithPlainPassword(datasourceId);
        if (datasource == null) {
            throw new RuntimeException("数据源不存在: " + datasourceId);
        }
        
        // 设置Writer名称
        String writerName = getWriterName(datasource.getType());
        writer.put("name", writerName);
        
        // 创建参数配置
        ObjectNode parameter = objectMapper.createObjectNode();
        
        // 基本连接信息
        parameter.put("username", datasource.getUsername());
        parameter.put("password", datasource.getPassword());
        
        // 字段列表 - 从columnMappings中提取目标字段
        ArrayNode columns = objectMapper.createArrayNode();
        if (config.has("columnMappings")) {
            JsonNode columnMappings = config.get("columnMappings");
            if (columnMappings.isArray()) {
                for (JsonNode mapping : columnMappings) {
                    if (mapping.has("targetColumn")) {
                        columns.add(mapping.get("targetColumn").asText());
                    }
                }
            }
        }
        parameter.set("column", columns);
        
        // 连接配置
        ArrayNode connection = objectMapper.createArrayNode();
        ObjectNode connectionItem = objectMapper.createObjectNode();
        
        // JDBC URL
        String jdbcUrl = buildJdbcUrl(datasource, targetConfig.get("database").asText());
        connectionItem.put("jdbcUrl", jdbcUrl);
        
        // 表名
        ArrayNode tables = objectMapper.createArrayNode();
        tables.add(targetConfig.get("tableName").asText());
        connectionItem.set("table", tables);
        
        connection.add(connectionItem);
        parameter.set("connection", connection);
        
        // 写入模式
        String writeMode = targetConfig.has("writeMode") ? 
            targetConfig.get("writeMode").asText() : "insert";
        parameter.put("writeMode", writeMode);
        
        // 前置SQL
        if (targetConfig.has("preSql") && !targetConfig.get("preSql").asText().isEmpty()) {
            ArrayNode preSql = objectMapper.createArrayNode();
            preSql.add(targetConfig.get("preSql").asText());
            parameter.set("preSql", preSql);
        }
        
        // 后置SQL
        if (targetConfig.has("postSql") && !targetConfig.get("postSql").asText().isEmpty()) {
            ArrayNode postSql = objectMapper.createArrayNode();
            postSql.add(targetConfig.get("postSql").asText());
            parameter.set("postSql", postSql);
        }
        
        writer.set("parameter", parameter);
        return writer;
    }

    /**
     * 根据数据源类型获取Reader名称
     */
    private String getReaderName(String datasourceType) {
        return switch (datasourceType.toUpperCase()) {
            case "MYSQL", "DORIS" -> "mysqlreader";
            case "POSTGRESQL" -> "postgresqlreader";
            case "ORACLE" -> "oraclereader";
            case "HIVE" -> "hdfsreader";
            default -> throw new RuntimeException("不支持的数据源类型: " + datasourceType);
        };
    }

    /**
     * 根据数据源类型获取Writer名称
     */
    private String getWriterName(String datasourceType) {
        return switch (datasourceType.toUpperCase()) {
            case "MYSQL" -> "mysqlwriter";
            case "POSTGRESQL" -> "postgresqlwriter";
            case "ORACLE" -> "oraclewriter";
            case "DORIS" -> "streamwriter";
            case "HIVE" -> "hdfswriter";
            default -> throw new RuntimeException("不支持的数据源类型: " + datasourceType);
        };
    }

    /**
     * 构建JDBC URL
     */
    private String buildJdbcUrl(Datasource datasource, String database) {
        String template = switch (datasource.getType().toUpperCase()) {
            case "MYSQL", "DORIS" -> "*************************************************************************************************************************";
            case "POSTGRESQL" -> "jdbc:postgresql://%s:%d/%s";
            case "ORACLE" -> "**************************";
            case "HIVE" -> "jdbc:hive2://%s:%d/%s";
            default -> throw new RuntimeException("不支持的数据源类型: " + datasource.getType());
        };
        
        return String.format(template, datasource.getHost(), datasource.getPort(), database);
    }

    /**
     * 检查是否为增量同步
     */
    private boolean isIncrementalSync(JsonNode nodeConfig) {
        JsonNode syncStrategy = nodeConfig.path("syncStrategy");
        return !syncStrategy.isMissingNode() &&
               "INCREMENTAL".equals(syncStrategy.path("type").asText());
    }

    /**
     * 生成增量同步DataX配置
     */
    private String generateIncrementalDataxConfig(WorkflowNode node, JsonNode nodeConfig) throws Exception {
        log.info("生成增量同步DataX配置，节点ID: {}", node.getId());

        // 获取同步策略配置
        JsonNode syncStrategy = nodeConfig.path("syncStrategy");
        if (syncStrategy.isMissingNode()) {
            throw new IllegalArgumentException("增量同步配置缺失");
        }

        // 获取增量配置参数
        String incrementalColumn = syncStrategy.path("incrementalColumn").asText();
        String incrementalValue = syncStrategy.path("incrementalValue").asText();

        if (!StringUtils.hasText(incrementalColumn)) {
            throw new IllegalArgumentException("增量字段不能为空");
        }

        // 解析变量
        String resolvedValue = variableResolver.resolveVariables(node, incrementalValue);

        // 构建增量WHERE条件
        String whereCondition = conditionBuilder.buildIncrementalWhere(
            incrementalColumn, resolvedValue, syncStrategy);

        // 生成增量DataX配置
        return generateDataxConfigWithWhere(nodeConfig, whereCondition);
    }

    /**
     * 生成带WHERE条件的DataX配置
     */
    private String generateDataxConfigWithWhere(JsonNode nodeConfig, String whereCondition) throws Exception {
        // 创建DataX配置根节点
        ObjectNode dataxConfig = objectMapper.createObjectNode();
        ObjectNode job = objectMapper.createObjectNode();
        dataxConfig.set("job", job);

        // 设置全局配置
        ObjectNode setting = createJobSetting(nodeConfig);
        job.set("setting", setting);

        // 创建内容配置
        ArrayNode content = objectMapper.createArrayNode();
        ObjectNode contentItem = objectMapper.createObjectNode();

        // 配置Reader（带WHERE条件）
        ObjectNode reader = createReaderWithWhere(nodeConfig, whereCondition);
        contentItem.set("reader", reader);

        // 配置Writer
        ObjectNode writer = createWriter(nodeConfig);
        contentItem.set("writer", writer);

        content.add(contentItem);
        job.set("content", content);

        return objectMapper.writerWithDefaultPrettyPrinter().writeValueAsString(dataxConfig);
    }

    /**
     * 创建带WHERE条件的Reader配置
     */
    private ObjectNode createReaderWithWhere(JsonNode config, String whereCondition) throws Exception {
        // 复用现有的createReader方法
        ObjectNode reader = createReader(config);
        ObjectNode parameter = (ObjectNode) reader.path("parameter");

        // 获取原有的WHERE条件
        String originalWhere = parameter.has("where") ? parameter.get("where").asText() : "";

        // 合并WHERE条件
        String finalWhere = combineWhereConditions(originalWhere, whereCondition);

        if (StringUtils.hasText(finalWhere)) {
            parameter.put("where", finalWhere);
            log.info("设置最终WHERE条件: {}", finalWhere);
        } else {
            // 如果没有任何WHERE条件，移除where字段
            parameter.remove("where");
            log.info("无WHERE条件，进行全量同步");
        }

        return reader;
    }

    /**
     * 合并WHERE条件
     */
    private String combineWhereConditions(String originalWhere, String incrementalWhere) {
        boolean hasOriginal = StringUtils.hasText(originalWhere);
        boolean hasIncremental = StringUtils.hasText(incrementalWhere);

        if (hasOriginal && hasIncremental) {
            // 两个条件都存在，用AND连接
            return "(" + originalWhere + ") AND (" + incrementalWhere + ")";
        } else if (hasOriginal) {
            // 只有原有条件
            return originalWhere;
        } else if (hasIncremental) {
            // 只有增量条件
            return incrementalWhere;
        } else {
            // 都没有
            return "";
        }
    }
}
