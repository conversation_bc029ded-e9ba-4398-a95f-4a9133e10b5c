package com.sevb.admin.core.alarm.util;

import com.sevb.admin.core.alarm.model.AuthConfig;

/**
 * AuthConfig工具类
 * 提供静态方法来访问AuthConfig的数据
 * <AUTHOR>
 */
public class AuthConfigUtil {

    /**
     * 获取字符串类型的认证数据
     */
    public static String getAuthDataAsString(AuthConfig authConfig, String key) {
        if (authConfig == null || authConfig.getAuthData() == null || !authConfig.getAuthData().containsKey(key)) {
            return null;
        }
        Object value = authConfig.getAuthData().get(key);
        return value != null ? value.toString() : null;
    }

    /**
     * 获取整数类型的认证数据
     */
    public static Integer getAuthDataAsInteger(AuthConfig authConfig, String key) {
        if (authConfig == null || authConfig.getAuthData() == null || !authConfig.getAuthData().containsKey(key)) {
            return null;
        }
        Object value = authConfig.getAuthData().get(key);
        if (value instanceof Integer) {
            return (Integer) value;
        }
        if (value instanceof Number) {
            return ((Number) value).intValue();
        }
        if (value instanceof String) {
            try {
                return Integer.parseInt((String) value);
            } catch (NumberFormatException e) {
                return null;
            }
        }
        return null;
    }

    /**
     * 获取布尔类型的认证数据
     */
    public static Boolean getAuthDataAsBoolean(AuthConfig authConfig, String key) {
        if (authConfig == null || authConfig.getAuthData() == null || !authConfig.getAuthData().containsKey(key)) {
            return null;
        }
        Object value = authConfig.getAuthData().get(key);
        if (value instanceof Boolean) {
            return (Boolean) value;
        }
        if (value instanceof String) {
            return Boolean.parseBoolean((String) value);
        }
        return null;
    }

    /**
     * 检查是否包含指定的认证数据
     */
    public static boolean hasAuthData(AuthConfig authConfig, String key) {
        return authConfig != null && authConfig.getAuthData() != null && authConfig.getAuthData().containsKey(key);
    }
}
