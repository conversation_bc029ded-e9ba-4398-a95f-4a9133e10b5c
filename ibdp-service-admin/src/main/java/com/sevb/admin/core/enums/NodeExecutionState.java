package com.sevb.admin.core.enums;

/**
 * 节点执行状态枚举
 * 
 * <AUTHOR>
 */
public enum NodeExecutionState {
    
    /**
     * 等待执行
     */
    WAITING(0, "等待执行"),
    
    /**
     * 运行中
     */
    RUNNING(1, "运行中"),
    
    /**
     * 成功
     */
    SUCCESS(2, "成功"),
    
    /**
     * 失败
     */
    FAILED(3, "失败"),
    
    /**
     * 跳过
     */
    SKIPPED(4, "跳过"),
    
    /**
     * 终止
     */
    TERMINATED(5, "终止");
    
    private final int code;
    private final String desc;
    
    NodeExecutionState(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }
    
    public int getCode() {
        return code;
    }
    
    public String getDesc() {
        return desc;
    }
    
    public static NodeExecutionState fromCode(int code) {
        for (NodeExecutionState state : values()) {
            if (state.code == code) {
                return state;
            }
        }
        throw new IllegalArgumentException("未知的节点执行状态码: " + code);
    }
}
