package com.sevb.admin.core.alarm.template;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.util.HashSet;
import java.util.Map;
import java.util.Set;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 默认模板引擎实现
 * 支持 ${variable} 格式的变量替换
 * <AUTHOR>
 */
@Component
public class DefaultTemplateEngine implements TemplateEngine {

    private static final Logger logger = LoggerFactory.getLogger(DefaultTemplateEngine.class);
    
    // 变量匹配模式: ${variableName}
    private static final Pattern VARIABLE_PATTERN = Pattern.compile("\\$\\{([^}]+)\\}");

    @Override
    public String render(String template, Map<String, Object> variables) {
        if (template == null || template.isEmpty()) {
            return template;
        }

        if (variables == null || variables.isEmpty()) {
            logger.warn("模板变量为空，直接返回原模板");
            return template;
        }

        try {
            String result = template;
            Matcher matcher = VARIABLE_PATTERN.matcher(template);
            
            while (matcher.find()) {
                String variableName = matcher.group(1);
                String placeholder = matcher.group(0); // ${variableName}
                
                Object value = variables.get(variableName);
                String replacement = value != null ? value.toString() : "";
                
                // 替换所有匹配的占位符
                result = result.replace(placeholder, replacement);
                
                if (value == null) {
                    logger.warn("模板变量 '{}' 未提供值，使用空字符串替换", variableName);
                }
            }
            
            return result;
        } catch (Exception e) {
            logger.error("模板渲染失败: template={}", template, e);
            throw new RuntimeException("模板渲染失败: " + e.getMessage(), e);
        }
    }

    @Override
    public boolean validateTemplate(String template) {
        if (template == null) {
            return false;
        }

        try {
            // 检查变量语法是否正确
            Matcher matcher = VARIABLE_PATTERN.matcher(template);
            while (matcher.find()) {
                String variableName = matcher.group(1);
                if (variableName.trim().isEmpty()) {
                    logger.warn("发现空变量名: {}", matcher.group(0));
                    return false;
                }
                
                // 检查变量名是否包含非法字符
                if (!variableName.matches("[a-zA-Z0-9_]+")) {
                    logger.warn("变量名包含非法字符: {}", variableName);
                    return false;
                }
            }
            
            return true;
        } catch (Exception e) {
            logger.error("模板验证失败: template={}", template, e);
            return false;
        }
    }

    @Override
    public Set<String> extractVariables(String template) {
        Set<String> variables = new HashSet<>();
        
        if (template == null || template.isEmpty()) {
            return variables;
        }

        try {
            Matcher matcher = VARIABLE_PATTERN.matcher(template);
            while (matcher.find()) {
                String variableName = matcher.group(1);
                if (!variableName.trim().isEmpty()) {
                    variables.add(variableName);
                }
            }
        } catch (Exception e) {
            logger.error("提取模板变量失败: template={}", template, e);
        }
        
        return variables;
    }

    @Override
    public Set<String> getMissingVariables(String template, Map<String, Object> variables) {
        Set<String> templateVariables = extractVariables(template);
        Set<String> missingVariables = new HashSet<>();
        
        for (String variable : templateVariables) {
            if (variables == null || !variables.containsKey(variable)) {
                missingVariables.add(variable);
            }
        }
        
        return missingVariables;
    }

    /**
     * 转义特殊字符
     */
    public String escapeSpecialCharacters(String text) {
        if (text == null) {
            return null;
        }
        
        return text.replace("\\", "\\\\")
                  .replace("\"", "\\\"")
                  .replace("\n", "\\n")
                  .replace("\r", "\\r")
                  .replace("\t", "\\t");
    }

    /**
     * 反转义特殊字符
     */
    public String unescapeSpecialCharacters(String text) {
        if (text == null) {
            return null;
        }
        
        return text.replace("\\\\", "\\")
                  .replace("\\\"", "\"")
                  .replace("\\n", "\n")
                  .replace("\\r", "\r")
                  .replace("\\t", "\t");
    }

    /**
     * 检查模板是否包含循环引用
     */
    public boolean hasCircularReference(String template) {
        // 简单实现：检查是否有变量引用自身
        Set<String> variables = extractVariables(template);
        for (String variable : variables) {
            if (template.contains("${" + variable + "}")) {
                // 更复杂的循环检测逻辑可以在这里实现
                continue;
            }
        }
        return false;
    }
}
