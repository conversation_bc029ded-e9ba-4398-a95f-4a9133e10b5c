package com.sevb.admin.core.model;

import lombok.Data;

import java.util.Date;

/**
 * 节点实例
 * 记录工作流中每个节点的执行情况
 * 
 * <AUTHOR>
 */
@Data
public class NodeInstance {

    /**
     * 节点实例ID
     */
    private Long id;

    /**
     * 工作流实例ID
     */
    private Long workflowInstanceId;

    /**
     * 工作流ID
     */
    private Long workflowId;

    /**
     * 工作流执行ID（UUID）
     */
    private String executionId;

    /**
     * 节点ID
     */
    private Long nodeId;

    /**
     * 节点编码
     */
    private String nodeCode;

    /**
     * 节点名称（冗余字段）
     */
    private String nodeName;

    /**
     * 节点类型（冗余字段）
     */
    private String nodeType;

    /**
     * XXL-JOB任务ID
     */
    private Integer jobId;

    /**
     * XXL-JOB日志ID
     */
    private Long jobLogId;

    /**
     * 执行器地址
     */
    private String executorAddress;

    /**
     * 输入参数JSON
     */
    private String inputParams;

    /**
     * 输出参数JSON
     */
    private String outputParams;

    /**
     * 回调URL
     */
    private String callbackUrl;

    /**
     * 节点实例状态：0-等待中，1-运行中，2-成功，3-失败，4-跳过，5-超时
     */
    private Integer status;

    /**
     * 开始执行时间
     */
    private Date startTime;

    /**
     * 结束时间
     */
    private Date endTime;

    /**
     * 执行耗时（毫秒）
     */
    private Long duration;

    /**
     * 执行参数
     */
    private String executeParams;

    /**
     * 执行结果
     */
    private String executeResult;

    /**
     * 错误信息
     */
    private String errorMessage;

    /**
     * 执行日志
     */
    private String executeLog;

    /**
     * 重试次数
     */
    private Integer retryCount;

    /**
     * 最大重试次数
     */
    private Integer maxRetryCount;

    /**
     * 执行顺序
     */
    private Integer executeOrder;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 节点实例状态枚举
     */
    public enum Status {
        WAITING(0, "等待中"),
        RUNNING(1, "运行中"),
        SUCCESS(2, "成功"),
        FAILED(3, "失败"),
        SKIPPED(4, "跳过"),
        TIMEOUT(5, "超时");

        private final Integer code;
        private final String name;

        Status(Integer code, String name) {
            this.code = code;
            this.name = name;
        }

        public Integer getCode() {
            return code;
        }

        public String getName() {
            return name;
        }

        public static Status getByCode(Integer code) {
            if (code == null) return null;
            for (Status status : values()) {
                if (status.getCode().equals(code)) {
                    return status;
                }
            }
            return null;
        }
    }

    /**
     * 获取状态名称
     */
    public String getStatusName() {
        Status statusEnum = Status.getByCode(this.status);
        return statusEnum != null ? statusEnum.getName() : "未知";
    }

    /**
     * 判断节点是否正在运行
     */
    public boolean isRunning() {
        return Status.RUNNING.getCode().equals(this.status);
    }

    /**
     * 判断节点是否已完成
     */
    public boolean isCompleted() {
        return Status.SUCCESS.getCode().equals(this.status) || 
               Status.FAILED.getCode().equals(this.status) ||
               Status.SKIPPED.getCode().equals(this.status) ||
               Status.TIMEOUT.getCode().equals(this.status);
    }

    /**
     * 判断节点是否成功
     */
    public boolean isSuccess() {
        return Status.SUCCESS.getCode().equals(this.status);
    }

    /**
     * 判断节点是否失败
     */
    public boolean isFailed() {
        return Status.FAILED.getCode().equals(this.status) ||
               Status.TIMEOUT.getCode().equals(this.status);
    }

    /**
     * 判断是否可以重试
     */
    public boolean canRetry() {
        return isFailed() && (retryCount == null || retryCount < maxRetryCount);
    }
}
