package com.sevb.admin.core.alarm.channel.impl;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.sevb.admin.core.alarm.channel.NotificationChannel;
import com.sevb.admin.core.alarm.model.AuthConfig;
import com.sevb.admin.core.alarm.model.NotificationRequest;
import com.sevb.admin.core.alarm.model.SendResult;
import com.sevb.admin.core.alarm.util.AuthConfigUtil;
import lombok.Getter;
import lombok.Setter;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.mail.javamail.JavaMailSenderImpl;
import org.springframework.mail.javamail.MimeMessageHelper;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import jakarta.mail.internet.MimeMessage;
import java.util.Map;
import java.util.Properties;

/**
 * 邮件通知渠道实现
 * <AUTHOR>
 */
@Component
public class EmailChannel implements NotificationChannel {

    private static final Logger logger = LoggerFactory.getLogger(EmailChannel.class);

    @Autowired(required = false)
    private JavaMailSender defaultMailSender;

    private final ObjectMapper objectMapper = new ObjectMapper();

    @Override
    public String getChannelType() {
        return "EMAIL";
    }

    @Override
    public String getSupportedAuthType() {
        return "SMTP";
    }

    @Override
    public String getChannelName() {
        return "邮件";
    }

    @Override
    public String getChannelDescription() {
        return "SMTP邮件通知";
    }

    @Override
    public SendResult sendNotification(NotificationRequest request) {
        if (request == null) {
            return SendResult.failure("请求参数不能为空");
        }

        if (request.getRecipients() == null || request.getRecipients().isEmpty()) {
            return SendResult.failure("收件人列表不能为空");
        }

        try {
            // 获取邮件发送器
            JavaMailSender mailSender = getMailSender(request.getAuthConfig());
            
            // 解析邮件内容
            EmailContent emailContent = parseEmailContent(request.getContent());
            
            // 创建邮件消息
            MimeMessage message = mailSender.createMimeMessage();
            MimeMessageHelper helper = new MimeMessageHelper(message, true, "UTF-8");

            // 设置发件人
            String fromEmail = getFromEmail(request.getAuthConfig());
            if (StringUtils.hasText(fromEmail)) {
                helper.setFrom(fromEmail);
            }

            // 设置收件人
            String[] recipients = request.getRecipients().toArray(new String[0]);
            helper.setTo(recipients);

            // 设置主题
            String subject = StringUtils.hasText(emailContent.getSubject()) ? 
                emailContent.getSubject() : (StringUtils.hasText(request.getTitle()) ? request.getTitle() : "告警通知");
            helper.setSubject(subject);

            // 设置邮件内容
            String body = StringUtils.hasText(emailContent.getBody()) ? 
                emailContent.getBody() : request.getContent();
            helper.setText(body, emailContent.isHtml());

            // 发送邮件
            long startTime = System.currentTimeMillis();
            mailSender.send(message);
            long endTime = System.currentTimeMillis();

            SendResult result = SendResult.success("邮件发送成功");
            result.setResponseTime(endTime - startTime);
            result.setResponseCode("200");
            return result;

        } catch (Exception e) {
            logger.error("邮件发送异常: requestId={}", request.getRequestId(), e);
            return SendResult.failure("邮件发送失败: " + e.getMessage(), e);
        }
    }



    @Override
    public boolean validateAuthConfig(AuthConfig authConfig) {
        if (authConfig == null) {
            return false;
        }

        String smtpHost = AuthConfigUtil.getAuthDataAsString(authConfig, "smtpHost");
        String username = AuthConfigUtil.getAuthDataAsString(authConfig, "username");

        return StringUtils.hasText(smtpHost) && StringUtils.hasText(username);
    }

    /**
     * 获取邮件发送器
     */
    private JavaMailSender getMailSender(AuthConfig authConfig) {
        if (authConfig != null && authConfig.getAuthData() != null && !authConfig.getAuthData().isEmpty()) {
            // 使用自定义配置
            return createMailSender(authConfig);
        } else {
            // 使用默认配置
            if (defaultMailSender == null) {
                throw new RuntimeException("未配置邮件发送器");
            }
            return defaultMailSender;
        }
    }

    /**
     * 创建邮件发送器
     */
    private JavaMailSender createMailSender(AuthConfig authConfig) {
        JavaMailSenderImpl mailSender = new JavaMailSenderImpl();

        // 基本配置
        mailSender.setHost(AuthConfigUtil.getAuthDataAsString(authConfig, "smtpHost"));
        mailSender.setPort(AuthConfigUtil.getAuthDataAsInteger(authConfig, "smtpPort") != null ?
            AuthConfigUtil.getAuthDataAsInteger(authConfig, "smtpPort") : 587);
        mailSender.setUsername(AuthConfigUtil.getAuthDataAsString(authConfig, "username"));
        mailSender.setPassword(AuthConfigUtil.getAuthDataAsString(authConfig, "password"));

        // 高级配置
        Properties props = mailSender.getJavaMailProperties();
        props.put("mail.transport.protocol", "smtp");
        props.put("mail.smtp.auth", "true");

        Boolean enableTls = AuthConfigUtil.getAuthDataAsBoolean(authConfig, "enableTls");
        if (enableTls != null && enableTls) {
            props.put("mail.smtp.starttls.enable", "true");
        }

        Boolean enableSsl = AuthConfigUtil.getAuthDataAsBoolean(authConfig, "enableSsl");
        if (enableSsl != null && enableSsl) {
            props.put("mail.smtp.ssl.enable", "true");
        }

        // 超时设置
        props.put("mail.smtp.connectiontimeout", "10000");
        props.put("mail.smtp.timeout", "10000");
        props.put("mail.smtp.writetimeout", "10000");

        return mailSender;
    }

    /**
     * 获取发件人邮箱
     */
    private String getFromEmail(AuthConfig authConfig) {
        if (authConfig != null) {
            // 直接使用用户名作为发件人，确保与认证用户一致
            return AuthConfigUtil.getAuthDataAsString(authConfig, "username");
        }
        return null;
    }

    /**
     * 解析邮件内容
     */
    private EmailContent parseEmailContent(String content) {
        try {
            // 尝试解析为JSON格式
            Map<String, Object> contentMap = objectMapper.readValue(content, Map.class);
            
            EmailContent emailContent = new EmailContent();
            emailContent.setSubject((String) contentMap.get("subject"));
            emailContent.setBody((String) contentMap.get("body"));
            
            Object htmlFlag = contentMap.get("html");
            emailContent.setHtml(htmlFlag != null && Boolean.parseBoolean(htmlFlag.toString()));
            
            return emailContent;
            
        } catch (Exception e) {
            // 如果不是JSON格式，作为纯文本处理
            EmailContent emailContent = new EmailContent();
            emailContent.setBody(content);
            emailContent.setHtml(false);
            return emailContent;
        }
    }

    /**
     * 邮件内容类
     */
    @Setter
    @Getter
    private static class EmailContent {
        private String subject;
        private String body;
        private boolean html = false;

    }
}
