package com.sevb.admin.core.conf;

import com.github.xiaoymin.knife4j.spring.annotations.EnableKnife4j;
import io.swagger.v3.oas.models.OpenAPI;
import io.swagger.v3.oas.models.info.Contact;
import io.swagger.v3.oas.models.info.Info;
import io.swagger.v3.oas.models.info.License;
import io.swagger.v3.oas.models.servers.Server;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.List;

/**
 * Knife4j增强配置类
 * <AUTHOR>
 */
@Configuration
@EnableKnife4j
public class Knife4jConfig {

    @Value("${server.port:8080}")
    private String serverPort;

    @Value("${server.servlet.context-path:}")
    private String contextPath;

    @Bean
    public OpenAPI customOpenAPI() {
        return new OpenAPI()
                .info(new Info()
                        .title("数据开发平台API文档")
                        .description("## 数据开发平台后端接口文档\n\n" +
                                "### 功能模块\n" +
                                "- **数据源管理**: 支持MySQL、PostgreSQL、Oracle等多种数据源\n" +
                                "- **节点类型管理**: 工作流节点类型的增删改查\n" +
                                "- **工作流管理**: 可视化工作流设计和调度\n" +
                                "- **任务调度**: 基于XXL-JOB的分布式任务调度\n" +
                                "- **执行监控**: 实时监控任务执行状态和日志\n\n" +
                                "### 技术栈\n" +
                                "- Spring Boot 3.x\n" +
                                "- MyBatis\n" +
                                "- XXL-JOB\n" +
                                "- MySQL 8.0+")
                        .version("3.0.0")
                        .contact(new Contact()
                                .name("SEVB开发团队")
                                .email("<EMAIL>")
                                .url("https://www.sevb.com"))
                        .license(new License()
                                .name("Apache 2.0")
                                .url("http://www.apache.org/licenses/LICENSE-2.0.html")))
                .servers(List.of(
                        new Server().url("http://localhost:" + serverPort + contextPath).description("本地开发环境"),
                        new Server().url("http://dev.sevb.com" + contextPath).description("开发环境"),
                        new Server().url("http://test.sevb.com" + contextPath).description("测试环境")
                ));
    }
}
