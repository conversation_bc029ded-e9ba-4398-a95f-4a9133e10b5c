package com.sevb.admin.controller;

import com.sevb.admin.controller.annotation.PermissionLimit;
import com.sevb.admin.core.model.JobUser;
import com.sevb.admin.service.impl.LoginService;
import com.sevb.admin.service.JobService;
import com.sevb.core.biz.model.ReturnT;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.beans.propertyeditors.CustomDateEditor;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.WebDataBinder;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;
import org.springframework.web.servlet.view.RedirectView;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Map;

/**
 * index controller
 * <AUTHOR> 2015-12-19 16:13:16
 */
@Controller
public class IndexController {

	@Resource
	private JobService jobService;
	@Resource
	private LoginService loginService;


	@RequestMapping("/")
	public String index(Model model) {

		Map<String, Object> dashboardMap = jobService.dashboardInfo();
		model.addAllAttributes(dashboardMap);

		return "index";
	}

    @RequestMapping("/chartInfo")
	@ResponseBody
	public ReturnT<Map<String, Object>> chartInfo(@RequestParam("startDate") Date startDate, @RequestParam("endDate") Date endDate) {
        return jobService.chartInfo(startDate, endDate);
    }
	
	@RequestMapping("/toLogin")
	@PermissionLimit(limit=false)
	public ModelAndView toLogin(HttpServletRequest request, HttpServletResponse response, ModelAndView modelAndView) {
		if (loginService.ifLogin(request, response) != null) {
			modelAndView.setView(new RedirectView("/",true,false));
			return modelAndView;
		}
		return new ModelAndView("login");
	}
	
	@RequestMapping(value="login", method=RequestMethod.POST)
	@ResponseBody
	@PermissionLimit(limit=false)
	public ReturnT<String> loginDo(HttpServletRequest request,
								   HttpServletResponse response,
								   @RequestParam("userName") String userName,
								   @RequestParam("password") String password,
								   @RequestParam(value = "ifRemember", required = false) String ifRemember){

		boolean ifRem = ifRemember != null && !ifRemember.trim().isEmpty() && "on".equals(ifRemember);
		return loginService.login(request, response, userName, password, ifRem);
	}
	
	@RequestMapping(value="logout", method=RequestMethod.POST)
	@ResponseBody
	@PermissionLimit(limit=false)
	public ReturnT<String> logout(HttpServletRequest request, HttpServletResponse response){
		return loginService.logout(request, response);
	}

	@RequestMapping(value="/user/current", method=RequestMethod.GET)
	@ResponseBody
	@PermissionLimit(limit=true)
	public ReturnT<JobUser> getCurrentUser(HttpServletRequest request){
		JobUser loginUser = loginService.ifLogin(request, null);
		if (loginUser != null) {
			// 清除密码信息
			loginUser.setPassword(null);
			return new ReturnT<>(loginUser);
		}
		return new ReturnT<>(ReturnT.FAIL_CODE, "用户未登录");
	}
	
	@RequestMapping("/help")
	public String help() {
		return "help";
	}

	@InitBinder
	public void initBinder(WebDataBinder binder) {
		SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
		dateFormat.setLenient(false);
		binder.registerCustomEditor(Date.class, new CustomDateEditor(dateFormat, true));
	}
	
}
