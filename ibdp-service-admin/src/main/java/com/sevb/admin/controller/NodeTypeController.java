package com.sevb.admin.controller;

import com.sevb.admin.core.model.NodeType;
import com.sevb.admin.service.NodeTypeService;
import com.sevb.core.biz.model.ReturnT;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import jakarta.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * 节点类型管理控制器
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/node-type")
@Tag(name = "节点类型管理", description = "节点类型相关接口")
public class NodeTypeController {

    @Resource
    private NodeTypeService nodeTypeService;

    /**
     * 分页查询节点类型列表
     */
    @Operation(summary = "分页查询节点类型列表", description = "支持按节点类型名称、分类、状态进行筛选查询")
    @GetMapping("/pageList")
    public Map<String, Object> pageList(
            @Parameter(description = "页码", example = "1") @RequestParam(value = "pageNum", defaultValue = "1") int pageNum,
            @Parameter(description = "每页大小", example = "10") @RequestParam(value = "pageSize", defaultValue = "10") int pageSize,
            @Parameter(description = "节点类型名称（模糊查询）") @RequestParam(value = "typeName", required = false) String typeName,
            @Parameter(description = "节点分类：DATA_SYNC-数据同步，COMPUTE-计算处理，CONTROL-流程控制") @RequestParam(value = "category", required = false) String category,
            @Parameter(description = "状态：0-禁用，1-启用") @RequestParam(value = "status", required = false) Integer status) {
        return nodeTypeService.pageList(pageNum, pageSize, typeName, category, status);
    }

    /**
     * 查询所有启用的节点类型
     */
    @Operation(summary = "查询所有启用的节点类型", description = "获取所有状态为启用的节点类型列表，用于工作流设计器")
    @GetMapping("/list")
    public ReturnT<List<NodeType>> list() {
        try {
            List<NodeType> list = nodeTypeService.findAllEnabled();
            return new ReturnT<>(list);
        } catch (Exception e) {
            log.error("查询节点类型列表失败", e);
            return new ReturnT<>(ReturnT.FAIL_CODE, "查询失败：" + e.getMessage());
        }
    }

    /**
     * 根据分类查询节点类型
     */
    @Operation(summary = "根据分类查询节点类型", description = "根据节点分类获取对应的节点类型列表")
    @GetMapping("/listByCategory")
    public ReturnT<List<NodeType>> listByCategory(
            @Parameter(description = "节点分类", required = true, example = "COMPUTE") @RequestParam("category") String category) {
        try {
            List<NodeType> list = nodeTypeService.findByCategory(category);
            return new ReturnT<>(list);
        } catch (Exception e) {
            log.error("根据分类查询节点类型失败", e);
            return new ReturnT<>(ReturnT.FAIL_CODE, "查询失败：" + e.getMessage());
        }
    }

    /**
     * 获取节点类型详情
     */
    @Operation(summary = "获取节点类型详情", description = "根据节点类型ID获取详细信息")
    @GetMapping("/{id}")
    public ReturnT<NodeType> load(@Parameter(description = "节点类型ID", required = true, example = "1") @PathVariable("id") Long id) {
        try {
            NodeType nodeType = nodeTypeService.load(id);
            if (nodeType == null) {
                return new ReturnT<>(ReturnT.FAIL_CODE, "节点类型不存在");
            }
            return new ReturnT<>(nodeType);
        } catch (Exception e) {
            log.error("获取节点类型详情失败", e);
            return new ReturnT<>(ReturnT.FAIL_CODE, "获取失败：" + e.getMessage());
        }
    }

    /**
     * 根据类型编码获取节点类型
     */
    @Operation(summary = "根据类型编码获取节点类型", description = "根据节点类型编码获取详细信息")
    @GetMapping("/getByCode/{typeCode}")
    public ReturnT<NodeType> getByCode(@Parameter(description = "节点类型编码", required = true, example = "SQL") @PathVariable("typeCode") String typeCode) {
        try {
            NodeType nodeType = nodeTypeService.findByTypeCode(typeCode);
            if (nodeType == null) {
                return new ReturnT<>(ReturnT.FAIL_CODE, "节点类型不存在");
            }
            return new ReturnT<>(nodeType);
        } catch (Exception e) {
            log.error("根据编码获取节点类型失败", e);
            return new ReturnT<>(ReturnT.FAIL_CODE, "获取失败：" + e.getMessage());
        }
    }

    /**
     * 添加节点类型
     */
    @Operation(summary = "添加节点类型", description = "创建新的节点类型，需要提供完整的节点类型信息")
    @PostMapping("/add")
    public ReturnT<String> add(@Parameter(description = "节点类型信息", required = true) @RequestBody NodeType nodeType) {
        return nodeTypeService.add(nodeType);
    }

    /**
     * 更新节点类型
     */
    @Operation(summary = "更新节点类型", description = "更新现有节点类型的信息")
    @PostMapping("/update")
    public ReturnT<String> update(@Parameter(description = "节点类型信息", required = true) @RequestBody NodeType nodeType) {
        return nodeTypeService.update(nodeType);
    }

    /**
     * 删除节点类型
     */
    @Operation(summary = "删除节点类型", description = "删除指定的节点类型，删除前会检查是否有工作流在使用")
    @PostMapping("/delete")
    public ReturnT<String> delete(@Parameter(description = "节点类型ID", required = true, example = "1") @RequestParam("id") Long id) {
        return nodeTypeService.delete(id);
    }

    /**
     * 启用/禁用节点类型
     */
    @Operation(summary = "启用/禁用节点类型", description = "更新节点类型的启用状态")
    @PostMapping("/updateStatus")
    public ReturnT<String> updateStatus(@Parameter(description = "节点类型ID", required = true, example = "1") @RequestParam("id") Long id,
                                       @Parameter(description = "状态：0-禁用，1-启用", required = true, example = "1") @RequestParam("status") Integer status) {
        return nodeTypeService.updateStatus(id, status);
    }

    /**
     * 获取节点分类列表
     */
    @Operation(summary = "获取节点分类列表", description = "获取所有可用的节点分类，用于下拉选择")
    @GetMapping("/categoryList")
    public ReturnT<List<Map<String, Object>>> getCategoryList() {
        try {
            List<Map<String, Object>> categoryList = nodeTypeService.getCategoryList();
            return new ReturnT<>(categoryList);
        } catch (Exception e) {
            log.error("获取节点分类列表失败", e);
            return new ReturnT<>(ReturnT.FAIL_CODE, "获取失败：" + e.getMessage());
        }
    }
}
