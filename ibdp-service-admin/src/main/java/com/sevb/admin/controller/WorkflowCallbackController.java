package com.sevb.admin.controller;

import com.sevb.admin.core.dto.NodeExecutionResult;
import com.sevb.admin.service.callback.DataxSyncCallbackHandler;
import com.sevb.admin.service.orchestration.CentralWorkflowOrchestrationService;
import com.sevb.core.biz.model.ReturnT;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * 工作流回调控制器
 * 用于处理执行器的节点执行完成回调
 * 
 * <AUTHOR>
 */
@RestController
@RequestMapping("/workflow/callback")
@Tag(name = "工作流回调接口", description = "处理执行器的节点执行完成回调")
@Slf4j
public class WorkflowCallbackController {
    
    @Autowired
    private CentralWorkflowOrchestrationService orchestrationService;

    @Autowired
    private DataxSyncCallbackHandler dataxSyncCallbackHandler;
    
    /**
     * 节点执行完成回调
     */
    @Operation(summary = "节点执行完成回调", description = "执行器调用此接口通知调度中心节点执行完成")
    @PostMapping("/nodeComplete")
    public ReturnT<String> handleNodeComplete(
            @Parameter(description = "节点执行结果", required = true)
            @RequestBody NodeExecutionResult result) {
        
        try {
            log.info("收到节点执行完成回调: executionId={}, nodeCode={}, success={}",
                    result.getExecutionId(), result.getNodeCode(), result.isSuccess());

            // 🚀 处理DataX增量同步回调
            handleDataxSyncCallback(result);

            return orchestrationService.handleNodeComplete(result);

        } catch (Exception e) {
            log.error("处理节点完成回调异常", e);
            return new ReturnT<>(ReturnT.FAIL_CODE, "处理回调失败: " + e.getMessage());
        }
    }
    
    /**
     * 节点执行完成回调（GET方式，用于简单测试）
     */
    @Operation(summary = "节点执行完成回调(GET)", description = "GET方式的节点执行完成回调，用于测试")
    @GetMapping("/nodeComplete")
    public ReturnT<String> handleNodeCompleteGet(
            @Parameter(description = "执行ID", required = true)
            @RequestParam("executionId") String executionId,
            @Parameter(description = "节点编码", required = true)
            @RequestParam("nodeCode") String nodeCode,
            @Parameter(description = "是否成功", required = true)
            @RequestParam("success") boolean success,
            @Parameter(description = "错误信息")
            @RequestParam(value = "errorMessage", required = false) String errorMessage,
            @Parameter(description = "执行器地址")
            @RequestParam(value = "executorAddress", required = false) String executorAddress,
            @Parameter(description = "任务日志ID")
            @RequestParam(value = "jobLogId", required = false) Long jobLogId) {

        try {
            log.info("收到GET方式节点执行完成回调: executionId={}, nodeCode={}, success={}",
                    executionId, nodeCode, success);

            // 构建回调结果
            NodeExecutionResult result = new NodeExecutionResult();
            result.setExecutionId(executionId);
            result.setNodeCode(nodeCode);
            result.setSuccess(success);
            result.setErrorMessage(errorMessage);
            result.setExecutorAddress(executorAddress);
            result.setJobLogId(jobLogId);
            result.setEndTime(System.currentTimeMillis());

            return orchestrationService.handleNodeComplete(result);

        } catch (Exception e) {
            log.error("处理GET方式节点完成回调异常", e);
            return new ReturnT<>(ReturnT.FAIL_CODE, "处理回调失败: " + e.getMessage());
        }
    }

    /**
     * 重试失败的节点
     */
    @Operation(summary = "重试失败的节点", description = "重新执行失败的节点")
    @PostMapping("/retryNode")
    public ReturnT<String> retryFailedNode(
            @Parameter(description = "执行ID", required = true)
            @RequestParam("executionId") String executionId,
            @Parameter(description = "节点编码", required = true)
            @RequestParam("nodeCode") String nodeCode) {

        try {
            log.info("收到节点重试请求: executionId={}, nodeCode={}", executionId, nodeCode);

            return orchestrationService.retryFailedNode(executionId, nodeCode);

        } catch (Exception e) {
            log.error("重试节点异常", e);
            return new ReturnT<>(ReturnT.FAIL_CODE, "重试失败: " + e.getMessage());
        }
    }

    /**
     * 终止工作流执行
     */
    @Operation(summary = "终止工作流执行", description = "终止正在执行的工作流")
    @PostMapping("/terminate")
    public ReturnT<String> terminateExecution(
            @Parameter(description = "执行ID", required = true)
            @RequestParam("executionId") String executionId,
            @Parameter(description = "终止原因")
            @RequestParam(value = "reason", required = false, defaultValue = "用户手动终止") String reason) {

        try {
            log.info("收到工作流终止请求: executionId={}, reason={}", executionId, reason);

            return orchestrationService.terminateExecution(executionId, reason);

        } catch (Exception e) {
            log.error("终止工作流异常", e);
            return new ReturnT<>(ReturnT.FAIL_CODE, "终止失败: " + e.getMessage());
        }
    }

    /**
     * 处理DataX增量同步回调
     */
    private void handleDataxSyncCallback(NodeExecutionResult result) {
        try {
            if (result.isSuccess()) {
                // 处理成功回调
                dataxSyncCallbackHandler.handleDataxSuccess(
                    result.getNodeCode(),
                    result.getExecutionId(),
                    result
                );
            } else {
                // 处理失败回调
                dataxSyncCallbackHandler.handleDataxFailure(
                    result.getNodeCode(),
                    result.getExecutionId(),
                    result
                );
            }
        } catch (Exception e) {
            log.error("处理DataX同步回调失败，节点: {}, 执行ID: {}",
                     result.getNodeCode(), result.getExecutionId(), e);
            // 不抛出异常，避免影响主流程
        }
    }
}
