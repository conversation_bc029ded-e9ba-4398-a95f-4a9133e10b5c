package com.sevb.admin.controller;

import com.sevb.admin.dto.WorkflowDesignDTO;
import com.sevb.admin.dto.WorkflowDesignIncrementalDTO;
import com.sevb.admin.service.WorkflowDesignerService;
import com.sevb.core.biz.model.ReturnT;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import jakarta.annotation.Resource;
import java.util.Map;

/**
 * 工作流设计器综合控制器
 */
@Slf4j
@RestController
@RequestMapping("/workflow-designer")
@Tag(name = "工作流设计器", description = "工作流设计器综合功能接口")
public class WorkflowDesignerController {
    
    @Resource
    private WorkflowDesignerService workflowDesignerService;
    
    @GetMapping("/loadDesign")
    @Operation(summary = "加载工作流设计数据", description = "一次性加载工作流的所有节点和关系数据")
    public ReturnT<WorkflowDesignDTO> loadWorkflowDesign(
            @Parameter(description = "工作流ID") @RequestParam("workflowId") Long workflowId) {
        return workflowDesignerService.loadWorkflowDesign(workflowId);
    }
    
    @PostMapping("/saveDesign")
    @Operation(summary = "保存工作流设计数据", description = "完整保存工作流设计数据")
    public ReturnT<String> saveWorkflowDesign(@RequestBody WorkflowDesignDTO designData) {
        String loginUser = "admin";
        return workflowDesignerService.saveWorkflowDesign(designData, loginUser);
    }
    
    @PostMapping("/saveDesignIncremental")
    @Operation(summary = "增量保存工作流设计数据", description = "增量保存变更数据")
    public ReturnT<String> saveWorkflowDesignIncremental(@RequestBody WorkflowDesignIncrementalDTO incrementalData) {
        String loginUser = "admin";
        return workflowDesignerService.saveWorkflowDesignIncremental(incrementalData, loginUser);
    }
    
    @GetMapping("/validateDesign")
    @Operation(summary = "验证工作流设计", description = "检查节点配置、循环依赖等")
    public ReturnT<Map<String, Object>> validateWorkflowDesign(
            @Parameter(description = "工作流ID") @RequestParam("workflowId") Long workflowId) {
        return workflowDesignerService.validateWorkflowDesign(workflowId);
    }
    
    @GetMapping("/getDAG")
    @Operation(summary = "获取工作流DAG结构", description = "返回工作流的有向无环图结构")
    public ReturnT<Map<String, Object>> getWorkflowDAG(
            @Parameter(description = "工作流ID") @RequestParam("workflowId") Long workflowId) {
        return new ReturnT<>(ReturnT.FAIL_CODE, "功能开发中");
    }
    
    @PostMapping("/copyDesign")
    @Operation(summary = "复制工作流设计", description = "复制工作流设计到另一个工作流")
    public ReturnT<String> copyWorkflowDesign(@RequestBody Map<String, Long> request) {
        return new ReturnT<>(ReturnT.FAIL_CODE, "功能开发中");
    }
    
    @PostMapping("/clearDesign")
    @Operation(summary = "清空工作流设计", description = "清空指定工作流的所有节点和关系")
    public ReturnT<String> clearWorkflowDesign(
            @Parameter(description = "工作流ID") @RequestParam("workflowId") Long workflowId) {
        String loginUser = "admin";
        return workflowDesignerService.clearWorkflowDesign(workflowId, loginUser);
    }
    
    @GetMapping("/getStats")
    @Operation(summary = "获取工作流设计统计信息", description = "返回节点数量、关系数量等统计信息")
    public ReturnT<Map<String, Object>> getWorkflowDesignStats(
            @Parameter(description = "工作流ID") @RequestParam("workflowId") Long workflowId) {
        return workflowDesignerService.getWorkflowDesignStats(workflowId);
    }
    
    @GetMapping("/export")
    @Operation(summary = "导出工作流设计", description = "导出工作流设计数据")
    public ReturnT<Map<String, Object>> exportWorkflowDesign(
            @Parameter(description = "工作流ID") @RequestParam("workflowId") Long workflowId) {
        return new ReturnT<>(ReturnT.FAIL_CODE, "功能开发中");
    }
    
    @PostMapping("/import")
    @Operation(summary = "导入工作流设计", description = "导入工作流设计数据")
    public ReturnT<String> importWorkflowDesign(@RequestBody Map<String, Object> request) {
        return new ReturnT<>(ReturnT.FAIL_CODE, "功能开发中");
    }
}
