package com.sevb.admin.controller;

import com.sevb.admin.controller.annotation.PermissionLimit;
import com.sevb.admin.core.model.Datasource;
import com.sevb.admin.core.util.DatasourceConnectionUtil;
import com.sevb.admin.service.DatasourceService;
import com.sevb.core.biz.model.ReturnT;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * 数据源管理控制器
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/datasource")
@Tag(name = "数据源管理", description = "数据源的增删改查、连接测试等功能")
public class DatasourceController {

    @Resource
    private DatasourceService datasourceService;

    @GetMapping("/pageList")
    @Operation(summary = "分页查询数据源列表", description = "支持按名称、类型、状态筛选")
    public ReturnT<Map<String, Object>> pageList(
            @Parameter(description = "页码，从1开始", example = "1") @RequestParam(value = "pageNum", defaultValue = "1") int pageNum,
            @Parameter(description = "每页大小", example = "10") @RequestParam(value = "pageSize", defaultValue = "10") int pageSize,
            @Parameter(description = "数据源名称") @RequestParam(value = "name", required = false) String name,
            @Parameter(description = "数据源类型") @RequestParam(value = "type", required = false) String type,
            @Parameter(description = "状态：0-禁用，1-启用") @RequestParam(value = "status", required = false) Integer status) {

        try {
            Map<String, Object> result = datasourceService.pageList(pageNum, pageSize, name, type, status);
            return new ReturnT<>(result);
        } catch (Exception e) {
            log.error("查询数据源列表失败", e);
            return new ReturnT<>(ReturnT.FAIL_CODE, "查询失败：" + e.getMessage());
        }
    }

    @GetMapping("/list")
    @Operation(summary = "查询所有启用的数据源", description = "返回所有状态为启用的数据源列表")
    public ReturnT<List<Datasource>> list() {
        try {
            List<Datasource> result = datasourceService.findAllEnabled();
            return new ReturnT<>(result);
        } catch (Exception e) {
            log.error("查询数据源列表失败", e);
            return new ReturnT<>(ReturnT.FAIL_CODE, "查询失败：" + e.getMessage());
        }
    }

    @GetMapping("/load/{id}")
    @Operation(summary = "根据ID查询数据源详情", description = "返回指定ID的数据源详细信息")
    public ReturnT<Datasource> load(@Parameter(description = "数据源ID") @PathVariable("id") Long id) {
        try {
            Datasource result = datasourceService.load(id);
            if (result == null) {
                return new ReturnT<>(ReturnT.FAIL_CODE, "数据源不存在");
            }
            return new ReturnT<>(result);
        } catch (Exception e) {
            log.error("查询数据源详情失败", e);
            return new ReturnT<>(ReturnT.FAIL_CODE, "查询失败：" + e.getMessage());
        }
    }

    @GetMapping("/loadForExecutor/{id}")
    @Operation(summary = "根据ID查询数据源详情（包含明文密码，仅供executor使用）", description = "返回指定ID的数据源详细信息，包含解密后的密码")
    @PermissionLimit(limit = false) // 允许accessToken访问
    public ReturnT<Datasource> loadForExecutor(@Parameter(description = "数据源ID") @PathVariable("id") Long id) {
        try {
            Datasource result = datasourceService.loadWithPlainPassword(id);
            if (result == null) {
                return new ReturnT<>(ReturnT.FAIL_CODE, "数据源不存在");
            }
            return new ReturnT<>(result);
        } catch (Exception e) {
            log.error("查询数据源详情失败", e);
            return new ReturnT<>(ReturnT.FAIL_CODE, "查询失败：" + e.getMessage());
        }
    }

    @PostMapping("/add")
    @Operation(summary = "新增数据源", description = "创建新的数据源配置")
    public ReturnT<String> add(@RequestBody Datasource datasource) {
        // TODO: 从登录用户获取用户名
        String loginUser = "admin"; // 临时写死，实际应该从session或token中获取
        
        return datasourceService.add(datasource, loginUser);
    }

    @PostMapping("/update")
    @Operation(summary = "更新数据源", description = "更新现有数据源配置")
    public ReturnT<String> update(@RequestBody Datasource datasource) {
        // TODO: 从登录用户获取用户名
        String loginUser = "admin"; // 临时写死，实际应该从session或token中获取
        
        return datasourceService.update(datasource, loginUser);
    }

    @PostMapping("/delete/{id}")
    @Operation(summary = "删除数据源", description = "逻辑删除指定的数据源")
    public ReturnT<String> delete(@Parameter(description = "数据源ID") @PathVariable("id") Long id) {
        // TODO: 从登录用户获取用户名
        String loginUser = "admin"; // 临时写死，实际应该从session或token中获取
        
        return datasourceService.delete(id, loginUser);
    }

    @PostMapping("/test/{id}")
    @Operation(summary = "测试数据源连接", description = "测试指定数据源的连接状态并保存测试结果")
    public ReturnT<String> testConnection(@Parameter(description = "数据源ID") @PathVariable("id") Long id) {
        // TODO: 从登录用户获取用户名
        String loginUser = "admin"; // 临时写死，实际应该从session或token中获取
        
        return datasourceService.testConnection(id, loginUser);
    }

    @PostMapping("/testOnly")
    @Operation(summary = "仅测试连接", description = "测试数据源连接但不保存测试结果，用于新增时的连接验证")
    public ReturnT<String> testConnectionOnly(@RequestBody Datasource datasource) {
        return datasourceService.testConnectionOnly(datasource);
    }

    @GetMapping("/types")
    @Operation(summary = "获取数据源类型列表", description = "返回系统支持的所有数据源类型")
    public ReturnT<List<Map<String, Object>>> getDatasourceTypes() {
        try {
            List<Map<String, Object>> result = datasourceService.getDatasourceTypes();
            return new ReturnT<>(result);
        } catch (Exception e) {
            log.error("获取数据源类型失败", e);
            return new ReturnT<>(ReturnT.FAIL_CODE, "获取失败：" + e.getMessage());
        }
    }

    @GetMapping("/defaultPort/{type}")
    @Operation(summary = "获取默认端口", description = "根据数据源类型获取默认端口号")
    public ReturnT<Integer> getDefaultPort(@Parameter(description = "数据源类型") @PathVariable("type") String type) {
        try {
            int port = DatasourceConnectionUtil.getDefaultPort(type);
            return new ReturnT<>(port);
        } catch (Exception e) {
            log.error("获取默认端口失败", e);
            return new ReturnT<>(ReturnT.FAIL_CODE, "获取失败：" + e.getMessage());
        }
    }

    @GetMapping("/defaultParams/{type}")
    @Operation(summary = "获取默认连接参数", description = "根据数据源类型获取默认连接参数")
    public ReturnT<String> getDefaultConnectionParams(@Parameter(description = "数据源类型") @PathVariable("type") String type) {
        try {
            String params = DatasourceConnectionUtil.getDefaultConnectionParams(type);
            return new ReturnT<>(params);
        } catch (Exception e) {
            log.error("获取默认连接参数失败", e);
            return new ReturnT<>(ReturnT.FAIL_CODE, "获取失败：" + e.getMessage());
        }
    }

    @GetMapping("/databases/{id}")
    @Operation(summary = "获取数据库列表", description = "获取指定数据源下的所有数据库")
    public ReturnT<List<String>> getDatabases(@Parameter(description = "数据源ID") @PathVariable("id") Long id) {
        return datasourceService.getDatabases(id);
    }

    @GetMapping("/tables/{id}")
    @Operation(summary = "获取表列表", description = "获取指定数据源和数据库下的所有表")
    public ReturnT<List<String>> getTables(
            @Parameter(description = "数据源ID") @PathVariable("id") Long id,
            @Parameter(description = "数据库名称") @RequestParam("database") String database) {
        return datasourceService.getTables(id, database);
    }

    @GetMapping("/columns/{id}")
    @Operation(summary = "获取字段列表", description = "获取指定表的所有字段信息")
    public ReturnT<List<Map<String, Object>>> getColumns(
            @Parameter(description = "数据源ID") @PathVariable("id") Long id,
            @Parameter(description = "数据库名称") @RequestParam("database") String database,
            @Parameter(description = "表名称") @RequestParam("table") String table) {
        return datasourceService.getColumns(id, database, table);
    }

    @PostMapping("/query/{id}")
    @Operation(summary = "执行SQL查询", description = "在指定数据源上执行SQL查询语句")
    public ReturnT<Map<String, Object>> executeQuery(
            @Parameter(description = "数据源ID") @PathVariable("id") Long id,
            @Parameter(description = "SQL语句") @RequestParam("sql") String sql,
            @Parameter(description = "限制返回行数", example = "100") @RequestParam(value = "limit", defaultValue = "100") int limit) {
        return datasourceService.executeQuery(id, sql, limit);
    }

    @GetMapping("/findByType/{type}")
    @Operation(summary = "根据类型查询数据源", description = "查询指定类型的所有启用数据源")
    public ReturnT<List<Datasource>> findByType(@Parameter(description = "数据源类型") @PathVariable("type") String type) {
        try {
            List<Datasource> result = datasourceService.findByType(type);
            return new ReturnT<>(result);
        } catch (Exception e) {
            log.error("根据类型查询数据源失败", e);
            return new ReturnT<>(ReturnT.FAIL_CODE, "查询失败：" + e.getMessage());
        }
    }
}
