package com.sevb.admin.controller;

import com.sevb.admin.core.model.AlertSendRecord;
import com.sevb.admin.service.AlertSendRecordService;
import com.sevb.core.biz.model.ReturnT;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.web.bind.annotation.*;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 告警发送记录管理控制器
 * <AUTHOR>
 */
@RestController
@RequestMapping("/api/alert/record")
public class AlertRecordController {

    private static final Logger logger = LoggerFactory.getLogger(AlertRecordController.class);

    @Autowired
    private AlertSendRecordService alertSendRecordService;

    /**
     * 分页查询发送记录列表
     */
    @GetMapping("/list")
    public ReturnT<Map<String, Object>> pageList(@RequestParam(required = false, defaultValue = "0") int start,
                                                 @RequestParam(required = false, defaultValue = "10") int length,
                                                 @RequestParam(required = false) String requestId,
                                                 @RequestParam(required = false) String templateCode,
                                                 @RequestParam(required = false) String channelType,
                                                 @RequestParam(required = false) String sendStatus,
                                                 @RequestParam(required = false) String sourceSystem,
                                                 @RequestParam(required = false) String businessId,
                                                 @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date startTime,
                                                 @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date endTime) {
        try {
            Map<String, Object> result = alertSendRecordService.pageList(start, length, requestId, templateCode, channelType,
                sendStatus, sourceSystem, businessId, startTime, endTime);
            return new ReturnT<>(result);
        } catch (Exception e) {
            logger.error("查询发送记录列表失败", e);
            return new ReturnT<>(ReturnT.FAIL_CODE, "查询失败: " + e.getMessage());
        }
    }

    /**
     * 获取发送记录详情
     */
    @RequestMapping("/detail")
    @ResponseBody
    public ReturnT<AlertSendRecord> detail(@RequestParam("id") Long id) {
        try {
            AlertSendRecord record = alertSendRecordService.load(id);
            if (record == null) {
                return new ReturnT<>(ReturnT.FAIL_CODE, "发送记录不存在");
            }
            return new ReturnT<>(record);
        } catch (Exception e) {
            logger.error("获取发送记录详情失败", e);
            return new ReturnT<>(ReturnT.FAIL_CODE, "获取失败: " + e.getMessage());
        }
    }

    /**
     * 根据请求ID查询记录列表
     */
    @RequestMapping("/listByRequestId")
    @ResponseBody
    public ReturnT<List<AlertSendRecord>> listByRequestId(@RequestParam("requestId") String requestId) {
        try {
            List<AlertSendRecord> records = alertSendRecordService.findByRequestId(requestId);
            return new ReturnT<>(records);
        } catch (Exception e) {
            logger.error("根据请求ID查询记录失败", e);
            return new ReturnT<>(ReturnT.FAIL_CODE, "查询失败: " + e.getMessage());
        }
    }

    /**
     * 根据业务ID查询记录列表
     */
    @RequestMapping("/listByBusinessId")
    @ResponseBody
    public ReturnT<List<AlertSendRecord>> listByBusinessId(@RequestParam("businessId") String businessId) {
        try {
            List<AlertSendRecord> records = alertSendRecordService.findByBusinessId(businessId);
            return new ReturnT<>(records);
        } catch (Exception e) {
            logger.error("根据业务ID查询记录失败", e);
            return new ReturnT<>(ReturnT.FAIL_CODE, "查询失败: " + e.getMessage());
        }
    }

    /**
     * 批量删除发送记录
     */
    @RequestMapping("/batchDelete")
    @ResponseBody
    public ReturnT<String> batchDelete(@RequestParam("ids") String ids) {
        try {
            String[] idArray = ids.split(",");
            List<Long> idList = new java.util.ArrayList<>();
            for (String idStr : idArray) {
                idList.add(Long.parseLong(idStr.trim()));
            }
            return alertSendRecordService.batchDelete(idList);
        } catch (Exception e) {
            logger.error("批量删除发送记录失败", e);
            return new ReturnT<>(ReturnT.FAIL_CODE, "批量删除失败: " + e.getMessage());
        }
    }

    /**
     * 按时间范围删除记录
     */
    @RequestMapping("/deleteByTimeRange")
    @ResponseBody
    public ReturnT<String> deleteByTimeRange(@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date startTime,
                                            @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date endTime) {
        return alertSendRecordService.deleteByTimeRange(startTime, endTime);
    }

    /**
     * 获取统计信息
     */
    @RequestMapping("/stats")
    @ResponseBody
    public ReturnT<Map<String, Object>> getStats(@RequestParam(defaultValue = "24") int hours) {
        try {
            Map<String, Object> stats = alertSendRecordService.getRecentStats(hours);
            return new ReturnT<>(stats);
        } catch (Exception e) {
            logger.error("获取统计信息失败", e);
            return new ReturnT<>(ReturnT.FAIL_CODE, "获取失败: " + e.getMessage());
        }
    }

    /**
     * 按状态统计
     */
    @RequestMapping("/statsByStatus")
    @ResponseBody
    public ReturnT<List<Map<String, Object>>> statsByStatus(
            @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date startTime,
            @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date endTime) {
        try {
            List<Map<String, Object>> stats = alertSendRecordService.countGroupByStatus(startTime, endTime);
            return new ReturnT<>(stats);
        } catch (Exception e) {
            logger.error("按状态统计失败", e);
            return new ReturnT<>(ReturnT.FAIL_CODE, "统计失败: " + e.getMessage());
        }
    }

    /**
     * 按通道类型统计
     */
    @RequestMapping("/statsByChannelType")
    @ResponseBody
    public ReturnT<List<Map<String, Object>>> statsByChannelType(
            @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date startTime,
            @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date endTime) {
        try {
            List<Map<String, Object>> stats = alertSendRecordService.countGroupByChannelType(startTime, endTime);
            return new ReturnT<>(stats);
        } catch (Exception e) {
            logger.error("按通道类型统计失败", e);
            return new ReturnT<>(ReturnT.FAIL_CODE, "统计失败: " + e.getMessage());
        }
    }

    /**
     * 获取成功率统计
     */
    @RequestMapping("/successRateStats")
    @ResponseBody
    public ReturnT<Map<String, Object>> getSuccessRateStats(
            @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date startTime,
            @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date endTime) {
        try {
            Map<String, Object> stats = alertSendRecordService.getSuccessRateStats(startTime, endTime);
            return new ReturnT<>(stats);
        } catch (Exception e) {
            logger.error("获取成功率统计失败", e);
            return new ReturnT<>(ReturnT.FAIL_CODE, "获取失败: " + e.getMessage());
        }
    }

    /**
     * 查询失败记录
     */
    @RequestMapping("/failedRecords")
    @ResponseBody
    public ReturnT<List<AlertSendRecord>> getFailedRecords(
            @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date startTime,
            @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date endTime) {
        try {
            List<AlertSendRecord> records = alertSendRecordService.findFailedRecords(startTime, endTime);
            return new ReturnT<>(records);
        } catch (Exception e) {
            logger.error("查询失败记录失败", e);
            return new ReturnT<>(ReturnT.FAIL_CODE, "查询失败: " + e.getMessage());
        }
    }

    /**
     * 查询重试记录
     */
    @RequestMapping("/retryRecords")
    @ResponseBody
    public ReturnT<List<AlertSendRecord>> getRetryRecords(@RequestParam(defaultValue = "100") int limit) {
        try {
            List<AlertSendRecord> records = alertSendRecordService.findRetryRecords(limit);
            return new ReturnT<>(records);
        } catch (Exception e) {
            logger.error("查询重试记录失败", e);
            return new ReturnT<>(ReturnT.FAIL_CODE, "查询失败: " + e.getMessage());
        }
    }
}
