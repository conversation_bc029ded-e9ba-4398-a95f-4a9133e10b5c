package com.sevb.admin.controller;

import com.sevb.admin.controller.interceptor.PermissionInterceptor;
import com.sevb.admin.core.exception.XxlJobException;
import com.sevb.admin.core.model.JobGroup;
import com.sevb.admin.core.model.JobInfo;
import com.sevb.admin.core.model.JobUser;
import com.sevb.admin.core.route.ExecutorRouteStrategyEnum;
import com.sevb.admin.core.scheduler.MisfireStrategyEnum;
import com.sevb.admin.core.scheduler.ScheduleTypeEnum;
import com.sevb.admin.core.thread.JobScheduleHelper;
import com.sevb.admin.core.util.I18nUtil;
import com.sevb.admin.dao.JobGroupDao;
import com.sevb.admin.service.JobService;
import com.sevb.core.biz.model.ReturnT;
import com.sevb.core.enums.ExecutorBlockStrategyEnum;
import com.sevb.core.glue.GlueTypeEnum;
import com.sevb.core.util.DateUtil;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.*;

import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 任务信息管理控制器
 * <AUTHOR>
 */
@Controller
@RequestMapping("/jobinfo")
@Tag(name = "任务信息管理", description = "XXL-JOB任务的增删改查、启停、触发等功能")
public class JobInfoController {

	private static final Logger logger = LoggerFactory.getLogger(JobInfoController.class);

	@Resource
	private JobGroupDao jobGroupDao;
	@Resource
	private JobService jobService;
	
	@GetMapping
	@Operation(summary = "任务管理页面", description = "返回任务管理的HTML页面")
	public String index(HttpServletRequest request, Model model,
						@Parameter(description = "执行器组ID", example = "1") @RequestParam(value = "jobGroup", required = false, defaultValue = "-1") int jobGroup) {

		// 枚举-字典
		model.addAttribute("ExecutorRouteStrategyEnum", ExecutorRouteStrategyEnum.values());	    // 路由策略-列表
		model.addAttribute("GlueTypeEnum", GlueTypeEnum.values());								// Glue类型-字典
		model.addAttribute("ExecutorBlockStrategyEnum", ExecutorBlockStrategyEnum.values());	    // 阻塞处理策略-字典
		model.addAttribute("ScheduleTypeEnum", ScheduleTypeEnum.values());	    				// 调度类型
		model.addAttribute("MisfireStrategyEnum", MisfireStrategyEnum.values());	    			// 调度过期策略

		// 执行器列表
		List<JobGroup> jobGroupList_all =  jobGroupDao.findAll();

		// filter group
		List<JobGroup> jobGroupList = PermissionInterceptor.filterJobGroupByRole(request, jobGroupList_all);
		if (jobGroupList==null || jobGroupList.isEmpty()) {
			throw new XxlJobException(I18nUtil.getString("jobgroup_empty"));
		}

		model.addAttribute("JobGroupList", jobGroupList);
		model.addAttribute("jobGroup", jobGroup);

		return "jobinfo/jobinfo.index";
	}

	@PostMapping("/pageList")
	@ResponseBody
	@Operation(summary = "分页查询任务列表", description = "根据条件分页查询任务信息列表")
	public Map<String, Object> pageList(
			@Parameter(description = "起始位置", example = "0") @RequestParam(value = "start", required = false, defaultValue = "0") int start,
			@Parameter(description = "每页数量", example = "10") @RequestParam(value = "length", required = false, defaultValue = "10") int length,
			@Parameter(description = "执行器组ID", example = "1") @RequestParam("jobGroup") int jobGroup,
			@Parameter(description = "触发状态：-1全部，0停止，1运行", example = "-1") @RequestParam("triggerStatus") int triggerStatus,
			@Parameter(description = "任务描述（模糊查询）", example = "测试任务") @RequestParam("jobDesc") String jobDesc,
			@Parameter(description = "执行器处理器（模糊查询）", example = "testHandler") @RequestParam("executorHandler") String executorHandler,
			@Parameter(description = "负责人（模糊查询）", example = "admin") @RequestParam("author") String author) {
		
		return jobService.pageList(start, length, jobGroup, triggerStatus, jobDesc, executorHandler, author);
	}
	
	@PostMapping("/add")
	@ResponseBody
	@Operation(summary = "新增任务", description = "创建一个新的定时任务")
	public ReturnT<String> add(HttpServletRequest request,
							   @Parameter(description = "任务信息对象") JobInfo jobInfo) {
		// valid permission
		PermissionInterceptor.validJobGroupPermission(request, jobInfo.getJobGroup());

		// opt
		JobUser loginUser = PermissionInterceptor.getLoginUser(request);
		return jobService.add(jobInfo, loginUser);
	}
	
	@PutMapping("/update")
	@ResponseBody
	@Operation(summary = "更新任务", description = "更新指定任务的配置信息")
	public ReturnT<String> update(HttpServletRequest request,
								  @Parameter(description = "任务信息对象") JobInfo jobInfo) {
		// valid permission
		PermissionInterceptor.validJobGroupPermission(request, jobInfo.getJobGroup());

		// opt
		JobUser loginUser = PermissionInterceptor.getLoginUser(request);
		return jobService.update(jobInfo, loginUser);
	}
	
	@DeleteMapping("/remove")
	@ResponseBody
	@Operation(summary = "删除任务", description = "删除指定ID的任务")
	public ReturnT<String> remove(@Parameter(description = "任务ID", example = "1") @RequestParam("id") int id) {
		return jobService.remove(id);
	}
	
	@PostMapping("/stop")
	@ResponseBody
	@Operation(summary = "停止任务", description = "停止指定ID的任务调度")
	public ReturnT<String> pause(@Parameter(description = "任务ID", example = "1") @RequestParam("id") int id) {
		return jobService.stop(id);
	}
	
	@PostMapping("/start")
	@ResponseBody
	@Operation(summary = "启动任务", description = "启动指定ID的任务调度")
	public ReturnT<String> start(@Parameter(description = "任务ID", example = "1") @RequestParam("id") int id) {
		return jobService.start(id);
	}
	
	@PostMapping("/trigger")
	@ResponseBody
	@Operation(summary = "手动触发任务", description = "立即执行指定ID的任务")
	public ReturnT<String> triggerJob(HttpServletRequest request,
									  @Parameter(description = "任务ID", example = "1") @RequestParam("id") int id,
									  @Parameter(description = "执行参数", example = "param1=value1") @RequestParam("executorParam") String executorParam,
									  @Parameter(description = "执行器地址列表，多个用逗号分隔", example = "127.0.0.1:9999") @RequestParam("addressList") String addressList) {

		// login user
		JobUser loginUser = PermissionInterceptor.getLoginUser(request);
		// trigger
		return jobService.trigger(loginUser, id, executorParam, addressList);
	}

	@GetMapping("/nextTriggerTime")
	@ResponseBody
	@Operation(summary = "计算下次触发时间", description = "根据调度配置计算未来几次的触发时间")
	public ReturnT<List<String>> nextTriggerTime(
			@Parameter(description = "调度类型：CRON、FIX_RATE等", example = "CRON") @RequestParam("scheduleType") String scheduleType,
			@Parameter(description = "调度配置：如CRON表达式", example = "0 0 12 * * ?") @RequestParam("scheduleConf") String scheduleConf) {

		JobInfo paramJobInfo = new JobInfo();
		paramJobInfo.setScheduleType(scheduleType);
		paramJobInfo.setScheduleConf(scheduleConf);

		List<String> result = new ArrayList<>();
		try {
			Date lastTime = new Date();
			for (int i = 0; i < 5; i++) {
				lastTime = JobScheduleHelper.generateNextValidTime(paramJobInfo, lastTime);
				if (lastTime != null) {
					result.add(DateUtil.formatDateTime(lastTime));
				} else {
					break;
				}
			}
		} catch (Exception e) {
			logger.error("nextTriggerTime error. scheduleType = {}, scheduleConf= {}", scheduleType, scheduleConf, e);
			return new ReturnT<List<String>>(ReturnT.FAIL_CODE, (I18nUtil.getString("schedule_type")+I18nUtil.getString("system_unvalid")) + e.getMessage());
		}
		return new ReturnT<List<String>>(result);

	}
	
}
