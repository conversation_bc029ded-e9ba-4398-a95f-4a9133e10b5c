package com.sevb.admin.controller;

import com.sevb.admin.core.alarm.model.AlertRequest;
import com.sevb.admin.service.AlertService;
import com.sevb.core.biz.model.ReturnT;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 告警API控制器
 * <AUTHOR>
 */
@RestController
@RequestMapping("/api/alert")
public class AlertController {

    private static final Logger logger = LoggerFactory.getLogger(AlertController.class);

    @Autowired
    private AlertService alertService;

    /**
     * 发送告警
     */
    @PostMapping("/send")
    public ReturnT<String> sendAlert(@RequestBody AlertRequest request) {
        try {
            String requestId = alertService.sendAlert(request);
            return new ReturnT<>(ReturnT.SUCCESS_CODE, "告警发送成功", requestId);
        } catch (Exception e) {
            logger.error("发送告警失败", e);
            return new ReturnT<>(ReturnT.FAIL_CODE, "发送失败: " + e.getMessage());
        }
    }

    /**
     * 批量发送告警
     */
    @PostMapping("/send/batch")
    public ReturnT<List<String>> sendAlertBatch(@RequestBody List<AlertRequest> requests) {
        try {
            List<String> requestIds = alertService.sendAlertBatch(requests);
            return new ReturnT<>(ReturnT.SUCCESS_CODE, "批量发送成功", requestIds);
        } catch (Exception e) {
            logger.error("批量发送告警失败", e);
            return new ReturnT<>(ReturnT.FAIL_CODE, "批量发送失败: " + e.getMessage());
        }
    }

    /**
     * 查询告警状态
     */
    @GetMapping("/status/{requestId}")
    public ReturnT<Map<String, Object>> getAlertStatus(@PathVariable String requestId) {
        try {
            Map<String, Object> status = alertService.getAlertStatus(requestId);
            return new ReturnT<>(ReturnT.SUCCESS_CODE, "查询成功", status);
        } catch (Exception e) {
            logger.error("查询告警状态失败: requestId={}", requestId, e);
            return new ReturnT<>(ReturnT.FAIL_CODE, "查询失败: " + e.getMessage());
        }
    }

    /**
     * 重试告警
     */
    @PostMapping("/retry/{recordId}")
    public ReturnT<String> retryAlert(@PathVariable Long recordId) {
        try {
            return alertService.retryAlert(recordId);
        } catch (Exception e) {
            logger.error("重试告警失败: recordId={}", recordId, e);
            return new ReturnT<>(ReturnT.FAIL_CODE, "重试失败: " + e.getMessage());
        }
    }

    /**
     * 取消告警
     */
    @PostMapping("/cancel/{requestId}")
    public ReturnT<String> cancelAlert(@PathVariable String requestId) {
        try {
            return alertService.cancelAlert(requestId);
        } catch (Exception e) {
            logger.error("取消告警失败: requestId={}", requestId, e);
            return new ReturnT<>(ReturnT.FAIL_CODE, "取消失败: " + e.getMessage());
        }
    }

    /**
     * 获取告警统计
     */
    @GetMapping("/stats")
    public ReturnT<Map<String, Object>> getAlertStats(@RequestParam(defaultValue = "24") int hours) {
        try {
            Map<String, Object> stats = alertService.getAlertStats(hours);
            return new ReturnT<>(ReturnT.SUCCESS_CODE, "查询成功", stats);
        } catch (Exception e) {
            logger.error("获取告警统计失败", e);
            return new ReturnT<>(ReturnT.FAIL_CODE, "查询失败: " + e.getMessage());
        }
    }

    /**
     * 测试告警
     */
    @PostMapping("/test")
    public ReturnT<String> testAlert(@RequestBody Map<String, Object> testRequest) {
        try {
            String templateCode = (String) testRequest.get("templateCode");
            String channelType = (String) testRequest.get("channelType");
            List<String> recipients = (List<String>) testRequest.get("recipients");
            Map<String, Object> variables = (Map<String, Object>) testRequest.get("variables");

            return alertService.testAlert(templateCode, channelType, recipients, variables);
        } catch (Exception e) {
            logger.error("测试告警失败", e);
            return new ReturnT<>(ReturnT.FAIL_CODE, "测试失败: " + e.getMessage());
        }
    }

    /**
     * 手动处理重试队列
     */
    @PostMapping("/retry/process")
    public ReturnT<String> processRetryQueue() {
        try {
            int processedCount = alertService.processRetryQueue();
            return new ReturnT<>(ReturnT.SUCCESS_CODE, "处理完成，处理记录数: " + processedCount);
        } catch (Exception e) {
            logger.error("处理重试队列失败", e);
            return new ReturnT<>(ReturnT.FAIL_CODE, "处理失败: " + e.getMessage());
        }
    }

    /**
     * 清理历史记录
     */
    @PostMapping("/clean")
    public ReturnT<String> cleanHistoryRecords(@RequestParam(defaultValue = "30") int days) {
        try {
            int cleanedCount = alertService.cleanHistoryRecords(days);
            return new ReturnT<>(ReturnT.SUCCESS_CODE, "清理完成，清理记录数: " + cleanedCount);
        } catch (Exception e) {
            logger.error("清理历史记录失败", e);
            return new ReturnT<>(ReturnT.FAIL_CODE, "清理失败: " + e.getMessage());
        }
    }

    /**
     * 健康检查
     */
    @GetMapping("/health")
    public ReturnT<Map<String, Object>> healthCheck() {
        try {
            Map<String, Object> health = new HashMap<>();
            health.put("status", "UP");
            health.put("timestamp", System.currentTimeMillis());
            
            // 获取最近1小时的统计
            Map<String, Object> stats = alertService.getAlertStats(1);
            health.put("recentStats", stats);
            
            return new ReturnT<>(ReturnT.SUCCESS_CODE, "健康检查通过", health);
        } catch (Exception e) {
            logger.error("健康检查失败", e);
            
            Map<String, Object> health = new HashMap<>();
            health.put("status", "DOWN");
            health.put("error", e.getMessage());
            health.put("timestamp", System.currentTimeMillis());
            
            return new ReturnT<>(ReturnT.FAIL_CODE, "健康检查失败", health);
        }
    }
}
