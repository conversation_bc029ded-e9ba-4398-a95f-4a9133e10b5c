package com.sevb.admin.controller;

import com.sevb.admin.controller.interceptor.PermissionInterceptor;
import com.sevb.admin.core.complete.XxlJobCompleter;
import com.sevb.admin.core.exception.XxlJobException;
import com.sevb.admin.core.model.JobGroup;
import com.sevb.admin.core.model.JobInfo;
import com.sevb.admin.core.model.JobLog;
import com.sevb.admin.core.scheduler.XxlJobScheduler;
import com.sevb.admin.core.util.I18nUtil;
import com.sevb.admin.dao.JobGroupDao;
import com.sevb.admin.dao.JobInfoDao;
import com.sevb.admin.dao.JobLogDao;
import com.sevb.core.biz.ExecutorBiz;
import com.sevb.core.biz.model.KillParam;
import com.sevb.core.biz.model.LogParam;
import com.sevb.core.biz.model.LogResult;
import com.sevb.core.biz.model.ReturnT;
import com.sevb.core.util.DateUtil;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.util.HtmlUtils;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 任务执行日志管理控制器
 * <AUTHOR>
 */
@Controller
@RequestMapping("/joblog")
@Tag(name = "任务执行日志管理", description = "XXL-JOB任务执行日志的查询、详情、终止等功能")
public class JobLogController {
	private static final Logger logger = LoggerFactory.getLogger(JobLogController.class);

	@Resource
	private JobGroupDao jobGroupDao;
	@Resource
	public JobInfoDao jobInfoDao;
	@Resource
	public JobLogDao jobLogDao;

	@GetMapping
	@Operation(summary = "任务日志管理页面", description = "返回任务执行日志管理的HTML页面")
	public String index(HttpServletRequest request, Model model,
						@Parameter(description = "任务ID", example = "1") @RequestParam(value = "jobId", required = false, defaultValue = "0") Integer jobId) {

		// 执行器列表
		List<JobGroup> jobGroupList_all =  jobGroupDao.findAll();

		// filter group
		List<JobGroup> jobGroupList = PermissionInterceptor.filterJobGroupByRole(request, jobGroupList_all);
		if (jobGroupList==null || jobGroupList.isEmpty()) {
			throw new XxlJobException(I18nUtil.getString("jobgroup_empty"));
		}

		model.addAttribute("JobGroupList", jobGroupList);

		// 任务
		if (jobId > 0) {
			JobInfo jobInfo = jobInfoDao.loadById(jobId);
			if (jobInfo == null) {
				throw new RuntimeException(I18nUtil.getString("jobinfo_field_id") + I18nUtil.getString("system_unvalid"));
			}

			model.addAttribute("jobInfo", jobInfo);

			// valid permission
			PermissionInterceptor.validJobGroupPermission(request, jobInfo.getJobGroup());
		}

		return "joblog/joblog.index";
	}

	@GetMapping("/getJobsByGroup")
	@ResponseBody
	@Operation(summary = "根据执行器组查询任务列表", description = "获取指定执行器组下的所有任务")
	public ReturnT<List<JobInfo>> getJobsByGroup(
			@Parameter(description = "执行器组ID", example = "1") @RequestParam("jobGroup") int jobGroup){
		List<JobInfo> list = jobInfoDao.getJobsByGroup(jobGroup);
		return new ReturnT<>(list);
	}
	
	@PostMapping("/pageList")
	@ResponseBody
	@Operation(summary = "分页查询任务执行日志", description = "根据条件分页查询任务执行日志列表")
	public Map<String, Object> pageList(HttpServletRequest request,
										@Parameter(description = "起始位置", example = "0") @RequestParam(value = "start", required = false, defaultValue = "0") int start,
										@Parameter(description = "每页数量", example = "10") @RequestParam(value = "length", required = false, defaultValue = "10") int length,
										@Parameter(description = "执行器组ID", example = "1") @RequestParam("jobGroup") int jobGroup,
										@Parameter(description = "任务ID", example = "1") @RequestParam("jobId") int jobId,
										@Parameter(description = "日志状态：-1全部，1成功，2失败，3进行中", example = "-1") @RequestParam("logStatus") int logStatus,
										@Parameter(description = "时间过滤条件", example = "2024-07-24 - 2024-07-25") @RequestParam("filterTime") String filterTime) {

		// valid permission
		PermissionInterceptor.validJobGroupPermission(request, jobGroup);	// 仅管理员支持查询全部；普通用户仅支持查询有权限的 jobGroup
		
		// parse param
		Date triggerTimeStart = null;
		Date triggerTimeEnd = null;
		if (filterTime!=null && !filterTime.trim().isEmpty()) {
			String[] temp = filterTime.split(" - ");
			if (temp.length == 2) {
				triggerTimeStart = DateUtil.parseDateTime(temp[0]);
				triggerTimeEnd = DateUtil.parseDateTime(temp[1]);
			}
		}
		
		// page query
		List<JobLog> list = jobLogDao.pageList(start, length, jobGroup, jobId, triggerTimeStart, triggerTimeEnd, logStatus);
		int list_count = jobLogDao.pageListCount(start, length, jobGroup, jobId, triggerTimeStart, triggerTimeEnd, logStatus);
		
		// package result
		Map<String, Object> maps = new HashMap<>();
	    maps.put("recordsTotal", list_count);		// 总记录数
	    maps.put("recordsFiltered", list_count);	// 过滤后的总记录数
	    maps.put("data", list);  					// 分页列表
		return maps;
	}

	@GetMapping("/logDetailPage")
	@Operation(summary = "任务日志详情页面", description = "返回任务执行日志详情的HTML页面")
	public String logDetailPage(@Parameter(description = "日志ID", example = "1") @RequestParam("id") int id, Model model){

		// base check
        JobLog jobLog = jobLogDao.load(id);
		if (jobLog == null) {
            throw new RuntimeException(I18nUtil.getString("joblog_logid_unvalid"));
		}

        model.addAttribute("triggerCode", jobLog.getTriggerCode());
        model.addAttribute("handleCode", jobLog.getHandleCode());
        model.addAttribute("logId", jobLog.getId());
		return "joblog/joblog.detail";
	}

	@GetMapping("/logDetailCat")
	@ResponseBody
	@Operation(summary = "获取任务执行日志内容", description = "分页获取指定任务执行的详细日志内容")
	public ReturnT<LogResult> logDetailCat(
			@Parameter(description = "日志ID", example = "1") @RequestParam("logId") long logId,
			@Parameter(description = "起始行号", example = "1") @RequestParam("fromLineNum") int fromLineNum){
		try {
			// valid
			JobLog jobLog = jobLogDao.load(logId);	// todo, need to improve performance
			if (jobLog == null) {
				return new ReturnT<>(ReturnT.FAIL_CODE, I18nUtil.getString("joblog_logid_unvalid"));
			}

			// log cat
			ExecutorBiz executorBiz = XxlJobScheduler.getExecutorBiz(jobLog.getExecutorAddress());
			ReturnT<LogResult> logResult = executorBiz.log(new LogParam(jobLog.getTriggerTime().getTime(), logId, fromLineNum));

			// is end
            if (logResult.getContent()!=null && logResult.getContent().getFromLineNum() > logResult.getContent().getToLineNum()) {
                if (jobLog.getHandleCode() > 0) {
                    logResult.getContent().setEnd(true);
                }
            }

			// fix xss
			if (logResult.getContent()!=null && StringUtils.hasText(logResult.getContent().getLogContent())) {
				String newLogContent = logResult.getContent().getLogContent();
				newLogContent = HtmlUtils.htmlEscape(newLogContent, "UTF-8");
				logResult.getContent().setLogContent(newLogContent);
			}

			return logResult;
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return new ReturnT<>(ReturnT.FAIL_CODE, e.getMessage());
		}
	}

	@PostMapping("/logKill")
	@ResponseBody
	@Operation(summary = "终止任务执行", description = "强制终止正在执行的任务")
	public ReturnT<String> logKill(@Parameter(description = "日志ID", example = "1") @RequestParam("id") int id){
		// base check
		JobLog log = jobLogDao.load(id);
		JobInfo jobInfo = jobInfoDao.loadById(log.getJobId());
		if (jobInfo==null) {
			return new ReturnT<>(500, I18nUtil.getString("jobinfo_glue_jobid_unvalid"));
		}
		if (ReturnT.SUCCESS_CODE != log.getTriggerCode()) {
			return new ReturnT<>(500, I18nUtil.getString("joblog_kill_log_limit"));
		}

		// request of kill
		ReturnT<String> runResult;
		try {
			ExecutorBiz executorBiz = XxlJobScheduler.getExecutorBiz(log.getExecutorAddress());
			runResult = executorBiz.kill(new KillParam(jobInfo.getId()));
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			runResult = new ReturnT<>(500, e.getMessage());
		}

		if (ReturnT.SUCCESS_CODE == runResult.getCode()) {
			log.setHandleCode(ReturnT.FAIL_CODE);
			log.setHandleMsg( I18nUtil.getString("joblog_kill_log_byman")+":" + (runResult.getMsg()!=null?runResult.getMsg():""));
			log.setHandleTime(new Date());
			XxlJobCompleter.updateHandleInfoAndFinish(log);
			return new ReturnT<>(runResult.getMsg());
		} else {
			return new ReturnT<>(500, runResult.getMsg());
		}
	}

	@DeleteMapping("/clearLog")
	@ResponseBody
	@Operation(summary = "清理任务执行日志", description = "根据条件清理历史任务执行日志")
	public ReturnT<String> clearLog(HttpServletRequest request,
									@Parameter(description = "执行器组ID", example = "1") @RequestParam("jobGroup") int jobGroup,
									@Parameter(description = "任务ID", example = "1") @RequestParam("jobId") int jobId,
									@Parameter(description = "清理类型：1-清理一个月前，2-清理三个月前，3-清理六个月前，9-清理全部", example = "1") @RequestParam("type") int type){
		// valid permission
		PermissionInterceptor.validJobGroupPermission(request, jobGroup);

		// opt
		Date clearBeforeTime = null;
		int clearBeforeNum = 0;
		if (type == 1) {
			clearBeforeTime = DateUtil.addMonths(new Date(), -1);	// 清理一个月之前日志数据
		} else if (type == 2) {
			clearBeforeTime = DateUtil.addMonths(new Date(), -3);	// 清理三个月之前日志数据
		} else if (type == 3) {
			clearBeforeTime = DateUtil.addMonths(new Date(), -6);	// 清理六个月之前日志数据
		} else if (type == 4) {
			clearBeforeTime = DateUtil.addYears(new Date(), -1);	// 清理一年之前日志数据
		} else if (type == 5) {
			clearBeforeNum = 1000;		// 清理一千条以前日志数据
		} else if (type == 6) {
			clearBeforeNum = 10000;		// 清理一万条以前日志数据
		} else if (type == 7) {
			clearBeforeNum = 30000;		// 清理三万条以前日志数据
		} else if (type == 8) {
			clearBeforeNum = 100000;	// 清理十万条以前日志数据
		} else if (type == 9) {
            // 清理所有日志数据
			clearBeforeNum = 0;
        } else {
			return new ReturnT<>(ReturnT.FAIL_CODE, I18nUtil.getString("joblog_clean_type_unvalid"));
		}

		List<Long> logIds;
		do {
			logIds = jobLogDao.findClearLogIds(jobGroup, jobId, clearBeforeTime, clearBeforeNum, 1000);
			if (logIds!=null && !logIds.isEmpty()) {
				jobLogDao.clearLog(logIds);
			}
		} while (logIds!=null && !logIds.isEmpty());

		return ReturnT.SUCCESS;
	}

}
