package com.sevb.admin.controller;

import com.sevb.admin.core.alarm.channel.NotificationChannelManager;
import com.sevb.admin.core.model.AlertChannelConfig;
import com.sevb.admin.service.AlertChannelService;
import com.sevb.core.biz.model.ReturnT;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * 告警通道配置管理控制器
 * <AUTHOR>
 */
@RestController
@RequestMapping("/api/alert/channel")
public class AlertChannelController {

    private static final Logger logger = LoggerFactory.getLogger(AlertChannelController.class);

    @Autowired
    private AlertChannelService alertChannelService;

    @Autowired
    private NotificationChannelManager channelManager;

    /**
     * 分页查询通道配置列表
     */
    @GetMapping("/list")
    public ReturnT<Map<String, Object>> pageList(@RequestParam(required = false, defaultValue = "0") int start,
                                                 @RequestParam(required = false, defaultValue = "10") int length,
                                                 @RequestParam(required = false) String configName,
                                                 @RequestParam(required = false) String channelType,
                                                 @RequestParam(required = false) Integer enabled) {
        try {
            Map<String, Object> result = alertChannelService.pageList(start, length, configName, channelType, enabled);
            return new ReturnT<>(result);
        } catch (Exception e) {
            logger.error("查询通道配置列表失败", e);
            return new ReturnT<>(ReturnT.FAIL_CODE, "查询失败: " + e.getMessage());
        }
    }

    /**
     * 保存通道配置
     */
    @PostMapping("/save")
    public ReturnT<String> save(@RequestBody AlertChannelConfig channelConfig) {
        channelConfig.setCreateUser("admin");
        return alertChannelService.save(channelConfig);
    }

    /**
     * 更新通道配置
     */
    @PutMapping("/update")
    public ReturnT<String> update(@RequestBody AlertChannelConfig channelConfig) {
        channelConfig.setUpdateUser("admin");
        return alertChannelService.update(channelConfig);
    }

    /**
     * 删除通道配置
     */
    @DeleteMapping("/delete/{id}")
    public ReturnT<String> delete(@PathVariable Long id) {
        return alertChannelService.delete(id, "admin");
    }

    /**
     * 启用/禁用通道配置
     */
    @PutMapping("/updateEnabled")
    public ReturnT<String> updateEnabled(@RequestParam("id") Long id, @RequestParam("enabled") Integer enabled) {
        return alertChannelService.updateEnabled(id, enabled, "admin");
    }

    /**
     * 批量删除通道配置
     */
    @DeleteMapping("/batchDelete")
    public ReturnT<String> batchDelete(@RequestBody List<Long> ids) {
        try {
            return alertChannelService.batchDelete(ids, "admin");
        } catch (Exception e) {
            logger.error("批量删除通道配置失败", e);
            return new ReturnT<>(ReturnT.FAIL_CODE, "批量删除失败: " + e.getMessage());
        }
    }

    /**
     * 获取通道配置详情
     */
    @GetMapping("/detail/{id}")
    public ReturnT<AlertChannelConfig> detail(@PathVariable Long id) {
        try {
            AlertChannelConfig config = alertChannelService.load(id);
            if (config == null) {
                return new ReturnT<>(ReturnT.FAIL_CODE, "通道配置不存在");
            }
            // 解密认证配置用于编辑
            try {
                String decryptedConfig = alertChannelService.decryptAuthConfig(config.getAuthConfig());
                config.setAuthConfig(decryptedConfig);
            } catch (Exception e) {
                logger.error("解密认证配置失败", e);
            }
            return new ReturnT<>(config);
        } catch (Exception e) {
            logger.error("获取通道配置详情失败", e);
            return new ReturnT<>(ReturnT.FAIL_CODE, "获取失败: " + e.getMessage());
        }
    }

    /**
     * 根据通道类型查询配置列表
     */
    @GetMapping("/listByType")
    public ReturnT<List<AlertChannelConfig>> listByType(@RequestParam("channelType") String channelType) {
        try {
            List<AlertChannelConfig> configs = alertChannelService.findByChannelType(channelType);
            return new ReturnT<>(configs);
        } catch (Exception e) {
            logger.error("根据通道类型查询失败", e);
            return new ReturnT<>(ReturnT.FAIL_CODE, "查询失败: " + e.getMessage());
        }
    }

    /**
     * 测试发送
     */
    @PostMapping("/testSend/{id}")
    public ReturnT<String> testSend(@PathVariable Long id, @RequestBody(required = false) Map<String, Object> requestBody) {
        try {
            List<String> recipients = null;
            if (requestBody != null && requestBody.containsKey("recipients")) {
                recipients = (List<String>) requestBody.get("recipients");
            }
            return alertChannelService.testSend(id, recipients);
        } catch (Exception e) {
            logger.error("测试发送失败", e);
            return new ReturnT<>(ReturnT.FAIL_CODE, "测试发送失败: " + e.getMessage());
        }
    }



    /**
     * 获取支持的通道类型
     */
    @RequestMapping("/supportedTypes")
    @ResponseBody
    public ReturnT<List<String>> getSupportedChannelTypes() {
        try {
            List<String> types = channelManager.getSupportedChannelTypes();
            return new ReturnT<>(types);
        } catch (Exception e) {
            logger.error("获取支持的通道类型失败", e);
            return new ReturnT<>(ReturnT.FAIL_CODE, "获取失败: " + e.getMessage());
        }
    }

    /**
     * 获取通道信息
     */
    @RequestMapping("/channelInfo")
    @ResponseBody
    public ReturnT<Map<String, Object>> getChannelInfo(@RequestParam("channelType") String channelType) {
        try {
            Map<String, Object> info = channelManager.getChannelInfo(channelType);
            if (info == null) {
                return new ReturnT<>(ReturnT.FAIL_CODE, "不支持的通道类型");
            }
            return new ReturnT<>(info);
        } catch (Exception e) {
            logger.error("获取通道信息失败", e);
            return new ReturnT<>(ReturnT.FAIL_CODE, "获取失败: " + e.getMessage());
        }
    }

    /**
     * 获取所有通道信息
     */
    @RequestMapping("/allChannelInfo")
    @ResponseBody
    public ReturnT<Map<String, Map<String, Object>>> getAllChannelInfo() {
        try {
            Map<String, Map<String, Object>> allInfo = channelManager.getAllChannelInfo();
            return new ReturnT<>(allInfo);
        } catch (Exception e) {
            logger.error("获取所有通道信息失败", e);
            return new ReturnT<>(ReturnT.FAIL_CODE, "获取失败: " + e.getMessage());
        }
    }

    /**
     * 验证认证配置
     */
    @RequestMapping("/validateAuthConfig")
    @ResponseBody
    public ReturnT<String> validateAuthConfig(@RequestParam("channelType") String channelType,
                                             @RequestParam("authType") String authType,
                                             @RequestParam("authConfig") String authConfig) {
        return alertChannelService.validateAuthConfig(channelType, authType, authConfig);
    }

    /**
     * 复制通道配置
     */
    @RequestMapping("/copy")
    @ResponseBody
    public ReturnT<String> copyChannelConfig(@RequestParam("id") Long id,
                                            @RequestParam("newConfigName") String newConfigName) {
        try {
            AlertChannelConfig sourceConfig = alertChannelService.load(id);
            if (sourceConfig == null) {
                return new ReturnT<>(ReturnT.FAIL_CODE, "源配置不存在");
            }

            AlertChannelConfig newConfig = new AlertChannelConfig();
            newConfig.setConfigName(newConfigName);
            newConfig.setChannelType(sourceConfig.getChannelType());
            newConfig.setAuthType(sourceConfig.getAuthType());
            newConfig.setAuthConfig(sourceConfig.getAuthConfig());
            newConfig.setChannelConfig(sourceConfig.getChannelConfig());
            newConfig.setDescription("复制自: " + sourceConfig.getConfigName());
            newConfig.setEnabled(0); // 默认禁用
            newConfig.setCreateUser("admin");

            return alertChannelService.save(newConfig);
        } catch (Exception e) {
            logger.error("复制通道配置失败", e);
            return new ReturnT<>(ReturnT.FAIL_CODE, "复制失败: " + e.getMessage());
        }
    }
}
