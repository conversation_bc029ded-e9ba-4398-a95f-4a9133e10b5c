package com.sevb.admin.controller;

import com.sevb.admin.core.model.Project;
import com.sevb.admin.service.ProjectService;
import com.sevb.core.biz.model.ReturnT;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import jakarta.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * 项目管理控制器
 * <AUTHOR>
 */
@Slf4j
@Tag(name = "项目管理", description = "项目管理相关接口")
@RestController
@RequestMapping("/project")
public class ProjectController {

    @Resource
    private ProjectService projectService;

    @GetMapping("/pageList")
    @Operation(summary = "分页查询项目列表", description = "支持按项目名称、类型、状态进行筛选，支持权限控制")
    public ReturnT<Map<String, Object>> pageList(
            @Parameter(description = "页码，从1开始") @RequestParam(value = "pageNum", defaultValue = "1") int pageNum,
            @Parameter(description = "每页大小") @RequestParam(value = "pageSize", defaultValue = "10") int pageSize,
            @Parameter(description = "项目名称，支持模糊查询") @RequestParam(value = "name", required = false) String name,
            @Parameter(description = "项目类型：PUBLIC-公共项目，PRIVATE-私有项目") @RequestParam(value = "type", required = false) String type,
            @Parameter(description = "项目状态：0-禁用，1-启用") @RequestParam(value = "status", required = false) Integer status) {

        // TODO: 从登录用户获取用户ID
        String userId = "admin"; // 临时写死，实际应该从session或token中获取

        Map<String, Object> result = projectService.pageList(pageNum, pageSize, name, type, status, userId);
        return new ReturnT<>(result);
    }

    @PostMapping("/add")
    @Operation(summary = "新增项目", description = "创建新项目，创建者自动成为项目管理员")
    public ReturnT<String> add(@RequestBody Project project) {
        // TODO: 从登录用户获取用户名
        String loginUser = "admin"; // 临时写死，实际应该从session或token中获取
        
        return projectService.add(project, loginUser);
    }

    @PostMapping("/update")
    @Operation(summary = "更新项目", description = "更新项目信息，仅项目管理员可操作")
    public ReturnT<String> update(@RequestBody Project project) {
        // TODO: 从登录用户获取用户名
        String loginUser = "admin"; // 临时写死，实际应该从session或token中获取
        
        return projectService.update(project, loginUser);
    }

    @PostMapping("/delete/{id}")
    @Operation(summary = "删除项目", description = "删除指定项目及其所有成员，仅项目管理员可操作")
    public ReturnT<String> delete(@Parameter(description = "项目ID") @PathVariable("id") Long id) {
        // TODO: 从登录用户获取用户名
        String loginUser = "admin"; // 临时写死，实际应该从session或token中获取
        
        return projectService.delete(id, loginUser);
    }

    @GetMapping("/load/{id}")
    @Operation(summary = "查询项目详情", description = "根据项目ID查询项目详细信息")
    public ReturnT<Project> load(@Parameter(description = "项目ID") @PathVariable("id") Long id) {
        Project project = projectService.load(id);
        return new ReturnT<>(project);
    }

    @GetMapping("/loadByCode/{code}")
    @Operation(summary = "根据编码查询项目", description = "根据项目编码查询项目信息")
    public ReturnT<Project> loadByCode(@Parameter(description = "项目编码") @PathVariable("code") String code) {
        Project project = projectService.loadByCode(code);
        return new ReturnT<>(project);
    }

    @GetMapping("/accessible")
    @Operation(summary = "查询可访问项目列表", description = "查询当前用户可访问的所有项目（公共项目+参与的私有项目）")
    public ReturnT<List<Project>> findAccessibleProjects() {
        // TODO: 从登录用户获取用户ID
        String userId = "admin"; // 临时写死，实际应该从session或token中获取
        
        List<Project> projects = projectService.findAccessibleProjects(userId);
        return new ReturnT<>(projects);
    }

    @GetMapping("/public")
    @Operation(summary = "查询公共项目列表", description = "查询所有公共项目")
    public ReturnT<List<Project>> findPublicProjects() {
        List<Project> projects = projectService.findPublicProjects();
        return new ReturnT<>(projects);
    }

    @GetMapping("/user")
    @Operation(summary = "查询用户参与的项目", description = "查询当前用户参与的所有项目")
    public ReturnT<List<Project>> findUserProjects() {
        // TODO: 从登录用户获取用户ID
        String userId = "admin"; // 临时写死，实际应该从session或token中获取
        
        List<Project> projects = projectService.findUserProjects(userId);
        return new ReturnT<>(projects);
    }

    @PostMapping("/updateStatus/{id}")
    @Operation(summary = "更新项目状态", description = "启用或禁用项目，仅项目管理员可操作")
    public ReturnT<String> updateStatus(
            @Parameter(description = "项目ID") @PathVariable("id") Long id,
            @Parameter(description = "状态：0-禁用，1-启用") @RequestParam("status") Integer status) {
        
        // TODO: 从登录用户获取用户名
        String loginUser = "admin"; // 临时写死，实际应该从session或token中获取
        
        return projectService.updateStatus(id, status, loginUser);
    }

    @PostMapping("/updateType/{id}")
    @Operation(summary = "更新项目类型", description = "切换项目类型（公共/私有），仅项目管理员可操作")
    public ReturnT<String> updateType(
            @Parameter(description = "项目ID") @PathVariable("id") Long id,
            @Parameter(description = "项目类型：PUBLIC-公共项目，PRIVATE-私有项目") @RequestParam("type") String type) {
        
        // TODO: 从登录用户获取用户名
        String loginUser = "admin"; // 临时写死，实际应该从session或token中获取
        
        return projectService.updateType(id, type, loginUser);
    }

    @GetMapping("/hasAccess/{id}")
    @Operation(summary = "检查项目访问权限", description = "检查当前用户是否有指定项目的访问权限")
    public ReturnT<Boolean> hasProjectAccess(@Parameter(description = "项目ID") @PathVariable("id") Long id) {
        // TODO: 从登录用户获取用户ID
        String userId = "admin"; // 临时写死，实际应该从session或token中获取
        
        boolean hasAccess = projectService.hasProjectAccess(id, userId);
        return new ReturnT<>(hasAccess);
    }

    @GetMapping("/isAdmin/{id}")
    @Operation(summary = "检查是否为项目管理员", description = "检查当前用户是否为指定项目的管理员")
    public ReturnT<Boolean> isProjectAdmin(@Parameter(description = "项目ID") @PathVariable("id") Long id) {
        // TODO: 从登录用户获取用户ID
        String userId = "admin"; // 临时写死，实际应该从session或token中获取
        
        boolean isAdmin = projectService.isProjectAdmin(id, userId);
        return new ReturnT<>(isAdmin);
    }

    @GetMapping("/userRole/{id}")
    @Operation(summary = "获取用户项目角色", description = "获取当前用户在指定项目中的角色")
    public ReturnT<String> getUserRole(@Parameter(description = "项目ID") @PathVariable("id") Long id) {
        // TODO: 从登录用户获取用户ID
        String userId = "admin"; // 临时写死，实际应该从session或token中获取
        
        String role = projectService.getUserRole(id, userId);
        return new ReturnT<>(role);
    }

    @GetMapping("/types")
    @Operation(summary = "获取项目类型列表", description = "获取所有可用的项目类型")
    public ReturnT<List<Map<String, Object>>> getProjectTypes() {
        List<Map<String, Object>> types = projectService.getProjectTypes();
        return new ReturnT<>(types);
    }

    @GetMapping("/enabled")
    @Operation(summary = "查询启用的项目列表", description = "查询所有启用状态的项目")
    public ReturnT<List<Project>> findAllEnabled() {
        List<Project> projects = projectService.findAllEnabled();
        return new ReturnT<>(projects);
    }

    @GetMapping("/byType/{type}")
    @Operation(summary = "根据类型查询项目", description = "根据项目类型查询项目列表")
    public ReturnT<List<Project>> findByType(@Parameter(description = "项目类型") @PathVariable("type") String type) {
        List<Project> projects = projectService.findByType(type);
        return new ReturnT<>(projects);
    }
}
