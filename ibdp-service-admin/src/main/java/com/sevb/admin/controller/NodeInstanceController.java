package com.sevb.admin.controller;

import com.sevb.admin.core.model.NodeInstance;
import com.sevb.admin.service.NodeInstanceService;
import com.sevb.core.biz.model.ReturnT;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.springframework.web.bind.annotation.*;

import java.util.Date;
import java.util.Map;

/**
 * 节点实例管理
 * 
 * <AUTHOR>
 */
@Tag(name = "节点实例管理", description = "节点实例的查询和管理")
@RestController
@RequestMapping("/api/node-instance")
public class NodeInstanceController {

    @Resource
    private NodeInstanceService nodeInstanceService;

    /**
     * 分页查询节点实例
     */
    @Operation(summary = "分页查询节点实例", description = "根据条件分页查询节点实例列表")
    @GetMapping("/pageList")
    public ReturnT<Map<String, Object>> pageList(
            @Parameter(description = "页码", example = "1") @RequestParam(value = "page", defaultValue = "1") int page,
            @Parameter(description = "每页大小", example = "10") @RequestParam(value = "size", defaultValue = "10") int size,
            @Parameter(description = "项目ID", required = true, example = "1") @RequestParam("projectId") Long projectId,
            @Parameter(description = "工作流实例ID") @RequestParam(value = "workflowInstanceId", required = false) Long workflowInstanceId,
            @Parameter(description = "工作流ID") @RequestParam(value = "workflowId", required = false) Long workflowId,
            @Parameter(description = "节点ID") @RequestParam(value = "nodeId", required = false) Long nodeId,
            @Parameter(description = "节点名称") @RequestParam(value = "nodeName", required = false) String nodeName,
            @Parameter(description = "节点类型") @RequestParam(value = "nodeType", required = false) String nodeType,
            @Parameter(description = "实例状态") @RequestParam(value = "status", required = false) Integer status,
            @Parameter(description = "开始时间") @RequestParam(value = "startTime", required = false) Date startTime,
            @Parameter(description = "结束时间") @RequestParam(value = "endTime", required = false) Date endTime) {

        return nodeInstanceService.pageList(page, size, projectId, workflowInstanceId, workflowId,
                nodeId, nodeName, nodeType, status, startTime, endTime);
    }

    /**
     * 查询节点实例详情
     */
    @Operation(summary = "查询节点实例详情", description = "根据ID查询节点实例的详细信息")
    @GetMapping("/{id}")
    public ReturnT<NodeInstance> getById(
            @Parameter(description = "节点实例ID", required = true, example = "1") 
            @PathVariable("id") Long id) {
        return nodeInstanceService.getById(id);
    }

    /**
     * 根据工作流实例ID查询节点实例
     */
    @Operation(summary = "查询工作流实例的节点", description = "查询指定工作流实例下的所有节点实例")
    @GetMapping("/workflow-instance/{workflowInstanceId}")
    public ReturnT<Map<String, Object>> getByWorkflowInstanceId(
            @Parameter(description = "工作流实例ID", required = true, example = "1") 
            @PathVariable("workflowInstanceId") Long workflowInstanceId) {
        return nodeInstanceService.getByWorkflowInstanceId(workflowInstanceId);
    }

    /**
     * 重试节点实例
     */
    @Operation(summary = "重试节点实例", description = "重新执行失败的节点实例")
    @PostMapping("/retry/{id}")
    public ReturnT<String> retryNodeInstance(
            @Parameter(description = "节点实例ID", required = true, example = "1") 
            @PathVariable("id") Long id) {
        return nodeInstanceService.retryNodeInstance(id);
    }

    /**
     * 停止节点实例
     */
    @Operation(summary = "停止节点实例", description = "停止正在运行的节点实例")
    @PostMapping("/stop/{id}")
    public ReturnT<String> stopNodeInstance(
            @Parameter(description = "节点实例ID", required = true, example = "1") 
            @PathVariable("id") Long id) {
        return nodeInstanceService.stopNodeInstance(id);
    }

    /**
     * 查询节点实例日志
     */
    @Operation(summary = "查询节点实例日志", description = "查询节点实例的执行日志")
    @GetMapping("/{id}/log")
    public ReturnT<String> getNodeInstanceLog(
            @Parameter(description = "节点实例ID", required = true, example = "1") 
            @PathVariable("id") Long id) {
        return nodeInstanceService.getNodeInstanceLog(id);
    }

    /**
     * 查询节点统计信息
     */
    @Operation(summary = "查询节点统计", description = "查询指定节点的执行统计信息")
    @GetMapping("/stats/{nodeId}")
    public ReturnT<Map<String, Object>> getNodeStats(
            @Parameter(description = "节点ID", required = true, example = "1") 
            @PathVariable("nodeId") Long nodeId,
            @Parameter(description = "统计天数", example = "7") 
            @RequestParam(value = "days", defaultValue = "7") Integer days) {
        return nodeInstanceService.getNodeStats(nodeId, days);
    }

    /**
     * 查询节点的最近实例
     */
    @Operation(summary = "查询最近节点实例", description = "查询指定节点的最近执行实例")
    @GetMapping("/recent/{nodeId}")
    public ReturnT<Map<String, Object>> getRecentNodeInstances(
            @Parameter(description = "节点ID", required = true, example = "1") 
            @PathVariable("nodeId") Long nodeId,
            @Parameter(description = "查询数量", example = "10") 
            @RequestParam(value = "limit", defaultValue = "10") Integer limit) {
        return nodeInstanceService.getRecentNodeInstances(nodeId, limit);
    }
}
