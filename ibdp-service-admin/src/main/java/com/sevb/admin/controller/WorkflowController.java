package com.sevb.admin.controller;

import com.sevb.admin.core.dto.WorkflowScheduleConfigDTO;
import com.sevb.admin.core.model.Workflow;
import com.sevb.admin.service.WorkflowService;
import com.sevb.admin.service.workflow.WorkflowScheduleService;
import com.sevb.admin.service.orchestration.CentralWorkflowOrchestrationService;
import com.sevb.core.biz.model.ReturnT;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 工作流管理控制器
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/workflow")
@Tag(name = "工作流管理", description = "工作流的增删改查、发布上线等操作")
public class WorkflowController {

    @Resource
    private WorkflowService workflowService;

    @Resource
    private WorkflowScheduleService workflowScheduleService;

    @Resource
    private CentralWorkflowOrchestrationService centralOrchestrationService;

    /**
     * 分页查询工作流列表
     */
    @Operation(summary = "分页查询工作流列表", description = "支持按项目、名称、分类、状态进行筛选查询")
    @GetMapping("/pageList")
    public ReturnT<Map<String, Object>> pageList(
            @Parameter(description = "页码", example = "1") @RequestParam(value = "pageNum", defaultValue = "1") int pageNum,
            @Parameter(description = "每页大小", example = "10") @RequestParam(value = "pageSize", defaultValue = "10") int pageSize,
            @Parameter(description = "项目ID", required = true, example = "1") @RequestParam("projectId") Long projectId,
            @Parameter(description = "工作流名称（模糊查询）") @RequestParam(value = "name", required = false) String name,
            @Parameter(description = "工作流分类") @RequestParam(value = "category", required = false) String category,
            @Parameter(description = "状态：0-草稿，1-已发布，2-已上线，3-已下线") @RequestParam(value = "status", required = false) Integer status) {
        Map<String, Object> result = workflowService.pageList(pageNum, pageSize, projectId, name, category, status);
        return new ReturnT<>(result);
    }

    /**
     * 根据项目ID查询所有工作流
     */
    @Operation(summary = "根据项目ID查询所有工作流", description = "获取指定项目下的所有工作流")
    @GetMapping("/listByProject")
    public ReturnT<List<Workflow>> listByProject(@Parameter(description = "项目ID", required = true, example = "1") @RequestParam("projectId") Long projectId) {
        try {
            List<Workflow> list = workflowService.findByProjectId(projectId);
            return new ReturnT<>(list);
        } catch (Exception e) {
            log.error("查询项目工作流列表失败", e);
            return new ReturnT<>(ReturnT.FAIL_CODE, "查询失败：" + e.getMessage());
        }
    }

    /**
     * 根据项目ID和状态查询工作流
     */
    @Operation(summary = "根据项目ID和状态查询工作流", description = "获取指定项目下指定状态的工作流")
    @GetMapping("/listByProjectAndStatus")
    public ReturnT<List<Workflow>> listByProjectAndStatus(
            @Parameter(description = "项目ID", required = true, example = "1") @RequestParam("projectId") Long projectId,
            @Parameter(description = "状态", required = true, example = "2") @RequestParam("status") Integer status) {
        try {
            List<Workflow> list = workflowService.findByProjectIdAndStatus(projectId, status);
            return new ReturnT<>(list);
        } catch (Exception e) {
            log.error("根据项目和状态查询工作流失败", e);
            return new ReturnT<>(ReturnT.FAIL_CODE, "查询失败：" + e.getMessage());
        }
    }

    /**
     * 获取工作流详情
     */
    @Operation(summary = "获取工作流详情", description = "根据工作流ID获取详细信息")
    @GetMapping("/{id}")
    public ReturnT<Workflow> load(@Parameter(description = "工作流ID", required = true, example = "1") @PathVariable("id") Long id) {
        try {
            Workflow workflow = workflowService.load(id);
            if (workflow == null) {
                return new ReturnT<>(ReturnT.FAIL_CODE, "工作流不存在");
            }
            return new ReturnT<>(workflow);
        } catch (Exception e) {
            log.error("获取工作流详情失败", e);
            return new ReturnT<>(ReturnT.FAIL_CODE, "获取失败：" + e.getMessage());
        }
    }

    /**
     * 根据项目ID和编码获取工作流
     */
    @Operation(summary = "根据项目ID和编码获取工作流", description = "根据项目ID和工作流编码获取工作流信息")
    @GetMapping("/getByCode")
    public ReturnT<Workflow> getByCode(
            @Parameter(description = "项目ID", required = true, example = "1") @RequestParam("projectId") Long projectId,
            @Parameter(description = "工作流编码", required = true, example = "USER_DATA_ETL") @RequestParam("code") String code) {
        try {
            Workflow workflow = workflowService.findByProjectIdAndCode(projectId, code);
            if (workflow == null) {
                return new ReturnT<>(ReturnT.FAIL_CODE, "工作流不存在");
            }
            return new ReturnT<>(workflow);
        } catch (Exception e) {
            log.error("根据编码获取工作流失败", e);
            return new ReturnT<>(ReturnT.FAIL_CODE, "获取失败：" + e.getMessage());
        }
    }

    /**
     * 添加工作流
     */
    @Operation(summary = "添加工作流", description = "创建新的工作流，需要提供完整的工作流信息")
    @PostMapping("/add")
    public ReturnT<String> add(@Parameter(description = "工作流信息", required = true) @RequestBody Workflow workflow) {
        return workflowService.add(workflow);
    }

    /**
     * 更新工作流
     */
    @Operation(summary = "更新工作流", description = "更新现有工作流的信息")
    @PostMapping("/update")
    public ReturnT<String> update(@Parameter(description = "工作流信息", required = true) @RequestBody Workflow workflow) {
        return workflowService.update(workflow);
    }

    /**
     * 删除工作流
     */
    @Operation(summary = "删除工作流", description = "删除指定的工作流，只有草稿状态才能删除")
    @PostMapping("/delete")
    public ReturnT<String> delete(@Parameter(description = "工作流ID", required = true, example = "1") @RequestParam("id") Long id) {
        // TODO: 从session或token中获取当前用户
        String updateUser = "admin";
        return workflowService.delete(id, updateUser);
    }

    /**
     * 发布工作流
     */
    @Operation(summary = "发布工作流", description = "将草稿状态的工作流发布")
    @PostMapping("/publish")
    public ReturnT<String> publish(@Parameter(description = "工作流ID", required = true, example = "1") @RequestParam("id") Long id) {
        // TODO: 从session或token中获取当前用户
        String updateUser = "admin";
        return workflowService.publish(id, updateUser);
    }

    /**
     * 上线工作流
     */
    @Operation(summary = "上线工作流", description = "将已发布的工作流上线，上线后可以被调度执行")
    @PostMapping("/online")
    public ReturnT<String> online(@Parameter(description = "工作流ID", required = true, example = "1") @RequestParam("id") Long id) {
        // TODO: 从session或token中获取当前用户
        String updateUser = "admin";
        return workflowService.online(id, updateUser);
    }

    /**
     * 下线工作流
     */
    @Operation(summary = "下线工作流", description = "将已上线的工作流下线，下线后不能被调度执行")
    @PostMapping("/offline")
    public ReturnT<String> offline(@Parameter(description = "工作流ID", required = true, example = "1") @RequestParam("id") Long id) {
        // TODO: 从session或token中获取当前用户
        String updateUser = "admin";
        return workflowService.offline(id, updateUser);
    }

    /**
     * 获取工作流调度配置
     */
    @Operation(summary = "获取工作流调度配置", description = "获取已上线工作流的调度配置信息")
    @GetMapping("/schedule-config/{id}")
    public ReturnT<WorkflowScheduleConfigDTO> getScheduleConfig(
            @Parameter(description = "工作流ID", required = true, example = "1")
            @PathVariable("id") Long id) {
        return workflowScheduleService.getScheduleConfig(id);
    }

    /**
     * 更新工作流调度配置
     */
    @Operation(summary = "更新工作流调度配置", description = "更新已上线工作流的调度配置，如Cron表达式、超时时间等")
    @PostMapping("/schedule-config")
    public ReturnT<String> updateScheduleConfig(
            @Parameter(description = "调度配置信息", required = true)
            @RequestBody WorkflowScheduleConfigDTO config) {
        // TODO: 从session或token中获取当前用户
        String updateUser = "admin";
        return workflowScheduleService.updateScheduleConfig(config, updateUser);
    }

    /**
     * 启动工作流调度
     */
    @Operation(summary = "启动工作流调度", description = "启动已上线工作流的调度任务")
    @PostMapping("/schedule/start/{id}")
    public ReturnT<String> startSchedule(
            @Parameter(description = "工作流ID", required = true, example = "1")
            @PathVariable("id") Long id) {
        return workflowScheduleService.startWorkflowSchedule(id);
    }

    /**
     * 停止工作流调度
     */
    @Operation(summary = "停止工作流调度", description = "停止已上线工作流的调度任务")
    @PostMapping("/schedule/stop/{id}")
    public ReturnT<String> stopSchedule(
            @Parameter(description = "工作流ID", required = true, example = "1")
            @PathVariable("id") Long id) {
        return workflowScheduleService.stopWorkflowSchedule(id);
    }

    /**
     * 获取工作流调度状态
     */
    @Operation(summary = "获取工作流调度状态", description = "获取工作流的调度任务状态信息")
    @GetMapping("/schedule/status/{id}")
    public ReturnT<Map<String, Object>> getScheduleStatus(
            @Parameter(description = "工作流ID", required = true, example = "1")
            @PathVariable("id") Long id) {
        return workflowScheduleService.getScheduleStatus(id);
    }

    /**
     * 检查工作流下线安全性
     */
    @Operation(summary = "检查工作流下线安全性", description = "检查工作流是否可以安全下线，返回警告信息")
    @GetMapping("/offline/check/{id}")
    public ReturnT<Map<String, Object>> checkOfflineSafety(
            @Parameter(description = "工作流ID", required = true, example = "1")
            @PathVariable("id") Long id) {
        return workflowScheduleService.checkOfflineSafety(id);
    }

    /**
     * 获取工作流分类列表
     */
    @Operation(summary = "获取工作流分类列表", description = "获取所有可用的工作流分类，用于下拉选择")
    @GetMapping("/categoryList")
    public ReturnT<List<Map<String, Object>>> getCategoryList() {
        try {
            List<Map<String, Object>> categoryList = workflowService.getCategoryList();
            return new ReturnT<>(categoryList);
        } catch (Exception e) {
            log.error("获取工作流分类列表失败", e);
            return new ReturnT<>(ReturnT.FAIL_CODE, "获取失败：" + e.getMessage());
        }
    }

    /**
     * 获取工作流状态列表
     */
    @Operation(summary = "获取工作流状态列表", description = "获取所有可用的工作流状态，用于下拉选择")
    @GetMapping("/statusList")
    public ReturnT<List<Map<String, Object>>> getStatusList() {
        try {
            List<Map<String, Object>> statusList = workflowService.getStatusList();
            return new ReturnT<>(statusList);
        } catch (Exception e) {
            log.error("获取工作流状态列表失败", e);
            return new ReturnT<>(ReturnT.FAIL_CODE, "获取失败：" + e.getMessage());
        }
    }

    /**
     * 统计项目下的工作流数量
     */
    @Operation(summary = "统计项目下的工作流数量", description = "统计指定项目下的工作流总数")
    @GetMapping("/count")
    public ReturnT<Integer> countByProject(@Parameter(description = "项目ID", required = true, example = "1") @RequestParam("projectId") Long projectId) {
        try {
            int count = workflowService.countByProjectId(projectId);
            return new ReturnT<>(count);
        } catch (Exception e) {
            log.error("统计工作流数量失败", e);
            return new ReturnT<>(ReturnT.FAIL_CODE, "统计失败：" + e.getMessage());
        }
    }

    /**
     * 根据分类统计工作流数量
     */
    @Operation(summary = "根据分类统计工作流数量", description = "统计指定项目下各分类的工作流数量")
    @GetMapping("/countByCategory")
    public ReturnT<Map<String, Integer>> countByCategory(@Parameter(description = "项目ID", required = true, example = "1") @RequestParam("projectId") Long projectId) {
        try {
            Map<String, Integer> countMap = workflowService.countByCategory(projectId);
            return new ReturnT<>(countMap);
        } catch (Exception e) {
            log.error("根据分类统计工作流数量失败", e);
            return new ReturnT<>(ReturnT.FAIL_CODE, "统计失败：" + e.getMessage());
        }
    }

    /**
     * 手动执行工作流
     */
    @Operation(summary = "手动执行工作流", description = "立即执行一次工作流，不影响定时调度")
    @PostMapping("/execute")
    public ReturnT<String> execute(@Parameter(description = "工作流ID", required = true, example = "1") @RequestParam("id") Long id) {
        try {
            // TODO: 从session或token中获取当前用户
            String executeUser = "admin";
            return workflowService.execute(id, executeUser);
        } catch (Exception e) {
            log.error("手动执行工作流失败: workflowId={}", id, e);
            return new ReturnT<>(ReturnT.FAIL_CODE, "执行失败：" + e.getMessage());
        }
    }

    /**
     * 手动执行工作流（调度中心编排）
     */
    @Operation(summary = "手动执行工作流(调度中心编排)", description = "使用调度中心编排模式立即执行一次工作流")
    @PostMapping("/execute/central")
    public ReturnT<String> executeCentral(@Parameter(description = "工作流ID", required = true, example = "1") @RequestParam("id") Long id) {
        try {
            // TODO: 从session或token中获取当前用户
            String executeUser = "admin";
            return centralOrchestrationService.executeWorkflow(id, executeUser);
        } catch (Exception e) {
            log.error("调度中心编排执行工作流失败: workflowId={}", id, e);
            return new ReturnT<>(ReturnT.FAIL_CODE, "执行失败：" + e.getMessage());
        }
    }

    /**
     * 获取工作流执行详情
     */
    @Operation(summary = "获取工作流执行详情", description = "根据执行ID获取工作流执行的详细信息")
    @GetMapping("/execution/{executionId}")
    public ReturnT<Map<String, Object>> getExecution(
            @Parameter(description = "执行ID", required = true, example = "9d25a48e-54ea-4ba6-8efb-425beb235300")
            @PathVariable("executionId") String executionId) {
        try {
            return centralOrchestrationService.getExecutionDetails(executionId);
        } catch (Exception e) {
            log.error("获取工作流执行详情失败: executionId={}", executionId, e);
            return new ReturnT<>(ReturnT.FAIL_CODE, "获取失败：" + e.getMessage());
        }
    }

    /**
     * 获取工作流执行历史
     */
    @Operation(summary = "获取工作流执行历史", description = "查询工作流的执行历史记录")
    @GetMapping("/execution-history/{id}")
    public ReturnT<List<Map<String, Object>>> getExecutionHistory(
            @Parameter(description = "工作流ID", required = true, example = "1") @PathVariable("id") Long id,
            @Parameter(description = "页码", example = "1") @RequestParam(value = "page", defaultValue = "1") int page,
            @Parameter(description = "每页大小", example = "10") @RequestParam(value = "size", defaultValue = "10") int size) {
        try {
            // TODO: 实现执行历史查询逻辑
            // 这里先返回模拟数据
            List<Map<String, Object>> history = new ArrayList<>();
            Map<String, Object> record = new HashMap<>();
            record.put("executionId", "exec_" + System.currentTimeMillis());
            record.put("executeType", "MANUAL");
            record.put("status", "SUCCESS");
            record.put("startTime", System.currentTimeMillis() - 300000);
            record.put("endTime", System.currentTimeMillis() - 60000);
            record.put("duration", 240000);
            record.put("executeUser", "admin");
            history.add(record);

            return new ReturnT<>(history);
        } catch (Exception e) {
            log.error("获取工作流执行历史失败: workflowId={}", id, e);
            return new ReturnT<>(ReturnT.FAIL_CODE, "查询失败：" + e.getMessage());
        }
    }
}
