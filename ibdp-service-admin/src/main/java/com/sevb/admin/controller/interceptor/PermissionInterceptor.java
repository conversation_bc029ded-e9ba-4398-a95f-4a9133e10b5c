package com.sevb.admin.controller.interceptor;

import com.sevb.admin.controller.annotation.PermissionLimit;
import com.sevb.admin.core.conf.JobAdminConfig;
import com.sevb.admin.core.model.JobGroup;
import com.sevb.admin.core.model.JobUser;
import com.sevb.admin.core.util.I18nUtil;
import com.sevb.admin.dao.JobUserDao;
import com.sevb.admin.service.impl.LoginService;
import com.sevb.core.util.XxlJobRemotingUtil;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.stereotype.Component;
import org.springframework.web.method.HandlerMethod;
import org.springframework.web.servlet.AsyncHandlerInterceptor;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * 权限拦截
 *
 * <AUTHOR> 2015-12-12 18:09:04
 */
@Component
public class PermissionInterceptor implements AsyncHandlerInterceptor {

	@Resource
	private LoginService loginService;

	@Resource
	private JobUserDao jobUserDao;

	@Override
	public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
		
		if (!(handler instanceof HandlerMethod)) {
			return true;	// proceed with the next interceptor
		}

		// if need login
		boolean needLogin = true;
		boolean needAdminuser = false;
		HandlerMethod method = (HandlerMethod)handler;
		PermissionLimit permission = method.getMethodAnnotation(PermissionLimit.class);
		if (permission!=null) {
			needLogin = permission.limit();
			needAdminuser = permission.adminuser();
		}

		if (needLogin) {
			JobUser loginUser = null;

			// 首先尝试accessToken认证
			String accessToken = request.getHeader(XxlJobRemotingUtil.XXL_JOB_ACCESS_TOKEN);
			if (accessToken != null && accessToken.trim().length() > 0) {
				// 验证accessToken
				if (JobAdminConfig.getAdminConfig().getAccessToken() != null
						&& JobAdminConfig.getAdminConfig().getAccessToken().equals(accessToken)) {
					// accessToken有效，创建一个管理员用户对象
					loginUser = jobUserDao.loadByUserName("admin");
					if (loginUser == null) {
						// 如果admin用户不存在，创建一个临时的管理员用户对象
						loginUser = new JobUser();
						loginUser.setId(1);
						loginUser.setUsername("admin");
						loginUser.setRole(1); // 管理员角色
					}
				}
			}

			// 如果accessToken认证失败，尝试Cookie认证
			if (loginUser == null) {
				loginUser = loginService.ifLogin(request, response);
			}

			if (loginUser == null) {
				response.setStatus(302);
				response.setHeader("location", request.getContextPath()+"/toLogin");
				return false;
			}
			if (needAdminuser && loginUser.getRole()!=1) {
				throw new RuntimeException(I18nUtil.getString("system_permission_limit"));
			}

			// set loginUser, with request
			setLoginUser(request, loginUser);
		}

		return true;	// proceed with the next interceptor
	}


	// -------------------- permission tool --------------------

	/**
	 * set loginUser
	 *
	 * @param request
	 * @param loginUser
	 */
	private static void setLoginUser(HttpServletRequest request, JobUser loginUser){
		request.setAttribute("loginUser", loginUser);
	}

	/**
	 * get loginUser
	 *
	 * @param request
	 * @return
	 */
	public static JobUser getLoginUser(HttpServletRequest request){
		JobUser loginUser = (JobUser) request.getAttribute("loginUser");	// get loginUser, with request
		return loginUser;
	}

	/**
	 * valid permission by JobGroup
	 *
	 * @param request
	 * @param jobGroup
	 */
	public static void validJobGroupPermission(HttpServletRequest request, int jobGroup) {
		JobUser loginUser = getLoginUser(request);
		if (!loginUser.validPermission(jobGroup)) {
			throw new RuntimeException(I18nUtil.getString("system_permission_limit") + "[username="+ loginUser.getUsername() +"]");
		}
	}

	/**
	 * filter XxlJobGroup by role
	 *
	 * @param request
	 * @param jobGroupList_all
	 * @return
	 */
	public static List<JobGroup> filterJobGroupByRole(HttpServletRequest request, List<JobGroup> jobGroupList_all){
		List<JobGroup> jobGroupList = new ArrayList<>();
		if (jobGroupList_all!=null && jobGroupList_all.size()>0) {
			JobUser loginUser = PermissionInterceptor.getLoginUser(request);
			if (loginUser.getRole() == 1) {
				jobGroupList = jobGroupList_all;
			} else {
				List<String> groupIdStrs = new ArrayList<>();
				if (loginUser.getPermission()!=null && loginUser.getPermission().trim().length()>0) {
					groupIdStrs = Arrays.asList(loginUser.getPermission().trim().split(","));
				}
				for (JobGroup groupItem:jobGroupList_all) {
					if (groupIdStrs.contains(String.valueOf(groupItem.getId()))) {
						jobGroupList.add(groupItem);
					}
				}
			}
		}
		return jobGroupList;
	}

	
}
