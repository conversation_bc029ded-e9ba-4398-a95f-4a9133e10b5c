package com.sevb.admin.controller;

import com.sevb.admin.core.model.WorkflowInstance;
import com.sevb.admin.service.WorkflowInstanceService;
import com.sevb.core.biz.model.ReturnT;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.springframework.web.bind.annotation.*;

import java.util.Date;
import java.util.Map;

/**
 * 工作流实例管理
 * 
 * <AUTHOR>
 */
@Tag(name = "工作流实例管理", description = "工作流实例的查询和管理")
@RestController
@RequestMapping("/api/workflow-instance")
public class WorkflowInstanceController {

    @Resource
    private WorkflowInstanceService workflowInstanceService;

    /**
     * 分页查询工作流实例
     */
    @Operation(summary = "分页查询工作流实例", description = "根据条件分页查询工作流实例列表")
    @GetMapping("/pageList")
    public ReturnT<Map<String, Object>> pageList(
            @Parameter(description = "页码", example = "1") @RequestParam(value = "page", defaultValue = "1") int page,
            @Parameter(description = "每页大小", example = "10") @RequestParam(value = "size", defaultValue = "10") int size,
            @Parameter(description = "项目ID", required = true, example = "1") @RequestParam("projectId") Long projectId,
            @Parameter(description = "工作流ID") @RequestParam(value = "workflowId", required = false) Long workflowId,
            @Parameter(description = "工作流名称") @RequestParam(value = "workflowName", required = false) String workflowName,
            @Parameter(description = "实例状态") @RequestParam(value = "status", required = false) Integer status,
            @Parameter(description = "触发类型") @RequestParam(value = "triggerType", required = false) String triggerType,
            @Parameter(description = "开始时间") @RequestParam(value = "startTime", required = false) Date startTime,
            @Parameter(description = "结束时间") @RequestParam(value = "endTime", required = false) Date endTime) {

        return workflowInstanceService.pageList(page, size, projectId, workflowId, workflowName,
                status, triggerType, startTime, endTime);
    }

    /**
     * 查询工作流实例详情
     */
    @Operation(summary = "查询工作流实例详情", description = "根据ID查询工作流实例的详细信息")
    @GetMapping("/{id}")
    public ReturnT<WorkflowInstance> getById(
            @Parameter(description = "实例ID", required = true, example = "1") 
            @PathVariable("id") Long id) {
        return workflowInstanceService.getById(id);
    }

    /**
     * 停止工作流实例
     */
    @Operation(summary = "停止工作流实例", description = "停止正在运行的工作流实例")
    @PostMapping("/stop/{id}")
    public ReturnT<String> stopInstance(
            @Parameter(description = "实例ID", required = true, example = "1") 
            @PathVariable("id") Long id) {
        return workflowInstanceService.stopInstance(id);
    }

    /**
     * 重试工作流实例
     */
    @Operation(summary = "重试工作流实例", description = "重新执行失败的工作流实例")
    @PostMapping("/retry/{id}")
    public ReturnT<String> retryInstance(
            @Parameter(description = "实例ID", required = true, example = "1") 
            @PathVariable("id") Long id) {
        return workflowInstanceService.retryInstance(id);
    }

    /**
     * 查询工作流实例统计信息
     */
    @Operation(summary = "查询工作流实例统计", description = "查询指定工作流的实例执行统计信息")
    @GetMapping("/stats/{workflowId}")
    public ReturnT<Map<String, Object>> getInstanceStats(
            @Parameter(description = "工作流ID", required = true, example = "1") 
            @PathVariable("workflowId") Long workflowId,
            @Parameter(description = "统计天数", example = "7") 
            @RequestParam(value = "days", defaultValue = "7") Integer days) {
        return workflowInstanceService.getInstanceStats(workflowId, days);
    }

    /**
     * 查询工作流的最近实例
     */
    @Operation(summary = "查询最近实例", description = "查询指定工作流的最近执行实例")
    @GetMapping("/recent/{workflowId}")
    public ReturnT<Map<String, Object>> getRecentInstances(
            @Parameter(description = "工作流ID", required = true, example = "1") 
            @PathVariable("workflowId") Long workflowId,
            @Parameter(description = "查询数量", example = "10") 
            @RequestParam(value = "limit", defaultValue = "10") Integer limit) {
        return workflowInstanceService.getRecentInstances(workflowId, limit);
    }

    /**
     * 删除工作流实例
     */
    @Operation(summary = "删除工作流实例", description = "删除指定的工作流实例（仅限已完成的实例）")
    @DeleteMapping("/{id}")
    public ReturnT<String> deleteInstance(
            @Parameter(description = "实例ID", required = true, example = "1") 
            @PathVariable("id") Long id) {
        return workflowInstanceService.deleteInstance(id);
    }

    /**
     * 批量删除工作流实例
     */
    @Operation(summary = "批量删除工作流实例", description = "批量删除指定的工作流实例")
    @DeleteMapping("/batch")
    public ReturnT<String> batchDeleteInstances(
            @Parameter(description = "实例ID列表", required = true) 
            @RequestBody Long[] ids) {
        return workflowInstanceService.batchDeleteInstances(ids);
    }
}
