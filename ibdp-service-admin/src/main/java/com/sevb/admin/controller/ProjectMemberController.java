package com.sevb.admin.controller;

import com.sevb.admin.core.model.ProjectMember;
import com.sevb.admin.service.ProjectMemberService;
import com.sevb.core.biz.model.ReturnT;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import jakarta.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * 项目成员管理控制器
 * <AUTHOR>
 */
@Slf4j
@Tag(name = "项目成员管理", description = "项目成员管理相关接口")
@RestController
@RequestMapping("/project/member")
public class ProjectMemberController {

    @Resource
    private ProjectMemberService projectMemberService;

    @GetMapping("/pageList")
    @Operation(summary = "分页查询项目成员列表", description = "支持按项目ID、用户名、角色、状态进行筛选")
    public ReturnT<Map<String, Object>> pageList(
            @Parameter(description = "起始位置，从0开始") @RequestParam(value = "start", defaultValue = "0") int start,
            @Parameter(description = "每页大小") @RequestParam(value = "length", defaultValue = "10") int length,
            @Parameter(description = "项目ID") @RequestParam(value = "projectId", required = false) Long projectId,
            @Parameter(description = "用户名，支持模糊查询") @RequestParam(value = "username", required = false) String username,
            @Parameter(description = "角色：ADMIN-项目管理员，DEVELOPER-开发者，VIEWER-查看者") @RequestParam(value = "role", required = false) String role,
            @Parameter(description = "状态：0-禁用，1-启用") @RequestParam(value = "status", required = false) Integer status) {
        
        Map<String, Object> result = projectMemberService.pageList(start, length, projectId, username, role, status);
        return new ReturnT<>(result);
    }

    @PostMapping("/add")
    @Operation(summary = "添加项目成员", description = "向项目中添加新成员，仅项目管理员可操作")
    public ReturnT<String> addMember(@RequestBody ProjectMember member) {
        // TODO: 从登录用户获取用户名
        String loginUser = "admin"; // 临时写死，实际应该从session或token中获取
        
        return projectMemberService.addMember(member, loginUser);
    }

    @PostMapping("/batchAdd")
    @Operation(summary = "批量添加项目成员", description = "批量向项目中添加成员，仅项目管理员可操作")
    public ReturnT<String> batchAddMembers(@RequestBody List<ProjectMember> members) {
        // TODO: 从登录用户获取用户名
        String loginUser = "admin"; // 临时写死，实际应该从session或token中获取
        
        return projectMemberService.batchAddMembers(members, loginUser);
    }

    @PostMapping("/remove/{id}")
    @Operation(summary = "移除项目成员", description = "从项目中移除指定成员，仅项目管理员可操作")
    public ReturnT<String> removeMember(@Parameter(description = "成员ID") @PathVariable("id") Long id) {
        // TODO: 从登录用户获取用户名
        String loginUser = "admin"; // 临时写死，实际应该从session或token中获取
        
        return projectMemberService.removeMember(id, loginUser);
    }

    @PostMapping("/removeByUser")
    @Operation(summary = "根据项目和用户移除成员", description = "根据项目ID和用户ID移除成员，仅项目管理员可操作")
    public ReturnT<String> removeMemberByProjectAndUser(
            @Parameter(description = "项目ID") @RequestParam("projectId") Long projectId,
            @Parameter(description = "用户ID") @RequestParam("userId") String userId) {
        
        // TODO: 从登录用户获取用户名
        String loginUser = "admin"; // 临时写死，实际应该从session或token中获取
        
        return projectMemberService.removeMemberByProjectAndUser(projectId, userId, loginUser);
    }

    @PostMapping("/updateRole/{id}")
    @Operation(summary = "更新成员角色", description = "修改项目成员的角色，仅项目管理员可操作")
    public ReturnT<String> updateMemberRole(
            @Parameter(description = "成员ID") @PathVariable("id") Long id,
            @Parameter(description = "角色：ADMIN-项目管理员，DEVELOPER-开发者，VIEWER-查看者") @RequestParam("role") String role) {
        
        // TODO: 从登录用户获取用户名
        String loginUser = "admin"; // 临时写死，实际应该从session或token中获取
        
        return projectMemberService.updateMemberRole(id, role, loginUser);
    }

    @PostMapping("/updateStatus/{id}")
    @Operation(summary = "更新成员状态", description = "启用或禁用项目成员，仅项目管理员可操作")
    public ReturnT<String> updateMemberStatus(
            @Parameter(description = "成员ID") @PathVariable("id") Long id,
            @Parameter(description = "状态：0-禁用，1-启用") @RequestParam("status") Integer status) {
        
        // TODO: 从登录用户获取用户名
        String loginUser = "admin"; // 临时写死，实际应该从session或token中获取
        
        return projectMemberService.updateMemberStatus(id, status, loginUser);
    }

    @GetMapping("/load/{id}")
    @Operation(summary = "查询项目成员详情", description = "根据成员ID查询成员详细信息")
    public ReturnT<ProjectMember> load(@Parameter(description = "成员ID") @PathVariable("id") Long id) {
        ProjectMember member = projectMemberService.load(id);
        return new ReturnT<>(member);
    }

    @GetMapping("/loadByProjectAndUser")
    @Operation(summary = "根据项目和用户查询成员", description = "根据项目ID和用户ID查询成员信息")
    public ReturnT<ProjectMember> loadByProjectAndUser(
            @Parameter(description = "项目ID") @RequestParam("projectId") Long projectId,
            @Parameter(description = "用户ID") @RequestParam("userId") String userId) {
        
        ProjectMember member = projectMemberService.loadByProjectAndUser(projectId, userId);
        return new ReturnT<>(member);
    }

    @GetMapping("/byProject/{projectId}")
    @Operation(summary = "查询项目成员列表", description = "查询指定项目的所有成员")
    public ReturnT<List<ProjectMember>> findByProjectId(@Parameter(description = "项目ID") @PathVariable("projectId") Long projectId) {
        List<ProjectMember> members = projectMemberService.findByProjectId(projectId);
        return new ReturnT<>(members);
    }

    @GetMapping("/byUser/{userId}")
    @Operation(summary = "查询用户参与的项目", description = "查询指定用户参与的所有项目成员记录")
    public ReturnT<List<ProjectMember>> findByUserId(@Parameter(description = "用户ID") @PathVariable("userId") String userId) {
        List<ProjectMember> members = projectMemberService.findByUserId(userId);
        return new ReturnT<>(members);
    }

    @GetMapping("/admins/{projectId}")
    @Operation(summary = "查询项目管理员", description = "查询指定项目的所有管理员")
    public ReturnT<List<ProjectMember>> findAdminsByProjectId(@Parameter(description = "项目ID") @PathVariable("projectId") Long projectId) {
        List<ProjectMember> admins = projectMemberService.findAdminsByProjectId(projectId);
        return new ReturnT<>(admins);
    }

    @GetMapping("/isMember")
    @Operation(summary = "检查是否为项目成员", description = "检查指定用户是否为项目成员")
    public ReturnT<Boolean> isMember(
            @Parameter(description = "项目ID") @RequestParam("projectId") Long projectId,
            @Parameter(description = "用户ID") @RequestParam("userId") String userId) {
        
        boolean isMember = projectMemberService.isMember(projectId, userId);
        return new ReturnT<>(isMember);
    }

    @GetMapping("/count/{projectId}")
    @Operation(summary = "统计项目成员数量", description = "统计指定项目的成员总数")
    public ReturnT<Integer> countByProjectId(@Parameter(description = "项目ID") @PathVariable("projectId") Long projectId) {
        int count = projectMemberService.countByProjectId(projectId);
        return new ReturnT<>(count);
    }

    @GetMapping("/roles")
    @Operation(summary = "获取项目角色列表", description = "获取所有可用的项目角色")
    public ReturnT<List<Map<String, Object>>> getProjectRoles() {
        List<Map<String, Object>> roles = projectMemberService.getProjectRoles();
        return new ReturnT<>(roles);
    }
}
