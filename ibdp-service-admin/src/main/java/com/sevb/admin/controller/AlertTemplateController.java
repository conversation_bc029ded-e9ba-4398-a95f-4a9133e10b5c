package com.sevb.admin.controller;

import com.sevb.admin.core.model.AlertTemplate;
import com.sevb.admin.service.AlertTemplateService;
import com.sevb.core.biz.model.ReturnT;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * 告警模板管理控制器
 * <AUTHOR>
 */
@RestController
@RequestMapping("/api/alert/template")
public class AlertTemplateController {

    private static final Logger logger = LoggerFactory.getLogger(AlertTemplateController.class);

    @Autowired
    private AlertTemplateService alertTemplateService;

    /**
     * 分页查询模板列表
     */
    @GetMapping("/list")
    public ReturnT<Map<String, Object>> pageList(@RequestParam(required = false, defaultValue = "0") int start,
                                                 @RequestParam(required = false, defaultValue = "10") int length,
                                                 @RequestParam(required = false) String templateCode,
                                                 @RequestParam(required = false) String templateName,
                                                 @RequestParam(required = false) String channelType,
                                                 @RequestParam(required = false) Integer enabled) {
        try {
            Map<String, Object> result = alertTemplateService.pageList(start, length, templateCode, templateName, channelType, enabled);
            return new ReturnT<>(result);
        } catch (Exception e) {
            logger.error("查询模板列表失败", e);
            return new ReturnT<>(ReturnT.FAIL_CODE, "查询失败: " + e.getMessage());
        }
    }

    /**
     * 保存模板
     */
    @RequestMapping("/save")
    @ResponseBody
    public ReturnT<String> save(AlertTemplate alertTemplate) {
        return alertTemplateService.save(alertTemplate);
    }

    /**
     * 更新模板
     */
    @RequestMapping("/update")
    @ResponseBody
    public ReturnT<String> update(AlertTemplate alertTemplate) {
        return alertTemplateService.update(alertTemplate);
    }

    /**
     * 删除模板
     */
    @RequestMapping("/delete")
    @ResponseBody
    public ReturnT<String> delete(@RequestParam("id") Long id) {
        return alertTemplateService.delete(id, "admin");
    }

    /**
     * 启用/禁用模板
     */
    @RequestMapping("/updateEnabled")
    @ResponseBody
    public ReturnT<String> updateEnabled(@RequestParam("id") Long id, @RequestParam("enabled") Integer enabled) {
        return alertTemplateService.updateEnabled(id, enabled, "admin");
    }

    /**
     * 批量删除模板
     */
    @RequestMapping("/batchDelete")
    @ResponseBody
    public ReturnT<String> batchDelete(@RequestParam("ids") String ids) {
        try {
            String[] idArray = ids.split(",");
            List<Long> idList = new java.util.ArrayList<>();
            for (String idStr : idArray) {
                idList.add(Long.parseLong(idStr.trim()));
            }
            return alertTemplateService.batchDelete(idList, "admin");
        } catch (Exception e) {
            logger.error("批量删除模板失败", e);
            return new ReturnT<>(ReturnT.FAIL_CODE, "批量删除失败: " + e.getMessage());
        }
    }

    /**
     * 获取模板详情
     */
    @RequestMapping("/detail")
    @ResponseBody
    public ReturnT<AlertTemplate> detail(@RequestParam("id") Long id) {
        try {
            AlertTemplate template = alertTemplateService.load(id);
            if (template == null) {
                return new ReturnT<>(ReturnT.FAIL_CODE, "模板不存在");
            }
            return new ReturnT<>(template);
        } catch (Exception e) {
            logger.error("获取模板详情失败", e);
            return new ReturnT<>(ReturnT.FAIL_CODE, "获取失败: " + e.getMessage());
        }
    }

    /**
     * 根据模板编码查询模板列表
     */
    @RequestMapping("/listByCode")
    @ResponseBody
    public ReturnT<List<AlertTemplate>> listByCode(@RequestParam("templateCode") String templateCode) {
        try {
            List<AlertTemplate> templates = alertTemplateService.findByTemplateCode(templateCode);
            return new ReturnT<>(templates);
        } catch (Exception e) {
            logger.error("根据模板编码查询失败", e);
            return new ReturnT<>(ReturnT.FAIL_CODE, "查询失败: " + e.getMessage());
        }
    }

    /**
     * 根据通道类型查询模板列表
     */
    @RequestMapping("/listByChannel")
    @ResponseBody
    public ReturnT<List<AlertTemplate>> listByChannel(@RequestParam("channelType") String channelType) {
        try {
            List<AlertTemplate> templates = alertTemplateService.findByChannelType(channelType);
            return new ReturnT<>(templates);
        } catch (Exception e) {
            logger.error("根据通道类型查询失败", e);
            return new ReturnT<>(ReturnT.FAIL_CODE, "查询失败: " + e.getMessage());
        }
    }

    /**
     * 获取模板变量
     */
    @RequestMapping("/variables")
    @ResponseBody
    public ReturnT<List<Map<String, Object>>> getTemplateVariables(@RequestParam("templateCode") String templateCode,
                                                                   @RequestParam("channelType") String channelType) {
        try {
            List<Map<String, Object>> variables = alertTemplateService.getTemplateVariables(templateCode, channelType);
            return new ReturnT<>(variables);
        } catch (Exception e) {
            logger.error("获取模板变量失败", e);
            return new ReturnT<>(ReturnT.FAIL_CODE, "获取失败: " + e.getMessage());
        }
    }

    /**
     * 验证模板
     */
    @RequestMapping("/validate")
    @ResponseBody
    public ReturnT<String> validateTemplate(@RequestParam("templateContent") String templateContent,
                                           @RequestParam(required = false) String variables) {
        return alertTemplateService.validateTemplateVariables(templateContent, variables);
    }

    /**
     * 预览模板
     */
    @RequestMapping("/preview")
    @ResponseBody
    public ReturnT<String> previewTemplate(@RequestParam("templateCode") String templateCode,
                                          @RequestParam("channelType") String channelType,
                                          @RequestBody Map<String, Object> variables) {
        try {
            String content = alertTemplateService.renderTemplate(templateCode, channelType, variables);
            return new ReturnT<>(ReturnT.SUCCESS_CODE, "预览成功", content);
        } catch (Exception e) {
            logger.error("预览模板失败", e);
            return new ReturnT<>(ReturnT.FAIL_CODE, "预览失败: " + e.getMessage());
        }
    }

    /**
     * 复制模板
     */
    @RequestMapping("/copy")
    @ResponseBody
    public ReturnT<String> copyTemplate(@RequestParam("id") Long id,
                                       @RequestParam("newTemplateCode") String newTemplateCode,
                                       @RequestParam("newTemplateName") String newTemplateName) {
        try {
            AlertTemplate sourceTemplate = alertTemplateService.load(id);
            if (sourceTemplate == null) {
                return new ReturnT<>(ReturnT.FAIL_CODE, "源模板不存在");
            }

            AlertTemplate newTemplate = new AlertTemplate();
            newTemplate.setTemplateCode(newTemplateCode);
            newTemplate.setTemplateName(newTemplateName);
            newTemplate.setChannelType(sourceTemplate.getChannelType());
            newTemplate.setTemplateContent(sourceTemplate.getTemplateContent());
            newTemplate.setVariables(sourceTemplate.getVariables());
            newTemplate.setDescription("复制自: " + sourceTemplate.getTemplateName());
            newTemplate.setEnabled(0); // 默认禁用
            newTemplate.setCreateUser("admin");

            return alertTemplateService.save(newTemplate);
        } catch (Exception e) {
            logger.error("复制模板失败", e);
            return new ReturnT<>(ReturnT.FAIL_CODE, "复制失败: " + e.getMessage());
        }
    }
}
