package com.sevb.admin.controller;

import com.sevb.admin.core.model.WorkflowNode;
import com.sevb.admin.dto.BatchNodePositionUpdateDTO;
import com.sevb.admin.dto.NodePositionUpdateDTO;
import com.sevb.admin.service.WorkflowNodeService;
import com.sevb.core.biz.model.ReturnT;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import jakarta.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * 工作流节点管理控制器
 */
@Slf4j
@RestController
@RequestMapping("/workflow-node")
@Tag(name = "工作流节点管理", description = "工作流节点的增删改查操作")
public class WorkflowNodeController {
    
    @Resource
    private WorkflowNodeService workflowNodeService;
    
    @GetMapping("/listByWorkflow")
    @Operation(summary = "根据工作流ID查询所有节点", description = "返回指定工作流的所有节点列表")
    public ReturnT<List<WorkflowNode>> listByWorkflow(
            @Parameter(description = "工作流ID") @RequestParam("workflowId") Long workflowId) {
        return workflowNodeService.listByWorkflow(workflowId);
    }
    
    @GetMapping("/pageList")
    @Operation(summary = "分页查询工作流节点", description = "支持按节点名称和类型筛选")
    public ReturnT<Map<String, Object>> pageList(
            @Parameter(description = "页码") @RequestParam(value = "pageNum", defaultValue = "1") int pageNum,
            @Parameter(description = "页大小") @RequestParam(value = "pageSize", defaultValue = "10") int pageSize,
            @Parameter(description = "工作流ID") @RequestParam("workflowId") Long workflowId,
            @Parameter(description = "节点名称") @RequestParam(value = "nodeName", required = false) String nodeName,
            @Parameter(description = "节点类型") @RequestParam(value = "nodeType", required = false) String nodeType) {
        return workflowNodeService.pageList(pageNum, pageSize, workflowId, nodeName, nodeType);
    }
    
    @GetMapping("/{id}")
    @Operation(summary = "根据ID获取节点详情", description = "返回指定ID的节点详细信息")
    public ReturnT<WorkflowNode> load(@Parameter(description = "节点ID") @PathVariable("id") Long id) {
        return workflowNodeService.load(id);
    }
    
    /**
     * 根据工作流ID和节点编码获取节点
     */
    @GetMapping("/getByCode")
    @Operation(summary = "根据工作流ID和节点编码获取节点", description = "返回指定工作流中指定编码的节点")
    public ReturnT<WorkflowNode> getByCode(
            @Parameter(description = "工作流ID") @RequestParam("workflowId") Long workflowId,
            @Parameter(description = "节点编码") @RequestParam("nodeCode") String nodeCode) {
        return workflowNodeService.loadByCode(workflowId, nodeCode);
    }
    
    @PostMapping("/add")
    @Operation(summary = "添加工作流节点", description = "创建新的工作流节点")
    public ReturnT<String> add(@RequestBody WorkflowNode workflowNode) {
        // TODO: 从登录用户获取用户名
        String loginUser = "admin"; // 临时写死，实际应该从session或token中获取

        return workflowNodeService.add(workflowNode, loginUser);
    }
    
    /**
     * 批量添加工作流节点
     */
    @PostMapping("/batchAdd")
    @Operation(summary = "批量添加工作流节点", description = "批量创建多个工作流节点")
    public ReturnT<String> batchAdd(@RequestBody List<WorkflowNode> nodes) {
        // TODO: 从登录用户获取用户名
        String loginUser = "admin"; // 临时写死，实际应该从session或token中获取
        
        return workflowNodeService.batchAdd(nodes, loginUser);
    }
    
    /**
     * 更新工作流节点
     */
    @PostMapping("/update")
    @Operation(summary = "更新工作流节点", description = "更新现有的工作流节点信息")
    public ReturnT<String> update(@RequestBody WorkflowNode workflowNode) {
        // TODO: 从登录用户获取用户名
        String loginUser = "admin"; // 临时写死，实际应该从session或token中获取
        
        return workflowNodeService.update(workflowNode, loginUser);
    }
    
    /**
     * 批量更新工作流节点
     */
    @PostMapping("/batchUpdate")
    @Operation(summary = "批量更新工作流节点", description = "批量更新多个工作流节点")
    public ReturnT<String> batchUpdate(@RequestBody List<WorkflowNode> nodes) {
        // TODO: 从登录用户获取用户名
        String loginUser = "admin"; // 临时写死，实际应该从session或token中获取
        
        return workflowNodeService.batchUpdate(nodes, loginUser);
    }
    
    /**
     * 删除工作流节点
     */
    @PostMapping("/delete")
    @Operation(summary = "删除工作流节点", description = "逻辑删除指定的工作流节点")
    public ReturnT<String> delete(@Parameter(description = "节点ID") @RequestParam("id") Long id) {
        // TODO: 从登录用户获取用户名
        String loginUser = "admin"; // 临时写死，实际应该从session或token中获取
        
        return workflowNodeService.delete(id, loginUser);
    }
    
    /**
     * 批量删除工作流节点
     */
    @PostMapping("/batchDelete")
    @Operation(summary = "批量删除工作流节点", description = "批量逻辑删除多个工作流节点")
    public ReturnT<String> batchDelete(@RequestBody Map<String, List<Long>> request) {
        // TODO: 从登录用户获取用户名
        String loginUser = "admin"; // 临时写死，实际应该从session或token中获取
        
        List<Long> ids = request.get("ids");
        return workflowNodeService.batchDelete(ids, loginUser);
    }
    
    /**
     * 根据工作流ID删除所有节点
     */
    @PostMapping("/deleteByWorkflow")
    @Operation(summary = "根据工作流ID删除所有节点", description = "逻辑删除指定工作流的所有节点")
    public ReturnT<String> deleteByWorkflow(@Parameter(description = "工作流ID") @RequestParam("workflowId") Long workflowId) {
        // TODO: 从登录用户获取用户名
        String loginUser = "admin"; // 临时写死，实际应该从session或token中获取
        
        // 这里需要在 WorkflowNodeService 中添加对应的方法
        // return workflowNodeService.deleteByWorkflow(workflowId, loginUser);
        return new ReturnT<>(ReturnT.FAIL_CODE, "功能开发中");
    }
    
    /**
     * 更新节点位置
     */
    @PostMapping("/updatePosition")
    @Operation(summary = "更新节点位置", description = "更新节点在画布中的坐标位置")
    public ReturnT<String> updatePosition(@RequestBody NodePositionUpdateDTO request) {
        // TODO: 从登录用户获取用户名
        String loginUser = "admin"; // 临时写死，实际应该从session或token中获取
        
        return workflowNodeService.updatePosition(request.getId(), request.getPositionX(), 
                request.getPositionY(), loginUser);
    }
    
    /**
     * 批量更新节点位置
     */
    @PostMapping("/batchUpdatePosition")
    @Operation(summary = "批量更新节点位置", description = "批量更新多个节点的坐标位置")
    public ReturnT<String> batchUpdatePosition(@RequestBody BatchNodePositionUpdateDTO request) {
        // TODO: 从登录用户获取用户名
        String loginUser = "admin"; // 临时写死，实际应该从session或token中获取
        
        // 这里需要在 WorkflowNodeService 中添加对应的方法
        // return workflowNodeService.batchUpdatePosition(request.getUpdates(), loginUser);
        return new ReturnT<>(ReturnT.FAIL_CODE, "功能开发中");
    }
    
    /**
     * 验证节点编码是否唯一
     */
    @GetMapping("/validateNodeCode")
    @Operation(summary = "验证节点编码是否唯一", description = "检查节点编码在指定工作流中是否唯一")
    public ReturnT<Boolean> validateNodeCode(
            @Parameter(description = "工作流ID") @RequestParam("workflowId") Long workflowId,
            @Parameter(description = "节点编码") @RequestParam("nodeCode") String nodeCode,
            @Parameter(description = "排除的节点ID") @RequestParam(value = "excludeId", required = false) Long excludeId) {
        return workflowNodeService.validateNodeCode(workflowId, nodeCode, excludeId);
    }
}
