package com.sevb.admin.dto;

import com.sevb.admin.core.model.WorkflowNode;
import com.sevb.admin.core.model.WorkflowNodeRelation;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import java.util.List;

/**
 * 工作流设计增量保存数据传输对象
 */
@Data
@Schema(description = "工作流设计增量保存数据")
public class WorkflowDesignIncrementalDTO {

    /**
     * 工作流ID
     */
    @Schema(description = "工作流ID", example = "1", required = true)
    private Long workflowId;

    /**
     * 新增的节点
     */
    @Schema(description = "新增的节点列表")
    private List<WorkflowNode> addedNodes;

    /**
     * 更新的节点
     */
    @Schema(description = "更新的节点列表")
    private List<WorkflowNode> updatedNodes;

    /**
     * 删除的节点ID列表
     */
    @Schema(description = "删除的节点ID列表", example = "[1, 2, 3]")
    private List<Long> deletedNodeIds;
    
    /**
     * 新增的关系
     */
    private List<WorkflowNodeRelation> addedRelations;
    
    /**
     * 更新的关系
     */
    private List<WorkflowNodeRelation> updatedRelations;
    
    /**
     * 删除的关系ID列表
     */
    @Schema(description = "删除的关系ID列表", example = "[1, 2, 3]")
    private List<Long> deletedRelationIds;
}
