package com.sevb.admin.dto;

import com.sevb.admin.core.model.WorkflowNode;
import com.sevb.admin.core.model.WorkflowNodeRelation;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import java.util.List;

/**
 * 工作流设计数据传输对象
 */
@Data
@Schema(description = "工作流设计数据")
public class WorkflowDesignDTO {

    /**
     * 工作流ID
     */
    @Schema(description = "工作流ID", example = "1", required = true)
    private Long workflowId;

    /**
     * 节点列表
     */
    @Schema(description = "节点列表")
    private List<WorkflowNode> nodes;

    /**
     * 关系列表
     */
    @Schema(description = "关系列表")
    private List<WorkflowNodeRelation> relations;
}


