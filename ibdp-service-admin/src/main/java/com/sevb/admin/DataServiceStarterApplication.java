package com.sevb.admin;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.stereotype.Component;

@SpringBootApplication
public class DataServiceStarterApplication {

	public static void main(String[] args) {
        SpringApplication.run(DataServiceStarterApplication.class, args);
	}

	/**
	 * Knife4j启动信息输出
	 */
	@Component
	@Slf4j
	static class Knife4jStartupRunner implements ApplicationRunner {

		@Value("${server.port:8080}")
		private String serverPort;

		@Value("${server.servlet.context-path:}")
		private String contextPath;

		@Value("${knife4j.enable:false}")
		private boolean knife4jEnable;

		@Override
		public void run(ApplicationArguments args) throws Exception {
			if (!knife4jEnable) {
				return;
			}

			// 格式化上下文路径
			String formattedContextPath = "";
			if (contextPath != null && !contextPath.trim().isEmpty()) {
				formattedContextPath = contextPath.startsWith("/") ? contextPath : "/" + contextPath;
			}

			// 构建文档访问地址
			String knife4jUrl = String.format("http://localhost:%s%s/doc.html", serverPort, formattedContextPath);

			// 输出启动信息
			log.info("");
			log.info("=================================================================");
			log.info("                    数据开发平台启动成功！");
			log.info("=================================================================");
			log.info("应用端口: {}", serverPort);
			log.info("上下文路径: {}", contextPath != null && !contextPath.trim().isEmpty() ? contextPath : "/");
			log.info("");			log.info("📚 API文档地址:");
			log.info("   Knife4j增强文档: {}", knife4jUrl);
			log.info("");
			log.info("💡 提示:");
			log.info("   - 使用Knife4j增强文档获得更好的体验");
			log.info("   - 文档包含完整的接口说明和在线测试功能");
			log.info("   - 支持多种数据源和工作流管理功能");
			log.info("=================================================================");
		}
	}
}